import request from "@/utils/request"
export function predictFlow(obj?: Object, query?: Object) {
  return request({
    url: '/cloud-jsonflow/run-flow/predict',
    method: 'post',
    data: obj,
    params: query
  })
}

export function delFlowInfo(obj: any) {
  return request({
    url: '/cloud-jsonflow/run-flow/del/flow/info',
    method: 'delete',
    data: obj
  })
}
export function getNodesByIdType(id, flowType, isEdit) {
	// flowType: 0 模板 1 实例
	// viewType: 0 查看 1 编辑
	if (flowType === "1") { return getNodesByFlowInstId(id, isEdit) }
	else { return getNodesByDefFlowId(id) }
}

export function getNodesByDefFlowId(id) {
	return request({
		url: "/cloud-jsonflow/def-flow/nodes/" + id,
		method: "get",
	})
}

export function getNodesByFlowInstId(id, isEdit) {
	return request({
		url: "/cloud-jsonflow/run-flow/nodes/" + id + "/" + isEdit,
		method: "get",
	})
}

export function addObjByDefFlowId(obj) {
	return request({
		url: "/cloud-jsonflow/def-flow",
		method: "post",
		data: obj,
    headers: {
      hiddenError: true,
    },
	})
}

export function addObjByFlowInstId(obj) {
	return request({
		url: "/cloud-jsonflow/run-flow/flow-inst-id",
		method: "post",
		data: obj,
	})
}

export function listRunFlowsByDefFlowId(defFlowId, version) {
	return request({
		url: "/cloud-jsonflow/run-flow/def-flow-id/" + defFlowId + "/" + version,
		method: "get",
	})
}

export function listDefFlowByFlowKey(flowKey) {
	return request({
		url: "/cloud-jsonflow/def-flow/flow-key/" + flowKey,
		method: "get",
    headers:{
      repeatSubmit: false
    }
	})
}

export function listFlowApplicationByFlowKey(flowKey) {
	return request({
		url: "/cloud-order/flow-application/flow-key/" + flowKey,
		method: "get",
	})
}

export function fetchComment(query) {
	return request({
		url: "/cloud-jsonflow/comment/comment/list",
		method: "get",
		params: query,
	})
}

export function fetchRunJobs(flowInstId) {
	return request({
		url: "/cloud-jsonflow/run-job/list/" + flowInstId,
		method: "get",
	})
}

export function fetchRunRejects(flowInstId) {
	return request({
		url: "/cloud-jsonflow/run-reject/list/" + flowInstId,
		method: "get",
	})
}
