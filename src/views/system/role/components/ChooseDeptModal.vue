<template>
	<el-dialog
		v-model="visible"
		title="请选择用户对应的角色部门"
		width="500px"
		top="8vh"
		append-to-body
		class="small-form-dialog"
		:before-close="handleClose"
	>
		<div class="dept-tree">
			<div class="search-wrapper">
				<el-input
					v-model="filterText"
					placeholder="请输入部门名称"
					clearable
					maxlength="50"
					suffix-icon="Search"
				/>
			</div>
			<el-tree
				ref="treeRef"
				class="tree-wrapper"
				:data="treeList"
				show-checkbox
				check-strictly
				node-key="id"
				:check-on-click-node="false"
				:expand-on-click-node="false"
				:filter-node-method="filterNode"
				@check="handleClick"
				@node-click="handleClick"
			/>
		</div>
		<template #footer>
			<el-button @click="handleClose">
				取消
			</el-button>
			<el-button type="primary" :loading="loading" @click="confirm">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { deptTreeSelect } from "@/api/system/user"
import { batchModifyZsRoleDeptInfo } from "@/api/system/role"
const { proxy } = getCurrentInstance()

const { roleId } = defineProps(["roleId"])
const emit = defineEmits(["ok"])

const visible = ref(false)
const loading = ref(false)
const treeRef = ref(null)
const deptId = ref(null) // 部门id
const userList = ref([]) // 多选用户
const treeList = ref([]) // 部门树形列表
const filterText = ref("")

watch(filterText, val => {
	treeRef.value.filter(val)
})

function filterNode(value, data) {
	if (!value) { return true }
	return data.label.includes(value)
}

function getDeptList() {
	deptTreeSelect().then(res => {
		treeList.value = res.data
	})
}

function handleClick(data, tree) {
	treeRef.value.setCheckedKeys([]) // 删除所有选中节点
	treeRef.value.setCheckedNodes([data]) // 选中已选中节点
	deptId.value = data.id
}

async function open(e) {
	visible.value = true
	userList.value = e
	await nextTick()
	if (e.length == 1) {
		deptId.value = e[0].extraData.roleDeptId
		treeRef.value.setCheckedKeys([deptId.value], true)
	}
}
async function confirm() {
	if (!deptId.value) {
		proxy.$modal.msgError("请先选择部门")
		return
	}

	const params = userList.value.map(item => ({
		userId: item.userId,
		roleId,
		roleDeptId: deptId.value,
		id: item.extraData.userRoleDeptId,
	}))

	loading.value = true
	try {
		let res = await batchModifyZsRoleDeptInfo({ params })
		console.log("res", res)
		let { successNum, failureNum } = res.object
		if (failureNum > 0) {
			let tip = params.length > 1 ? `本次修改成功${successNum}条，失败${failureNum}条` : "修改失败"
			proxy.$modal.confirm(
				tip,
				"失败原因：同一个用户，角色部门不能重复！",
				"info",
				false,
			)
		}
		else {
			proxy.$modal.msgSuccess("操作成功")
		}
		handleClose()
		emit("ok")
	}
	catch (error) {
		proxy.$modal.msgError("操作失败，请重试")
	}
	finally {
		loading.value = false
	}
}
function handleClose() {
	treeRef.value.setCheckedKeys([]) // 删除所有选中节点
	visible.value = false
	deptId.value = null
}

getDeptList()

defineExpose({
	open,
})
</script>

<style scoped lang="scss">
.dept-tree {
  background: #f7f8f9;
  border-radius: 5px;
  .el-tree {
    background: transparent;
    --el-tree-node-content-height: 30px;
    &.tree-wrapper {
      max-height: 50vh;
      overflow-y: auto;
      padding: 0 15px 10px 15px;
    }
  }
  .search-wrapper {
    padding: 15px;
  }
}
</style>
