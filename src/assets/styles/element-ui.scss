$disabled-color: #0c1433;

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse>div>.el-submenu>.el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

.el-table {
  --el-table-row-hover-bg-color: #ddf1eb;
  --el-fill-color-lighter: #f7f8f9;

  th.el-table-fixed-column--right {
    background: #f0f1f3;
  }

  thead {
    color: #293240;

    th {
      font-weight: 400;

      &.el-table__cell {
        background: #f0f1f3 !important;
        border-bottom: 0;
        padding: 10px 0;
      }
    }
  }

  td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border-bottom: 0;
  }

  .el-table__inner-wrapper::before {
    opacity: 0;
  }

  tbody {
    .el-table__cell {
      padding: 15px 0;
    }
  }
}

@mixin dialog-form-width($min-width: 290px, $width: auto) {
  .el-form {
    &.el-form--inline .el-form-item {
      min-width: $min-width;
      width: $width;

      &.search-btns {
        min-width: 180px;
        width: 180px;
      }
    }
  }
}

.el-dialog {
  border-radius: 12px;

  .el-form {
    &.el-form--inline .el-form-item {
      min-width: 300px;

      .el-input,
      .el-select,
      .el-date-editor,
      .el-input-number {
        min-width: 190px;

        .el-input__suffix {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }

  &.small-form-dialog {
    @include dialog-form-width(240px, 240px);
  }

  .el-dialog__footer {
    .el-button {
      min-width: 75px;
    }
  }
}

.el-dialog:not(.el-dialog--center) {
  .el-dialog__header {
    .el-dialog__title {
      @apply title-line;
      font-weight: bold;
      color: #0c1433;
      font-size: 16px;
    }
  }
}

.el-button {
  --el-button-font-weight: 400;
  --el-button-hover-link-text-color: #048a5f;
  --el-color-primary-light-10: #D9EDE8;
  --el-button-hover-border-color: var(--el-button-hover-bg-color);

  &:hover {
    --el-button-font-weight: 500;
  }

  &:not(.is-circle, .el-button--small, .is-link) {
    min-height: 35px;
  }
}

.el-button--primary.is-plain:not(.has-line) {
  --el-button-border-color: var(--el-color-primary-light-9);
  --el-button-bg-color: var(--el-color-primary-light-9);
}

.el-button--primary.is-plain.is-deep {
  --el-button-border-color: var(--el-color-primary-light-10);
  --el-button-bg-color: var(--el-color-primary-light-10);
  &.is-disabled{
    border-color: var(--el-color-primary-light-9);
  }
}

.el-button--danger.is-plain {
  --el-button-border-color: var(--el-color-danger-light-8);
  --el-button-bg-color: var(--el-color-danger-light-8);

  &.is-disabled {
    border-color: var(--el-color-danger-light-9);
  }
}

.el-button--info.is-link {
  --el-button-text-color: #6b6b6b;
}

.el-button+.operation-btn {
  margin-left: 12px;
}

.el-form.el-form--label-right .el-form-item:not(.el-form-item--label-top) .el-form-item__label {
  line-height: 1.2;
  align-items: center;
  text-align: right;
}

.el-form-item--default {
  $label-height: 35px; // 使用变量管理高度

  &:not(.el-form-item--label-top) {
    .el-form-item__label {
      min-height: $label-height;
    }
  }
}

.el-form--inline .el-form-item {
  margin-right: 20px;
}

// el-radio 调整成方形打勾
.el-radio__inner {
  width: 16px;
  height: 16px;
  border: 1px solid #dcdcdc;
}

.el-radio__input.is-disabled.is-checked {
  .el-radio__inner {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary);
  }
}

.el-radio__inner::after {
  background: var(--el-color-primary) !important;
}

.el-radio-button__original-radio:disabled:checked+.el-radio-button__inner {
  --el-radio-button-disabled-checked-fill: var(--el-color-primary);
  color: #fff;
}

.el-radio__input.is-checked .el-radio__inner::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 95%;
  height: 95%;
  border-radius: 50%;
  border: 2.8px solid #fff;
  content: '';
  transform: translate(-50%, -50%);
}

.el-radio__input.is-disabled+span.el-radio__label {
  color: $disabled-color;
}

.el-checkbox__input.is-disabled+span.el-checkbox__label {
  color: $disabled-color;
}

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner , .el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner{
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);

  &::after{
    border-color: #fff;
  }
  &::before {
    background-color: #fff;
    border-color: #fff;
  }
}

.el-checkbox__inner::after {
  top: 2px;
  left: 5px;
}

.el-select--default .el-select__wrapper,
.el-input--default .el-input__wrapper {
  min-height: 35px;
}

// 禁用样式
.el-textarea.is-disabled .el-textarea__inner {
  background: #f0f0f1;
  box-shadow: none;
  color: $disabled-color;
}

.el-input.is-disabled {
  .el-input__wrapper {
    background: #f0f0f1;
    box-shadow: none;
  }

  .el-input__inner {
    color: $disabled-color;
    -webkit-text-fill-color: $disabled-color;
  }
}

.el-select__wrapper.is-disabled {
  background: #f0f0f1;
  box-shadow: none;

  &:hover {
    box-shadow: none;
  }

  .el-select__selected-item {
    color: $disabled-color;

    &.el-select__placeholder.is-transparent {
      color: transparent;
    }
  }

  .el-tag.el-tag--info {
    --el-tag-bg-color: var(--el-color-info-light-7);
  }

  .el-select__suffix {
    display: none;
  }
}

input:disabled::-webkit-input-placeholder,
textarea:disabled::-webkit-input-placeholder {
  -webkit-text-fill-color: rgba(255, 255, 255, 0);
}

input:read-only::-webkit-input-placeholder,
textarea:read-only::-webkit-input-placeholder {
  -webkit-text-fill-color: rgba(255, 255, 255, 0);
}

// el-input-number 样式
.el-input-number.is-controls-right:not([class*='small']) .el-input-number__increase {
  --el-input-number-controls-height: 17.5px !important;
}

.el-input-number.is-disabled {
  width: min-content;

  &.is-controls-right {
    .el-input__wrapper {
      padding-right: 15px;
    }
  }

  .el-input {
    min-width: 80px;
  }

  .el-input__wrapper {
    padding-right: 10px;
    padding-left: 10px;
  }

  .el-input-number__increase,
  .el-input-number__decrease {
    display: none;
  }
}

.el-input-number:not([class*='is-controls-right']) {

  .el-input-number__increase,
  .el-input-number__decrease {
    border: 4px solid #fff;
    background: #E6E9ED;
    border-radius: 8px;
    color: #8F939A;
    font-weight: bold;

    &:hover {
      background: var(--el-color-primary);
      color: #fff;
    }
  }
}

// message 颜色
.el-message--success {
  --el-message-text-color: #27ba9b;
}

.el-button--danger.is-link:not(.is-disabled):hover {
  color: red;
}

.el-tag.el-tag--info {
  --el-tag-text-color: $disabled-color;
}

.el-notification--success {
  color: #24a87e !important;
}

// 自定义确认弹窗
.el-message-box-danger {
  border-radius: 12px;
  padding-left: 15px;
  --el-messagebox-width: 440px;

  .el-message-box-icon--warning {
    font-size: 35px;
    color: #f94e4f;
  }

  .el-message-box-icon--info {
    font-size: 35px;
  }

  .el-message-box-icon--success {
    font-size: 35px;
    color: var(--el-color-primary);
  }

  .el-message-box__container {
    align-items: start;

    .el-message-box__message {
      color: #0c1433;
      min-height: 45px;
      padding-top: 4px;

      p {
        white-space: pre-line;
      }

      strong {
        font-size: 16px;
      }
    }
  }

  .confirm-content {
    color: #6b6b6b;
    font-size: 14px;
  }

  .el-message-box__close {
    color: #d1d1d1;
    font-size: 18px;
  }

  .el-message-box__btns {
    padding: 20px 10px 5px;

    .el-button {
      min-width: 80px;
      height: 34px;
    }

    .el-button+.el-button {
      margin-left: 12px;
    }
  }
}

// 自定义居中弹窗
.el-alert-box-custom {
  min-width: 140px;
  width: max-content;
  border-radius: 12px;
  padding-left: 20px;
  padding-right: 20px;

  .el-message-box__header {
    padding-top: 10px;
    padding-bottom: 0;
    .el-message-box__title{
    font-size: 14px;
    }
  }

  .el-message-box-icon--success {
    font-size: 32px;
    color: var(--el-color-primary);
  }
}

.el-drawer__header {
  margin-bottom: 15px;
}
