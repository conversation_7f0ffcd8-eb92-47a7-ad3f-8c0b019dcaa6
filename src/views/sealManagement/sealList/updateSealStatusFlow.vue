<template>
  <div class="app-container seal-flow">
    <div class="title-line">
      {{ configKey === flowConfigKey.SEAL_ENABLE_FLOW_KEY ? "启用" : "停用" }}
    </div>
    <flow-initiate
      class="white-body-box p-30px! min-h-70vh!"
      :curr-flow-form="currFlowForm"
      :showSuccessMsg="false"
      @handle-initiate-order="handleSuccess"
    />
  </div>
</template>

<script setup>
import { fetchByConfig } from "@/api/order/flow-application"
import { flowConfigKey } from "@/config/constant"

const route = useRoute()
const { proxy } = getCurrentInstance()

const FlowInitiate = defineAsyncComponent(() =>
  import("@/views/order/flow-application/initiate.vue")
)
const currFlowForm = ref({})
const configKey = route.query.configKey
async function getFlowConfig() {
  let res = await fetchByConfig(configKey)
  if (+res.code === 200) {
    currFlowForm.value = res.object
  }
}

function handleSuccess(e) {
  proxy.$modal.confirm("提交成功", "即将返回印章列表", "success", false).then(() => {
    proxy.$tab.closeOpenPage({ path: "/ess/sealList" })
  })
}

getFlowConfig()
</script>

<style scoped lang="scss">
.seal-flow {
  :deep(.el-dialog__footer) {
    text-align: center;
    .draft-btn {
      display: none;
    }
  }
}
</style>
