<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item :label="$t('tabsOption.formName')" prop="formName">
						<el-input
							v-model="state.queryForm.formName"
							:placeholder="t('tabsOption.inputFormNameTip')"
							style="max-width: 180px;"
						/>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							{{ $t('common.queryBtn') }}
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							{{ $t('common.resetBtn') }}
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%;">
					<el-tooltip placement="top">
						<template #content>
							{{ $t('jfI18n.oneBtnDesign') }}
						</template>
						<el-button
							v-auth="'jsonflow_tabsoption_add'"
							type="primary"
							class="ml10"
							icon="Pointer"
							@click="handleDesignTabs(true)"
						/>
					</el-tooltip>

					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.addBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_tabsoption_add'"
							icon="Plus"
							type="primary"
							class="ml10"
							@click="formDialogRef.openDialog('add')"
						/>
					</el-tooltip>

					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.delBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_tabsoption_del'"
							plain
							:disabled="multiple"
							icon="Delete"
							type="primary"
							class="ml10"
							@click="handleDelete(selectObjs)"
						/>
					</el-tooltip>

					<right-toolbar
						v-model:show-search="showSearch"
						:export="'jsonflow_tabsoption_export'"
						class="ml10"
						style="float: right; margin-right: 20px;"
						@export-excel="exportExcel"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%;"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" :label="t('tabsOption.index')" width="60" />
				<el-table-column prop="icon" :label="t('tabsOption.icon')" show-overflow-tooltip>
					<template #default="scope">
						<SvgIcon :name="scope.row.icon" :size="20" />
					</template>
				</el-table-column>
				<el-table-column prop="formName" :label="t('tabsOption.formName')" show-overflow-tooltip />
				<el-table-column
					prop="groupName"
					:label="t('tabsOption.groupName')"
					show-overflow-tooltip
				/>
				<el-table-column prop="path" label="PC端路径" show-overflow-tooltip />
				<!--            <el-table-column prop="isActive" label="默认展示"  show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.YES_OR_NO" :value="scope.row.isActive"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="isAutoAudit" label="提交时自动审批"  show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.YES_OR_NO" :value="scope.row.isAutoAudit"></dict-tag>
                    </template>
                </el-table-column>-->
				<el-table-column prop="type" label="表单类型" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.FORM_TYPE" :value="scope.row.type" />
					</template>
				</el-table-column>
				<el-table-column prop="status" label="状态" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.TEMP_STATUS" :value="scope.row.status" />
					</template>
				</el-table-column>
				<el-table-column prop="sort" label="排序值" show-overflow-tooltip />
				<el-table-column prop="version" label="版本" show-overflow-tooltip />
				<!--                <el-table-column prop="createTime" :label="t('tabsOption.createTime')"  show-overflow-tooltip/>-->
				<el-table-column :label="$t('common.action')" width="180">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.viewBtn') }}
							</template>
							<el-button
								v-auth="'jsonflow_tabsoption_view'"
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								修改
							</template>
							<el-button
								v-auth="'jsonflow_tabsoption_edit'"
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.delBtn') }}
							</template>
							<el-button
								v-auth="'jsonflow_tabsoption_del'"
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								{{ $t('jfI18n.printTemplate') }}
							</template>
							<el-button
								icon="Document"
								text
								type="primary"
								@click="handlePrintTemplate(scope.row)"
							/>
						</el-tooltip>

						<el-tooltip v-if="scope.row.formInfo" placement="top">
							<template #content>
								{{ $t('jfI18n.viewPageBtn') }}
							</template>
							<el-button
								icon="ZoomIn"
								text
								type="primary"
								@click="openPreview(scope.row)"
							/>
						</el-tooltip>

						<el-tooltip placement="top">
							<template #content>
								{{ $t('jfI18n.onceMoreDesign') }}
							</template>
							<el-button
								v-auth="'jsonflow_tabsoption_edit'"
								text
								type="primary"
								icon="Pointer"
								@click.native="handleDesignTabs(true, scope.row)"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination
				v-bind="state.pagination"
				@size-change="sizeChangeHandle"
				@current-change="currentChangeHandle"
			/>
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />

		<!-- 表单设计 -->
		<el-drawer
			v-model="data.showDesignTabs"
			class="flow-overflow-drawer"
			direction="rtl"
			append-to-body
			size="100%"
			:with-header="false"
		>
			<tabs-form-design
				v-if="data.showDesignTabs"
				:curr-flow-form="data.currFlowForm"
				@handle-design-tabs="handleDesignTabs"
			/>
		</el-drawer>

		<!-- 查看表单 -->
		<el-dialog
			v-model="data.showViewOrder"
			top="20px"
			width="80%"
			title="查看表单"
			append-to-body
		>
			<form-render
				v-if="data.showViewOrder"
				:curr-flow-form="data.currFlowForm"
				:render-type="'-1'"
			/>
		</el-dialog>

		<!-- 打印模板设计器 -->
		<el-drawer
			v-model="data.showTinymceEditor"
			class="tinymce-print-drawer"
			append-to-body
			direction="rtl"
			size="100%"
			:title="data.tinymceTitle"
			@close="getDataList"
		>
			<tinymce-editor
				v-if="data.showTinymceEditor"
				:curr-flow-form="data.currFlowForm"
			/>
		</el-drawer>
	</div>
</template>

<script setup lang="ts" name="systemTabsOption">
import { BasicTableProps, useTable } from '@/hooks/table'
import { fetchList, delObjs, putObj } from '@/api/order/flow-application'
import { useMessage, useMessageBox } from '@/hooks/message'

import { useI18n } from 'vue-i18n'
import { deepClone } from '@/utils/index'
import { parseWithFunctions } from '@/flow'
import { utils } from '@/flow/designer/utils/common'

// 引入组件
const FormDialog = defineAsyncComponent(() => import('@/flow/components/tabs-option/form.vue'))
const TabsFormDesign = defineAsyncComponent(() =>
	import('@/flow/components/tabs-option/design.vue')
)
const FormRender = defineAsyncComponent(() => import('@/flow/components/form-create/render.vue'))
const TinymceEditor = defineAsyncComponent(() =>
	import('@/flow/components/tinymce/TinymceEditor.vue')
)

const { t } = useI18n()
// 定义查询字典

// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
	descs: ['create_time']
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	// 清空多选
	selectObjs.value = []
	getDataList()
}

// 导出excel
const exportExcel = () => {
	downBlobFile('/cloud-order/flow-application/export', state.queryForm, 'flow-application.xlsx')
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'))
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess(t('common.delSuccessText'))
	}
	catch (err) {
		proxy.$modal.msgError(err.msg)
	}
}

const data = reactive({
	showDesignTabs: false,
	showViewOrder: false,
	currFlowForm: {},
	showTinymceEditor: false,
	tinymceTitle: ''
})

function handlePrintTemplate(row) {
	data.currFlowForm = {
		id: row.id,
		formType: row.type,
		formId: row.id,
		formName: row.formName,
		path: row.path,
		isForm: true
	}
	data.currFlowForm.formInfo = row.formInfo
	data.tinymceTitle = '打印模板设计器（自定义《' + row.formName + '》打印格式）'
	data.showTinymceEditor = true
}

// 一键设计
const handleDesignTabs = async (bool: boolean, row?: any) => {
	data.showDesignTabs = bool
	if (bool === false) {
		getDataList()
		return
	}
	if (row) {
		data.currFlowForm = deepClone(row)
		data.currFlowForm.formInfo = parseWithFunctions(row.formInfo, true)
		data.currFlowForm.active = 'tabsSetting'
	}
	else {
		data.currFlowForm = {
			id: utils.getId(),
			active: 'tabsSetting',
			icon: 'flow-reg',
			formName: '未命名',
			type: '0',
			isActive: '1',
			isAutoAudit: '0',
			status: '-1',
			version: 1,
			isNew: true,
			sort: 1
		}
	}
}
// 查看表单
const openPreview = async (row: any) => {
	data.currFlowForm = deepClone(row)
	data.showViewOrder = true
}
</script>

<style lang="scss">
@use '../../../flow/components/style/flow-drawer.scss' as *;
</style>
