<template>
  <div class="login">
    <div class="login-logo">
      <img :src="settingsStore.loginLogoUrl" />
      <span>{{ settingsStore.systemTitle }}</span>
    </div>
    <el-form
      ref="loginRef"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      size="large"
      :show-message="false"
    >
      <h3 class="title">Netid登录</h3>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          auto-complete="off"
          placeholder="请输入netid"
          clearable
        >
          <template #prefix>
            <svg-icon name="local-login-user" class="el-input__icon input-icon" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="请输入密码"
          clearable
          show-password
          @keyup.enter="handleLogin"
        >
          <template #prefix>
            <svg-icon name="local-login-lock" class="el-input__icon input-icon" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="captchaEnabled" prop="code">
        <div class="code-input-box">
          <el-input
            v-model="loginForm.code"
            auto-complete="off"
            placeholder="请输入验证码"
            clearable
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <svg-icon name="local-login-code" class="el-input__icon input-icon" />
            </template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" class="login-code-img" @click="refreshCaptcha" />
          </div>
        </div>
      </el-form-item>
      <el-checkbox class="login-checkbox" v-model="loginForm.rememberMe">
        记住账号
      </el-checkbox>
      <el-form-item>
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          class="login-btn"
          @click.prevent="handleLogin"
        >
          <span>{{ loading ? "登录中..." : "登录" }}</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <footer v-if="defaultSettings.copyrightText" class="el-login-footer">
      {{ defaultSettings.copyrightText }}
    </footer>
  </div>
</template>

<script setup>
import { Session } from "@/utils/storage"
import Cookies from "js-cookie"
import { getCodeImg } from "@/api/login"
import { encrypt, decrypt } from "@/utils/jsencrypt"
import useUserStore from "@/store/modules/user"
import useSettingsStore from "@/store/modules/settings"
import defaultSettings from "@/settings"
const settingsStore = useSettingsStore()
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const cookiePrefix = import.meta.env.VITE_APP_MODULE
const rememberMeKey = `${cookiePrefix}-rememberMe`
const usernameKey = `${cookiePrefix}-username`
const passwordKey = `${cookiePrefix}-password`

const loginForm = ref({
  username: Cookies.get(usernameKey) || "",
  password: Cookies.get(passwordKey) ? decrypt(passwordKey) : "",
  rememberMe: Boolean(Cookies.get(rememberMeKey)) || false,
  code: "",
  uuid: "",
})

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的netid" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
}

const codeUrl = ref("")
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(true)
const redirect = ref(undefined)

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect
  },
  { immediate: true }
)

async function handleAuthCookies() {
  const { rememberMe, username, password } = loginForm.value
  const cookieMethod = rememberMe ? Cookies.set : Cookies.remove
  const options = rememberMe ? { expires: 30 } : undefined

  cookieMethod(usernameKey, username, options)
  cookieMethod(passwordKey, rememberMe ? encrypt(password) : "", options)
  cookieMethod(rememberMeKey, rememberMe, options)
}

async function handleLogin() {
  try {
    await proxy.$refs.loginRef.validate()
    loading.value = true
    await handleAuthCookies()
    await userStore.login(loginForm.value)
  } catch (error) {
    handleLoginError(error)
  } finally {
    loading.value = false
  }
}
function handleLoginError(error) {
  if (captchaEnabled.value) {
    refreshCaptcha()
  }
  console.error("登录失败:", error)
}

async function refreshCaptcha() {
  try {
    const res = await getCodeImg()
    captchaEnabled.value = res.captchaOnOff ?? true
    if (captchaEnabled.value) {
      codeUrl.value = `data:image/gif;base64,${res.img}`
      loginForm.value.uuid = res.uuid
    }
  } catch (error) {
    console.error("获取验证码失败:", error)
  }
}

refreshCaptcha()
</script>

<style lang="scss" scoped>
@import url("@/assets/fonts/font.scss");
.login {
  background-image: url("@/assets/images/login-bg.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  height: 100%;
}

.login-logo {
  padding: 40px 50px;
  display: flex;
  align-items: center;
  font-size: 32px;
  font-family: "DingTalk";
  img {
    object-fit: contain;
    height: 55px;
    margin-right: 12px;
  }
}

$inputHeight: 55px;

.login-form {
  border-radius: 6px;
  background: linear-gradient(to bottom, #cbffee 0%, #ffffff 6%);
  min-width: 420px;
  width: 20vw;
  max-width: 480px;
  padding: 50px 50px 40px;
  min-height: 470px;
  max-height: 600px;
  position: absolute;
  right: 15%;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0px 8px 11px 1px rgba(8, 131, 255, 0.08);
  z-index: 99;
  .title {
    margin: 0px auto 40px auto;
    text-align: center;
    color: #0c1433;
    position: relative;
    font-size: 25px;
    font-family: "DingTalk";
    font-weight: normal;
  }
  :deep(.el-form-item--large) {
    margin-bottom: 18px;
  }
  :deep(.el-checkbox.el-checkbox--large) {
    height: 30px;
    margin-top: -12px;
    color: #677281;
  }
  .el-input {
    height: $inputHeight;
    --el-input-border-color: #efefef;
    --el-input-placeholder-color: #a8adb4;
    input {
      height: $inputHeight;
    }
  }
  .input-icon {
    height: 30px;
    width: 16px;
    margin-right: 10px;
  }
}

.code-input-box {
  display: flex;
  align-items: center;
  width: 100%;
}
.login-code {
  width: 120px;
  height: $inputHeight;
  flex-shrink: 0;
  img {
    width: 100%;
    cursor: pointer;
    vertical-align: middle;
    height: $inputHeight;
    padding-left: 20px;
  }
}

.login-btn {
  width: 100%;
  height: 55px;
  border-radius: 6px;
  margin-top: 30px;
  font-size: 16px;
  border-radius: 5px;
}

.el-login-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-size: 14px;
  letter-spacing: 1px;
  padding: 15px 0;
}
@media only screen and (min-width: 1680px) {
  .login-form {
    min-height: 520px;
    :deep(.el-form-item) {
      margin-bottom: 20px;
      .el-input__inner {
        line-height: 55px;
        height: 55px;
        font-size: 16px;
      }
      .svg-icon {
        font-size: 18px;
      }
    }
    .login-btn {
      height: 60px;
      font-weight: bold;
      font-size: 18px;
      margin-top: 40px;
    }
    .login-code {
      height: 54px;
    }
  }
}

.login-checkbox {
  :deep(.el-checkbox__inner::after) {
    top: 1px;
    left: 4px;
  }
}
</style>
