<template>
  <div class="app-container">
    <div>
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
        class="white-form-box"
        label-width="100px"
      >
        <el-form-item label="印章编号" prop="sealNo">
          <el-input
            v-model="queryParams.sealNo"
            placeholder="请输入印章编号"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="印章名称" prop="sealName">
          <el-input
            v-model="queryParams.sealName"
            placeholder="请输入印章名称"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="印章类型" prop="sealType">
          <dict-select
            v-model="queryParams.sealType"
            :options="seal_type"
            value-type="string"
          />
        </el-form-item>
        <el-form-item label="印章状态" prop="sealStatus">
          <dict-select
            v-model="queryParams.sealStatus"
            :options="seal_status"
            value-type="string"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTimeRange">
          <el-date-picker
            v-model="queryParams.createTimeRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="common-input-width"
            clearable
            :default-time="dateDefaultTime"
          />
        </el-form-item>
        <el-form-item label="创建人" prop="createUser">
          <el-input
            v-model="queryParams.createUser"
            placeholder="请输入创建人姓名"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="归属部门" prop="deptId">
          <dept-picker v-model="queryParams.deptId" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <CustomTable
        ref="customTableRef"
        v-model:show-search="showSearch"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :data="tableData"
        custom
        :loading="loading"
        :total="total"
        :row-class-name="rowClassName"
        class="table-container"
        @reload="getList"
      >
        <template #actions>
          <el-button v-auths="['ess:list:add']" type="primary" @click="handleAdd">
            添加印章
          </el-button>
          <el-button
            v-auths="['ess:list:export']"
            type="primary"
            plain
            class="is-deep"
            @click="handleExport"
          >
            导出
          </el-button>
        </template>
        <vxe-column type="checkbox" width="40" fixed="left" />
        <vxe-column type="seq" width="70" />
        <vxe-column field="sealNo" title="印章编号" :min-width="isEdit ? 250 : 150">
          <template #default="{ row }">
            <span v-if="!row.isEdit" :class="{ 'text-muted': !row.sealNo }">
              {{ row.sealNo || "-" }}
            </span>
            <el-input v-else v-model="row.sealNo" clearable />
          </template>
        </vxe-column>
        <vxe-column title="印章归属部门" field="deptName" :min-width="isEdit ? 250 : 150">
          <template #default="{ row }">
            <span v-if="!row.isEdit" :class="{ 'text-muted': !row.deptName }">
              {{ row.deptName || "-" }}
            </span>
            <el-tree-select
              v-else
              v-model="row.deptId"
              :data="deptOptions"
              check-strictly
              :render-after-expand="false"
              clearable
              value-key="id"
            />
          </template>
        </vxe-column>
        <vxe-column title="印章ID" field="essSealId" min-width="160" />
        <vxe-column title="印章名称" field="sealName" min-width="180" show-overflow />
        <vxe-column title="印章状态" field="sealStatus" min-width="100" show-overflow>
          <template #default="{ row }">
            <dict-tag :options="seal_status" :value="row.sealStatus" />
          </template>
        </vxe-column>
        <vxe-column title="印章归属主体" field="sealBelongSubject" min-width="110">
          <template #default="{ row }">
            <dict-tag :options="seal_belong_subject" :value="row.sealBelongSubject" />
          </template>
        </vxe-column>
        <vxe-column title="印章类型" field="sealType" min-width="150">
          <template #default="{ row }">
            {{ selectDictLabel(seal_type, row.sealType) }}
          </template>
        </vxe-column>
        <vxe-column title="创建人" field="createUser" min-width="100" show-overflow />
        <vxe-column title="创建时间" field="createTime" width="170">
          <template #default="{ row }">
            <span>{{ parseTime(row.createTime) }}</span>
          </template>
        </vxe-column>
        <vxe-column title="操作" field="opt" fixed="right" width="240">
          <template #default="{ row }">
            <TableColOptBtn
              :key="Math.random()"
              :buttons="[
                {
                  text: '编辑归属',
                  click: () => handleUpdateBelong(row),
                  permission: ['ess:list:edit'],
                  hidden: row.isEdit,
                },
                {
                  text: '保存',
                  click: () => saveBelong(row),
                  hidden: !row.isEdit,
                },
                {
                  text: '取消',
                  click: () => cancelBelong(row),
                  hidden: !row.isEdit,
                },
                {
                  text: '授权列表',
                  click: () => viewAuthorizationList(row),
                  permission: ['ess:list:authList'],
                  hidden: ['CHECKING', 'CHECKING-SADM'].includes(row.sealStatus),
                },
                {
                  text: `${row.sealStatus == 'DISABLE' ? '启用' : '停用'}(发起审核)`,
                  click: () => updateStatusToAudit(row),
                  permission: ['ess:list:disableAudit'],
                  hidden: !['DISABLE', 'SUCCESS'].includes(row.sealStatus),
                },
                {
                  text: row.sealStatus == 'DISABLE' ? '启用' : '停用',
                  click: () => updateStatus(row),
                  permission: ['ess:list:disable'],
                  hidden: !['DISABLE', 'SUCCESS'].includes(row.sealStatus),
                },
                {
                  text: '查看',
                  click: () => viewDetails(row),
                  permission: ['ess:seal:sealDetails'],
                },
              ]"
            />
          </template>
        </vxe-column>
      </CustomTable>
    </div>

    <AddSealInfoPage ref="addSealInfoRef" />

    <!-- 印章授权 -->
    <SealAuthListPage ref="sealAuthListRef" :dept-options="deptOptions" />

    <FlowInitiateModal ref="flowInitiateModalRef" />
  </div>
</template>

<script setup name="SealList">
import {
  getSealListPage,
  updateSealBelong,
  adminEnableSeal,
  adminDisableSeal,
} from "@/api/ess/seal/seal"
import { fetchByConfig } from "@/api/order/flow-application"
import { deptTreeSelect } from "@/api/system/user"
import { essPrefix, flowConfigKey } from "@/config/constant"
import useWebSocket from "@/hooks/websocket"

const FlowInitiateModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowInitiateModal.vue")
)
const AddSealInfoPage = defineAsyncComponent(() =>
  import("./components/AddSealInfoPage.vue")
)
const SealAuthListPage = defineAsyncComponent(() =>
  import("./components/SealAuthListPage.vue")
)

const { proxy } = getCurrentInstance()
const { seal_type, seal_status, seal_belong_subject } = proxy.useDict(
  "seal_type",
  "seal_status",
  "seal_belong_subject"
)

const router = useRouter()

// ------------------------------ 列表的  变量和方法 ------------------------------
const tableData = ref([])
const deptOptions = ref([]) // 部门
const loading = ref(true)
const showSearch = ref(true)
const isEdit = ref(false) // 是否编辑状态 （编辑状态下单元格宽度加长）
const saveLoading = ref(false) // 编辑loading

const total = ref(0)

const queryParams = reactive({
  createTimeRange: [],
  deptId: null,
  sealType: null,
  sealStatus: null,
  limit: 10,
  page: 1,
})

const customTableRef = ref(null)
const selections = computed(() => {
  if (customTableRef.value) {
    return customTableRef.value.getCheckboxRecords()
  }
  return []
})

/** 查询列表 */
function getList() {
  loading.value = true
  queryParams.createStartTime = queryParams.createTimeRange?.[0]
  queryParams.createEndTime = queryParams.createTimeRange?.[1]
  getSealListPage(queryParams)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}
function getDeptList() {
  deptTreeSelect().then((res) => {
    deptOptions.value = res.data
  })
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

const addSealInfoRef = ref(null) // 新增印章
const flowInitiateModalRef = ref(null) // 新增表单
/** 新增按钮操作 */
async function handleAdd() {
  let res = await fetchByConfig("SEAL_REGISTER_FLOW_ID")
  flowInitiateModalRef.value.open(res.object)
}

// 修改编辑状态
function updateEditStatus(row, type) {
  row.isEdit = type
  isEdit.value = type
}
/** 修改归属按钮操作 */
function handleUpdateBelong(row) {
  updateEditStatus(row, true)
  row.oldSealNo = row.sealNo
  row.oldDeptId = row.deptId
}

function saveBelong(row) {
  if (!row.deptId || !row.sealNo) {
    proxy.$modal.msgError("请输入印章编号和归属部门")
    return
  }
  saveLoading.value = true
  updateSealBelong({
    id: row.id,
    deptId: row.deptId,
    sealNo: row.sealNo,
  })
    .then((res) => {
      proxy.$modal.msgSuccess("修改成功")
      updateEditStatus(row, false)
      getList()
    })
    .finally(() => {
      saveLoading.value = false
    })
}
// 取消修改
function cancelBelong(row) {
  updateEditStatus(row, false)
  row.sealNo = row.oldSealNo
  row.deptId = row.oldDeptId
}

const sealAuthListRef = ref(null)
/** 查看授权列表按钮操作 */
function viewAuthorizationList(row) {
  if (!row.deptId) {
    proxy.$modal.msgError("请先补充印章归属部门！")
    return
  }
  sealAuthListRef.value.open(row)
}
/** 查看详情按钮操作 */
function viewDetails(row) {
  router.push(`/ess/sealDetails/${row.id}`)
}
/** 启用/停用 发起审核 */
function updateStatusToAudit(row) {
  let configKey =
    row.sealStatus == "DISABLE"
      ? flowConfigKey.SEAL_ENABLE_FLOW_KEY
      : flowConfigKey.SEAL_DISABLE_FLOW_KEY
  router.push(`/ess/updateSealStatusFlow?sealId=${row.id}&configKey=${configKey}`)
}

/** 启用/停用 start */
// 使用WebSocket hooks
const successMsg = ref()
const { onConnect, wsClose } = useWebSocket({
  // 接收消息回调
  onMessage: (data) => {
    if (data === "SUCCESS") {
      loading.value = false
      proxy.$modal.msgSuccess(successMsg.value)
      getList()
      wsClose()
    }
  },
})
/** 启用/停用 */
function updateStatus(row) {
  const text = row.sealStatus == "DISABLE" ? "启用" : "停用"
  successMsg.value = `${text}成功`
  proxy.$modal.confirm(`确定${text}【${row.sealName}】印章？`).then(() => {
    const actionUrl = row.sealStatus == "DISABLE" ? adminEnableSeal : adminDisableSeal
    actionUrl(row.id).then((res) => {
      onConnect(row.essSealId)
      loading.value = true
      // if (+res.code === 200) {
      //   proxy.$modal.msgSuccess(`${text}成功`)
      //   getList()
      // }
    })
  })
}

/** 启用/停用end */

/** 导出按钮操作 */
function handleExport() {
  console.log("selections", selections.value)
  proxy.download(
    essPrefix + "/seal/export",
    {
      ...queryParams,
    },
    `印章列表_${new Date().getTime()}.xlsx`
  )
}

// 没有印章归属部门的那一行用颜色标注
function rowClassName({ row }) {
  return !row.deptId ? "row-grey" : ""
}

getList()
getDeptList()

// 组件卸载时断开连接
onUnmounted(() => {
  wsClose()
})
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
}
.table-container {
  :deep(.row-grey) {
    background-color: #f0f3f3;
  }
}
</style>
