<template>
  <div>
    <template v-for="(item, index) in values">
      <template v-if="validateGroupOptions(item)">
        <span v-if="!props.elTagType" :key="index" :class="props.elTagClass" :style="props.style">
          {{ showKeyName(item, values, index) }}
        </span>
        <el-tag
          v-else
          :disable-transitions="true"
          :key="index * 2"
          :index="index"
          :type="props.elTagType === 'primary' ? null : props.elTagType"
          :class="props.elTagClass"
          :style="props.style"
        >
          {{ showKeyName(item, values, index) }}
        </el-tag>
      </template>
    </template>
  </div>
</template>

<script setup lang="ts" name="convert-group-name">
import { computed } from 'vue'

const props = defineProps({
  // 数据
  options: {
    type: Array as any,
    default: []
  },
  // 当前的值
  value: [Number, String, Array],
  // 当前KEY
  valueKey: String,
  // 显示KEY
  showKey: String,
  // type
  elTagType: String,
  // class
  elTagClass: String,
  // style
  style: String
})

const values = computed(() => {
  if (props.value !== null && typeof props.value !== 'undefined') {
    return Array.isArray(props.value) ? props.value : [String(props.value)]
  } else {
    return []
  }
})

function validateGroupOptions(item) {
  let exist = null
  for (const option of props.options) {
    let find = option.options.find((f) => f[props.valueKey] === item)
    if (find) {
      exist = find
      break
    }
  }
  return exist
}

function showKeyName(item, values, index) {
  let exist = validateGroupOptions(item)
  return exist[props.showKey] + (index !== values.length - 1 ? ',' : '')
}
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
