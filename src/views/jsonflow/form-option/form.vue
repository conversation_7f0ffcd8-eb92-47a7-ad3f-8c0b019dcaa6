<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="120px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item label="类型" prop="type">
						<el-select
							v-model="form.type"
							placeholder="请选择类型"
							clearable
							filterable
							:disabled="true"
							@change="typeChange"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.FORM_DATA_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="表单类型" prop="formType">
						<el-select
							v-model="form.formType"
							placeholder="请选择表单类型"
							clearable
							filterable
							:disabled="disabledFields.formType"
							@change="formTypeChange"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.FORM_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="表单名称" prop="formName">
						<el-input v-model="form.formName" placeholder="请输入表单名称" />
					</el-form-item>
				</el-col>

				<el-col v-if="!hiddenFields.defFlowId" :span="12" class="mb-1">
					<el-form-item label="流程名称" prop="defFlowId">
						<el-select
							v-model="form.defFlowId"
							placeholder="请选择流程名称"
							clearable
							filterable
							@change="cascadeChange('defFlowId', ['flowNodeId'])"
						>
							<el-option
								v-for="(item, index) in dicData.defFlowId"
								:key="index"
								:label="item.flowName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col v-if="!hiddenFields.flowNodeId" :span="12" class="mb-1">
					<el-form-item label="节点名称" prop="flowNodeId">
						<el-select
							v-model="form.flowNodeId"
							placeholder="请选择节点名称"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col v-if="!hiddenFields.propId" :span="12" class="mb-1">
					<el-form-item label="属性ID" prop="propId">
						<el-input v-model="form.propId" placeholder="请输入属性ID" />
					</el-form-item>
				</el-col>
				<el-col v-if="!hiddenFields.label" :span="12" class="mb-1">
					<el-form-item label="属性名称" prop="label">
						<el-input v-model="form.label" placeholder="请输入属性名称" />
					</el-form-item>
				</el-col>
				<el-col v-if="!hiddenFields.prop" :span="12" class="mb-1">
					<el-form-item label="属性名" prop="prop">
						<el-input v-model="form.prop" placeholder="请输入属性名" />
					</el-form-item>
				</el-col>
				<el-col v-if="!hiddenFields.subForm" :span="12" class="mb-1">
					<el-form-item label="子表单属性名" prop="subForm">
						<el-input v-model="form.subForm" placeholder="请输入子表单属性名" />
					</el-form-item>
				</el-col>
				<el-col v-if="!hiddenFields.propType" :span="12" class="mb-1">
					<el-form-item label="属性类型" prop="propType">
						<el-input v-model="form.propType" placeholder="请输入属性类型" />
					</el-form-item>
				</el-col>
				<el-col v-if="!hiddenFields.permType" :span="12" class="mb-1">
					<el-form-item label="权限类型" prop="type">
						<el-radio-group v-model="form.permType">
							<el-radio v-for="(item, index) in DIC_PROP.FORM_PERM_TYPE" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<el-col v-if="!hiddenFields.printInfo" :span="12" class="mb-1">
					<el-form-item label="打印信息" prop="printInfo">
						<el-input v-model="form.printInfo" placeholder="请输入打印信息" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup name="FormOptionDialog">
import { useMessage } from "@/hooks/message"
import { getObj, putObj } from "@/api/jsonflow/form-option"
import { setPropsValue } from "@/flow/support/common"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
import { DIC_PROP } from "@/flow/support/dict-prop"

const emit = defineEmits(["refresh"])

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 提交表单数据
const form = reactive({
	id: "",
	flowKey: "",
	defFlowId: "",
	flowNodeId: "",
	propId: "",
	label: "",
	prop: "",
	subForm: "",
	propType: "",
	permType: "",
	formType: "",
	formId: "",
	formName: "",
	path: "",
	printInfo: "",
	type: "",
})

// 定义校验规则
const dataRules = ref({
	formType: [{ required: true, message: "表单来源不能为空", trigger: "blur" }],
	formId: [{ required: true, message: "表单名称不能为空", trigger: "blur" }],
	type: [{ required: true, message: "数据类型不能为空", trigger: "blur" }],
})

// 定义查询字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "defFlowId" }, { key: "formId" })
const onCascade = onCascadeChange(cascadeDic, { key: "defFlowId", cascades: ["flowNodeId"] })
onMounted(() => {
	onLoad(dicData)
})
function cascadeChange(key, cascades) {
	onCascade(form, { key: key, cascades: cascades })
	if (key === "defFlowId") {
		let find = dicData.defFlowId.find(f => f.id === form.defFlowId)
		if (find) { form.flowKey = find.flowKey }
		else { form.flowKey = null }
	}
}

// 打开弹窗
const openDialog = async (type, id, dataType) => {
	visible.value = true
	operType.value = type
	form.id = ""

	if (type === "add") {
		title.value = "新增"
	}
	else if (type === "edit") {
		title.value = "编辑"
	}
	else if (type === "view") {
		title.value = "查看"
	}

	// 重置表单数据
	nextTick(async () => {
		dataFormRef.value.resetFields()
		// 获取FormOption信息
		if (id) {
			form.id = id
			await getFormOptionData(id)
			await onCascade(form)
		}
		if (type === "add") { form.type = dataType }
		typeChange(form.type)
	})
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true
		await putObj(form)
		proxy.$modal.msgSuccess(form.id ? "编辑成功" : "新增成功")
		visible.value = false
		emit("refresh")
	}
	catch (err) {
		proxy.$modal.msgError(err)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getFormOptionData = async (id) => {
	// 获取数据
	loading.value = true
	let res = await getObj(id)
	Object.assign(form, res.object)
	loading.value = false
}

// 定义字段显隐
const hiddenFields = reactive({
	defFlowId: true,
	flowNodeId: true,
	label: true,
	prop: true,
	propId: true,
	subForm: true,
	propType: true,
	permType: true,
	printInfo: true,
})

// 定义字段是否可编辑
const disabledFields = reactive({
	formType: false,
})

const formTypeChange = value => {
	if (value) { dicData.formIdByType = dicData.formId?.filter(f => f.type === value) }
	else { dicData.formIdByType = [] }
}

const formIdChange = value => {
	if (!value) { return }
	let find = dicData.formIdByType.find(f => f.id === value)
	form.formName = find.formName
	form.path = find.path
}

const typeChange = value => {
	disabledFields.formType = false
	setPropsValue(hiddenFields, true, "defFlowId", "flowNodeId", "propId", "label", "prop", "subForm", "propType", "permType", "printInfo")
	if (value === DIC_PROP.FORM_DATA_TYPE[0].value) {
		disabledFields.formType = true
		setPropsValue(hiddenFields, false, "propId", "label", "prop", "subForm", "propType")
		form.formType = DIC_PROP.FORM_TYPE[1].value
	}
	else if (value === DIC_PROP.FORM_DATA_TYPE[1].value) {
		setPropsValue(hiddenFields, false, "defFlowId", "flowNodeId", "propId", "label", "prop", "subForm", "propType", "permType")
	}
	else {
		hiddenFields.printInfo = true
	}
	formTypeChange(form.formType)
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
