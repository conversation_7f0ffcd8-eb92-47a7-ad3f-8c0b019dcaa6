// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 富文本组件
// import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from '@/components/FileUpload'
// 图片上传组件
import ImageUpload from '@/components/ImageUpload'
// 图片预览组件
import ImagePreview from '@/components/ImagePreview'
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 字典标签组件
import DictTag from '@/components/DictTag'
// 字典标签select组件
import DictSelect from '@/components/DictSelect'
// table 更多操作组件
import TableColumnOperation from '@/components/TableColumnOperation'
// table 更多操作组件
import TableColOptBtn from '@/components/TableColOptBtn'
// 导入文件组件
import ImportFile from '@/components/ImportFile'
// 详情页
import DrawerPage from '@/components/DrawerPage'
// 自定义tabs
import CustomTabs from '@/components/CustomTabs'
// 自定义表格
import CustomTable from '@/components/CustomTable'
// 自定义表格空占位
import CustomEmpty from '@/components/CustomEmpty'
import SvgIcon from '@/components/SvgIcon/index.vue'

import UploadFile from '@/components/Upload/index.vue'

// 工作流相关 ---------
// 部门树组件
import vue3TreeOrg from 'vue3-tree-org'
import 'vue3-tree-org/lib/vue3-tree-org.css'
import FcDesigner from 'form-create-designer'

// 日历组件
import { setupCalendar } from 'v-calendar'

// 第三方组件
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

export default function registerGlobalComponents(app) {
  app.component('DictTag', DictTag)
  app.component('Pagination', Pagination)
  app.component('TreeSelect', TreeSelect)
  app.component('FileUpload', FileUpload)
  app.component('ImageUpload', ImageUpload)
  app.component('ImagePreview', ImagePreview)
  app.component('RightToolbar', RightToolbar)
  app.component('CustomTabs', CustomTabs)
  app.component('TableColumnOperation', TableColumnOperation)
  app.component('TableColOptBtn', TableColOptBtn)
  app.component('ImportFile', ImportFile)
  app.component('DictSelect', DictSelect)
  app.component('DrawerPage', DrawerPage)
  app.component('CustomTable', CustomTable)
  app.component('CustomEmpty', CustomEmpty)

  app.component('SvgIcon', SvgIcon)

  // jsonflow上传组件
  app.component('UploadFile', UploadFile)

  app.use(vue3TreeOrg) // 组织架构组件
  app.use(FcDesigner)
  app.use(FcDesigner.formCreate)

  app.use(setupCalendar, {}) // 日历组件

  // 导入全部的elmenet-plus的图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
    // 兼容性
    app.component(`ele-${key}`, component)
  }
}
