<template>
  <div>
    <form-create
      ref="formCreateRef"
      v-model="design.formData"
      v-model:api="design.fApi"
      :rule="design.rule"
      :option="design.option"
    />
  </div>
</template>

<script setup lang="ts" name="FormRender">
import useUserStore from "@/store/modules/user"
import { initFcDesignerFetch } from "./api"
import { validateNull } from "@/utils/validate"
import { parseWithFunctions } from "../../index"
import { compatibleAppHeight } from "@/api/order/order-key-vue"
import { validateApp } from "@/views/order"

const router = useRouter()

const formCreateRef = ref(null)

const props = defineProps({
  currFlowForm: {
    type: Object,
    default: {},
  },
  initFormPermPrint: null,
  renderType: null,
})

const design = reactive({
  rule: [],
  option: {},
  fApi: null,
  formData: null,
})

const data = reactive({
  globalDsv: {
    type: Object,
    default: {},
  },
})

function doInitFcDesigner(formInfo) {
  design.rule = formInfo.widgetList
  delete formInfo.widgetList
  design.option = formInfo
  design.formData = parseWithFunctions(props.currFlowForm.formData)
  // 隐藏提交按钮
  design.option.submitBtn = false
}

const $route = useRoute()
async function initFcDesigner() {
  console.log("表单渲染", props.currFlowForm)
  if (validateNull(props.currFlowForm)) return
  data.globalDsv.userInfos = useUserStore().userInfo
  let formInfoStr = props.currFlowForm.formInfo
  if (validateNull(formInfoStr)) return
  initFcDesignerFetch(formCreateRef.value, design.formData, data.globalDsv)
  let formInfo = parseWithFunctions(formInfoStr, true)
  let elTab = null
  // 保证组件事件后执行
  if (props.initFormPermPrint) elTab = await props.initFormPermPrint(formInfo)
  // 保证子表单新增行的字段权限生效
  doInitFcDesigner(formInfo)
  nextTick(async () => {
    // 在nextTick中保证生效
    if (elTab && elTab.isFormEdit === "0") design.fApi.disabled(true)
    // 查看时在表单权限后disabled
    if (props.renderType === "-1") design.fApi.disabled(true)
    compatibleAppHeight(validateApp($route))
  })
}

// 监听双向绑定
watch(
  () => props.currFlowForm.id,
  () => {
    initFcDesigner()
  }
)

watch(
  () => design.fApi, 
  () => {
    design.fApi.bus.$on("router-change", (e)=>{
      router.push(e)
    }) 
  }
)

onMounted(() => {
  initFcDesigner()
})

// 暴露变量
defineExpose({
  design,
})
</script>

<style scoped lang="scss"></style>
