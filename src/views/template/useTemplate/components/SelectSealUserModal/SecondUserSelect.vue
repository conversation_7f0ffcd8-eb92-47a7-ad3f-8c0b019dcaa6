<template>
  <div class="select-user-list">
    <div class="list-type">
      <el-tree
        :data="treeData"
        :props="{
          children: 'schoolLevelStaffDtos',
          label: 'businessLineName',
          value: 'businessLineId',
        }"
        default-expand-all
      >
        <template #default="{ node, data }">
          <div
            class="custom-tree-node"
            :class="{ 'is-leaf': node.isLeaf, active: businessLineId === data.id }"
            v-tooltip
            @click="nodeClick(data, node)"
          >
            {{ node.label }}
          </div>
        </template>
      </el-tree>
    </div>
    <div class="list-content">
      <!-- 多选 -->
      <el-checkbox-group :model-value="checked" @change="handleChange" v-if="multiple">
        <el-checkbox
          v-for="item in businessUserList"
          :value="item.userId"
          :key="item.userId"
        >
          <UserNameItem :item="item" class="list-content-item-name" />
        </el-checkbox>
      </el-checkbox-group>
      <!-- 单选 -->
      <el-radio-group v-model="checked" @change="handleChange" v-else>
        <el-radio
          class="list-content-item"
          v-for="item in businessUserList"
          :key="item.userId"
          :value="item.userId"
        >
          <UserNameItem :item="item" class="list-content-item-name" />
        </el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script setup>
import { getSecondLevelStaffAPI } from "@/api/ess/seal/seal"
const UserNameItem = defineAsyncComponent(() => import("./UserNameItem.vue"))

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
})

const treeData = ref([])
const loading = ref(false)
const checked = ref()
const businessLineId = ref(null) // 已选节点ID
const businessUserList = ref([])
const previousCheckedList = ref([]) // 上次数据

watch(
  () => props.modelValue,
  (newValue) => {
    if (!props.multiple) {
      checked.value = newValue[0]?.userId || ""
      return
    }
    checked.value = newValue?.map((item) => item.userId) || []
  },
  { immediate: true, deep: true }
)
async function getList() {
  try {
    loading.value = true
    const res = await getSecondLevelStaffAPI()
    treeData.value = res.object.map((i) => {
      return {
        ...i,
        businessLineId: i.deptId, // 为了数据统一，将deptId作为businessLineId
        businessLineName: i.deptName,
        schoolLevelStaffDtos: i.schoolLevelStaffDtos.map((n) => {
          return {
            ...n,
            id: `${i.deptId}-${n.businessLineId}`,
          }
        }),
      }
    })
    // 默认选择第一个
    if (treeData.value.length && treeData.value?.[0].schoolLevelStaffDtos?.length) {
      businessLineId.value = treeData.value?.[0].schoolLevelStaffDtos?.[0].id
      businessLineName.value =
        treeData.value?.[0].schoolLevelStaffDtos?.[0].businessLineName
      businessUserList.value =
        treeData.value?.[0].schoolLevelStaffDtos?.[0].sealHoldStaffDtos
    }
  } catch (error) {
    console.error("Error fetching tree data:", error)
  } finally {
    loading.value = false
  }
}

const businessLineName = ref("")
const emit = defineEmits(["on-select", "on-cancel"])

function handleChange(e) {
  // 获取用户基础信息
  const getUserBaseInfo = (user) => ({
    ...user,
    ascription: 1, // 二级
    holdFlag: 1, // 非持章人
    businessId: businessLineId.value,
    businessName: businessLineName.value,
  })

  if (props.multiple) {
    // 处理取消项
    const cancelItems = previousCheckedList.value.filter((i) => !e.includes(i))
    if (cancelItems.length) {
      emit("on-cancel", cancelItems[0])
    }

    const selectedUsers = businessUserList.value
      .filter((user) => e.includes(user.userId))
      .map((user) => getUserBaseInfo(user))

    // 更新 previousCheckedList 后再触发事件
    previousCheckedList.value = [...e]
    emit("on-select", selectedUsers)
  } else {
    const user = businessUserList.value.find((item) => item.userId === e)
    if (user) {
      emit("on-select", [getUserBaseInfo(user)])
    }
  }
}

function nodeClick(data, node) {
  if (node.isLeaf) {
    businessLineId.value = data.id
    businessLineName.value = data.businessLineName
    businessUserList.value = data.sealHoldStaffDtos
  }
}

// 清空选择
function clearSelect(e) {
  checked.value = null
}

getList()

defineExpose({
  clearSelect,
})
</script>

<style scoped lang="scss">
.select-user-list {
  display: flex;
  height: 100%;
  .list-type {
    width: 100px;
    flex-shrink: 0;
    margin-right: 15px;
    margin-left: -5px;
    .el-tree {
      background: transparent;
      :deep(.el-tree-node__expand-icon) {
        color: var(--el-color-primary);
      }
      :deep(.el-tree-node__expand-icon.is-leaf) {
        display: none;
      }
      :deep(.el-tree-node) {
        margin-bottom: 5px;
      }
      :deep(.el-tree-node__children) {
        .el-tree-node__content {
          padding-left: 0 !important;
        }
      }
      .custom-tree-node {
        font-weight: 600;
        width: 100%;
        font-size: 14px;
        &.is-leaf {
          text-align: center;
          font-weight: 400;
          color: #38383a;
          border-radius: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          &.active,
          &:hover {
            color: var(--el-color-primary);
            background: var(--el-color-primary-light-8);
          }
        }
      }
    }
    &-item {
      cursor: pointer;
      border: 1px solid #d8d8d8;
      border-radius: 5px;
      padding: 5px;

      &.active,
      &:hover {
        background: var(--el-color-primary);
        border-color: var(--el-color-primary);
        color: #fff;
      }
    }
  }
  .list-content {
    flex: auto;
    background: #fff;
    border-radius: 5px;
    height: 100%;
    padding: 15px 5px 15px 15px;
    .list-content-item-name {
      width: 120px;
    }
    .el-radio {
      margin-right: 10px;
    }
  }
}
</style>
