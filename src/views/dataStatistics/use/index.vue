<template>
  <div class="app-container">
    <div class="components-table">
      <el-form
          ref="queryRef"
          :model="queryParams"
          label-width="80px"
          class="white-form-box"
          inline
      >
        <el-form-item label="印章名称" prop="sealName">
          <el-input
              v-model="queryParams.sealName"
              placeholder="请输入印章名称"
              clearable
              maxlength="100"
              @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="印章类型" prop="sealType">
          <dict-select
              v-model="queryParams.sealType"
              :options="seal_type"
              value-type="string"
          />
        </el-form-item>
        <el-form-item label="归属部门" prop="deptId">
          <dept-picker v-model="queryParams.deptId"/>
        </el-form-item>
        <el-form-item label="使用部门" prop="deptUseId">
          <dept-picker v-model="queryParams.deptUseId"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <CustomTable
          ref="customTableRef"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          :has-toolbar="false"
          :data="tableData"
          :loading="loading"
          :total="total"
          :opt-width="180"
          @reload="getList"
      >
        <template #actions>
          <el-button type="primary" v-auths="['ess:sealReport:export']" @click="handleExport">
            导出
          </el-button>
        </template>
        <vxe-column type="seq" width="60"/>
        <vxe-column title="印章名称" field="sealName" min-width="140" show-overflow/>
        <vxe-column title="印章类型" field="sealType" min-width="100" show-overflow>
          <template #default="{row}">
            {{ selectDictLabel(seal_type, row.sealType) }}
          </template>
        </vxe-column>
        <vxe-column title="归属部门" field="deptName" min-width="100" show-overflow/>
        <vxe-column title="使用部门" field="deptUseName" min-width="100" show-overflow/>
        <vxe-column
            title="使用次数"
            fixed="sealCount"
        >
          <template #header>
            <div class="table-opt-set">
              使用次数
              <div class="flex flex-col">
                <el-icon class="pt-2px" size="8" @click="toSort()">
                  <ArrowUpBold :color="queryParams?.orderByDesc ?  '#909090' : '#24a87e'"/>
                </el-icon>
                <el-icon class="pb-2px" size="8" @click="toSort()">
                  <ArrowDownBold :color="queryParams?.orderByDesc ? '#24a87e' : '#909090'"/>
                </el-icon>
              </div>
            </div>
          </template>
        </vxe-column>
      </CustomTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="Use">
import {
  getSealUsedListAPI,
} from "@/api/ess/dataStatistics/sealUsedAndDetail.ts"
import {essPrefix} from "@/config/constant"
import {selectDictLabel} from "@/utils/common";

const {proxy} = getCurrentInstance()
const {seal_type} = proxy.useDict(
    'seal_type'
)

const tableData = ref([])
const loading = ref(false)
const total = ref(0)

const queryParams = reactive({
  page: 1,
  limit: 10,
  sealName: '',
  sealType: '',
  deptId: '',
  deptUseId: '',
  orderByDesc: true
})

async function getList() {
  try {
    loading.value = true
    let res = await getSealUsedListAPI(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
      essPrefix + "/sealReport/export",
      {
        ...queryParams,
      },
      `印章使用统计_${new Date().getTime()}.xlsx`,
  )
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 排序分类操作 */
function toSort() {
  queryParams.orderByDesc = !queryParams.orderByDesc
  queryParams.page = 1
  getList()
}

getList()
</script>

<style scoped lang="scss">

</style>
