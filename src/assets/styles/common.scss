/** 基础通用 **/
.bold {
  font-weight: bold;
}

.center {
  text-align: center;
}

/** 基础通用 **/
@for $i from 12 through 20 {
  .font-#{$i} {
    font-size: $i + px;
  }
}

@for $i from 0 through 80 {

  // 只要双数和能被5除尽的数
  // @if $i % 2== or $i % 5==0 {
  // 得出: u-margin-30或者u-m-30
  .u-margin-#{$i},
  .u-m-#{$i} {
    margin: $i + px;
  }

  // 得出: u-padding-30或者u-p-30
  .u-padding-#{$i},
  .u-p-#{$i} {
    padding: $i + px;
  }

  @each $short,
  $long in l left,
  t top,
  r right,
  b bottom {

    //缩写版，结果如: u-m-L-30
    //定义外边距
    .u-m-#{$short}-#{$i} {
      margin-#{$long}: $i + px;
    }

    //定义内边距
    .u-p-#{$short}-#{$i} {
      padding-#{$long}: $i + px;
    }

    //完整版，结果如: u-margin-Left-30
    //定义外边距
    .u-margin-#{$long}-#{$i} {
      margin-#{$long}: $i + px;
    }
  }
}

.pointer {
  cursor: pointer;
}

/* text color */

.text-primary,
.text-green {
  color: #24a87e;
}

.text-success,
.text-blue {
  color: #2e89ff !important;
}

.text-info {
  color: #909090 !important;
}

.text-info-light {
  color: #aaaaaa !important;
}

.text-warning {
  color: #ff7b17 !important;
}

.text-warning-light {
  color: #ff9300 !important;
}

.text-danger {
  color: #f94e4f !important;
}

.text-muted {
  color: #6b6b6b !important;
}

.system-confirm .el-message-box__message p {
  white-space: pre-line;
}

.table-empty {
  .el-table__row td .cell:empty::before {
    content: '-';
    color: #909090;
  }
}

.descriptions-empty {
  .el-descriptions-row .el-descriptions-item__content:empty::before {
    content: '-';
    color: #ccc;
  }
}

// 页面搜索框固定宽度 el-form-item  margin-bottom:18px  2+18=20px
.common-input-width {
  width: 220px !important;
}

.white-form-box {
  background: #fff;
  border-radius: 12px;
  padding: 20px 12px 2px;
  margin-bottom: 12px;

  .el-select,
  .el-input,
  .el-date-editor {
    min-width: 220px;
  }

  .el-date-editor .el-range__icon {
    margin-right: 3px;
  }
}

.white-body-box {
  background: #fff;
  border-radius: 12px;
  padding: 12px;
  margin-top: 12px;
  box-sizing: border-box;
  min-height: 200px;
}

.table-handle-box {
  display: flex;
  justify-content: space-between;
  position: relative;
  min-height: 32px;

  &__left {
    .el-button:not([class*='is-circle']) {
      min-width: 80px;
    }
  }

  .el-button--info.is-trans.is-plain:not([class*='is-disabled']) {
    background: #f7f8f9;
    color: #6b6b6b;
    --el-button-border-color: #aaaaaa;

    &.is-white {
      background: #fff;
    }

    &:hover {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary);
    }
  }


}

.el-button--primary.is-trans.is-plain:not([class*='is-disabled']) {
  background: transparent;
  padding: 0 12px;
  --el-button-border-color: var(--el-color-primary);

  &:hover {
    color: #fff;
    background: var(--el-color-primary);
  }

  &.is-white {
    background: #fff;

    &:hover {
      color: #fff;
      background: var(--el-color-primary);
    }
  }
}

.vxe-modal--wrapper {
  --vxe-ui-font-primary-color: var(--el-color-primary) !important;

  .vxe-modal--header {
    background: #fff !important;
    border-bottom: 0 !important;
  }

  .vxe-modal--content {
    color: #141a26;
  }
}

.form-b-border {
  border-bottom: 1px dashed rgba(112, 112, 112, 0.19);
  margin-bottom: 20px;
}

// 分割线
.divider-line {
  height: 1px;
  border-bottom: 1px dashed rgba(112, 112, 112, 0.19);
  width: 100%;
  margin-bottom: 20px
}

.box-title-line-b {
  @apply title-line-b before:(bg-[var(--el-color-primary-light-6)]) mb-10px;

  span {
    @apply z-10 relative;
  }
}
