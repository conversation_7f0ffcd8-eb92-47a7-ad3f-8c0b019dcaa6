<template>
  <el-dialog
    v-model="dialogVisible"
    title="角色用户"
    :before-close="handleClose"
    width="1050px"
    append-to-body
    class="small-form-dialog"
    top="7vh"
  >
    <el-form ref="queryRef" :model="queryParams" inline label-width="50px">
      <el-form-item label="netId" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入netId"
          clearable
          :maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="workNumber">
        <el-input
          v-model="queryParams.workNumber"
          placeholder="请输入工号"
          clearable
          :maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入姓名"
          clearable
          :maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item class="search-btns">
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <div class="u-m-b-8" v-if="!isDisabled">
      <el-button v-auths="['system:role:addUser']" type="primary" @click="openSelectUser">
        新增用户
      </el-button>
      <el-button
        v-auths="['system:role:removeUser']"
        type="danger"
        plain
        :disabled="multiple"
        @click="cancelAuthUserAll"
      >
        删除用户
      </el-button>
    </div>

    <el-table
      ref="tableRef"
      v-loading="loading"
      class="table-empty min-p-table"
      :data="userList"
      stripe
      row-key="userRoleDeptId"
      max-height="65vh"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" reserve-selection />
      <el-table-column
        align="center"
        :index="1 + queryParams.limit * (queryParams.page - 1)"
        label="序号"
        type="index"
        min-width="60"
      />
      <el-table-column
        label="netId"
        prop="userName"
        show-overflow-tooltip
        min-width="80"
      />
      <el-table-column label="工号" prop="workNumber" show-overflow-tooltip />
      <el-table-column label="姓名" prop="nickName" show-overflow-tooltip />
      <el-table-column label="人事部门" prop="deptName" show-overflow-tooltip />
      <el-table-column
        label="管理部门"
        prop="extraData.roleDeptName"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="$auth.hasPermiOr(['system:role:removeUser']) && !isDisabled"
        label="操作"
        class-name="small-padding fixed-width"
        min-width="50"
      >
        <template #default="{ row }">
          <TableColOptBtn
            :key="Math.random()"
            :buttons="[
              {
                text: '删除',
                type: 'danger',
                click: () => cancelAuthUser(row),
              },
            ]"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :total="total"
      @pagination="getList"
    />
    <SelectUserModal ref="selectRef" :role-id="queryParams.roleId" @ok="handleRefresh" />
    <ChooseDeptModal
      ref="chooseDeptRef"
      :role-id="queryParams.roleId"
      @ok="handleRefresh"
    />
  </el-dialog>
</template>

<script setup name="AuthUserModal">
import SelectUserModal from "./SelectUserModal"
import { roleUserPageList, batchDelZsUsersBindRole } from "@/api/system/role"
import ChooseDeptModal from "./ChooseDeptModal.vue"
const { proxy } = getCurrentInstance()

const userList = ref([])
const loading = ref(true)
const dialogVisible = ref(false)
const multiple = ref(true)
const total = ref(0)
const selections = ref([]) // 多选数据

const queryParams = reactive({
  page: 1,
  limit: 10,
  roleId: null,
  userName: null,
  workNumber: null,
  nickName: null,
})
const isDisabled = ref(import.meta.env.VITE_SYS_EDIT === "false") // 中大表单禁用 只有system:menu:editEss
const roleInfo = ref({})

const open = (row) => {
  roleInfo.value = row
  dialogVisible.value = true
  queryParams.roleId = row.roleId
  resetQuery()
}

const handleClose = () => {
  proxy.$refs.tableRef.clearSelection()
  selections.value = []
  dialogVisible.value = false
}

/** 查询授权用户列表 */
function getList() {
  loading.value = true
  roleUserPageList(queryParams).then((response) => {
    userList.value = response.records
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

function handleRefresh() {
  proxy.$refs.tableRef.clearSelection()
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  selections.value = selection
  multiple.value = !selection.length
}

/** 打开授权用户表弹窗 */
function openSelectUser() {
  proxy.$refs["selectRef"].show()
}

/** 单个取消授权 */
function cancelAuthUser(row) {
  handleCancelAuth([row])
}

/** 批量取消授权 */
function cancelAuthUserAll() {
  if (selections.value.length === 0) {
    proxy.$modal.msgWarning("请先选择要操作的用户")
    return
  }
  handleCancelAuth(selections.value, true)
}

function handleCancelAuth(users, isBatch = false) {
  const actionText = isBatch ? `已选${users.length}个用户` : `【${users[0].userName}】`
  const bindUsers = users.map((item) => ({
    roleDeptId: item.extraData?.roleDeptId,
    userId: item.userId,
    id: item.extraData?.userRoleDeptId,
  }))

  proxy.$modal.confirm(`确认要取消${actionText}的角色授权吗？`).then(() => {
    loading.value = true
    batchDelZsUsersBindRole({
      roleId: queryParams.roleId,
      bindUsers,
    })
      .then((res) => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
        proxy.$refs.tableRef.clearSelection()
      })
      .finally(() => {
        loading.value = false
      })
  })
}

// 修改管理部门
function updateUserRoleDept(row) {
  proxy.$refs["chooseDeptRef"].open(row.roleId ? [row] : selections.value)
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.min-p-table {
  :deep(tbody .el-table__cell) {
    padding: 12px 0;
  }
}
</style>
