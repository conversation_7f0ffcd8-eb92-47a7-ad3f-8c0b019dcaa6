<template>
  <div class="select-seal-user-modal">
    <el-dialog
      title="校内印章管理员"
      v-model="dialogVisible"
      width="1100px"
      :before-close="handleClose"
      append-to-body
      top="7vh"
      v-if="dialogVisible"
    >
      <el-radio-group :disabled="radioDisabled"
        v-model="organizationId"
        class="belong-radio-box"
        v-if="seal_belong_subject.length > 1"
      >
        <el-radio v-for="item in seal_belong_subject" :value="item.value" border>
          代表{{ item.label }}签署
        </el-radio>
      </el-radio-group>
      <div class="seal-user-wrapper">
        <div class="left-box">
          <div class="box-header">
            <CustomTabs v-model="currentTab" :options="tabs" />
          </div>
          <div class="box-body">
            <SchoolUserSelect
              ref="schoolUserSelectRef"
              :multiple="multiple"
              v-model="selectItems"
              v-if="currentTab === 0"
              @on-select="selectUser"
              @on-cancel="cancelUser"
            />
            <SecondUserSelect
              ref="secondUserSelectRef"
              v-model="selectItems"
              v-if="currentTab === 1"
              :multiple="multiple"
              @on-select="selectUser"
              @on-cancel="cancelUser"
            />
            <HoldSealUser
              ref="holdSealUserRef"
              v-if="currentTab === 2"
              v-model="selectItems"
              :multiple="multiple"
              @on-select="selectUser"
              @on-cancel="cancelUser"
            />
          </div>
        </div>
        <div class="arrow-right">
          <img src="@/assets/images/icon/arrow-right.png" alt="" />
        </div>
        <div class="right-box">
          <div class="box-header">
            <div>已选人员</div>
            <div class="box-header-right">
              <span>已选择{{ selectItems.length }}人</span>
              <span class="clear-icon" @click="handleClearSelect">全部清空</span>
            </div>
          </div>
          <div class="box-body">
            <CheckedList :list="selectItems" @remove="handleRemoveItem" />
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </template>
    </el-dialog>

    <!-- 请选择签署方弹窗 -->
    <el-dialog
      v-model="belongVisible"
      append-to-body
      modal-class="belong-dialog"
      width="300"
      top="0"
    >
      <div class="belong-dialog-content">
        <div class="title">请选择</div>
        <div class="belong-dialog-radio">
          <div
            v-for="item in seal_belong_subject"
            :key="item.value"
            class="item"
            @click="selectBelong(item.value)"
          >
            代表{{ item.label }}签署
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { uniqBy } from "lodash-es"
const SchoolUserSelect = defineAsyncComponent(() => import("./SchoolUserSelect.vue"))
const SecondUserSelect = defineAsyncComponent(() => import("./SecondUserSelect.vue"))
const HoldSealUser = defineAsyncComponent(() => import("./HoldSealUser.vue"))
const CheckedList = defineAsyncComponent(() => import("./CheckedList.vue"))
const { proxy } = getCurrentInstance()
const props = defineProps({
  // 是否多选
  multiple: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(["select"])

const { seal_belong_subject } = proxy.useDict("seal_belong_subject")
const organizationId = ref(null)

watchEffect(() => {
  if (seal_belong_subject.value?.length && !organizationId.value) {
    organizationId.value = seal_belong_subject.value[0].value
  }
})

const dialogVisible = ref(false)

const currentTab = ref(0)
const tabs = ref([
  { label: "校级", value: 0, compName: "schoolUserSelectRef" },
  { label: "二级单位", value: 1, compName: "secondUserSelectRef" },
  { label: "印章持有人", value: 2, compName: "holdSealUserRef" },
])
const selectItems = ref<Array<any>>([])

const setSelectItems = (newVal: Array<any>) => {
  if (!props.multiple) {
    handleClearSelect()
    return
  }

  if (!newVal.length) {
    selectItems.value = []
    return
  }

  selectItems.value = newVal.filter(
    (item) => !(item.signAttributes.length === 1 && item.signAttributes[0] === "签字")
  )
}

// watch(
//   () => props.modelValue,
//   (newVal) => {
//     setSelectItems(newVal)
//   },
//   {
//     immediate: true,
//     deep: true,
//   }
// )
//禁用选择
const radioDisabled = ref(false)

// 打开弹窗
/**
 * 
 * @param config - 配置选项
 * @param {Array} config.templateUsers - 父组件传入的已选签署方数据
 * @param {Boolean} config.isShowBelong - 是否直接显示选择框
 * @param {Boolean} config.disabled - 是否禁用选择
 * @param {Number} config.activeName - 选择框默认显示的选项
 *
 */
const open = (config: any = { templateUsers : [], isShowBelong: false, disabled: false, activeName: 0 }) => {
  const {templateUsers, isShowBelong, disabled, activeName } = config

  //判断是否直接显示选择框
  if (isShowBelong) {
    const match = seal_belong_subject.value.find((item: any) => item.label == activeName);
    organizationId.value = match ? match.value : null;
    radioDisabled.value = disabled;
    dialogVisible.value = true;
    
    if(templateUsers.length > 0){
      selectItems.value = templateUsers;
    }else{
      handleClearSelect();
    }
    // handleClearSelect()
    return
  }

  if (seal_belong_subject.value.length > 1) {
    // 有两个归属主体才展示
    belongVisible.value = true
  } else {
    organizationId.value = seal_belong_subject.value[0].value ?? null
    dialogVisible.value = true
    handleClearSelect()
  }
}
const handleClose = () => {
  dialogVisible.value = false
  currentTab.value = 0
}

// 确定
const handleConfirm = () => {
  const orgName = proxy.selectDictLabel(seal_belong_subject.value, organizationId.value)
  selectItems.value = selectItems.value.map((i) => {
    return {
      ...i,
      organizationId: organizationId.value,
      organizationName: orgName,
    }
  })
  console.log("selectItems.value", selectItems.value)
  emit("select", selectItems.value)
  handleClose()
}

// 已选用户
const selectUser = (e: any) => {
  if (!props.multiple) {
    selectItems.value = e
  } else {
    const newArr = selectItems.value.concat(e)
    selectItems.value = uniqBy(newArr, "userId")
  }
}

const cancelUser = (e: string) => {
  const index = selectItems.value.findIndex((i) => i.userId === e)
  if (index > -1) {
    selectItems.value.splice(index, 1)
  }
}

// 全部清空
const handleClearSelect = () => {
  selectItems.value = []
}

// 移除
const handleRemoveItem = (item: object, index: number) => {
  selectItems.value.splice(index, 1)
}

// ------------------选择签署方弹窗------------------
const belongVisible = ref(false)
const selectBelong = (e: string) => {
  organizationId.value = e
  dialogVisible.value = true
  belongVisible.value = false
  setSelectItems(props.modelValue)
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.seal-user-wrapper {
  display: flex;
  justify-content: space-between;
  height: 100%;
  .box {
    background: #f7f8f9;
    border-radius: 8px;
    padding: 15px;
    .box-body {
      height: 420px;
    }
  }
  .arrow-right {
    flex-shrink: 0;
    margin: 0 8px;
    display: flex;
    align-items: center;
    img {
      width: 12px;
    }
  }
  .left-box {
    @extend .box;
    flex: auto;
    .box-header {
      font-size: 16px;
    }
  }
  .right-box {
    @extend .box;
    flex-shrink: 0;
    width: 360px;
    .box-header {
      display: flex;
      justify-content: space-between;
      font-weight: 500;
      .box-header-right {
        font-weight: 400;
        color: #38383a;
        .clear-icon {
          margin-left: 10px;
          color: var(--el-color-primary);
          cursor: pointer;
        }
      }
    }
  }
}

.belong-radio-box {
  margin-bottom: 10px;
  :deep(.el-radio) {
    background: var(--el-color-primary-light-9);
    border: none;
    height: 40px;
    min-width: 200px;
    padding-left: 15px;
    border-radius: 6px;
    .el-radio__inner {
      background: none;
      border-color: #b2b2b2;
    }
    &.is-checked {
      background: var(--el-color-primary);
      .el-radio__label {
        color: #fff;
      }
      .el-radio__inner {
        border-color: #fff;
        &::before {
          border-color: var(--el-color-primary);
        }
        &::after {
          background: #fff !important;
          width: 7px;
          height: 7px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.belong-dialog {
  .el-dialog {
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  .el-dialog__title {
    display: none;
  }
  .belong-dialog-content {
    .title {
      font-weight: bold;
      margin-bottom: 20px;
    }
    .belong-dialog-radio {
      margin-bottom: 20px;
      .item {
        background-color: var(--el-color-primary-light-9);
        border: 1px solid var(--el-color-primary-light-8);
        min-width: 235px;
        padding: 18px;
        border-radius: 6px;
        margin-bottom: 10px;
        cursor: pointer;
        &:hover,
        &.is-active {
          background-color: var(--el-color-primary);
          color: #fff;
        }
      }
    }
  }
}
</style>
