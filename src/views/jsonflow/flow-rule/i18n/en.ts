export default {
	flowrule: {
		index: "#",
		importFlowRuleTip: " import FlowRule",
		id: "id",
		flowKey: "flowKey",
		defFlowId: "defFlowId",
		flowNodeId: "flowNodeId",
		flowNodeRelId: "flowNodeRelId",
		roleId: "roleId",
		jobType: "jobType",
		type: "type",
		valType: "valType",
		groupId: "groupId",
		groupsType: "groupsType",
		groupType: "groupType",
		varKeyVal: "varKeyVal",
		operator: "operator",
		varVal: "varVal",
		paramFrom: "paramFrom",
		paramValType: "paramValType",
		targetProp: "targetProp",
		paramType: "paramType",
		createUser: "createUser",
		createTime: "createTime",
		updateUser: "updateUser",
		updateTime: "updateTime",
		inputIdTip: "input id",
		inputFlowKeyTip: "input flowKey",
		inputDefFlowIdTip: "input defFlowId",
		inputFlowNodeIdTip: "input flowNodeId",
		inputFlowNodeRelIdTip: "input flowNodeRelId",
		inputRoleIdTip: "input roleId",
		inputJobTypeTip: "input jobType",
		inputTypeTip: "input type",
		inputValTypeTip: "input valType",
		inputGroupIdTip: "input groupId",
		inputGroupsTypeTip: "input groupsType",
		inputGroupTypeTip: "input groupType",
		inputVarKeyValTip: "input varKeyVal",
		inputOperatorTip: "input operator",
		inputVarValTip: "input varVal",
		inputParamFromTip: "input paramFrom",
		inputParamValTypeTip: "input paramValType",
		inputTargetPropTip: "input targetProp",
		inputParamTypeTip: "input paramType",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputUpdateUserTip: "input updateUser",
		inputUpdateTimeTip: "input updateTime",
	},
}
