<template>
  <div class="app-container">
    <div v-show="step !== 'finish'">
      <div v-if="step === 'start'" class="white-body-box p-20px">
        <div class="text-16px font-bold mb-10px ml-10px">请补充模版扩展信息</div>
        <CreateBaseInfoForm ref="baseInfoFormRef" :create-type="createType" />
      </div>
      <div class="btn-box" v-if="step !== 'finish'">
        <el-button type="primary" @click="nextStep">下一步</el-button>
        <el-button plain @click="back">取消</el-button>
      </div>
    </div>

    <!-- 内嵌页 -->
    <div
      v-if="step === 'finish'"
      v-loading="loading && !isFinish"
      class="white-body-box"
      :element-loading-text="loadingText"
      element-loading-background="#fff"
    >
      <iframe
        v-if="iframeUrl"
        ref="iframeRef"
        :src="iframeUrl"
        frameborder="0"
        width="100%"
        height="100%"
      />
      <el-button v-if="isFinish" class="back-btn" type="primary" @click="successBack">
        返回
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { createTemplate } from "@/api/ess/template/template"
import { useIframeCommunication } from "@/views/template/templateManagement/useIframeCommunication"
const CreateBaseInfoForm = defineAsyncComponent(() => import("./CreateBaseInfoForm.vue"))
import useWebSocket from "@/hooks/websocket"

// import { useWebSocket } from "@vueuse/core"
const { proxy } = getCurrentInstance()

const props = defineProps({
  getUrlFunc: {
    type: Function,
    required: true,
    default: createTemplate,
  },
  createType: {
    type: Number,
    default: 0, // 0公有 1 私有
  },
  // 返回上一页的路径
  backUrl: {
    default: "/template/templateManagement",
  },
})

const loading = ref(false)
const loadingText = ref("")
const iframeUrl = ref(null)
const iframeRef = ref(null)
const baseInfoFormRef = ref(null) // 基础信息

const step = ref("start")
const isFinish = ref(false) // 是否保存完成
const { onConnect, wsClose } = useWebSocket({
  // 接收消息回调
  onMessage: (data) => {
    if (data === "SUCCESS") {
      isFinish.value = true
      loading.value = false
      wsClose()
    } else if (data === "PRE-SUCCESS") {
      loading.value = true
    }
  },
  onClose: () => {
    loading.value = false
  },
})

// 获取模版信息
const getUrl = async (form) => {
  const res = await props.getUrlFunc(form)
  iframeUrl.value = res.object.url
  loading.value = false
  onConnect(res.object.uniqueId)
}

// 下一步
async function nextStep() {
  await nextTick()
  baseInfoFormRef.value.validate(({ valid, form }) => {
    if (valid) {
      loading.value = true
      getUrl(form)
      step.value = "finish"
    } else {
      proxy.$modal.msgWarning("请完善表单信息！")
    }
  })
}

// 上一步
function prevStep() {
  step.value = "start"
}

// 返回
const back = () => proxy.$tab.closeOpenPage({ path: props.backUrl })
// 成功返回
const successBack = () => {
  proxy.$tab.closeOpenPage({ path: props.backUrl, query: { r: 1 } })
}
onUnmounted(() => {
  wsClose()
})
useIframeCommunication(iframeRef, getUrl, props.backUrl)
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.app-container {
  .white-body-box {
    margin-top: 0;
    min-height: 550px;
    position: relative;
  }
  iframe {
    width: 100%;
    height: max(550px, calc(#{$base-main-page-height} - 55px));
  }
}

.btn-box {
  @apply flex-x-center mt-30px pb-30px;
  .el-button {
    width: 120px;
    margin-left: 40px;
  }
}

.back-btn {
  @apply absolute left-0 right-0 top-80% m-auto w-100px;
}
</style>
