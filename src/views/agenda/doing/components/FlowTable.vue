<template>
  <div class="components-table">
    <el-form
      ref="queryRef"
      :model="queryParams"
      label-width="70px"
      class="white-form-box"
      inline
    >
      <el-form-item label="发起人" prop="createUserName">
        <el-input
          v-model="queryParams.createUserName"
          placeholder="请输入发起人"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="标题" prop="flowTitle">
        <el-input
          v-model="queryParams.flowTitle"
          placeholder="请输入标题"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="工单编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入工单编号"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <!-- <el-form-item label="任务状态" prop="status">
        <dict-select
          v-model="queryParams.status"
          :options="DIC_PROP.FLOW_STATUS"
          value-type="string"
        />
      </el-form-item> -->
      <el-form-item label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="queryParams.createTimeRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="common-input-width"
          clearable
          :default-time="dateDefaultTime"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>
    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      :has-toolbar="false"
      :data="tableData"
      :loading="loading"
      :total="total"
      :opt-width="120"
      @reload="getList"
    >
      <vxe-column type="seq" width="60" />
      <vxe-column title="工单编号" field="code" min-width="100" show-overflow />
      <vxe-column title="发起人" field="createUserName" min-width="120" show-overflow />
      <vxe-column title="标题" field="flowTitle" min-width="140" show-overflow />
      <vxe-column title="任务状态" field="status" min-width="100" show-overflow>
        <template #default="scope">
          <dict-tag :options="DIC_PROP.FLOW_STATUS" :value="scope.row.status"></dict-tag>
        </template>
      </vxe-column>
      <vxe-column title="发起时间" field="createTime" min-width="120" show-overflow />
      <vxe-column title="当前节点" field="nodeName" min-width="100" show-overflow />
      <vxe-column
        title="当前节点停留时间"
        field="stayTime"
        min-width="140"
        show-overflow
      />
      <!-- <vxe-column title="摘要" field="remark" min-width="80" show-overflow /> -->
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '查看',
              click: () => handleJob(row),
              permission: ['todo:doingMine:details'],
            },
            {
              text: '撤销',
              click: () => handleRevocation(row),
              permission: ['todo:doingMine:revocation'],
              hidden: userInfo.userId !== row.createUser,
            },
          ]"
        />
      </template>
    </CustomTable>

    <!-- 查看审批过程 -->
    <!-- <el-dialog
      v-model="data.showComment"
      v-if="data.showComment"
      top="20px"
      width="90%"
      title="查看审批过程"
      append-to-body
    >
      <comment :curr-job="data.currJob"></comment>
    </el-dialog> -->

    <el-dialog
      v-model="showHandleForm"
      top="20px"
      width="90%"
      title="查看表单"
      append-to-body
    >
      <custom-form
        v-if="showHandleForm"
        :curr-job="currFlowForm"
        @onHandleForm="showHandleForm = false"
      ></custom-form>
    </el-dialog>
  </div>
</template>

<script setup>
import { onGoingFlowPage, recallReset } from "@/api/jsonflow/run-flow"
import { handleFlowPreview } from "@/flow/support/extend"
import { DIC_PROP } from "@/flow/support/dict-prop"
import { useFlowJob } from "@/flow/stores/flowJob"
import { getObj } from "@/api/order/flow-application"
import useUserStore from "@/store/modules/user"

const props = defineProps({
  mine: {
    type: Number,
    default: 0,
  },
})

const { proxy } = getCurrentInstance()
const { userInfo } = useUserStore()

const Comment = defineAsyncComponent(() =>
  import("@/views/jsonflow/comment/timeline.vue")
)

const CustomForm = defineAsyncComponent(() =>
  import("@/flow/components/custom-form/handle.vue")
)

const flowJob = useFlowJob()
const $router = useRouter()

// 查看表单
const showHandleForm = ref(false)
const currFlowForm = ref({})

const tableData = ref([])
const loading = ref(false)
const total = ref(0)

const queryParams = reactive({
  current: 1,
  size: 10,
})

async function getList() {
  await nextTick()
  try {
    loading.value = true
    queryParams.mine = props.mine
    let res = await onGoingFlowPage(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.current = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}
/** 查看 */
async function viewDetails(row) {
  const res = await getObj(row.id)
}

function handleJob(row) {
  // 复制一行，防止原数据被改
  const copyRow = Object.assign({}, row)
  copyRow.id = row.jobId
  copyRow.flowInstId = row.id
  copyRow.isHiJob = "1"
  handleFlowPreview($router, copyRow, true, true, true, flowJob, getList)
}

// 撤销
function handleRevocation(row) {
  proxy.$modal
    .confirm(`确定撤销该“${row.flowTitle}”申请？`)
    .then(() => {
      const reqData = {
        id: row.id,
        status: "0",
        flowKey: row.flowKey,
      }
      return recallReset(reqData)
    })
    .then((res) => {
      if (+res.code === 200) {
        proxy.$modal.alertCenter("撤销成功")
        getList()
      }
    })
}

getList()

defineExpose({
  resetQuery,
})
</script>

<style scoped lang="scss"></style>
