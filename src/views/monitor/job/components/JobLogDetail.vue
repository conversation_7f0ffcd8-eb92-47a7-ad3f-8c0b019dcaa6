<template>
  <el-dialog
    title="调度日志详细"
    v-model="dialogVisible"
    width="700px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="100px" :disabled="true">
      <el-form-item label="日志序号：">{{ form.jobLogId }}</el-form-item>
      <el-form-item label="任务名称：">{{ form.jobName }}</el-form-item>

      <el-form-item label="任务分组：">{{ form.jobGroup }}</el-form-item>
      <el-form-item label="执行时间：">{{ form.createTime }}</el-form-item>

      <el-form-item label="调用方法：">{{ form.invokeTarget }}</el-form-item>

      <el-form-item label="日志信息：">{{ form.jobMessage }}</el-form-item>

      <el-form-item label="执行状态：">
        <dict-tag :options="sys_job_status" :value="form.status" />
      </el-form-item>

      <el-form-item label="异常信息：" v-if="form.status == 1">{{
        form.exceptionInfo
      }}</el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup name="JobLogDetail">
defineOptions({
  inheritAttrs: false,
})

const props = defineProps({
  sys_job_status: Array,
})

const dialogVisible = ref(false)
const form = ref({})
const formRef = ref(null)

function open(row) {
  form.value = { ...row }
  dialogVisible.value = true
}

function handleClose() {
  form.value = {}
  dialogVisible.value = false
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
