<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
      class="white-form-box"
    >
      <el-form-item label="任务名称" prop="jobName">
        <el-input
          v-model="queryParams.jobName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务分组" prop="jobGroup">
        <dict-select
          v-model="queryParams.jobGroup"
          :options="sys_job_group"
          value-type="string"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <dict-select
          v-model="queryParams.status"
          :options="sys_job_status"
          value-type="string"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" :loading="loading">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery" :loading="loading">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <div class="table-handle-box">
      <div>
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-auths="['monitor:job:add']"
          >新增
        </el-button>
        <el-button
          type="primary"
          plain
          class="is-deep"
          icon="Tickets"
          @click="handleJobLog"
          v-auths="['monitor:job:query']"
          >日志</el-button
        >
        <el-button
          type="danger"
          plain
          :disabled="multiple"
          @click="handleDelete"
          class="is-deep"
          icon="Delete"
          v-auths="['monitor:job:remove']"
          >删除</el-button
        >
      </div>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </div>

    <div class="white-body-box">
      <el-table
        v-loading="loading"
        :data="jobList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="任务编号" prop="jobId" />
        <el-table-column label="任务名称" prop="jobName" show-overflow-tooltip />
        <el-table-column label="任务组名" prop="jobGroup" width="90">
          <template #default="{ row }">
            <dict-tag :options="sys_job_group" :value="row.jobGroup" />
          </template>
        </el-table-column>
        <el-table-column
          label="调用目标字符串"
          prop="invokeTarget"
          show-overflow-tooltip
        />
        <el-table-column
          label="cron执行表达式"
          prop="cronExpression"
          show-overflow-tooltip
        />
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <TableColOptBtn
              :key="Math.random()"
              :buttons="[
                {
                  text: '执行一次',
                  click: () => handleRun(row),
                  permission: ['monitor:job:changeStatus'],
                },
                {
                  text: '修改',
                  click: () => handleUpdate(row),
                  permission: ['monitor:job:edit'],
                },
                {
                  text: '详情',
                  click: () => handleView(row),
                  permission: ['monitor:job:query'],
                },
              ]"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <EditJobForm
      ref="editJobFormRef"
      :sys_job_group="sys_job_group"
      :sys_job_status="sys_job_status"
      @change="getList"
      @refresh="handleQuery"
    />
  </div>
</template>

<script setup name="Job">
import { listJob, delJob, runJob, changeJobStatus } from "@/api/monitor/job"

const EditJobForm = defineAsyncComponent(() => import("./components/EditJobForm"))

defineOptions({
  inheritAttrs: false,
})

const router = useRouter()
const { proxy } = getCurrentInstance()
const { sys_job_group, sys_job_status } = proxy.useDict("sys_job_group", "sys_job_status")

const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const showSearch = ref(true)
const total = ref(0)
const jobList = ref([])
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  jobName: undefined,
  jobGroup: undefined,
  status: undefined,
})
const queryFormRef = ref()
const selections = ref([])

function getList() {
  loading.value = true
  listJob(queryParams).then((response) => {
    jobList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

function handleQuery() {
  queryParams.pageNum = 1
  getList()
}
function resetQuery() {
  proxy.resetForm("queryFormRef")
  handleQuery()
}
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.jobId)
  selections.value = selection
  single.value = selection.length != 1
  multiple.value = !selection.length
}
const STATUS = {
  ENABLED: "0",
  DISABLED: "1",
}

function handleStatusChange(row) {
  const { jobId, jobName, status } = row
  const isEnabled = status === STATUS.ENABLED
  const action = isEnabled ? "启用" : "停用"

  proxy.$modal
    .confirm(`确认要${action}任务"${jobName}"吗?`)
    .then(() => changeJobStatus(jobId, status))
    .then(() => proxy.$modal.msgSuccess(`${action}成功`))
    .catch(() => (row.status = isEnabled ? STATUS.DISABLED : STATUS.ENABLED))
}
function handleRun(row) {
  proxy.$modal
    .confirm(`确认要立即执行一次"${row.jobName}"任务吗?`)
    .then(() => {
      return runJob(row.jobId, row.jobGroup)
    })
    .then(() => {
      proxy.$modal.msgSuccess("执行成功")
    })
}
function handleView(row) {
  proxy.$refs.editJobFormRef.open(row, "详情")
}
function handleJobLog() {
  router.push("/job/log")
}
function handleAdd() {
  proxy.$refs.editJobFormRef.open()
}
function handleUpdate(row) {
  proxy.$refs.editJobFormRef.open(row)
}
function handleDelete(row) {
  const jobIds = row?.jobId || ids.value
  const names = row?.jobName || selections.value.map((item) => item.jobName).join(",")
  proxy.$modal
    .confirm(`是否确认删除定时任务?`, `当前选择"${names}"`)
    .then(() => {
      return delJob(jobIds)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
}

onMounted(() => {
  getList()
})
</script>
