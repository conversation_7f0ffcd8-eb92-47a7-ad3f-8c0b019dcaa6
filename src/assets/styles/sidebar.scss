
@use './_variables.scss' as *;

$active-color:var(--el-color-primary);

#app {
  .main-container {
    height: calc(100% - #{$base-nav-height});
    transition: margin-left 0.28s;
    margin-left: $base-sidebar-width;
    position: relative;
    top: $base-nav-height;
    background-color: $base-main-page-background;
  }

  .app-main {
    background-color: $base-main-page-background;
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    -webkit-transition: width 0.28s;
    transition: width 0.28s;
    width: $base-sidebar-width !important;
    background-color: $base-menu-background;
    height: calc(100% - #{$base-nav-height});
    position: fixed;
    font-size: 0px;
    top: $base-nav-height;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    padding-top: 10px;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 8px;
      font-size: 18px;
      height: 20px !important;
      width: 20px !important;
      color: var(--el-color-primary);
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 88% !important;
      margin: auto;
      .el-menu-item.is-active{
        color: #000;
      }
    }

    .el-menu-item,
    .el-sub-menu__title {
      height: 42px;
      line-height: 42px;
      padding: 0 10px;
      margin-top: 5px;
    }

    .el-menu-item,
    .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      font-size: 16px;
    }

    .el-menu-item .el-menu-tooltip__trigger {
      display: inline-block !important;
    }

    // 激活样式
    .active-style {
      background: #fff;
      border-radius: 6px;
    }

    // menu hover
    .el-menu-item.submenu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        @extend.active-style;
      }
    }
    .el-menu-item.is-active.submenu-title-noDropdown,
    .el-sub-menu.is-active.is-opened > .el-sub-menu__title {
      @extend.active-style;
    }

    & .theme-dark .is-active > .el-sub-menu__title {
      color: $active-color !important;
      font-weight: bold;
    }

    .el-sub-menu .el-sub-menu__icon-arrow {
      color: #a7aead;
      font-weight: bold;
      right: 12px;
    }

    // 带有三级菜单的激活
    .el-sub-menu.is-active.is-opened .nest-menu .el-submenu.is-active {
      .el-sub-menu__title {
        color: $active-color;
        font-weight: bold;
      }
    }

    .menu-title-dot {
      width: 7px;
      height: 7px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 7px;
      background: $active-color;
    }

    .nest-menu .el-menu-item {
      height: 30px;
      line-height: 30px;
      .svg-icon {
        margin-right: 5px;
        font-size: 12px;
        height: 14px;
        width: 14px;
        margin-left: -5px;
      }

      .menu-title {
        font-size: 14px;
      }
    }

    & .nest-menu .el-sub-menu.is-active.is-opened {
      > .el-sub-menu__title {
        color: $active-color;
        background: transparent;
        font-weight: bold;
      }
      .el-menu-item.is-active {
        font-weight: bold;
      }
    }

    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      margin-left: 10px;
      height: 30px;
      line-height: 30px;
      &:hover, &.is-active {
        color: $active-color;
        background: transparent;
        font-weight: bold;
      }
      .menu-title {
        font-size: 14px;
      }
    }

    & .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: $base-sub-menu-background !important;

      &:hover {
        background-color: $base-sub-menu-hover !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: $base-collapse-sidebar-width !important;
    }

    .main-container {
      margin-left: $base-collapse-sidebar-width;
    }

    .sub-menu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      & > .el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    // .el-menu--collapse {
    //   .el-sub-menu {
    //     & > .el-sub-menu__title {
    //       & > span {
    //         height: 0;
    //         width: 0;
    //         overflow: hidden;
    //         visibility: hidden;
    //         display: inline-block;
    //       }
    //       & > i {
    //         height: 0;
    //         width: 0;
    //         overflow: hidden;
    //         visibility: hidden;
    //         display: inline-block;
    //       }
    //     }
    //   }
    // }
  }

  // .el-menu--collapse .el-menu .el-sub-menu {
  //   min-width: $base-sidebar-width !important;
  // }

  // mobile responsive
  .mobile {
    // .main-container {
    //   margin-left: 0px;
    // }

    // .sidebar-container {
    //   transition: transform 0.28s;
    //   width: $base-sidebar-width !important;
    // }

    // &.hideSidebar {
    //   .sidebar-container {
    //     pointer-events: none;
    //     transition-duration: 0.3s;
    //     transform: translate3d(-$base-sidebar-width, 0, 0);
    //   }
    // }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 10px;
    }
  }

  .nest-menu .el-sub-menu > .el-sub-menu__title,
  .el-menu-item {
    &:hover {
    }
  }

  // the scroll bar appears when the sub-menu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
