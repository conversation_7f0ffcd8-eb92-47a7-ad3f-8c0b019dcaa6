<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`草稿箱（${total}）`"
    width="900px"
    :before-close="handleClose"
    top="5vh"
  >
    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :data="tableData"
      custom
      :hasToolbar="false"
      :loading="loading"
      :total="total"
      @reload="getList"
      :optWidth="60"
      class="draft-table"
    >
      <template #actions>
        <el-button
          w-80px
          type="danger"
          @click="handleDelete"
          :disabled="!selections.length"
        >
          删除
        </el-button>
      </template>
      <vxe-column type="checkbox" width="40" />
      <vxe-column type="seq" width="70" />
      <vxe-column field="flowName" title="文件名称" min-width="200" />
      <vxe-column field="createTime" title="创建时间" width="170" />
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '继续编辑',
              click: () => handleUpdate(row),
            },
            {
              type: 'danger',
              text: '删除',
              click: () => handleDelete(row),
            },
          ]"
        />
      </template>
    </CustomTable>
  </el-dialog>
</template>

<script setup>
import { removeDraft, getFileDraftListAPI } from "@/api/ess/fileManagement/file"
const props = defineProps({
  title: {
    type: String,
    default: "固定审批流程",
  },
})
const { proxy } = getCurrentInstance()

const router = useRouter()
const dialogVisible = ref(false)
const loading = ref(true)
const tableData = ref([])
const total = ref(0)
const queryParams = reactive({
  page: 1,
  limit: 10,
})

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})
const open = () => {
  dialogVisible.value = true
  getList()
}
const emit = defineEmits(["updateTotal"])
const getList = async () => {
  loading.value = true
  const res = await getFileDraftListAPI(queryParams)
  tableData.value = res.object.records
  total.value = res.object.total
  loading.value = false
  emit("updateTotal", res.object.total)
}

const handleClose = () => {
  dialogVisible.value = false
}
// 继续编辑
const handleUpdate = (row) => {
  dialogVisible.value = false
  router.push(`/file/fileLunch/${row.id}/${row.flowFlag}`)
}

// 删除
const handleDelete = (row) => {
  const id = row.id || selections.value.map((item) => item.id)
  const names = row.flowName || selections.value.map((item) => item.flowName).join("、")
  proxy.$modal
    .confirm(`是否确认删除${names}？`)
    .then(function () {
      deleteDraft(id, !!row.id)
    })
    .catch(() => {})
}

async function deleteDraft(ids) {
  try {
    loading.value = true
    await removeDraft(ids)
    getList()
    proxy.$modal.msgSuccess("删除成功")
  } finally {
    loading.value = false
  }
}

getList()
defineExpose({
  open,
  getList,
})
</script>
<style scoped lang="scss">
.draft-table:deep(.white-body-box) {
  padding: 0;
}
</style>
