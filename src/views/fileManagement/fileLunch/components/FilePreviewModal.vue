<!--
 * @Author: ljn
 * @Date: 2025-03-06 14:52:45
 * @LastEditors: ljn
 * @LastEditTime: 2025-05-07 09:54:54
 * @Description: 文件预览
-->

<template>
  <el-dialog
    title="文件预览"
    v-model="dialogVisible"
    width="820px"
    :before-close="handleClose"
    top="2vh"
  >
    <div class="max-h-86vh overflow-y-auto">
      <FilePreview :fileRaw="fileRaw" :previewUrl="fileInfo.previewUrl" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
const FilePreview = defineAsyncComponent(() => import("./FilePreview.vue"))
const props = defineProps({
  fileInfo: {
    type: Object,
    default: () => ({}),
  },
  fileRaw: {
    type: Object,
    default: () => ({}),
  },
})
const dialogVisible = ref<boolean>(false)
function open() {
  dialogVisible.value = true
}
function handleClose() {
  dialogVisible.value = false
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
