export default {
	defFlow: {
		index: "#",
		importdefFlowTip: "导入流程定义管理",
		id: "主键ID",
		flowName: "流程名称",
		flowKey: "流程KEY",
		groupName: "分组名称",
		status: "状态",
		remark: "备注",
		version: "版本",
		createUser: "创建人",
		createTime: "创建时间",
		sort: "排序值",
		isIndependent: "发起后配置独立",
		updateUser: "修改人",
		updateTime: "修改时间",
		delFlag: "删除标识",

		inputIdTip: "请输入主键ID",
		inputFlowNameTip: "请输入流程名称",
		inputFlowKeyTip: "请输入流程KEY",
		inputGroupNameTip: "请输入分组名称",
		inputStatusTip: "请输入状态",
		inputRemarkTip: "请输入备注",
		inputVersionTip: "请输入版本",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputSortTip: "请输入排序值",
		inputIsIndependentTip: "请选择发起后配置独立",
		inputUpdateUserTip: "请输入修改人",
		inputUpdateTimeTip: "请输入修改时间",
		inputDelFlagTip: "请输入删除标识",

	},
}
