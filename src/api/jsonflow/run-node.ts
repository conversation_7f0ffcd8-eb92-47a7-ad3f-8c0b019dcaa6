import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-jsonflow/run-node/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/run-node",
		method: "post",
		data: obj,
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-node/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/cloud-jsonflow/run-node/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/cloud-jsonflow/run-node/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-jsonflow/run-node",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/run-node",
		method: "put",
		data: obj,
	})
}

// 催办节点
export function remind(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-node/remind",
		method: "put",
		data: obj,
	})
}

// 加签
export function signature(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-node/signature",
		method: "post",
		data: obj,
	})
}
