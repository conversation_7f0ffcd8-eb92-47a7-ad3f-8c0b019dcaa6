@use './transition.scss'as *;
@use './element-ui.scss'as *;
@use './sidebar.scss'as *;
@use './common.scss'as *;

body {
  height: 100%;
  margin: 0;
  font-family: <PERSON>Fang SC, Microsoft YaHei, <PERSON><PERSON>, sans-serif, Sim<PERSON>un;
  color: #141a26;
}

html,
#app {
  height: 100%;
  box-sizing: border-box;
}

input:-webkit-autofill {
  // -webkit-box-shadow: 0 0 0 1000px #fff inset !important;
  -webkit-text-fill-color: #000000;
  -webkit-transition-delay: 999999999s;
  -webkit-transition: color 999999999s ease-out, background-color 999999999s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

/* 添加vxe-tooltip全局样式 */
.vxe-tooltip--wrapper {
  z-index: 9999 !important; /* 使用极高值确保覆盖其他组件 */
}

/* 滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: #4848482b;
  border-radius: 10px;
}

/* 滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 10px;
}

/* 只有非mac下才进行调整，mac下使用默认滚动条 */
html:not([data-platform='macOs']) {
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-thumb {
    background: #4848482b;
    border-radius: 12px;
  }

  ::-webkit-scrollbar-track {
    border-radius: 4px;
    box-shadow: none;
    border: none;
    background: transparent;
  }
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

//main-container全局样式
.app-container {
  padding: 15px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type='number'] {
  -moz-appearance: textfield;
}
