<template>
  <div class="icon-nav" @click="handleClick">
    <img :src="imagePath" alt="" />
    <div v-tooltip>{{ info.appName }}</div>
  </div>
</template>

<script setup>
import { isHttp } from "@/utils/validate"
import { imageApi } from "@/config/constant"

const router = useRouter()
const props = defineProps({
  info: {
    type: Object,
    default: () => ({
      iconPath: "",
      appName: "",
    }),
  },
})

// 判断是否是绝对路径（URL 或 / 开头）
function isAbsoluteUrl(path) {
  return /\.(png|jpe?g|gif|webp|svg|ico)(\?.*)?$/i.test(path)
}

const imagePath = computed(() => {
  const isLocalIcon = isAbsoluteUrl(props.info.iconPath)
  return isLocalIcon
    ? imageApi + props.info.iconPath
    : new URL(
        `../../../assets/images/index-icon/${props.info.iconPath}.png`,
        import.meta.url
      ).href
})

const handleClick = () => {
  const { info } = props
  if (info.appAttr == 1) {
    window.open(info.pcUrl)
  } else {
    if (isHttp(info.pcUrl)) {
      // 如果内链直接填的是地址 则截取url上的路由地址
      const routePath = getRoutePath(info.pcUrl) // 获取路由地址
      router.push(routePath)
    } else {
      router.push(info.pcUrl)
    }
  }
}

function getRoutePath(url) {
  try {
    const parsedUrl = new URL(url)
    const pathname = parsedUrl.pathname
    const publicPath = import.meta.env.VITE_PUBLIC_PATH
    const publicPathWithSlash = publicPath ? `/${publicPath}/` : "/"
    return pathname.replace(new RegExp(`^${publicPathWithSlash}`), "")
  } catch (error) {
    console.error("Invalid URL:", error)
    return ""
  }
}
</script>

<style scoped lang="scss">
.icon-nav {
  text-align: center;
  font-size: 15px;
  width: 110px;
  margin-right: 5px;
  cursor: pointer;
  flex-shrink: 0;
  line-height: 22px;

  &:hover {
    img {
      transform: scale(1.05);
    }
  }
  img {
    width: 55px;
    height: 55px;
    margin: 10px auto 5px;
    border-radius: 8px;
    transition: all 0.2s;
  }
  div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
