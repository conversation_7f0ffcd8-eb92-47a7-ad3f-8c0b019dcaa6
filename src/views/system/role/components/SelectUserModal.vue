<template>
	<!-- 授权用户 -->
	<el-dialog
		v-model="visible"
		title="选择用户"
		width="1020px"
		top="7vh"
		append-to-body
		class="small-form-dialog"
		:before-close="handleClose"
	>
		<el-form ref="queryRef" :model="queryParams" inline>
			<el-form-item label="netId" prop="userName">
				<el-input
					v-model="queryParams.userName"
					placeholder="请输入netId"
					clearable
					:maxlength="100"
					@keyup.enter="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="工号" prop="workNumber">
				<el-input
					v-model="queryParams.workNumber"
					placeholder="请输入工号"
					clearable
					:maxlength="100"
					@keyup.enter="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="姓名" prop="nickName">
				<el-input
					v-model="queryParams.nickName"
					placeholder="请输入姓名"
					clearable
					:maxlength="100"
					@keyup.enter="handleQuery"
				/>
			</el-form-item>
			<el-form-item class="search-btns">
				<el-button type="primary" icon="Search" @click="handleQuery">
					搜索
				</el-button>
				<el-button icon="Refresh" @click="resetQuery">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<el-row>
			<el-table
				ref="refTable"
				v-loading="loading"
				stripe
				:data="userList"
				max-height="800"
				row-key="userId"
				@selection-change="handleSelectionChange"
				@row-click="clickRow"
			>
				<el-table-column
					type="selection"
					width="55"
					align="center"
					reserve-selection
				/>
				<el-table-column label="netId" prop="userName" show-overflow-tooltip />
				<el-table-column label="工号" prop="workNumber" show-overflow-tooltip />
				<el-table-column label="姓名" prop="nickName" show-overflow-tooltip />
				<el-table-column label="所属部门" prop="deptName" show-overflow-tooltip />
			</el-table>
			<pagination
				v-show="total > 0"
				v-model:page="queryParams.pageNum"
				v-model:limit="queryParams.pageSize"
				:total="total"
				@pagination="getList"
			/>
		</el-row>
		<template #footer>
			<el-button @click="handleClose">
				取消
			</el-button>
			<el-button type="primary" :loading="loading" @click="handleSelectUser">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script setup name="SelectUser">
import { batchAddZsUsersBindRole } from "@/api/system/role"
import { zsList } from "@/api/system/user"

const props = defineProps({
	roleId: {
		type: [Number, String],
	},
})

const { proxy } = getCurrentInstance()
const userList = ref([])
const visible = ref(false)
const loading = ref(false)
const total = ref(0)
const selections = ref([]) // 多选数据

const queryParams = reactive({
	pageNum: 1,
	pageSize: 8,
	roleId: null,
	userName: null,
	phonenumber: null,
})

// 显示弹框
function show() {
	queryParams.roleId = props.roleId
	resetQuery()
	visible.value = true
}

/** 选择行 */
function clickRow(row) {
	proxy.$refs["refTable"].toggleRowSelection(row)
}

// 多选框选中数据
function handleSelectionChange(selection) {
	selections.value = selection
}

// 查询表数据
async function getList() {
	loading.value = true
	let res = await zsList(queryParams)
	userList.value = res.rows
	total.value = res.total
	loading.value = false
}

/** 搜索按钮操作 */
function handleQuery() {
	queryParams.pageNum = 1
	getList()
}

/** 重置按钮操作 */
function resetQuery() {
	proxy.resetForm("queryRef")
	handleQuery()
}

const emit = defineEmits(["ok"])
/** 选择授权用户操作 */
function handleSelectUser() {
	if (!selections.value.length) {
		proxy.$modal.msgError("请选择要分配的用户")
		return
	}
	loading.value = true
	batchAddZsUsersBindRole({
		roleId: queryParams.roleId,
		bindUsers: selections.value.map(user => ({
			userId: user.userId,
		})),
	})
		.then(res => {
			proxy.$modal.msgSuccess("新增成功")
			handleClose()
			emit("ok")
		})
		.finally(() => {
			loading.value = false
		})
}

function handleClose() {
	visible.value = false
	proxy.$refs["refTable"].clearSelection()
}

defineExpose({
	show,
})
</script>
