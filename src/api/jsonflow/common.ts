/**
 * 公共方法封装
 * <AUTHOR>
 */
import request from "@/utils/request"

export function listDicData(url: string, obj?: Object) {
	return request({
		url: url,
		method: "post",
		data: obj,
	})
}

export function listDicUrl(url: string, query) {
	return request({
		url: url,
		method: "get",
		params: query,
	})
}

// 参与者选择器数据
export function fetchUserRolePicker() {
	return request({
		url: "/cloud-jsonflow/user-role-auditor/user-role/picker",
		method: "get",
	})
}

// 查询参与者下人员
export function listUsersByRoleId(roleId, jobType) {
	return request({
		url: "/cloud-jsonflow/user-role-auditor/list-users/" + roleId,
		method: "get",
		params: { jobType: jobType },
	})
}

export function listUserByFlowInst(roleId, jobType, flowInstId) {
	return request({
		url: "/cloud-jsonflow//runFlowExt/queryFlowRoleUser",
		method: "get",
		params: { jobType: jobType, flowInstId: flowInstId, roleId },

	})
}

// 查询人员
export function listUsersPage(params) {
	return request({
		url: "/cloud-jsonflow/user-role-auditor/list/pageUsers",
		method: "get",
		params,
	})
}
