<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="600px"
    :before-close="handleClose"
    top="5vh"
    append-to-body
  >
    <div class="flow-modal-content" v-loading="loading">
      <FlowSimpleDesign disabled :nodes="flowNodes" />
    </div>
    <slot name="footer" />
  </el-dialog>
</template>

<script setup>
import { getSimpleFlowNodes } from "@/api/jsonflow/def-flow"
const FlowSimpleDesign = defineAsyncComponent(() =>
  import("@/views/flow/createFlowSimple/components/FlowSimpleDesign.vue")
)
defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  title: {
    type: String,
    default: "固定审批流程",
  },
})

const dialogVisible = ref(false)
const loading = ref(true)
const flowNodes = ref([])
const open = (defFlowId) => {
  dialogVisible.value = true
  init(defFlowId)
}

const init = async (defFlowId) => {
  loading.value = true
  const res = await getSimpleFlowNodes(defFlowId)
  flowNodes.value = res.object
  loading.value = false
}

const handleClose = () => {
  dialogVisible.value = false
  flowNodes.value = []
}

defineExpose({
  open,
  handleClose,
})
</script>
<style scoped lang="scss">
.flow-modal-content {
  min-height: 200px;
  background: #f7f8f9;
  border-radius: 6px;
  margin-bottom: 15px;
  :deep(.flow-simple-design) {
    background: none;
  }
}
</style>
