<template>
  <div class="app-container">
    <div class="flex-x-between mb-10px">
      <StepsFill
        class="step-box"
        width="900px"
        :steps="stepOptions"
        v-model:current="stepIndex"
        :disabled="stepValue === 'finish'"
      />
      <div class="right-opt-box">
        <div
          class="btn-box"
          v-if="!['finish', 'setSignatory'].includes(stepValue)"
          @click="stepIndex = stepIndex - 1"
        >
          <el-icon><ArrowLeft /></el-icon>
        </div>
        <div class="btn-box close-btn" @click="handleClose">
          <el-icon><Close /></el-icon>
        </div>
      </div>
    </div>

    <div
      class="white-body-box relative main-create-box"
      v-loading="loading"
      :class="{ 'no-bg': stepValue === 'setFlowConfig' }"
    >
      <FillApplicationInfo
        ref="fillApplicationInfoRef"
        :flowFlag="flowFlag"
        :draftForm="draftForm"
        v-show="stepValue === 'setSignatory'"
      />
      <SetFlowConfig
        ref="setFlowConfigRef"
        v-show="stepValue === 'setFlowConfig'"
        v-if="flowFlag === 0"
      />
      <EditFileContent
        ref="editFileContentRef"
        :flowId="form.flowId"
        @success="editTemplateSuccess"
        v-show="['fileContent', 'finish'].includes(stepValue)"
      />
      <div
        class="table-handle-box"
        v-if="stepValue === 'setSignatory' || stepValue === 'setFlowConfig'"
      >
        <el-button plain @click="stepIndex = stepIndex - 1" v-if="stepIndex !== 0" w-80px>
          上一步
        </el-button>
        <el-button type="primary" @click="handleStep" w-80px :loading="loading">
          下一步
        </el-button>
      </div>
    </div>

    <FlowViewModal ref="flowViewModalRef" />
  </div>
</template>

<script setup name="FileLunch">
import {
  saveFileDraftAPI,
  getFileDraftInfoAPI,
  getDraftPreviewUrlAPI,
  updateDraftAPI,
} from "@/api/ess/fileManagement/file"
import { removeDraft } from "@/api/ess/fileManagement/file"
import mittBus from "@/utils/mitt"

const StepsFill = defineAsyncComponent(() => import("@/components/StepsFill/index.vue"))
const FlowViewModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowViewModal.vue")
)
const FillApplicationInfo = defineAsyncComponent(() =>
  import("./components/Steps/FillApplicationInfo.vue")
)
const SetFlowConfig = defineAsyncComponent(() =>
  import("./components/Steps/SetFlowConfig.vue")
)
const EditFileContent = defineAsyncComponent(() =>
  import("./components/Steps/EditFileContent.vue")
)

const props = defineProps({
  fileData: {
    type: Object,
  },
})
const { proxy } = getCurrentInstance()
const route = useRoute()
const flowFlag = ref(+route.params.flowFlag ?? 1) // 是否需要审批 0-是 1-否
const draftId = ref(route.params.draftId === "add" ? null : route.params.draftId) // 草稿箱id
const stepIndex = ref(0)
const form = ref({}) // 保存草稿的数据
const draftForm = ref({}) // 回显草稿数据
const stepValue = computed(() => {
  return stepOptions.value[stepIndex.value]?.value
})
// 提取公共基础步骤
const BASE_STEPS = [
  { title: "申请信息", value: "setSignatory" },
  { title: "完善合同内容", value: "fileContent" },
  { title: "办结", value: "finish" },
]

// 定义不同流程类型的步骤配置
const STEP_CONFIGS = {
  FLOW: [
    { title: "申请信息", value: "setSignatory" },
    { title: "配置审批流程", value: "setFlowConfig" },
    ...BASE_STEPS.slice(1), // 复用后续步骤
  ],
  NO_APPROVAL: BASE_STEPS,
}
const stepOptions = ref([])
const loading = ref(true)

// // 获取草稿箱数据
// watch(
//   () => draftId.value,
//   (val) => {
//     if (val) {
//       draftId.value = val
//       stepIndex.value = 0
//       getDraftInfo()
//     }
//   }
// )

// 获取草稿信息
async function getDraftInfo() {
  if (!draftId.value) return
  try {
    loading.value = true
    const res = await getFileDraftInfoAPI(draftId.value)
    const previewUrl = await getDraftPreviewUrlAPI(res.object.id) // 文件预览地址
    form.value.flowId = res.object.id
    draftForm.value = {
      ...res.object,
      flowId: res.object.id,
      previewUrl: previewUrl.object,
      filePaths: [res.object.uploadUrl],
    }
  } finally {
    loading.value = false
  }
}

async function init() {
  try {
    await nextTick()
    if (+flowFlag.value === 0) {
      stepOptions.value = STEP_CONFIGS.FLOW
    } else {
      stepOptions.value = STEP_CONFIGS.NO_APPROVAL
    }
    getDraftInfo()
  } finally {
    loading.value = false
  }
}

// 保存基础信息
async function submitBaseInfo(closed = false) {
  try {
    const data = await proxy.$refs.fillApplicationInfoRef?.validForm()
    form.value = { ...form.value, ...data }
    await saveDraft()
    !closed && stepIndex.value++
  } catch (error) {}
}

// 保存草稿
async function saveDraft() {
  try {
    loading.value = true
    const res = await saveFileDraftAPI(form.value)
    form.value.flowId = res.object
    mittBus.emit("refreshFileList")
  } finally {
    loading.value = false
  }
}

// 下一步
async function handleStep() {
  try {
    if (stepIndex.value === 0) {
      submitBaseInfo()
    } else if (stepValue.value === "setFlowConfig") {
      const flowRes = await proxy.$refs.setFlowConfigRef.submit()
      form.value.defFlowId = flowRes.defFlowId
      const updateRes = await updateDraftAPI(form.value.flowId, form.value.defFlowId, 1)
      form.value.flowId = updateRes.object
      stepIndex.value++
      // proxy.$refs.editFileContentRef.getFileInitiateUrl()
    }
  } catch (error) {}
}

watch(stepValue, (val) => {
  if (val === "fileContent") {
    proxy.$refs.editFileContentRef?.getFileInitiateUrl()
  }
})
// 填写模版内容成功
function editTemplateSuccess() {
  stepIndex.value++
}

function handleClose() {
  if (stepValue.value === "finish") {
    proxy.$tab.closePage()
    mittBus.emit("refreshFileList")
    return
  }
  proxy.$modal
    .confirm(
      "是否将本次编辑内容保存为草稿",
      "可在文件管理-我发起的草稿箱里继续编辑",
      "info",
      true,
      "保存并退出",
      "不保存"
    )
    .then(async () => {
      await submitBaseInfo(true)
      proxy.$modal.msgSuccess("保存成功")
      mittBus.emit("refreshFileList")
      proxy.$tab.closePage()
    })
    .catch((action) => {
      if (action === "cancel") {
        if (form.value.flowId) {
          removeDraft(form.value.flowId)
        }
        proxy.$tab.closePage()
      }
    })
}

onMounted(() => {
  init()
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;
.white-body-box {
  min-height: calc(#{$base-main-page-height} - 80px);
}
.step-box {
  :deep(.steps-title) {
    font-size: 15px !important;
  }
}

.table-handle-box {
  position: absolute;
  right: 10px;
  top: 10px;
}

.right-opt-box {
  flex-shrink: 0;
  display: flex;
  .btn-box {
    background: rgba(235, 236, 237, 0.4);
    border: 1px solid #d1d1d1;
    @apply flex-center text-14px cursor-pointer rounded-4px w-50px h-30px;
    margin-left: 10px;
  }
}

.main-create-box {
  &.no-bg {
    background: none;
    padding: 0;
  }
}
</style>
