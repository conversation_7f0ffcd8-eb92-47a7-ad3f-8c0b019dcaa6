<template>
  <div class="manual-container">
    <div class="sidebar">
      <el-menu
          :default-active="activeMenuIndex"
          class="manual-menu"
          @select="handleDocSelect"
      >
        <el-menu-item
            v-for="(item, index) in tableData"
            :key="item.id"
            :index="item.id"
            class="manual-menu-item"
        >
      <span class="menu-item-content">
        <span class="menu-title-dot"></span>
        <span class="menu-title">{{ item.docName }}</span>
      </span>
        </el-menu-item>
      </el-menu>
    </div>

    <div class="main-content">
      <Navbar disabled/>
      <div class="content-header" v-if="currentDoc">
        <div class="doc-info">
          <h2 class="doc-title">{{ currentDoc.docName }}</h2>
          <div class="doc-meta">
            <span class="meta-item">格式：{{ fileType(currentDoc.fileName) }}</span>
            <span class="meta-item">上传人：{{ currentDoc.createName || '未知上传人' }}</span>
            <span class="meta-item">版本号：{{ currentDoc.version }}</span>
            <span class="meta-item">上传时间：{{ currentDoc.createTime }}</span>
          </div>
        </div>
      </div>

      <div class="content-body">
        <el-card class="preview-card" shadow="never">
          <FilePreview :fileRaw="fileRaw" v-if="fileRaw.raw"></FilePreview>
          <div v-else class="empty-content">
            <el-empty description="请选择要查看的操作手册"></el-empty>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup name="PreviewOutside">
import Navbar from "./components/previewNavBar.vue"
import FilePreview from "@/views/fileManagement/fileLunch/components/FilePreview.vue"
import { NoAuthListDevManualPage, NoAuthGetDevManual } from "@/api/ess/doc/devManualApi.ts"
import { startDownload } from "@/api/ess/downloadCenter/downloadCenter"

// 表格数据
const tableData = ref([])
const total = ref(0)
const fileId = ref()
const activeMenuIndex = ref('0')
const currentDoc = ref(null)
const queryParams = ref({
  page: 1,
  limit: 20,
  docName: null,
  createName: null,
  status: null,
  foreignFlag: '0'
})
const fileRaw = ref({})
const loading = ref(true)

// 文件类型
const fileType = (fileName) => {
  return fileName ? fileName.replace(/[?#].*$/, "").split(".").pop().toUpperCase() : "PDF"
}

//获取列表
const getList = async () => {
  loading.value = true
  try {
    const res = await NoAuthListDevManualPage(queryParams.value)
    tableData.value = res.object.records
    total.value = res.object.total

    // 如果有数据且没有选中项，默认选中第一项
    if (tableData.value.length > 0 && !currentDoc.value) {
      fileId.value = tableData.value[0].id
      currentDoc.value = tableData.value[0]
      activeMenuIndex.value = tableData.value[0].id
      await getFileDetail()
    }
  } catch (error) {
    console.error('获取文档列表失败:', error)
  } finally {
    loading.value = false
  }
}

//获取文件详情
const getFileDetail = async () => {
  if (!fileId.value) return

  try {
    const fileDetail = await NoAuthGetDevManual(fileId.value)
    fileRaw.value.name = fileDetail.object.fileName
    currentDoc.value = {
      ...currentDoc.value,
      ...fileDetail.object,
    }

    const res = await startDownload(fileDetail.object.fileUrl)
    const blob = new Blob([res.data], { type: 'application/pdf' })
    fileRaw.value.raw = URL.createObjectURL(blob)
  } catch (error) {
    console.error('获取文件详情失败:', error)
    proxy.$modal.msgError('获取文件失败')
  }
}

// 处理文档选择
const handleDocSelect = async (id) => {
  fileId.value = id
  activeMenuIndex.value = id
  currentDoc.value = tableData.value.find(item => item.id === id)
  await getFileDetail()
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.manual-container {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
  padding: 70px 10px 10px 10px;
}

.sidebar {
  width: 300px;
  background: white;
  margin-right: 10px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  border-radius: 10px 0 0 10px;
}

.manual-menu {
  border: none;
  background: transparent;
  flex: 1;
  padding: 10px 0;
  font-size: 15px;

  :deep(.el-menu-item) {
    height: 45px;
    line-height: 40px;
    margin: 0 8px 5px;
    border-radius: 4px;
    padding: 0 10px !important;

    &:hover {
      background-color: rgba(36, 168, 126, 0.11);
      color: var(--el-color-primary);
    }

    &.is-active {
      background-color: rgba(36, 168, 126, 0.11);
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }
}

.menu-item-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.menu-title-dot {
  width: 6px;
  height: 6px;
  background: var(--el-color-primary);
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.menu-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 15px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0 10px 10px 0;
}

.content-header {
  padding: 20px;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 2px;
    background: repeating-linear-gradient(
            to right,
            #F3F3F3 0,
            #F3F3F3 5px,
            transparent 5px,
            transparent 10px
    );
  }
}

.doc-info {
  flex: 1;
}

.doc-title {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.doc-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.meta-item {
  font-size: 14px;
  color: #6B6B6B;
}

.content-body {
  flex: 1;
  overflow: hidden;
}

.preview-card {
  height: 100%;
  border: none;

  :deep(.el-card__body) {
    height: 100%;
    padding: 0;
  }
}

.file-preview-container {
  height: 100%;
}

.empty-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>
