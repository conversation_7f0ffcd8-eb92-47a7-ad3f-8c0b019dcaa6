<template>
  <el-dialog
      v-model="dialogVisible"
      width="500px"
      :title="title"
      :before-close="handleClose"
      append-to-body
  >
    <el-form
        :model="form"
        ref="formRef"
        :rules="rules"
        label-width="110px"
        :inline="false"
    >
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input
            v-model="form.enterpriseName"
            clearable
            :maxlength="50"
            show-word-limit
            placeholder="请补充工商营业执照上的企业名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="经办人姓名" prop="operatorName">
        <el-input
            v-model="form.operatorName"
            clearable
            :maxlength="50"
            show-word-limit
            placeholder="请输入经办人姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="经办人手机号" prop="phoneNumber">
        <el-input
            v-model="form.phoneNumber"
            clearable
            :maxlength="11"
            placeholder="请输入经办人手机号"
        ></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="success" v-if="!props.hiddenAdd" :loading="loading" @click="handleSave('add')">
        保存并新建
      </el-button>
      <el-button type="primary" @click="handleSave" :loading="loading"> 确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { addEnterpriseUserAPI, updateEnterpriseUserAPI } from "@/api/ess/baseSet/contacts"
import { rule } from "@/utils/validate"

defineOptions({
  inheritAttrs: false,
})

const props = defineProps({
  hiddenAdd: {
    type: Boolean,
    default: false
  }
})

const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)
const title = ref("")
const form = ref({
  enterpriseName: "",
  operatorName: "",
  phoneNumber: "",
})
let _form = toRaw({
  ...form.value,
})
const rules = reactive({
  enterpriseName: [
    { required: true, message: "请输入企业名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 100 个字符", trigger: "blur" },
    { validator: rule.inputName, trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  operatorName: [
    { required: true, message: "请输入经办人姓名", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
    { validator: rule.inputName, trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  phoneNumber: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { validator: rule.mobilePhone, trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
})

const emit = defineEmits(["change", "refresh"])

function open(row) {
  dialogVisible.value = true
  if (row) {
    form.value = { ...row }
    delete form.value.createTime
  }
  title.value = row?.id ? "修改企业" : "新增企业"
}

function handleClose() {
  formRef.value.resetFields()
  form.value = { ..._form }
  dialogVisible.value = false
}

/** 提交按钮操作 */
async function handleSave(type = "save") {
  try {
    // 表单验证
    const isValid = await formRef.value.validate()
    if (!isValid) return

    loading.value = true

    const apiMethod = form.value.id ? updateEnterpriseUserAPI : addEnterpriseUserAPI
    const res = await apiMethod(form.value)

    if (+res.code !== 200) return
    handleSuccess(type)
  } catch (error) {
    console.error("保存企业失败:", error)
  } finally {
    loading.value = false
  }
}

/** 成功处理逻辑 */
function handleSuccess(type) {
  const isEdit = !!form.value.id
  proxy.$modal.msgSuccess(isEdit ? "修改成功" : "新增成功")
  // 保存并新建
  if (type === "add") {
    proxy.resetForm("formRef")
    emit("change")
    title.value = "新增企业"
  } else {
    handleClose()
    emit(isEdit ? "change" : "refresh")
  }
}

defineExpose({
  open,
})
</script>
