import request from "@/utils/request"
import {essPrefix} from "@/config/constant"

// 查询公告列表
export function listNotice(query: object) {
    return request({
        url: essPrefix + "/notice/listPage",
        method: "post",
        data: query,
    })
}

// 查询公告详细
export function getNotice(noticeId: string) {
    return request({
        url: essPrefix + "/notice/" + noticeId,
        method: "get",
    })
}

// 新增公告
export function addNotice(data: object) {
    return request({
        url: essPrefix + "/notice/add",
        method: "post",
        data: data,
    })
}

// 修改公告
export function updateNotice(data: object) {
    return request({
        url: essPrefix + "/notice/edit",
        method: "post",
        data: data,
    })
}

// 删除公告
export function delNotice(ids: Array) {
    return request({
        url: essPrefix + "/notice/remove",
        method: "post",
        data: ids
    })
}
