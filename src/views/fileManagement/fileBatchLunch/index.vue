<template>
  <div class="app-container">
    <!-- 标题区域 - 仅在特定流程类型下显示 -->
    <div class="app-container__title" v-if="templateInfo.flowFlag != 0 && templateInfo.flowFlag != 1">
      模版批量发起文件申请
    </div>

    <!-- 顶部区域：步骤导航和关闭按钮 -->
    <div class="flex-x-between mb-10px">
      <!-- 步骤导航组件 - 仅在特定流程类型下显示 -->
      <StepsFill class="step-box" width="900px" :steps="stepOptions" v-model:current="stepIndex"
        v-if="templateInfo.flowFlag != 0 && templateInfo.flowFlag != 1" />

      <!-- 简单标题 - 在无步骤导航时显示 -->
      <div v-else class="app-container__title">模版批量发起文件申请</div>

      <!-- 右侧关闭按钮 -->
      <div class="right-opt-box">
        <div class="btn-box close-btn" @click="handleClose">
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
    </div>

    <div class="white-body-box relative"
      :style="stepValue == 'setFlowConfig' ? 'padding:0;background-color:#f7f8f9;' : ''">
      <template v-if="templateInfo.id">
        <FillApplicationInfo :key="1" ref="fillApplicationInfoRef" v-show="stepValue === 'setSignatory'"
          :templateInfo="templateInfo" />
      </template>

      <SetFlowConfig ref="setFlowConfigRef" v-if="templateInfo.flowFlag === '2'"
        v-show="stepValue === 'setFlowConfig'" />

      <div class="table-handle-box">

        <el-button v-auths="['file:fileList:batchLunch:flowSetting']" type="primary" plain v-if="+templateInfo.flowFlag === 1 && templateInfo.workId"
          @click="showFlowModal">
          固定审批流程
        </el-button>

        <el-button type="primary" @click="handleConfirm" w-80px :loading="loading" v-if="+templateInfo.flowFlag == 0">
          确认发起
        </el-button>

        <el-button type="primary" @click="handleConfirm" w-80px :loading="loading"
          v-else-if="+templateInfo.flowFlag == 2 && stepValue == 'setFlowConfig' || +templateInfo.flowFlag == 1">
          提交审批
        </el-button>

        <el-button type="primary" w-80px :loading="loading" @click="nextStep" v-else>下一步</el-button>
      </div>
    </div>

    <FlowViewModal ref="flowViewModalRef" />
  </div>
</template>

<script setup name="FileBatchLunch">
import { getTemplateInfo } from "@/api/ess/template/template"
import { fileBatchSave, startFile } from "@/api/ess/fileManagement/fileBatchLunch" // 批量保存文件API
const StepsFill = defineAsyncComponent(() => import("@/components/StepsFill/index.vue"))
const FillApplicationInfo = defineAsyncComponent(() =>
  import("./components/Steps/FillApplicationInfo.vue")
)
const FlowViewModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowViewModal.vue")
)
const SetFlowConfig = defineAsyncComponent(() =>
  import('./components/Steps/SetFlowConfig.vue')
)



const stepIndex = ref(0)
//审批
const setFlowConfigRef = ref(null);

//下一步
const nextStep = async () => {

  try {
    if (templateInfo.value.flowFlag == 2) {
      // 等待表单校验成功后，再进行下一步
      const res = await fillApplicationInfoRef.value.validForm();
      stepIndex.value++
      await fileBatchSave(res);
    }
  } catch (err) {

  }
}
const stepOptions = ref([]) // 步骤配置
const stepValue = computed(() => {
  return stepOptions.value[stepIndex.value]?.value
})

const router = useRouter()
const route = useRoute()
const { proxy } = getCurrentInstance() // 获取组件实例



const BASE_STEPS = [
  { title: "申请信息", value: "setSignatory" },
  // { title: "签字盖章", value: "signature" },
  // { title: "办结", value: "completion" },
]

// 定义不同流程类型的步骤配置
const STEP_CONFIGS = {
  FLOW: [
    { title: "申请信息", value: "setSignatory" },
    { title: "配置审批流程", value: "setFlowConfig" },
    ...BASE_STEPS.slice(1), // 复用后续步骤
  ],
  APPROVAL: [
    ...BASE_STEPS.slice(0, 1),
    { title: "事项审批", value: "itemApproval" },
    ...BASE_STEPS.slice(1),
  ],
  NO_APPROVAL: BASE_STEPS,
}

const templateId = ref(route.params.id)
const templateInfo = ref({})
const loading = ref(false)


// 获取模版信息
async function getTemplate() {
  try {
    const res = await getTemplateInfo(templateId.value)
    templateInfo.value = res.object
    await nextTick()
    // 根据流程类型选择步骤配置
    switch (+templateInfo.value.flowFlag) {
      case 2: // 发起人指定流程
        stepOptions.value = STEP_CONFIGS.FLOW
        break
      case 0: // 无须审批
        stepOptions.value = STEP_CONFIGS.NO_APPROVAL
        break
      default:
        // 默认审批流程
        stepOptions.value = STEP_CONFIGS.APPROVAL
    }

    if (route.query?.isDraft) {
      Object.keys(route.query).forEach(e => {
        templateInfo.value[e] = route.query[e]
      })
    }
    
  }finally{
  }
}

// 表单组件引用
const fillApplicationInfoRef = ref(null);

function handleClose() {
  proxy.$modal
    .confirm(
      "是否将本次编辑内容保存为草稿",
      "可在文件管理-我发起的草稿箱里继续编辑",
      "info",
      true,
      "保存并退出", // 确认按钮文本
      "不保存" // 取消按钮文本
    )
    .then(async () => {
      // 用户选择保存草稿
      // 验证表单
      const res = await fillApplicationInfoRef.value.validForm(true, ['approveDtoList']);
      // 判断返回的res对象是否有flowBatchPublishId上传文件的id
      if (!res.flowBatchPublishId) {
        proxy.$modal.msgError("请先上传文件")
        return
      }

      delete res.approveDtoList
      await fileBatchSave(res); // 注释的代码，可能是之前的保存逻辑
      proxy.$tab.closePage().then(() => router.push("/file/fileBatchLunchList"))
    })
    .catch((action) => {
      // 用户选择不保存或关闭对话框
      if (action === "cancel") {
        proxy.$tab.closePage().then(() => router.push("/file/fileBatchLunchList")) // 直接关闭页面
      }
    })
}
/**
 * 提交表单
 */
async function handleConfirm() {

  const res = await fillApplicationInfoRef.value.validForm()
  delete res.approveDtoList
  

  try {
    await proxy.$modal.confirm('是否确认发起？', `事项审批通过后，将发起 ${fillApplicationInfoRef.value.contractNum || templateInfo.value.contractNum || 0}份 合同，是否确认发起?`, "warning", true, '确认', '取消')
    if (res) {
      //将我的待办处理批量发起需要的参数添加到extParam中
      res.extParam = {
        ...res.extParam,
        signerCount: fillApplicationInfoRef.value.contractNum || templateInfo.value.contractNum,
        templateName: templateInfo.value.templateName
      }
      //发起人指定流程，需先获取到defFlowId，再发起文件申请
      if (stepValue.value === 'setFlowConfig' && templateInfo.value.flowFlag == 2) {
        // 当当前是配置审批流程，并且模版类型为发起人指定流程，则另外
        const flowRes = await setFlowConfigRef.value.submit();
        res.defFlowId = flowRes.defFlowId;
      } else if (templateInfo.value.flowFlag == 1 && stepValue.value != 'setFlowConfig') {
        res.workId = templateInfo.value.workId;
        // 当当前是固定流程，并且当前不是配置审批流程，则直接发起文件申请
        if (templateInfo.value.batchDefFlowId) {
          res.defFlowId = templateInfo.value.batchDefFlowId;
        }
        // 只要不是无须审批，则需要确认发起
      } else if (+templateInfo.value.flowFlag !== 0) {
        // proxy.$modal.alertWarning(error.object)
      }


      try {
        await startFile(res);
        proxy.$modal.msgSuccess('发起成功')
        proxy.$tab.closePage().then(() => router.push('/file/fileBatchLunchList'))
      } catch (err) {
        proxy.$modal.msgError(err.message)
      }
      // 发起文件申请 无需审批
    }

  } catch (err) {
    return
  }

}



// flow组件ref
const flowViewModalRef = ref(null)

// 展示固定审批流程
function showFlowModal() {
  flowViewModalRef.value.open(templateInfo.value.workId)
}

onMounted(() => {
  getTemplate()
})

</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.white-body-box {
  min-height: calc(#{$base-main-page-height} - 80px);
}

.app-container__title {
  @apply title-line text-18px mb-10px;
}

.right-opt-box {
  flex-shrink: 0;
  display: flex;

  .btn-box {
    background: rgba(235, 236, 237, 0.4);
    border: 1px solid #d1d1d1;
    @apply flex-center text-14px cursor-pointer rounded-4px w-50px h-30px;
    margin-left: 10px;
  }
}

.table-handle-box {
  position: absolute;
  right: 10px;
  top: 10px;
}
</style>
