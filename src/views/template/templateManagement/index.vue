<template>
  <div class="has-iframe-page">
    <div v-show="!showPreviewPage && !showReplenishPage" class="app-container">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
        class="white-form-box"
        label-width="90px"
        @submit.native.prevent
      >
        <el-form-item label="模板名称" prop="templateName">
          <el-input
            v-model="queryParams.templateName"
            placeholder="请输入模板名称"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属业务线" prop="businessLineId">
          <BusinessSelect v-model="queryParams.businessLineId" />
        </el-form-item>
        <el-form-item label="模版状态" prop="templateStatus">
          <dict-select
            v-model="queryParams.templateStatus"
            :options="template_status"
            value-type="string"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTimeRange">
          <el-date-picker
            v-model="queryParams.createTimeRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="common-input-width"
            clearable
            :default-time="dateDefaultTime"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <CustomTable
        ref="customTableRef"
        v-model:show-search="showSearch"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :data="tableData"
        custom
        :loading="loading"
        :total="total"
        @reload="getList"
      >
        <template #actions>
          <el-button v-auths="['template:list:add']" type="primary" @click="handleAdd">
            创建模版
          </el-button>
        </template>
        <vxe-column type="seq" width="70" fixed="left" />
        <vxe-column field="templateName" title="模板名称" fixed="left" min-width="200" />
        <vxe-column field="templateType" title="模版类型" width="90">
          <template #default="{ row }">
            <dict-tag :options="template_type" :value="row.templateType" />
          </template>
        </vxe-column>
        <vxe-column field="businessLineName" title="所属业务线" min-width="120" />
        <vxe-column field="templateStatus" title="模版状态" min-width="160">
          <template #default="{ row }">
            <div class="flex-y-center">
              <el-switch
                :model-value="row.templateStatus"
                active-value="ENABLE"
                :before-change="beforeChange"
                :disabled="!$auth.hasPermi('template:list:disable')"
                @click="updateStatus(row)"
              />
              <span
                class="u-m-l-5"
                :class="{ 'text-muted': row.templateStatus != 'ENABLE' }"
              >
                {{ selectDictLabel(template_status, row.templateStatus) }}
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="essTemplateId" title="模板id" min-width="160" />
        <vxe-column field="createUser" title="创建人" min-width="100" />
        <vxe-column field="createTime" title="创建时间" width="170" />
        <vxe-column field="flowFlag" title="使用模板流程设置" width="160">
          <template #default="{ row }">
            <div class="flex-y-center">
              {{ selectDictLabel(template_flow_flag, row.flowFlag) }}
              <el-link
                type="success"
                v-if="+row.flowFlag === 1"
                underline="never"
                class="ml-5px"
                @click="toFlowSetting(row)"
                v-auths="['template:list:flowSetting']"
              >
                (流程配置)
              </el-link>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="opt" title="操作" min-width="160" fixed="right">
          <template #default="{ row }">
            <TableColOptBtn
              :key="Math.random()"
              :buttons="[
                {
                  text: '预览',
                  click: () => handlePreview(row),
                  permission: ['template:list:preview'],
                },
                {
                  text: '编辑',
                  click: () => handleUpdate(row),
                  permission: ['template:list:edit'],
                },
                {
                  text: row.templateStatus != 'ENABLE' ? '启用' : '停用',
                  click: () => updateStatus(row),
                  permission: ['template:list:edit'],
                },
                {
                  text: '下载模版',
                  click: () => handleDownload(row),
                  permission: ['template:list:download'],
                },
                {
                  text: '修改扩展信息',
                  click: () => handleReplenish(row),
                  permission: ['template:list:replenish'],
                },
                {
                  text: '查看扩展信息',
                  click: () => viewReplenishInfo(row),
                  permission: ['template:list:replenishView'],
                },
              ]"
            />
          </template>
        </vxe-column>
      </CustomTable>
    </div>

    <!-- 修改扩展信息 -->
    <ReplenishInfoPage ref="replenishInfoRef" v-model="showReplenishPage" />
    <!-- 预览 -->
    <PreviewTemplatePage ref="previewRef" v-model="showPreviewPage" />

    <FlowInitiateModal ref="flowInitiateModalRef" @opened="setTemplateId" />
  </div>
</template>

<script setup name="TemplateManagement">
import { fetchByConfig } from "@/api/order/flow-application"
import {
  getTemplateListPage,
  disableTemplate,
  downloadTemplate,
} from "@/api/ess/template/template"
import { flowConfigKey } from "@/config/constant"
import mittBus from "@/utils/mitt"

const FlowInitiateModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowInitiateModal.vue")
)
const BusinessSelect = defineAsyncComponent(() =>
  import("@/views/components/BusinessSelect.vue")
)
const PreviewTemplatePage = defineAsyncComponent(() =>
  import("./components/PreviewTemplatePage.vue")
)
const ReplenishInfoPage = defineAsyncComponent(() =>
  import("./components/ReplenishInfoPage.vue")
)

const { proxy } = getCurrentInstance()
const { template_status, template_type, template_flow_flag } = proxy.useDict(
  "template_status",
  "template_type",
  "template_flow_flag"
)

const router = useRouter()
const route = useRoute()

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const previewRef = ref(null) // 预览page
const showPreviewPage = ref(false) // 预览页面
const showReplenishPage = ref(false) // 扩展信息页面
const replenishInfoRef = ref(null) // 修改扩展信息

const queryParams = reactive({
  templateName: null,
  templateStatus: null,
  businessLineId: null,
  createTimeRange: [],
  limit: 10,
  page: 1,
})

/** 查询列表 */
function getList() {
  loading.value = true
  queryParams.queryStartTime = queryParams.createTimeRange?.[0]
  queryParams.queryEndTime = queryParams.createTimeRange?.[1]
  getTemplateListPage(queryParams)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 创建模版 */
function handleAdd() {
  router.push("/template/createTemplate")
}

// 编辑
function handleUpdate(row) {
  const url = `/template/editTemplate/${row.essTemplateId}?to=templateManagement`
  if (row.templateStatus === "ENABLE") {
    confirmToUpdate(() => {
      router.push(url)
    })
  } else {
    router.push(url)
  }
}

// 配置流程
function toFlowSetting(row) {
  router.push(`/template/tempFlowConfig/${row.id}?to=templateManagement`)
}

/** 预览按钮操作 */
function handlePreview(row) {
  showPreviewPage.value = true
  previewRef.value.open(row)
}
/** 修改扩展信息 */
function handleReplenish(row) {
  console.log("修改扩展信息", row)
  if (row.templateStatus === "ENABLE") {
    confirmToUpdate(() => {
      replenishInfoRef.value.open(row)
      showReplenishPage.value = true
    })
  } else {
    replenishInfoRef.value.open(row)
    showReplenishPage.value = true
  }
}

function confirmToUpdate(callback) {
  proxy.$modal
    .confirm(
      "确认编辑当前模版?",
      "编辑模板信息后，模板状态将变成未启用，用户将无法正常使用该模板发起合同，是否进入编辑模板信息界面？"
    )
    .then(function () {
      callback()
    })
    .catch(() => {})
}

/** 查看扩展信息 */
function viewReplenishInfo(row) {
  replenishInfoRef.value.open(row, true)
}
function beforeChange() {
  return false
}
/** 停用/启用 */
function updateStatus(row) {
  // 固定流程需要先配流程才能启用
  if (!row.workId && +row.flowFlag === 1) {
    proxy.$modal
      .confirm(
        "该模板为固定流程模板，流程尚未配置。请先完成流程配置，再启用模板",
        "",
        "info",
        true,
        "去配置"
      )
      .then(() => {
        toFlowSetting(row)
      })
    return
  }
  if (row.templateStatus == "ENABLE") {
    handleDisableTemplate(row) // 停用
  } else {
    toStartTemplate(row) // 启用
  }
}

function handleDisableTemplate(row) {
  proxy.$modal
    .confirm(
      `确认停用"${row.templateName}"?`,
      "停用后，用户将无法使用该模版发起合同，确认停用吗？"
    )
    .then(async function () {
      let res = await disableTemplate(row.id)
      if (+res.code == 200) {
        getList()
        proxy.$modal.msgSuccess("停用成功")
      }
    })
    .catch((err) => {
      console.log("err", err)
    })
}

const updateRow = ref({})
async function openFlowModal(row) {
  const res = await fetchByConfig(flowConfigKey.TEMPLATE_ENABLE_FLOW)
  proxy.$refs.flowInitiateModalRef.open(res.object, false)
}
// 启用
function toStartTemplate(row) {
  proxy.$modal
    .confirm(
      `确认启用"${row.templateName}"?`,
      "启用需要进入审批申请，申请通过后，用户将正常使用该模板发起合同，是否进入申请界面？"
    )
    .then(function () {
      updateRow.value = row
      openFlowModal(row)
    })
    .catch(() => {})
}
// 下载模版
async function handleDownload(row) {
  proxy.$download.downloadGetFilename(downloadTemplate(row.id))
}

// 设置模版id
function setTemplateId(initiateRef) {
  if (updateRow.value) {
    // 印章授权记录ID
    initiateRef?.design?.fApi?.setValue("templateId", updateRow.value.id)
    initiateRef?.design?.fApi?.setValue("essTemplateId", updateRow.value.essTemplateId)
  }
}

getList()

// 页面加载时
onMounted(() => {
  mittBus.on("refreshTemplateList", () => {
    getList()
  })
})
// 页面卸载时
onUnmounted(() => {
  mittBus.off("refreshTemplateList", () => {
    getList()
  })
})

onActivated(() => {
  if (route.query.r) {
    getList()
    delete route.query.r
  }
})
</script>
