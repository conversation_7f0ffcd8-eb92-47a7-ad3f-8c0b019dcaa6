<template>
  <div class="app-container">
    <div>
      <!-- 选项卡tabs -->
      <!-- <el-tabs
        ref="tabs"
        v-model="data.currElTab.active"
        @tab-click="methods.handleClick"
      >
        <el-tab-pane
          v-for="(item, index) in data.elTabs"
          :key="index"
          :label="item.formName"
          :name="item.id"
        >
        </el-tab-pane>
      </el-tabs> -->
      <CustomTabs
        v-model="data.currElTab.active"
        :options="data.elTabs"
        labelKey="formName"
        valueKey="id"
        @tab-click="methods.handleClick"
      />

      <!-- 审批人信息 -->
      <template
        v-if="
          !data.currJob.hiJob &&
          !orderVue.vueKeySys.sysPaths.includes(data.currElTab.path) &&
          data.currJob.order
        "
      >
        <div class="create-info">
          <div class="create-info-title">
            <img src="@/assets/images/icon/form-icon.png" />
            {{ data.currJob.order.createUserName }}
            <span v-if="data.currJob.order.createUserWorkNumber">
              ({{ data.currJob.order.createUserWorkNumber }}) </span
            >提交的{{ data.currElTab.formName }}
          </div>
          <div class="create-info-desc">
            <span
              >工单编号：<label>{{ data.currJob.order.code }}</label></span
            >
            <span
              >所在部门：<label>{{ data.currJob.order.createUserDeptName }}</label></span
            >
            <span v-if="data.currJob.order.createUserPhone">
              联系方式：<label>{{
                replacePhone(data.currJob.order.createUserPhone)
              }}</label>
            </span>
            <span
              >申请时间：<label>{{ data.currJob.startTime }}</label></span
            >
          </div>
        </div>
      </template>

      <div id="handle_job" v-loading="data.loading">
        <div class="white-form-box">
          <!-- 动态组件 -->
          <template v-if="data.currElTab.path && data.currElTab.path.startsWith('http')">
            <dynamic-iframe
              v-if="data.currElTab.active"
              :curr-job="data.currJob"
              :curr-el-tab="data.currElTab"
              @handle-job="methods.handleJob"
            />
          </template>
          <template v-else>
            <dynamic-link
              v-if="data.currElTab.active"
              :curr-job="data.currJob"
              :curr-el-tab="data.currElTab"
              :showMsg="false"
              @handle-job="methods.handleJob"
              :class="{
                'max-w-800px':
                  data.currElTab && data.currElTab.path === '/order/run-application/flow',
              }"
            />
          </template>
        </div>

        <!-- 审批项 -->
        <template
          v-if="
            !data.currJob.hiJob &&
            !orderVue.vueKeySys.sysPaths.includes(data.currElTab.path)
          "
        >
          <!--          <div class="white-form-box">-->
          <!--            <el-form label-width="110px" max-w-800px>-->
          <!--              <template v-if="data.currElTab.isAutoAudit !== '1'">-->
          <!--                <el-form-item label="审批意见">-->
          <!--                  <el-input-->
          <!--                    v-model="data.currJob.comment"-->
          <!--                    :autosize="{ minRows: 3, maxRows: 6 }"-->
          <!--                    type="textarea"-->
          <!--                    placeholder="请输入审批意见"-->
          <!--                    @change="methods.changeComment"-->
          <!--                  />-->
          <!--                </el-form-item>-->
          <!--                &lt;!&ndash; <el-form-item label="审批签名">-->
          <!--                <sign-name ref="signNameRef" :curr-job="data.currJob" />-->
          <!--              </el-form-item> &ndash;&gt;-->
          <!--              </template>-->
          <!--            </el-form>-->
          <!--          </div>-->

          <div v-if="data.currElTab.isAutoAudit !== '1'" class="mb-20px">
            <template
              v-for="(jobBtn, index) in data.jobBtns.length > 6
                ? data.jobBtns.slice(0, 6)
                : data.jobBtns"
              :key="index"
            >
              <el-button
                v-preventReclick
                :loading="data.loading"
                :plain="getJobBtnStyle(jobBtn, 'plain')"
                :type="getJobBtnStyle(jobBtn, 'type')"
                class="is-deep"
                @click="methods.handleSubmit(jobBtn, true)"
              >
                {{ methods.getJobBtnName(jobBtn) }}
              </el-button>
            </template>
            <el-dropdown
              v-if="data.jobBtns.length > 6"
              max-height="500px"
              size="small"
              placement="top"
              class="m-l-10px align-middle"
            >
              <el-button icon="More">更多</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="(jobBtn, index) in data.jobBtns.slice(6)"
                    :key="index"
                    divided
                    @click.native="methods.handleSubmit(jobBtn, true)"
                  >
                    {{ methods.getJobBtnName(jobBtn) }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </div>

      <el-dialog
        v-if="data.showToRunNode"
        v-model="data.showToRunNode"
        width="600px"
        title="选择任意驳回节点"
        append-to-body
      >
        <template #header>
          <div class="flex-y-center">
            选择任意驳回节点
            <el-tooltip
              effect="dark"
              content="什么是被驳回人？当被驳回人为空时，则默认会驳回到该节点下的全部审批人。当被驳回人不为空时，则会驳回到该节点下指定的审批人员"
              placement="bottom-start"
              popper-class="dialog__popper"
            >
              <svg-icon name="local-question" class="ml-5px" />
            </el-tooltip>
          </div>
        </template>
        <run-reject :curr-job="data.currJob" @on-reject-job="btnMethods.onRejectJob" />
      </el-dialog>

      <el-dialog
        v-if="data.showJumpToRunNode"
        v-model="data.showJumpToRunNode"
        top="20px"
        width="70%"
        title="选择任意跳转节点"
        append-to-body
      >
        <run-any-jump
          :curr-job="data.currJob"
          @on-any-jump-job="btnMethods.onAnyJumpJob"
        />
      </el-dialog>

      <el-dialog
        v-if="data.showJobSignature"
        v-model="data.showJobSignature"
        top="20px"
        width="850px"
        :title="data.jobSignatureTitle"
        append-to-body
      >
        <template #header>
          <div class="flex-y-center">
            {{ data.jobSignatureTitle }}
            <el-tooltip
              effect="dark"
              content="什么是被驳回人？当被驳回人为空时，则默认会驳回到该节点下的全部审批人。当被驳回人不为空时，则会驳回到该节点下指定的审批人员"
              placement="bottom-start"
              popper-class="dialog__popper"
            >
              <svg-icon name="local-question" class="ml-5px" />
              <template #content>
                <div>
                  什么是加签减签？加签指在同一个节点里增加其他【参与者】参与审批，加签分为前加签、后加签、加并签
                  <div class="mt-10px leading-20px">
                    1、前加签即在当前审批人之前增加一个参与者参与审批，该参与者审批完成之后会回到当前审批人
                    <br />
                    2、后加签即在当前审批人之后增加一个参与者参与审批，当前审批人审批之后流转到该参与者<br />
                    3、加并签即与当前审批人同时审批 <br />
                    4、减签即减少参与者
                  </div>
                </div>
              </template>
            </el-tooltip>
          </div>
        </template>
        <job-signature
          :curr-job="data.currJob"
          @on-job-signature="btnMethods.onJobSignature"
        />
      </el-dialog>

      <el-dialog
        v-if="data.showJobCopyPass"
        v-model="data.showJobCopyPass"
        top="20px"
        width="800px"
        :title="data.jobSignatureTitle"
        append-to-body
      >
        <copy-pass-job
          :curr-job="data.currJob"
          @on-job-signature="btnMethods.onJobSignature"
        />
      </el-dialog>

      <el-dialog
        v-if="data.showNodeSignature"
        v-model="data.showNodeSignature"
        top="20px"
        width="800px"
        :title="data.nodeSignatureTitle"
        append-to-body
      >
        <node-signature
          :curr-job="data.currJob"
          @on-node-signature="btnMethods.onNodeSignature"
        />
      </el-dialog>

      <user-role-picker
        ref="userRolePicker"
        :is-only-one="true"
        @on-select-items="btnMethods.onSelectItems"
      />

      <!--提交任务弹窗-->
      <jobBtnModal
        ref="jobbtnmodal"
        @confirm="methods.handleJob"
        v-model="data.currJob.comment"
      />
    </div>
  </div>
</template>

<script setup lang="ts" name="HandleJob">
import { deepClone } from "@/utils/index"
import { useMessageBox } from "@/hooks/message"
import mittBus from "@/utils/mitt"
import useUserStore from "@/store/modules/user"
import * as orderVue from "@/api/order/order-key-vue"
import { validateNull } from "@/utils/validate"
import { DIC_PROP } from "../../support/dict-prop"
import { getJobBtnName } from "../../index"
import * as doJob from "@/api/jsonflow/do-job"
import * as runNode from "@/api/jsonflow/run-node"
import * as runFlow from "@/api/jsonflow/run-flow"
import { useFlowJob } from "../../stores/flowJob"
import { setPropsDataValue } from "../../support/common"
import { initHandleJobHeight } from "../../utils"
import { handleTodoDetail, handleToDoneDetail } from "../../support/extend"
import { replacePhone } from "@/utils/index"
import jobBtnModal from "./jobbtn-modal.vue"

const { proxy } = getCurrentInstance()

const userStore = useUserStore()
const flowJob = useFlowJob()
const route = useRoute()
const router = useRouter()

// 引入组件
const DynamicLink = defineAsyncComponent(() => import("./dynamic-link.vue"))
const DynamicIframe = defineAsyncComponent(() => import("./dynamic-iframe.vue"))
const SignName = defineAsyncComponent(() => import("../sign-name/index.vue"))
const UserRolePicker = defineAsyncComponent(() => import("../user-role/picker2.vue"))
const JobSignature = defineAsyncComponent(() => import("./job-signature.vue"))
const CopyPassJob = defineAsyncComponent(() => import("./copy-pass-job.vue"))
const NodeSignature = defineAsyncComponent(() => import("./node-signature.vue"))
const RunReject = defineAsyncComponent(
  () => import("@/views/jsonflow/run-reject/flow.vue")
)
const RunAnyJump = defineAsyncComponent(() => import("@/views/jsonflow/run-job/flow.vue"))
const jobbtnmodal = ref()

const data = reactive({
  currJob: {},
  // 0、转办 1、下一办理人
  nodeUserType: undefined,
  jobSignatureTitle: undefined,
  showJobSignature: false,
  showJobCopyPass: false,
  nodeSignatureTitle: undefined,
  showNodeSignature: false,
  showJumpToRunNode: false,
  showToRunNode: false,
  elTabs: {},
  jobBtns: [],
  currElTab: { active: null },
  loading: false,
})

const dynamicLinkRef = ref()
const dynamicLinkKey = ref(0)

const channel = new BroadcastChannel("currJob_channel")
const btnMethods = {
  onHandleJob(jobBtn) {
    data.currJob.jobBtn = jobBtn
    let nodeSignatures = [
      DIC_PROP.JOB_BTNS[1].value,
      DIC_PROP.JOB_BTNS[2].value,
      DIC_PROP.JOB_BTNS[3].value,
    ]
    if (jobBtn === DIC_PROP.JOB_BTNS[13].value) btnMethods.openJobRoleUserId("1")
    else if (jobBtn === DIC_PROP.JOB_BTNS[14].value) btnMethods.openJobRoleUserId("0")
    else if (jobBtn === DIC_PROP.JOB_BTNS[15].value)
      btnMethods.handleTerminateFlow(data.currJob)
    else if (jobBtn === DIC_PROP.JOB_BTNS[16].value)
      btnMethods.handleEarlyComplete(data.currJob)
    else if (jobBtn === DIC_PROP.JOB_BTNS[17].value)
      btnMethods.handleInvalidFlow(data.currJob)
    else if (jobBtn === DIC_PROP.JOB_BTNS[11].value) btnMethods.doJobSignature(jobBtn)
    else if (jobBtn === DIC_PROP.JOB_BTNS[12].value) btnMethods.doJobSignature(jobBtn)
    else if (jobBtn === DIC_PROP.JOB_BTNS[7].value)
      btnMethods.onBackFirstJob(data.currJob)
    else if (jobBtn === DIC_PROP.JOB_BTNS[8].value) btnMethods.onBackPreJob(data.currJob)
    else if (nodeSignatures.includes(jobBtn)) btnMethods.doNodeSignature(jobBtn)
    else btnMethods.doHandleJob(jobBtn)
  },
  doHandleJob(jobBtn) {
    data.currJob.jobBtn = jobBtn
    if (jobBtn === DIC_PROP.JOB_BTNS[9].value) {
      return btnMethods.doAnyJumpJob()
    }
    if (jobBtn === DIC_PROP.JOB_BTNS[10].value) {
      return btnMethods.doRejectJob()
    }
    let jobSignatures = [
      DIC_PROP.JOB_BTNS[4].value,
      DIC_PROP.JOB_BTNS[5].value,
      DIC_PROP.JOB_BTNS[6].value,
    ]
    if (jobSignatures.includes(jobBtn)) {
      return btnMethods.doJobSignature(jobBtn)
    }
    data.loading = true
    doJob
      .complete(data.currJob)
      .then(() => {
        btnMethods.onContextmenuClose()
        proxy.$modal.confirm("审批成功", "返回我的待办", "success", false).then(() => {
          // proxy.$tab.closeOpenPage({ path: "/agenda/mine" })
          proxy.$tab.closeOpenPage()
          router.push("/agenda/mine")
        })
      })
      .finally(() => {
        data.loading = false
      })
  },
  onBackFirstJob(currJob) {
    doJob.backFirstJob(currJob).then(() => {
      proxy.$modal.msgSuccess("退回首节点成功")
      btnMethods.onContextmenuClose()
    })
  },
  onBackPreJob(currJob) {
    doJob.backPreJob(currJob).then(() => {
      proxy.$modal.msgSuccess("退回上一步成功")
      toResolveSaves()
      btnMethods.onContextmenuClose()
    })
  },
  doAnyJumpJob() {
    data.currJob.runRejectVO = {}
    data.showJumpToRunNode = true
  },
  onAnyJumpJob(currJob) {
    doJob.anyJump(currJob).then(() => {
      proxy.$modal.msgSuccess("跳转成功")
      data.showJumpToRunNode = false
      btnMethods.onContextmenuClose()
    })
  },
  doRejectJob() {
    data.currJob.runRejectVO = {}
    data.showToRunNode = true
  },
  onRejectJob(currJob) {
    doJob.reject(currJob).then(() => {
      proxy.$modal.msgSuccess("驳回成功")
      data.showToRunNode = false
      toResolveSaves()
      btnMethods.onContextmenuClose()
    })
  },
  doJobSignature(jobBtn) {
    data.currJob.nodeJobSigned = {}
    data.jobSignatureTitle = getJobBtnName(jobBtn)
    if (
      jobBtn === DIC_PROP.JOB_BTNS[11].value ||
      jobBtn === DIC_PROP.JOB_BTNS[12].value
    ) {
      data.currJob.nodeJobSigned.signedType = DIC_PROP.SIGNATURE_TYPE[2].value
      if (jobBtn === DIC_PROP.JOB_BTNS[11].value)
        data.currJob.nodeJobSigned.belongType = "1"
      else data.currJob.nodeJobSigned.belongType = "2"
      data.showJobCopyPass = true
    } else {
      let type = DIC_PROP.SIGNATURE_TYPE[0].value
      if (DIC_PROP.JOB_BTNS[5].value === jobBtn) type = DIC_PROP.SIGNATURE_TYPE[1].value
      else if (DIC_PROP.JOB_BTNS[6].value === jobBtn)
        type = DIC_PROP.SIGNATURE_TYPE[2].value
      data.currJob.nodeJobSigned.signedType = type
      data.currJob.nodeJobSigned.belongType = "0"
      data.showJobSignature = true
    }
  },
  doNodeSignature(jobBtn) {
    data.currJob.nodeJobSigned = {}
    data.nodeSignatureTitle = getJobBtnName(jobBtn)
    data.currJob.nodeJobSigned.signedType = DIC_PROP.NODE_SEQUENTIAL_TYPE[2].value
    let nodeType = DIC_PROP.NODE_TYPE[1].value
    let type = DIC_PROP.NODE_SIGNATURE_TYPE[0].value
    if (DIC_PROP.JOB_BTNS[2].value === jobBtn)
      type = DIC_PROP.NODE_SIGNATURE_TYPE[1].value
    else if (DIC_PROP.JOB_BTNS[3].value === jobBtn) {
      type = DIC_PROP.NODE_SIGNATURE_TYPE[2].value
      nodeType = DIC_PROP.NODE_TYPE[2].value
      if (data.currJob.runNodeVO.nodeType !== nodeType) {
        proxy.$modal.msg("当前节点不是并行节点，必须在并行节点上才能加并节点")
        return
      }
    }
    data.currJob.nodeJobSigned.nodeSignedType = type
    data.currJob.nodeJobSigned.nodeType = nodeType
    data.showNodeSignature = true
    toResolveSaves()
  },
  onJobSignature(currJob) {
    doJob.signature(currJob).then(() => {
      if (data.currJob.nodeJobSigned.belongType !== "0") {
        if (data.currJob.nodeJobSigned.belongType === "1")
          proxy.$modal.msgSuccess("抄送成功")
        else proxy.$modal.msgSuccess("传阅成功")
        data.showJobCopyPass = false
      } else {
        proxy.$modal.msgSuccess("加签成功")
        data.showJobSignature = false
      }
      if (DIC_PROP.SIGNATURE_TYPE[2].value !== data.currJob.nodeJobSigned.signedType) {
        btnMethods.onContextmenuClose()
      }
      toResolveSaves()
    })
  },
  onNodeSignature(currJob) {
    runNode.signature(currJob).then(() => {
      proxy.$modal.msgSuccess("加节点成功")
      data.showNodeSignature = false
      if (
        DIC_PROP.NODE_SIGNATURE_TYPE[2].value !==
        data.currJob.nodeJobSigned.nodeSignedType
      ) {
        btnMethods.onContextmenuClose()
      }
    })
  },
  // 选择参与者
  openJobRoleUserId(type) {
    data.nodeUserType = type
    proxy.$refs.userRolePicker.onOpen()
  },
  onSelectItems(items) {
    if (data.nodeUserType === "1") {
      btnMethods.onJobNextUserRole(items[0])
    } else {
      btnMethods.onTurnRunJob(items[0])
    }
  },
  // 提前结束流程
  handleEarlyComplete(row) {
    useMessageBox()
      .prompt("请输入提前结束流程理由")
      .then(({ value }) => {
        row.jobBtn = DIC_PROP.JOB_BTNS[16].value
        row.invalidReason = value
        row.flowStatus = "0"
        return runFlow.earlyComplete(row)
      })
      .then(() => {
        proxy.$modal.msgSuccess("操作成功")
        btnMethods.onContextmenuClose()
      })
  },
  // 终止流程
  handleTerminateFlow(row) {
    try {
      row.jobBtn = DIC_PROP.JOB_BTNS[15].value
      row.invalidReason = data.currJob.comment
      row.flowStatus = "0"
      runFlow.terminateFlow(row)
      proxy.$modal.msgSuccess("操作成功")
      toResolveSaves()
      btnMethods.onContextmenuClose()
    } catch (err) {
      console.error(err)
    }
  },
  // 作废流程
  handleInvalidFlow(row) {
    useMessageBox()
      .prompt("请输入作废理由")
      .then(({ value }) => {
        row.jobBtn = DIC_PROP.JOB_BTNS[17].value
        row.invalidReason = value
        row.flowStatus = "0"
        return runFlow.invalidFlow(row)
      })
      .then(() => {
        proxy.$modal.msgSuccess("操作成功")
        flowJob.delJobLen()
        btnMethods.onContextmenuClose()
      })
  },
  // 转办任务
  onTurnRunJob(role) {
    useMessageBox()
      .confirm('是否确认转办名称为"' + data.currJob.jobName + '"的任务?')
      .then(() => {
        data.currJob.jobType = role.jobType
        data.currJob.roleId = role.roleId
        data.currJob.jobBtn = DIC_PROP.JOB_BTNS[14].value
        return doJob.turnRunJob(data.currJob)
      })
      .then(() => {
        proxy.$modal.msgSuccess("操作成功")
        btnMethods.onContextmenuClose()
      })
  },
  // 指定下一办理人
  onJobNextUserRole(role) {
    useMessageBox()
      .confirm("是否确认指定下一步参与者?")
      .then(() => {
        data.currJob.nextUserRole = role
        return btnMethods.doHandleJob(DIC_PROP.JOB_BTNS[13].value)
      })
  },
  onContextmenuClose() {
    btnMethods.postMessage()
    // 防止重复进入
    if (route.fullPath.indexOf("tokenLoaded") === -1) {
      proxy.$tab.closeOpenPage({ path: "/agenda/mine" })
      // mittBus.emit("onCurrentContextmenuClick", Object.assign({}, source))
    } else {
      let timer = setTimeout(() => {
        window.close()
        clearTimeout(timer)
      }, 2000)
    }
  },
  postMessage() {
    channel.postMessage({ type: "currJob", data: "" })
  },
}

const $route = useRoute()
const methods = {
  // 初始化数据
  async initJobData() {
    data.loading = false
    let row = {}
    setPropsDataValue(
      row,
      $route.query,
      "id",
      "isHiJob",
      "isView",
      "isRead",
      "isApp",
      "isForm"
    )
    if (row.isHiJob !== "0") {
      await handleToDoneDetail(row, data, row.isApp, row.isForm)
    } else {
      await handleTodoDetail(
        row,
        data,
        row.isView,
        row.isRead,
        true,
        btnMethods.postMessage
      )
    }
    data.jobBtns = data.currJob.jobBtns
    // 隐藏模板自带的提交按钮
    data.currJob.showSubmitBtn = false
    methods.handleElTabs()
    data.currJob.resolveSaves = []
    nextTick(() => {
      initHandleJobHeight(data)
    })
  },
  // 处理tabs
  handleElTabs() {
    data.elTabs = methods.removeCloneElTab()
    // 排序
    let elTab = data.elTabs
      .sort((s1, s2) => {
        return s1.sort - s2.sort
      })
      .find((f) => f.isActive === "1")
    // 默认展示的tab
    if (elTab) {
      data.currElTab = elTab
    } else {
      data.currElTab = data.elTabs[0]
    }

    data.currElTab.active = data.currElTab.id
    data.currJob.currElTab = data.currElTab
  },
  removeCloneElTab() {
    // 判断是否显隐
    let clone = deepClone(data.currJob.elTabs)
    let conditions = orderVue.handleElTab(data.currJob)
    conditions.forEach((cd) => {
      if (!cd.isShow || cd.isShow === "0") {
        data.currJob.elTabs = data.currJob.elTabs.filter((f) => f.path !== cd.path)
        clone = clone.filter((f) => f.path !== cd.path)
      }
    })
    return clone
  },
  // 页面选择
  handleClick(tab) {
    if (data.currElTab.id === tab) {
      return
    }
    // 切换页面
    data.currElTab = data.elTabs.find((f) => f.id === tab)
    data.currElTab.active = data.currElTab.id
    data.currJob.currElTab = data.currElTab
    // 隐藏弹出框滚动条
    initHandleJobHeight(data)
  },
  // 按钮提交
  async handleSubmit(jobBtn, isAudit) {
    // 开始节点提交
    if (methods.isStartNode(jobBtn)) {
      proxy.$modal.confirm("是否确认提交?").then(function () {
        methods.handleJob(jobBtn, isAudit)
      })
      // 非加签 驳回
    } else if (
      ![
        DIC_PROP.JOB_BTNS[4].value,
        DIC_PROP.JOB_BTNS[5].value,
        DIC_PROP.JOB_BTNS[10].value,
      ].includes(jobBtn)
    ) {
      jobbtnmodal.value.open(jobBtn, isAudit)
    } else {
      methods.handleJob(jobBtn, isAudit)
    }
  },
  // 提交任务
  async handleJob(jobBtn, isAudit) {
    // 提交时自动审批
    if (proxy.$refs.signNameRef) {
      await proxy.$refs.signNameRef.handleGenerate()
    }

    let b = !jobBtn.includes(DIC_PROP.JOB_BTNS[0].value)
    if (b) {
      methods.timeoutLoading()
      // 操作前回调函数
      let resolve = data.currJob.resolve
      if (resolve) {
        if (resolve() === true) {
          btnMethods.onHandleJob(jobBtn)
        }
      } else {
        btnMethods.onHandleJob(jobBtn)
      }
      return
    }
    // 审批前回调函数
    let resolve = data.currJob.resolve
    if (resolve) {
      methods.timeoutLoading()
      if (resolve() === true) {
        btnMethods.onHandleJob(jobBtn)
      }
      return
    }

    // 审批前保存各页面回调函数
    let resolveSaves = data.currJob.resolveSaves
    if (isAudit && !validateNull(resolveSaves)) {
      for (const resolveSave of resolveSaves) {
        if (resolveSave) {
          methods.timeoutLoading()
          await resolveSave()
        }
      }
    }

    // 判断页面是否保存
    let hiJob = data.currJob.hiJob
    let elTabs = data.currJob.elTabs.filter((f) => f.isFormEdit !== "0" && !hiJob)
    if (elTabs) {
      let find = elTabs.find((f) => f.isSave !== true)
      if (find) {
        proxy.$modal.msg(find.formName + " 未保存")
      } else {
        methods.timeoutLoading()
        btnMethods.onHandleJob(jobBtn)
      }
      return
    }
    methods.timeoutLoading()
    btnMethods.onHandleJob(jobBtn)
  },
  handleJobBtns(jobBtn) {
    // TODO 可在此处做全局权限判断
    let value = DIC_PROP.JOB_BTNS[13].value
    let next = userStore.permissions.some((v) => v === "jsonflow_runjob_invalid")
    if (next) {
      return (jobBtn === value && next) || jobBtn !== value
    } else {
      return jobBtn !== value
    }
  },

  getJobBtnName(jobBtn) {
    const isStart = methods.isStartNode(jobBtn)
    return isStart ? "提交" : getJobBtnName(jobBtn)
  },

  // 判断是否为开始节点
  isStartNode(jobBtn) {
    const isStart =
      jobBtn === DIC_PROP.JOB_BTNS[0].value && data.currJob.runNodeVO.nodeType === "-1"
    return isStart
  },

  timeoutLoading() {
    data.loading = true
    setTimeout(() => {
      // 防重复提交
      data.loading = false
    }, 3 * 1000)
  },
}

//非同意按钮与退回按钮执行保存
async function toResolveSaves() {
  let resolveSaves = data.currJob.resolveSaves
  if (!validateNull(resolveSaves)) {
    for (const resolveSave of resolveSaves) {
      if (resolveSave) {
        methods.timeoutLoading()
        await resolveSave()
      }
    }
  }
}

function getJobBtnStyle(jobBtn, key) {
  let item = DIC_PROP.JOB_BTNS.find((f) => f.value === jobBtn)
  return item[key]
}

onMounted(() => {
  methods.initJobData()
})
</script>

<style lang="scss">
@use "../../../flow/components/style/flow-drawer.scss" as *;

.dialog__popper {
  max-width: 400px;
}
</style>

<style lang="scss" scoped>
.create-info {
  @apply bg-#fff rounded-10px p-18px mb-10px;
  .create-info-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #0c1433;
    img {
      width: 15px;
      flex-shrink: 0;
      margin-right: 5px;
    }
  }
  .create-info-desc {
    font-size: 14px;
    margin-top: 12px;
    span {
      margin-right: 40px;
      label {
        // font-weight: 500;
      }
    }
  }
}
</style>
