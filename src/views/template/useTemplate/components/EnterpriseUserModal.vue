<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择企业"
    width="400px"
    :before-close="handleClose"
    append-to-body
  >
    <!-- 列表区域 -->
    <div class="list-wrapper">
      <div class="search-box">
        <el-input
          v-model="queryParams.keyWord"
          placeholder="请输入企业名称/联系人/联系人手机号"
          maxlength="100"
          clearable
          @input="handleQuery"
        >
          <template #suffix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <img src="@/assets/images/icon/add-user.png" @click="handleAdd" />
      </div>
      <div
        class="list-box"
        v-infinite-scroll="load"
        :infinite-scroll-immediate="false"
        v-loading="loading"
      >
        <template v-if="!loading && !list.length">
          <CustomEmpty />
        </template>
        <div
          class="list-row"
          v-for="item in list"
          :key="item.id"
          @click="onSelect(item)"
          :class="{ active: isActive(item) }"
        >
          <svg-icon name="local-com"></svg-icon>
          <div class="row-info">
            <div>{{ item.enterpriseName }}</div>
            <div class="color-#6B6B6B">{{ item.operatorName }}</div>
            <div class="color-#6B6B6B">{{ replacePhone(item.phoneNumber) }}</div>
          </div>
          <div class="item-checked">
            <img v-if="isActive(item)" src="@/assets/images/icon/success.png" />
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm"> 确定 </el-button>
    </template>

    <EditCompanyContactModal ref="editCompanyRef" @refresh="handleQuery" />
  </el-dialog>
</template>

<script setup>
import { getEnterpriseUserListAPI } from "@/api/ess/baseSet/contacts"
import { debounce } from "lodash-es"
import { replacePhone } from "@/utils/index"

const EditCompanyContactModal = defineAsyncComponent(() =>
  import("@/views/baseSet/contactManage/components/EditCompanyContactModal.vue")
)
const props = defineProps({
  // 是否多选
  multiple: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
})
const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)
const queryParams = reactive({
  keyWord: "",
  limit: 1000,
  page: 1,
})
const loading = ref(true)
const list = ref([])
const selected = ref([])
const total = ref(0)

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal.length) {
      selected.value = [...newVal]
    }
  },
  { deep: true, immediate: true }
)

async function getList() {
  loading.value = true
  const res = await getEnterpriseUserListAPI(queryParams)
  loading.value = false
  list.value = [...list.value, ...res.object.records]
  total.value = res.object.total
}
function load() {
  if (total.value > list.value.length) {
    queryParams.page++
    getList()
  }
}
const handleQuery = debounce(function () {
  queryParams.page = 1
  list.value = []
  getList()
}, 500) // 500毫秒后触发

// ------------------表单操作------------------
const editCompanyRef = ref(null)
function handleAdd() {
  editCompanyRef.value.open()
}
// ------------------表单操作end------------------

// 选择数据
function onSelect(item) {
  if (props.multiple) {
    const index = selected.value.findIndex((i) => i.id === item.id)
    index >= 0 ? selected.value.splice(index, 1) : selected.value.push(item)
  } else {
    selected.value = [item]
  }
}
function open(config = {isFlag:false,templateUsers:[]}) {
  const { isFlag,templateUsers} = config;
  
  if(isFlag){
    selected.value = templateUsers
  }
  dialogVisible.value = true
}

function handleClose() {
  dialogVisible.value = false
  // selected.value = []
}

const emit = defineEmits(["select"])
// 确定
function handleConfirm() {
  if (!selected.value.length) {
    proxy.$modal.msgError("请先选择企业！")
    return
  }
  emit("select", selected.value)
  handleClose()
}

function isActive(row) {
  return selected.value.some((item) => item.id === row.id)
}

function clearSelected() {
  selected.value = []
}

getList()
defineExpose({
  open,
  clearSelected
})
</script>

<style scoped lang="scss">
.search-box {
  @apply flex items-center justify-between;
  img {
    @apply cursor-pointer w-30px h-30px flex-shrink-0 ml-10px;
  }
}

.list-row {
  @apply cursor-pointer flex bg-#fafafa rounded-4px py-10px px-15px text-14px mt-12px relative;
  padding-right: 50px;
  .svg-icon {
    color: #e3e3e3;
    @apply flex-shrink-0 h16px! w16px! mt-5px mr-10px text-18px;
  }
  .row-info {
    flex: auto;
    div + div {
      margin-top: 5px;
    }
  }
  .item-checked {
    margin-left: 20px;
    border: 1px solid #cbcfd6;
    @apply abs-y-center left-85% w-18px h-18px flex-shrink-0 rounded-50%;
    img {
      @apply w-18px h-18px abs-center;
    }
  }
  &.active,
  &:hover {
    @apply bg-#F3FAF7;
    .svg-icon {
      color: #7fccb3;
    }
  }
}

.list-box {
  height: 420px;
  overflow-y: auto;
}
</style>
