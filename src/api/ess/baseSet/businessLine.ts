import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询业务线列表 带分页
export function businessLinePage(data: Object) {
	return request({
		url: essPrefix + "/businessLine/listPage",
		method: "post",
		data,
	})
}

// 查询业务线管理配置列表
export function getBusinessLineList() {
	return request({
		url: essPrefix + "/businessLine/list",
		method: "get",
	})
}

// 新增业务线
export function addBusinessLine(data: Object) {
	return request({
		url: essPrefix + "/businessLine/add",
		method: "post",
		data,
	})
}

// 修改业务线
export function updateBusinessLine(data: Object) {
	return request({
		url: essPrefix + "/businessLine/edit",
		method: "post",
		data,
	})
}

// 删除业务线
export function delBusinessLine(ids: string) {
	return request({
		url: essPrefix + "/businessLine/" + ids,
		method: "delete",
	})
}

// 查询业务线管理配置详细
export function getBusinessLineInfo(id) {
	return request({
		url: essPrefix + "/businessLine/" + id,
		method: "get",
	})
}
