<!--
 * @Author: ljn
 * @Date: 2025-02-27 15:12:13
 * @LastEditors: ljn
 * @LastEditTime: 2025-05-26 14:49:27
 * @Description: 暂时不用
-->

<template>
  <el-dialog
    v-model="visible"
    title="选择用户"
    width="780px"
    :before-close="handleClose"
    append-to-body
  >
    <UserTreeTransfer
      ref="userPickerRef"
      :select-types="['user', 'role']"
      v-model:users="userList"
    />
    <template #footer>
      <el-button @click="handleClose"> 取消 </el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 确定 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import UserTreeTransfer from "@/views/components/UserTreeTransfer/index.vue"

const visible = defineModel("visible")
const loading = ref(false)
const userTreeTransferRef = ref(null)

const userList = ref([])

const reset = () => {
  userList.value = []
  userTreeTransferRef.value?.reset()
  visible.value = false
}

const open = (data) => {
  visible.value = true
  userList.value = data
}

function handleClose() {
  reset()
}

const emit = defineEmits(["confirm"])
function submitForm() {
  userList.value = userTreeTransferRef.value.getCheckedNodes()
  emit("confirm", userList.value)
  handleClose()
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
