<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="90px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item label="流程ID" prop="defFlowId">
						<el-select
							v-model="form.defFlowId"
							placeholder="请选择流程ID"
							clearable
							filterable
							@change="defFlowIdChange"
						>
							<el-option
								v-for="(item, index) in dicData.defFlowId"
								:key="index"
								:label="item.flowName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="节点ID" prop="flowNodeId">
						<el-select
							v-model="form.flowNodeId"
							placeholder="请选择节点ID"
							clearable
							filterable
							@change="cascadeChange('flowNodeId', ['nodeJobId'])"
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="监听类" prop="clazz">
						<el-input v-model="form.clazz" placeholder="请输入监听类" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="HTTP地址" prop="httpUrl">
						<el-input v-model="form.httpUrl" placeholder="请输入HTTP地址" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="HTTP方法" prop="httpMethod">
						<el-select v-model="form.httpMethod" placeholder="请选择HTTP方法">
							<el-option
								v-for="(item, index) in DIC_PROP.HTTP_METHODS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="方法" prop="methods">
						<el-select
							v-model="form.methods"
							placeholder="请选择方法"
							clearable
							filterable
							multiple
						>
							<el-option
								v-for="(item, index) in DIC_PROP.FLOW_METHODS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="值类型" prop="valType">
						<el-select
							v-model="form.valType"
							placeholder="请选择值类型"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.VAL_TYPE.slice(2, 5)"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="变量键值" prop="varKeyVal">
						<el-input v-model="form.varKeyVal" placeholder="请输入变量键值" clearable />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="操作符" prop="operator">
						<el-select
							v-model="form.operator"
							placeholder="请选择操作符"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.OPERATOR"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="变量值" prop="varVal">
						<el-input v-model="form.varVal" placeholder="请输入变量值" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="类型" prop="type">
						<el-select
							v-model="form.type"
							placeholder="请选择类型"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.CLAZZ_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="排序" prop="sort">
						<el-input-number
							v-model="form.sort"
							:min="1"
							:max="1000"
							placeholder="请输入排序"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="备注" prop="remark">
						<el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="FlowClazzDialog">

import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/jsonflow/flow-clazz"
import { rule } from "@/utils/validate"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
import { notifyLeft } from "@/flow"
const emit = defineEmits(["refresh"])

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "defFlowId" })
const onCascade = onCascadeChange(cascadeDic, { key: "defFlowId", cascades: ["flowNodeId"] })
onMounted(() => {
	onLoad(dicData)
})
function cascadeChange(key, cascades) {
	onCascade(form, { key: key, cascades: cascades })
}
const defFlowIdChange = (newVal: any) => {
	onCascade(form, { key: "defFlowId", cascades: ["flowNodeId"] })
	if (newVal) {
		const defFlow = dicData.defFlowId.find(f => f.id === newVal)
		form.flowKey = defFlow.flowKey
	}
	else {
		form.flowKey = null
	}
}

// 提交表单数据
const form = reactive({
	sort: 0,
	defFlowId: "",
	flowNodeId: "",
	clazz: "",
	httpUrl: "",
	httpMethod: "GET",
	methods: "",
	valType: "",
	varKeyVal: "",
	operator: "",
	varVal: "",
	remark: "",
	type: "",
})

// 定义校验规则
const dataRules = ref({
	defFlowId: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
	methods: [{ required: true, message: "方法名称不能为空", trigger: "blur" }],
	type: [{ required: true, message: "事件类型不能为空", trigger: "blur" }],
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

	if (type === "add") {
		title.value = "新增"
	}
	else if (type === "edit") {
		title.value = "编辑"
	}
	else if (type === "view") {
		title.value = "查看"
	}

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取FlowClazz信息
	if (id) {
		form.id = id
		getFlowClazzData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	if (!form.clazz && !form.httpUrl) {
		notifyLeft("请填写 监听类 或者 Http请求地址", "warning")
		return false
	}

	try {
		loading.value = true
		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? "修改成功" : "新增成功")
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getFlowClazzData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
		onCascade(form)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
