import type { DragRule } from 'form-create-designer'
import { getNonDuplicateID } from '@/utils/index'
import { uploadBaseUrl } from "@/config/constant"

// Create a map of rule creators
const ruleCreators: Record<string, () => DragRule> = {
  DeptPicker: () => ({
    menu: 'biz',
    icon: 'icon-group',
    label: '部门',
    name: 'DeptPicker',
    mask: true,
    rule() {
      return {
        type: 'DeptPicker',
        field: 'DeptPicker' + getNonDuplicateID(),
        title: '部门',
        $required: true,
        props: {
          multiple: false
        }
      }
    },
    props() {
      return [
        {
          type: 'switch',
          title: '多选',
          field: 'multiple',
          value: false
        },
        {
          type: 'switch',
          title: '禁用',
          field: 'disabled',
          value: false
        }
      ]
    }
  }),
  FormNameInput: () => ({
    menu: 'biz',
    icon: 'fc-icon icon-textarea',
    label: '表单名称',
    name: 'FormNameInput',
    mask: true,
    rule() {
      return {
        type: 'FormNameInput',
        field: 'formName',
        title: '表单名称',
        $required: true,
        props: {
          disabled: false,
        },
      };
    },
    props() {
      return [
        {
          type: 'switch',
          title: '禁用',
          field: 'disabled',
          value: false,
        }
      ];
    },
  }),
  // TagShowList: () => ({
  //   menu: 'biz',
  //   icon: 'fc-icon icon-tag',
  //   label: '可见范围',
  //   name: 'TagShowList',
  //   mask: true,
  //   rule() {
  //     return {
  //       type: 'TagShowList',
  //       field: 'userRefs',
  //       title: '可见范围',
  //     };
  //   },
  //   props() {
  //     return [
  //       {
  //         type: 'number',
  //         title: '格子大小',
  //         field: 'itemSecondarySize',
  //         value: 100,
  //         props: {
  //           min: 1,
  //           max: 500
  //         }
  //       },
  //       {
  //         type: 'number',
  //         title: '每行数量',
  //         field: 'gridItems',
  //         value: 4,
  //         props: {
  //           min: 1,
  //           max: 20
  //         }
  //       },
  //     ];
  //   },
  // }),
  FlowNameInput: () => ({
    menu: 'biz',
    icon: 'fc-icon icon-textarea',
    label: '流程名称',
    name: 'FlowNameInput',
    mask: true,
    rule() {
      return {
        type: 'FlowNameInput',
        field: 'flowName',
        title: '流程名称',
        $required: true,
        props: {
          disabled: false,
        },
      };
    },
    props() {
      return [
        {
          type: 'switch',
          title: '禁用',
          field: 'disabled',
          value: false,
        }
      ];
    },
  }),
  FormCodeInput: () => ({
    menu: 'biz',
    icon: 'fc-icon icon-textarea',
    label: '流程CODE',
    name: 'FormCodeInput',
    mask: true,
    rule() {
      return {
        type: 'FormCodeInput',
        field: 'code',
        title: '流程CODE',
        $required: true,
        props: {
          disabled: false,
        },
      };
    },
    props() {
      return [
        {
          type: 'switch',
          title: '禁用',
          field: 'disabled',
          value: false,
        }
      ];
    },
  }),
  fileUpload: () => ({
    menu: 'biz',
    icon: 'icon-folder',
    label: '上传',
    name: 'FileUpload' + getNonDuplicateID(),
    mask: true,
    rule() {
      return {
        type: 'FileUpload',
        field: 'FileUpload',
        title: '文件上传',
        $required: true,
        props: {}
      }
    },
    props() {
      return [
        {
          type: 'input',
          title: '上传地址',
          field: 'uploadFileUrl',
          value: uploadBaseUrl
        },
        {
          type: 'input',
          title: '按钮名称',
          field: 'btnText',
          value: '选取文件'
        },
        {
          type: 'switch',
          title: '拖拽上传',
          field: 'uploadDragger',
          value: false
        },
        {
          type: 'number',
          title: '数量限制',
          field: 'limit',
          value: 1,
          props: {
            min: 1,
            max: 100
          }
        },
        {
          type: 'number',
          title: '大小限制(MB)',
          field: 'fileSize',
          value: 5,
          props: {
            min: 1,
            max: 500
          }
        },
        // {
        // 	type: 'select',
        // 	title: '上传类型',
        // 	field: 'type',
        // 	value: 'default',
        // 	options: [
        // 		{ label: '默认', value: 'default' },
        // 		{ label: '简单', value: 'simple' }
        // 	]
        // },
        {
          type: 'input',
          title: '上传目录',
          field: 'dir',
          value: ''
        },
        // {
        // 	type: 'switch',
        // 	title: '自动上传',
        // 	field: 'autoUpload',
        // 	value: true
        // },
        {
          type: 'switch',
          title: '显示提示',
          field: 'isShowTip',
          value: true
        },
        {
          type: 'switch',
          title: '高亮提示',
          field: 'highlightTip',
          value: true
        },
        {
          type: 'switch',
          title: '禁用',
          field: 'disabled',
          value: false
        },
        {
          type: 'select',
          title: '已上传展示类型',
          field: 'showListType',
          value: 'list',
          options: [
            { label: '列表', value: 'list' },
            { label: '卡片', value: 'card' },
          ]
        },
        {
          type: 'select',
          title: '文件类型',
          field: 'fileType',
          value: ['png', 'jpg', 'jpeg', 'doc', 'xls', 'ppt', 'txt', 'pdf', 'docx', 'xlsx', 'pptx'],
          props: {
            multiple: true
          },
          options: [
            { label: 'PNG', value: 'png' },
            { label: 'JPG', value: 'jpg' },
            { label: 'JPEG', value: 'jpeg' },
            { label: 'DOC', value: 'doc' },
            { label: 'DOCX', value: 'docx' },
            { label: 'XLS', value: 'xls' },
            { label: 'XLSX', value: 'xlsx' },
            { label: 'PPT', value: 'ppt' },
            { label: 'PPTX', value: 'pptx' },
            { label: 'PDF', value: 'pdf' },
            { label: 'TXT', value: 'txt' }
          ]
        }
      ]
    }
  }),
  // UserSelect: () => ({
  // 	menu: 'biz',
  // 	icon: 'fc-icon icon-select',
  // 	label: '人员',
  // 	name: 'UserSelect',
  // 	mask: true,
  // 	rule() {
  // 		return {
  // 			type: 'UserSelect',
  // 			field: 'UserPicker' + getNonDuplicateID(),
  // 			title: '人员',
  // 			$required: true,
  // 			props: {
  // 				multiple: false
  // 			}
  // 		}
  // 	},
  // 	props() {
  // 		return [
  // 			{
  // 				type: 'switch',
  // 				title: '多选',
  // 				field: 'multiple',
  // 				value: false
  // 			},
  // 			{
  // 				type: 'switch',
  // 				title: '禁用',
  // 				field: 'disabled',
  // 				value: false
  // 			}
  // 		]
  // 	}
  // }),
  UserPickerBtn: () => ({
    menu: 'biz',
    icon: 'fc-icon icon-button',
    label: '人员',
    name: 'UserPickerBtn',
    mask: true,
    rule() {
      return {
        type: 'UserPickerBtn',
        field: 'UserPickerBtn' + getNonDuplicateID(),
        title: '人员',
        $required: true,
        props: {}
      }
    },
    props() {
      return [
        {
          type: 'switch',
          title: '单选',
          field: 'isOnlyOne',
          value: true
        },
        {
          type: 'switch',
          title: 'Ess用户',
          field: 'izEssUser',
          value: false
        },
      ]
    }
  }),
  // RolePicker: () => ({
  // 	menu: 'biz',
  // 	// icon: ' icon-gerenzhongxin',
  // 	label: '角色',
  // 	name: 'RolePicker',
  // 	mask: true,
  // 	rule() {
  // 		return {
  // 			type: 'RolePicker',
  // 			field: 'RolePicker' + getNonDuplicateID(),
  // 			title: '角色',
  // 			$required: true,
  // 			props: {
  // 				multiple: false
  // 			}
  // 		}
  // 	},
  // 	props() {
  // 		return [
  // 			{
  // 				type: 'switch',
  // 				title: '多选',
  // 				field: 'multiple',
  // 				value: false
  // 			},
  // 			{
  // 				type: 'switch',
  // 				title: '禁用',
  // 				field: 'disabled',
  // 				value: false
  // 			}
  // 		]
  // 	}
  // }),
  // PostPicker: () => ({
  // 	menu: 'biz',
  // 	// icon: 'iconfont icon-shuxingtu',
  // 	label: '岗位',
  // 	name: 'PostPicker',
  // 	mask: true,
  // 	rule() {
  // 		return {
  // 			type: 'PostPicker',
  // 			field: 'PostPicker' + getNonDuplicateID(),
  // 			title: '岗位',
  // 			$required: true,
  // 			props: {
  // 				multiple: false
  // 			}
  // 		}
  // 	},
  // 	props() {
  // 		return [
  // 			{
  // 				type: 'switch',
  // 				title: '多选',
  // 				field: 'multiple',
  // 				value: false
  // 			},
  // 			{
  // 				type: 'switch',
  // 				title: '禁用',
  // 				field: 'disabled',
  // 				value: false
  // 			}
  // 		]
  // 	}
  // }),

  // PhoneInput: () => ({
  // 	menu: 'biz',
  // 	// icon: 'iconfont icon-dianhua',
  // 	label: '手机号',
  // 	name: 'PhoneInput',
  // 	mask: true,
  // 	rule() {
  // 		return {
  // 			type: 'PhoneInput',
  // 			field: 'PhoneInput' + getNonDuplicateID(),
  // 			title: '手机号',
  // 			$required: true,
  // 			props: {
  // 				disabled: false
  // 			}
  // 		}
  // 	},
  // 	props() {
  // 		return [
  // 			{
  // 				type: 'switch',
  // 				title: '禁用',
  // 				field: 'disabled',
  // 				value: false
  // 			}
  // 		]
  // 	}
  // }),
  // IdCartInput: () => ({
  // 	menu: 'biz',
  // 	// icon: 'iconfont icon-tupianyulan',
  // 	label: '身份证号',
  // 	name: 'IdCartInput',
  // 	mask: true,
  // 	rule() {
  // 		return {
  // 			type: 'IdCartInput',
  // 			field: 'IdCartInput' + getNonDuplicateID(),
  // 			title: '身份证号',
  // 			$required: true,
  // 			props: {
  // 				disabled: false
  // 			}
  // 		}
  // 	},
  // 	props() {
  // 		return [
  // 			{
  // 				type: 'switch',
  // 				title: '禁用',
  // 				field: 'disabled',
  // 				value: false
  // 			}
  // 		]
  // 	}
  // }),
  // EmailInput: () => ({
  // 	menu: 'biz',
  // 	// icon: 'iconfont icon-xingqiu',
  // 	label: '邮箱',
  // 	name: 'EmailInput',
  // 	mask: true,
  // 	rule() {
  // 		return {
  // 			type: 'EmailInput',
  // 			field: 'EmailInput' + getNonDuplicateID(),
  // 			title: '邮箱',
  // 			$required: true,
  // 			props: {
  // 				disabled: false
  // 			}
  // 		}
  // 	},
  // 	props() {
  // 		return [
  // 			{
  // 				type: 'switch',
  // 				title: '禁用',
  // 				field: 'disabled',
  // 				value: false
  // 			}
  // 		]
  // 	}
  // }),
  // Sign: () => ({
  // 	menu: 'biz',
  // 	// icon: 'icon-edit',
  // 	label: '签名',
  // 	name: 'Sign',
  // 	mask: true,
  // 	rule() {
  // 		return {
  // 			type: 'Sign',
  // 			field: 'Sign' + getNonDuplicateID(),
  // 			title: '签名',
  // 			$required: true,
  // 			props: {
  // 				bgColor: '#F6F8FA',
  // 				isClearBgColor: false
  // 			}
  // 		}
  // 	},
  // 	props() {
  // 		return [
  // 			{
  // 				type: 'number',
  // 				title: '宽度',
  // 				field: 'width',
  // 				value: 300,
  // 				props: {
  // 					min: 100,
  // 					max: 1000
  // 				}
  // 			},
  // 			{
  // 				type: 'number',
  // 				title: '高度',
  // 				field: 'height',
  // 				value: 150,
  // 				props: {
  // 					min: 50,
  // 					max: 500
  // 				}
  // 			},
  // 			{
  // 				type: 'number',
  // 				title: '线宽',
  // 				field: 'lineWidth',
  // 				value: 4,
  // 				props: {
  // 					min: 1,
  // 					max: 20
  // 				}
  // 			},
  // 			{
  // 				type: 'colorPicker',
  // 				title: '线条颜色',
  // 				field: 'lineColor',
  // 				value: '#000000'
  // 			},
  // 			{
  // 				type: 'colorPicker',
  // 				title: '背景颜色',
  // 				field: 'bgColor',
  // 				value: '#F6F8FA'
  // 			},
  // 			{
  // 				type: 'switch',
  // 				title: '裁剪空白',
  // 				field: 'isCrop',
  // 				value: false
  // 			},
  // 			{
  // 				type: 'switch',
  // 				title: '清除背景',
  // 				field: 'isClearBgColor',
  // 				value: false
  // 			}
  // 		]
  // 	}
  // })
}

// Export all rules as an array
export const rules: any[] = Object.values(ruleCreators).map(creator => creator())
