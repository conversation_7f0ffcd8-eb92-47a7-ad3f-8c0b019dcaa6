export default {
	jfI18n: {
		temp: "暂存",
		invalid: "作废",
		pub: "发布",
		oneBtnDesign: "一键设计",
		viewPageBtn: "查看表单",
		onceMoreDesign: "再次设计",
		designFlow: "设计流程图",
		viewFlow: "查看流程图",
		print: "打印",
		printTemplate: "打印设计",
		submit: "提交",
		viewForm: "查看表单",
		initHandover: "发起交接",
		initialBtn: "发起",
		batchBtn: "批量保存",
		saveBtn: "保存",
		cancelBtn: "取消",
		updateFlow: "修改工单",
		rejectBtn: "驳回选中项",
		receiveBtn: "确认接收",
		recallBtn: "撤回",
		resetBtn: "重发",
		operate: "操作",
		more: "更多",
	},
	jfAttr: {
		paramValType: "参数值类型",
		varKeyVal: "表单字段或固定值",
    sysVarKeyVal: '系统字段或固定值',
		subVarKeyVal: "子流程表单字段",
		subVarKeyVal2: "子流程表单字段或固定值",
		requestHeader: "请求头",
		requestParam: "请求参数",
		returnParam: "返回值",
	},
	jfForm: {
		urgentTask: "催办任务",
		urgentSubFlow: "催办子流程",
		reduceSign: "减签",
	},
}
