<template>
	<el-container>
		<el-header style="background: white">
			<tabs-design-header
				:curr-flow-form="props.currFlowForm"
				@handle-design-tabs="handleDesignTabs"
				@sync-curr-flow-form="syncCurrFlowForm"
				@publish="publishTabs"
			/>
		</el-header>
		<div id="initClientHeight" style="min-width: 980px;">
			<tabs-setting v-show="props.currFlowForm.active === 'tabsSetting'" ref="tabsSetting" :curr-flow-form="props.currFlowForm" />
			<form-design v-show="showFormDesign()" ref="formDesign" :curr-flow-form="props.currFlowForm" />
			<form-def-perm v-show="showFormDefPerm()" ref="formDefPerm" :curr-flow-form="props.currFlowForm" />
		</div>
	</el-container>
</template>

<script setup lang="ts" name="TabsFormDesign">
import { useI18n } from "vue-i18n"
import { formWidgetDesignHeight } from "@/flow/utils"
import { DIC_PROP } from "../../support/dict-prop"

const { proxy } = getCurrentInstance()
// 引入组件
const TabsDesignHeader = defineAsyncComponent(() => import("./header.vue"))
const FormDesign = defineAsyncComponent(() => import("../form-create/designer.vue"))
const TabsSetting = defineAsyncComponent(() => import("./setting.vue"))
const FormDefPerm = defineAsyncComponent(() => import("@/views/jsonflow/form-option/form-def-perm.vue"))

const $emit = defineEmits(["handleDesignTabs"])
const props = defineProps({
	currFlowForm: {
		type: Object,
		default: null,
	},
})

function initClientHeight() {
	nextTick(() => {
		let browserHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
		formWidgetDesignHeight(browserHeight)
	})
}

function handleDesignTabs(bool: boolean) {
	$emit("handleDesignTabs", bool)
}

function showFormDesign() {
	return props.currFlowForm.active === "formDesign" && props.currFlowForm.type !== DIC_PROP.FORM_TYPE[1].value
}
function showFormDefPerm() {
	return props.currFlowForm.active === "formDesign" && props.currFlowForm.type === DIC_PROP.FORM_TYPE[1].value
}

async function syncCurrFlowForm(callback) {
	let preSave = await proxy.$refs.tabsSetting.validatePreSave()
	callback(preSave)
}

async function syncCurrFormInfo(callback?) {
	if (props.currFlowForm.type !== DIC_PROP.FORM_TYPE[1].value) {
		proxy.$refs.formDefPerm.syncCurrFormInfo(true)
		// 保存表单信息
		return await proxy.$refs.formDesign.handleSubmit(() => {
			if (callback) { callback() }
		}, true)
	}
	else {
		proxy.$refs.formDefPerm.syncCurrFormInfo(false)
		props.currFlowForm.formInfo = null
		return true
	}
}

async function publishTabs(menu, callback, status) {
	// 保存表单信息
	let preSave = await syncCurrFormInfo(callback)
	if (!preSave) { return }
	// 校验表单设计
	let otherSave = await proxy.$refs.tabsSetting.validateOtherSave()
	if (!otherSave) { return }
	// 保存字段信息
	if (props.currFlowForm.type === DIC_PROP.FORM_TYPE[1].value) {
		await proxy.$refs.formDefPerm.handleSubmit(() => {
			if (callback) { callback() }
		})
	}
	// 保存页面配置
	proxy.$refs.tabsSetting.handleSubmit(() => {
		if (callback) { callback() }
	}, status)
}

// 监听双向绑定
watch(
	() => props.currFlowForm.id,
	val => {
		initClientHeight()
	},
)

onMounted(() => {
	initClientHeight()
})

</script>

<style lang="scss" scoped>

</style>
