<template>
  <div class="layout-padding">
    <div>
      <!-- 动态组件 -->
			<dynamic-link v-if="data.currElTab.active" :currJob="props.currJob" :currElTab="data.currElTab"></dynamic-link>

      <template v-if="operType !== 'view'">
        <div style="text-align: right;">
          <span class="dialog-footer">
            <template v-if="props.currJob.status !== DIC_PROP.ORDER_STATUS[0].value">
              <el-button type="primary" @click="methods.handleForm('onSubmit')" :disabled="loading">
                发起
              </el-button>
              <el-button @click="methods.cancelButton">
                取消
              </el-button>
              <el-button type="primary" @click="methods.handleForm('onTemp')" :disabled="loading">
                暂存
              </el-button>
            </template>
            <template v-else>
              <el-button type="primary" @click="methods.handleForm('onSubmit')" :disabled="loading">
                修改
              </el-button>
              <el-button type="primary" @click="printForm" v-if="props.currJob.printInfo">
                打印
              </el-button>
            </template>
          </span>
        </div>
      </template>
    </div>

    <!-- 打印表单 -->
    <el-dialog
      v-model="data.showTinymceView"
      top="20px"
      width="700px"
      :title="data.tinymceTitle"
      append-to-body
      @close="closePrint"
    >
      <tinymce-view v-if="data.showTinymceView" :curr-flow-form="props.currJob" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="CustomForm">
const { proxy } = getCurrentInstance()

// 引入组件
const DynamicLink = defineAsyncComponent(() => import('../handle-job/dynamic-link.vue'))
const TinymceView = defineAsyncComponent(() => import('@/flow/components/tinymce/TinymceView.vue'))

const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref('')

const props = defineProps({
  currJob: {
    type: Object,
    default: null
  }
})

const data = reactive({
  currElTab: { active: null },
  showTinymceView: false,
  tinymceTitle: null
})
const $emit = defineEmits(['onHandleForm'])

const methods = {
  // 初始化数据
  initJobData() {
    methods.openDialog(props.currJob.operType)
    data.currElTab = props.currJob.currElTab
    console.log('props.currJob.currElTab2', props)
    data.currElTab.active = data.currElTab.id
  },
  // 打开弹窗
  openDialog(type: string) {
    operType.value = type

    const btnMap = {
      add: '新增1',
      edit: '修改',
      view: '查看',
      copy: '复制'
    }
    title.value = btnMap[type]
  },
  // 提交表单
  async handleForm(method) {
    let exist = props.currJob[method]
    if (exist) {
      let res = await exist()
      if (res === true) {
        methods.cancelButton()
      }
    } else {
      proxy.$modal.msg('正在加载, 请稍等')
    }
  },
  cancelButton() {
    $emit('onHandleForm', false)
  }
}

function printForm() {
  props.currJob.resolvePrintForm()
  data.tinymceTitle = props.currJob.formName
  data.showTinymceView = true
}

function closePrint() {
  props.currJob.resolveClosePrint()
}

async function getFormData() {
  return await props.currJob.getFormData()
}

// 暴露变量
defineExpose({
  getFormData
})

// 监听双向绑定
watch(
  () => props.currJob.id,
  (val) => {
    methods.initJobData()
  }
)

onMounted(() => {
  methods.initJobData()
})
</script>
