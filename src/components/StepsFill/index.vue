<template>
  <div class="steps-fill">
    <div
      class="steps-item"
      :class="{
        'steps-finish': currentStep > index,
        'steps-process': currentStep === index,
        'steps-wait': currentStep < index,
        pointer: currentStep > index && !disabled,
      }"
      :style="{ 'z-index': 100 - index }"
      v-for="(item, index) in steps"
      :key="item.value"
      @click="handleClick(item, index)"
    >
      {{ item.title }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Step {
  title?: string // 标题
  value?: string // 步骤值
}
interface Props {
  disabled?: boolean // 是否禁用步骤条
  steps?: Step[] // 步骤数组
  width?: number | string // 步骤条总宽度，单位 px
  current?: number // (v-model) 当前选中的步骤，设置 v-model 后，Steps 变为可点击状态。从 1 开始计数
}
const props = withDefaults(defineProps<Props>(), {
  steps: () => [],
  width: "auto",
  disabled: false,
})
const current = defineModel("current", {
  type: Number,
  default: 0,
})
const totalSteps = computed(() => {
  // 步骤总数
  return props.steps.length
})
const currentStep = computed(() => {
  if (current.value === 0) {
    return 0
  } else if (current.value === totalSteps.value - 1) {
    return totalSteps.value
  } else {
    return current.value
  }
})
const emit = defineEmits(["change"])
const handleClick = (item: Step, index: number) => {
  if (props.disabled) return
  if (currentStep.value > index) {
    current.value = index
    emit("change", item, index)
  }
}
</script>

<style scoped lang="scss">
.steps-fill {
  display: flex;
}

$arrowHeight: 36px;
$borderWidth: 18px;
$finishColor: #24a87e;
$processColor: #fb9547;
.steps-item {
  background: #e9e9eb;
  padding: 0 8px 0 18px;
  height: $arrowHeight;
  line-height: $arrowHeight;
  font-size: 14px;
  min-width: 180px;
  text-align: center;
  margin-right: 12px;
  position: relative;
  color: #000;
  box-sizing: border-box;
  &:first-child {
    border-radius: 30px 0 0 30px;
    padding: 0 8px 0 16px;
    background: $processColor;
    color: #fff;
    z-index: 11;
    &::after {
      content: "";
      position: absolute;
      top: 0;
      right: calc(-1 * $borderWidth + 1px);
      width: 0;
      height: 0;
      border-top: $borderWidth solid transparent;
      border-bottom: $borderWidth solid transparent;
      border-left: $borderWidth solid $processColor;
    }
  }

  &:not(:first-child) {
    /* 左箭头 */
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 0;
      height: 0;
      border-top: 19px solid transparent;
      border-bottom: 19px solid transparent;
      border-left: 19px solid #f7f8f9; /* 镂空效果用背景色 */
    }

    /* 右箭头 */
    &::after {
      content: "";
      position: absolute;
      right: -18px;
      top: 0;
      width: 0;
      height: 0;
      border-top: 17px solid transparent;
      border-bottom: 19px solid transparent;
      border-left: 19px solid #e9e9eb;
    }
  }
  &.steps-finish {
    background: $finishColor;
    color: #fff;
    &::after {
      border-left-color: $finishColor;
    }
  }
  &.steps-process {
    background: $processColor;
    color: #fff;
    &::after {
      border-left-color: $processColor;
    }
  }
}
</style>
