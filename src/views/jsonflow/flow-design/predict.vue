<template>
  <div>
    <!-- 发起申请 -->

    <!-- <el-tabs
      class="flow-predict_display_center"
      v-model="data.currActive"
      @tab-click="handleTabClick"
    >
      <el-tab-pane label="① 录入表单" name="form"></el-tab-pane>
      <el-tab-pane label="② 流程预测" name="flow"></el-tab-pane>
      <el-tab-pane label="③ 审批预测" name="comment"></el-tab-pane>
    </el-tabs> -->

    <slot :currActive="data.currActive"></slot>

    <div v-show="data.currActive === 'flow'">
      <div class="flow-predict_display_center">
        <el-button
          type="primary"
          round
          icon="VideoPlay"
          @click="submitPredict(false)"
          v-if="!data.isPredicted || !data.predictFlow.flowInstId"
          :disabled="data.btnLoading"
        >
          开始预测
        </el-button>
        <el-button
          type="primary"
          round
          icon="Refresh"
          @click="submitPredict(true)"
          v-if="data.isPredicted && data.predictFlow.flowInstId"
          :disabled="data.btnLoading"
        >
          刷新预测
        </el-button>
      </div>

      <flow-photo ref="flow" v-if="data.isPredicted" :curr-job="data.predictFlow"></flow-photo>

      <div
        v-if="!data.isPredicted && data.predictFlow.defFlowId"
        class="flow-predict_display_center"
        style="font-size: 16px; margin-top: 20px"
      >
        全流程预测进行中：流转轨迹预测、节点参与者预测、连线条件预测、审批过程预测
      </div>

      <div
        v-if="!data.predictFlow.defFlowId"
        class="flow-predict_display_center"
        style="font-size: 16px; margin-top: 20px; "
      >
        请点击《开始预测》按钮，全流程预测：流转轨迹预测、节点参与者预测、连线条件预测、审批过程预测
      </div>
    </div>

    <!-- <div
      id="predictClientHeight"
      style="min-width: 980px; overflow-y: auto;"
      v-show="data.currActive === 'comment'"
    >
      <comment
        ref="comment"
        v-if="data.isPredicted"
        v-show="data.currActive === 'comment'"
        :curr-job="data.predictFlow"
      ></comment>
    </div> -->
  </div>
</template>

<script setup lang="ts" name="JsonFlowPredict">
import { utils } from '@/flow/designer/utils/common'
import * as defFlow from '@/flow/designer/api/jsonflow'

// 引入组件
const FlowPhoto = defineAsyncComponent(() => import('@/views/jsonflow/flow-design/view.vue'))
const Comment = defineAsyncComponent(() => import('@/views/jsonflow/comment/timeline.vue'))

const { proxy } = getCurrentInstance()
const $emit = defineEmits(['handleInitiateOrder', 'cancel', 'opened'])

defineOptions({
  name: 'JsonFlowPredict',
  inheritAttrs: false
})

const props = defineProps({
  currFlowForm: {
    type: Object,
    default: {}
  },
  proxy: {
    type: Object,
    default: null
  }
})

const data = reactive({
  showInitiateOrder: false,
  predictFlow: {},
  currActive: 'form',
  isPredicted: false,
  btnLoading: false
})

function initClientHeight() {
  nextTick(() => {
    let browserHeight =
      window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
    let initClientHeight = document.getElementById('predictClientHeight')
    if (!initClientHeight) return
    initClientHeight.style.height = browserHeight - initClientHeight.offsetTop + 'px'
  })
}

function initPredictFlow(row) {
  data.currActive = 'form'
  data.predictFlow = { defFlowId: row.defFlowId, flowKey: row.flowKey }
  data.isPredicted = false
  data.btnLoading = false
}

async function handleTabClick(tab) {
  let currActive = data.currActive
  if (currActive === tab.paneName) return
  if (currActive === 'form' && tab.paneName !== 'form') {
    let formData
    try {
      formData = await props.proxy.$refs.flowInitiateRef.getFormData()
    } catch (err) {
      // 表单校验报错
    }
    if (!formData) {
      data.currActive = currActive
      return
    }
  }
  if (tab.paneName === 'flow') {
    // 定制化开发工单
    if (data.predictFlow.defFlowId) {
      data.isPredicted = true
    }
  }
  if (tab.paneName === 'comment') {
    if (!data.predictFlow.flowInstId) {
      proxy.$modal.msgWarning('请先点击第二步，完成流程预测')
      // 不生效
      data.currActive = currActive
      return
    }
  }
  data.currActive = tab.paneName
}

async function submitPredict(isRefresh) {
  data.isPredicted = false
  data.btnLoading = true
  let flowInstId = data.predictFlow.flowInstId
  // 流程预测
  let formData = await props.proxy.$refs.flowInitiateRef.getFormData()
  formData.id = utils.getId()
  formData.flowInstId = formData.id
  // 定制化开发工单新增时
  if (!formData.flowKey) {
    formData.flowKey = data.predictFlow.flowKey
  }
  await defFlow.predictFlow(formData, null).finally(() => {
    data.btnLoading = false
  })
  if (flowInstId) await delFlowInfo(flowInstId)
  data.predictFlow.flowInstId = formData.flowInstId
  data.isPredicted = true
}

async function delFlowInfo(flowInstId?, closed?) {
  let row = { id: flowInstId, isPredicted: '1' }
  await defFlow.delFlowInfo(row)
  if (!closed) return
}

async function open(row, bool) {
  initPredictFlow(row)
  data.showInitiateOrder = bool
  initClientHeight()
  await nextTick()
  $emit('opened')
}

// 暴露变量
defineExpose({
  open,
  delFlowInfo
})
</script>

<style lang="scss" scoped>
.flow-predict_display_center {
  display: flex;
  justify-content: center;
  width: 100%;
}
</style>
