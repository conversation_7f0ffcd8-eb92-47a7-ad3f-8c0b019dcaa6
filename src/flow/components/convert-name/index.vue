<template>
	<div>
		<template v-for="(item, index) in values">
			<template v-if="props.options.some(s => s[props.valueKey] === item)">
				<span
					v-if="!props.elTagType"
					:key="index"
					:class="props.elTagClass"
					:style="props.style"
				>
					{{ showKeyName(item, values , index) }}</span>
				<el-tag
					v-else
					:key="index * 2"
					:disable-transitions="true"
					:index="index"
					:type="props.elTagType === 'primary' ? null : props.elTagType"
					:class="props.elTagClass"
					:style="props.style"
				>
					{{ showKeyName(item, values, index) }}
				</el-tag>
			</template>
		</template>
	</div>
</template>

<script setup lang="ts" name="convert-name">
import { computed } from "vue"

const props = defineProps({
	// 数据
	options: {
		type: Array as any,
		default: [],
	},
	// 当前的值
	value: [Number, String, Array],
	// 当前KEY
	valueKey: String,
	// 显示KEY
	showKey: String,
	// type
	elTagType: String,
	// class
	elTagClass: String,
	// style
	style: String,
})

const values = computed(() => {
	if (props.value !== null && typeof props.value !== "undefined") {
		return Array.isArray(props.value) ? props.value : [String(props.value)]
	}
	else {
		return []
	}
})

function showKeyName(item, values, index) {
	let find = props.options.find(f => f[props.valueKey] === item)
	return find[props.showKey] + (index !== (values.length - 1) ? "," : "")
}

</script>

<style scoped>
    .el-tag + .el-tag {
        margin-left: 10px;
    }
</style>
