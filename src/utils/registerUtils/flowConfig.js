import { ConvertName, ConvertRoleName, dynamicImport } from "@/flow/components/index"
import { DIC_PROP } from "@/flow/support/dict-prop"
import { PROP_CONST } from "@/flow/support/prop-const"
import { validateRunFlow, getLabelByLanguage } from "@/flow"
import { validateNull } from "@/utils/validate"

export function registerFlow(app) {
	// 导入通用自定义组件
	app.component("ConvertName", ConvertName)
	app.component("ConvertRoleName", ConvertRoleName)
	// 全局方法挂载
	app.config.globalProperties.DIC_PROP = DIC_PROP
	app.config.globalProperties.PROP_CONST = PROP_CONST
	app.config.globalProperties.validateRunFlow = validateRunFlow
	app.config.globalProperties.getLabelByLanguage = getLabelByLanguage
	app.config.globalProperties.validateNull = validateNull
	dynamicImport(app)
}
