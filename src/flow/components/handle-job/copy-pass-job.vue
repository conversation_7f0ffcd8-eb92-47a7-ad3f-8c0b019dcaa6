<template>
  <div>
    <el-form
      ref="dataFormRef"
      v-loading="loading"
      :model="form"
      :rules="dataRules"
      label-width="150px"
      :disabled="operType === 'view'"
    >
      <span>什么是抄送任务？被抄送的参与者需要参与审批，而传阅任务不需要参与审批只参与阅览</span>
      <el-row :gutter="24">
        <el-divider>参与者设置</el-divider>

        <el-col :span="12" class="mb-1">
          <el-form-item label="参与者类型" prop="jobType">
            <el-radio-group v-model="form.jobType" @change="handleRoleType">
              <el-radio
                v-for="(item, index) in DIC_PROP.JOB_USER_TYPE"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[0].value" :span="12" class="mb-1">
          <el-form-item label="指定参与人员" prop="roleId">
            <UserSelect ref="userSelectRef" v-model="form.roleId" />
          </el-form-item>
        </el-col>

        <el-col v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[1].value" :span="12" class="mb-1">
          <el-form-item label="指定参与角色" prop="roleId">
            <el-select v-model="form.roleId" placeholder="请选择参与角色" clearable filterable>
              <el-option
                v-for="(item, index) in dicData.roles"
                :key="index"
                :label="item.roleName"
                :value="item.roleId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value" :span="12" class="mb-1">
          <el-form-item label="指定参与部门" prop="roleId">
            <DeptPicker ref="deptPickerRef" v-model="form.roleId" />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="任务名称" prop="jobName">
            <el-input v-model="form.jobName" placeholder="请输入任务名称" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <footer class="el-dialog__footer">
      <el-button type="primary" :loading="loading" @click="handleUpdate">确定</el-button>
    </footer>
  </div>
</template>

<script setup lang="ts" name="CopyPassJob">
import { onLoadDicUrl } from '../convert-name/convert'
const { proxy } = getCurrentInstance()
import { handleChangeJobType } from '../../index'

const $emit = defineEmits(['onJobSignature'])
// 定义变量内容
const dataFormRef = ref()
const loading = ref(false)
const operType = ref(false)

// 定义字典
const dicData = reactive({})
// const onLoad = onLoadDicUrl({ key: 'users' }, { key: 'roles' }, { key: 'posts' }, { key: 'depts' })
const onLoad = onLoadDicUrl({ key: 'roles' })
onMounted(() => {
  onLoad(dicData)
})

// 提交表单数据
const form = reactive({
  jobType: '',
  roleId: null,
  jobName: ''
})

// 定义校验规则
const dataRules = ref({
  jobType: [{ required: true, message: '参与者类型不能为空', trigger: 'blur' }],
  roleId: [{ required: true, message: '任务参与者不能为空', trigger: 'blur' }],
  jobName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }]
})

const props = defineProps({
  currJob: {
    default: null
  }
})

function initJobData() {
  if (props.currJob) {
    Object.assign(form, props.currJob)
  }
}
const userSelectRef = ref(null)
const deptPickerRef = ref(null)
function handleRoleType() {
  let dictData = {
    users: userSelectRef?.value?.users || [],
    depts: deptPickerRef?.value?.deptOptions || [],
    posts: dicData.posts
  }
  handleChangeJobType(dictData, form)
}
async function handleUpdate() {
  const valid = await dataFormRef.value.validate().catch(() => {})
  if (!valid) return false

  form.nextUserRole = { jobType: form.jobType, roleId: form.roleId, jobName: form.jobName }

  props.currJob.nextUserRole = form.nextUserRole

  loading.value = true
  try {
    $emit('onJobSignature', props.currJob)
    setTimeout(() => {
      // 异步异常
      loading.value = false
    }, 3000)
  } catch (e) {
    loading.value = false
  }
}

// 监听双向绑定
watch(
  () => props.currJob?.id,
  (val) => {
    initJobData()
  }
)

onMounted(() => {
  initJobData()
})
</script>

<style lang="scss" scoped></style>
