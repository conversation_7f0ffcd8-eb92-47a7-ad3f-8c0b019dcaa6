<template>
  <div class="inline-block line-height-20px">
    <template v-for="(item, index) in values">
      <el-tooltip
        v-if="+item.jobType === 1"
        :key="index"
        content="点击可查看参与者具体的人员信息"
        placement="bottom"
      >
        <span
          class="cursor-pointer color-primary mr-10px"
          @click="handleShowJobRoleUsers(item)"
        >
          {{ item.roleName || item.name || item.userRoleNameOrUserName }}(角色)
          {{ index === values.length - 1 ? "" : "," }}
        </span>
      </el-tooltip>
      <span v-else class="mr-5px">
        {{ item.name || item.userRoleNameOrUserName }}
        {{ index === values.length - 1 ? "" : "," }}
      </span>
    </template>

    <el-dialog
      v-model="showRoleUsers"
      title="参与者候选人员"
      append-to-body
      width="630px"
      top="8vh"
    >
      <CustomTable
        ref="customTableRef"
        :data="tableData"
        :hasToolbar="false"
        :loading="loading"
      >
        <vxe-column field="workNumber" title="工号" />
        <vxe-column field="nickName" title="姓名" />
        <vxe-column field="deptName" title="所属部门" />
        <vxe-column field="phonenumber" title="手机号" />
      </CustomTable>
    </el-dialog>
  </div>
</template>

<script setup name="ConvertPickerName">
import { selectUserByParentRoleKey } from "@/api/system/user"
const props = defineProps({
  // 当前的值
  value: [Number, String, Object, Array],
  // type
  elTagType: String,
  // class
  elTagClass: String,
  isJobType: String,
})

const showRoleUsers = ref(false)
const loading = ref(false)
const tableData = ref([])

const values = computed(() => {
  if (props.value !== null && typeof props.value !== "undefined") {
    if (Array.isArray(props.value)) {
      return props.value
    } else if (typeof props.value === "object") {
      return [props.value]
    }
    return [String(props.value)]
  } else {
    return []
  }
})

async function handleShowJobRoleUsers(item) {
  showRoleUsers.value = true
  try {
    const res = await selectUserByParentRoleKey(item.roleKey)
    tableData.value = res.object
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss"></style>
