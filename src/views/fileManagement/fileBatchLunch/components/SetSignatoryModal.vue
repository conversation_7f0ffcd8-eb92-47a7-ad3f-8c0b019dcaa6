<template>
  <!-- 批量设置签署方弹窗 - 用于批量导入和设置文件签署方信息 -->
  <el-dialog v-model="dialogVisible" width="1300px" title="批量设置签署方" :before-close="handleClose" top="2vh">
    <template #header>
      <div class="title-line">
        批量设置签署方
        <span class="text-12px text-danger font-400">
          注:请先下载Excel表格，按照表格模板完成数据填写，并上传Excel文件。
        </span>
      </div>
    </template>
    <!-- 主容器：左侧为表单区域，右侧为文件预览区域 -->
    <div class="signatory-container">
      <!-- 左侧表单区域 -->
      <div class="form-body">
        <div class="box-title-line-b"><span>批量导入签署方</span></div>
        <div class="import-form">
          <!-- 快速选择签署方区域 -->
          <div class="quick-select-users">

            <div v-if="computedSum() > 0" class="selected-info">
              <!-- 显示已选择的签署方数量 -->
              <div class="selected-count">
                <svg-icon name="local-selected-users" :size="16" class="selected-icon" />
                <span class="selected-text">已选<span class="number">{{ computedSum() }}</span>人</span>
              </div>

              <!-- 清空已选择的签署方 -->
              <div class="clear-button" @click="clearTemplateUsers">
                <el-icon class="clear-icon">
                  <RefreshLeft />
                </el-icon>
                <span class="clear-text">清空</span>
              </div>
            </div>
            <!-- 打开快速选择签署方弹窗按钮 -->
            <el-button type="primary" @click="handleSelectUsers">
              快速选择签署方
            </el-button>
          </div>
          <!-- 表单区域：包含下载模板和上传签署方信息 -->
          <el-form :model="form" ref="form" label-width="auto">
            <!-- 下载签署方模板按钮 -->
            <el-form-item label="下载签署方模版:">
              <el-button type="primary" @click="downloadTemplate" class="min-h-32px! w-94px">
                <svg-icon name="local-download-line" :size="16" class="mr-2px" />
                下载模版
              </el-button>
            </el-form-item>
            <!-- 上传签署方信息组件 -->
            <el-form-item label="上传签署方信息:" label-position="top">
              <UploadBatchFileSign :essTemplateId="templateInfo.essTemplateId" @exportPublishId="getPublishId" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 右侧文件预览区域 -->
      <div class="w-63% flex-shrink-0">
        <TemplateIframe ref="templateIframeRef" class="h-full" :template-info="templateInfo" title="文件预览" />
      </div>
    </div>
    <!-- 弹窗底部按钮区域 -->
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>


    <!-- 快速选择签署方弹窗组件 - 用于选择不同类型的签署方 -->
    <BatchSelectUsers ref="batchSelectUsersRef" v-model="recipientTemplate" :recipientList="recipientList" />

  </el-dialog>
</template>

<script setup>
// 引入节流函数，防止频繁触发
import { throttle } from "lodash-es"
// 引入API前缀常量
import { essPrefix } from "@/config/constant"
// 引入获取模板签署方信息的API
import { getTemplateRecipientsAPI } from "@/api/ess/template/myTemplate"

// 异步加载组件，优化首次加载性能
const TemplateIframe = defineAsyncComponent(() =>
  import("@/views/template/useTemplate/components/TemplateIframe.vue") // 文件预览组件
)

// 上传批量签署方文件组件
const UploadBatchFileSign = defineAsyncComponent(() =>
  import("./UploadBatchFileSign.vue")
)

// 批量选择签署方组件
const BatchSelectUsers = defineAsyncComponent(() => import("./BatchSelectUsers.vue"))

const { proxy } = getCurrentInstance() // 获取组件实例，用于访问全局属性和方法

// 模板与选中的签署人数据
const recipientTemplate = ref([]); // 存储选中的签署方信息


// 组件属性定义
const props = defineProps({
  templateInfo: {
    type: Object, // 模板信息对象
    default: () => ({}),
  },
})



const dialogVisible = ref(false) // 控制弹窗显示状态
const recipientList = ref([]) // 签署方信息列表

// 表单数据
const form = ref({
  approveDtoList: [], // 审批人列表
})

/**
 * 关闭弹窗方法
 */
const handleClose = () => {
  dialogVisible.value = false
}

// 模板预览组件引用
const templateIframeRef = ref(null)

/**
 * 打开弹窗方法 - 供父组件调用
 */
const open = () => {
  dialogVisible.value = true
  // 注释掉的代码：获取模板预览URL
  // templateIframeRef.value?.getTemplatePreviewUrl()
}

// 定义事件，用于向父组件传递已选择的签署方
const emit = defineEmits(["confirm"])

const uploadResult = ref(null);

// 上传文件成功后获取的响应值
const getPublishId = (res) => {
  if(!res) return proxy.$message.error("上传失败")
  uploadResult.value = res.object.flowBatchPublishId;
}

/**
 * 提交方法 - 确认选择的签署方并关闭弹窗
 */
const handleSubmit = () => {
  // 合并签署方数据
  emit("confirm", recipientTemplate.value, JSON.stringify(uploadResult.value)) // 将选中的签署方传递给父组件
  handleClose() // 关闭弹窗
}


/**
 * 打开快速选择签署方弹窗
 */
const handleSelectUsers = () => {
  proxy.$refs.batchSelectUsersRef.open({ recipientTemplate: recipientTemplate.value })
}


/**
 * 获取模版签署方信息
 * 从API获取模板的签署方信息并格式化数据结构
 */
const getTemplateRecipients = async () => {
  const res = await getTemplateRecipientsAPI(props.templateInfo.essTemplateId)
  
  // Object.entries 将对象转换成数组，map 将数组转换成对象
  // 将API返回的数据结构转换为组件需要的格式
  recipientList.value = Object.entries(res.object).map(([key, value]) => ({
    key, // 签署方类型键
    list: value, // 签署方列表
  }))
}

/**
 * 下载签署方模版
 * 使用throttle防止频繁点击导致多次下载
 */
const downloadTemplate = throttle(function () {
  let recipientTemplateCopy = dedupeListAndMergeTemplateUsers(JSON.parse(JSON.stringify(recipientTemplate.value)));
  if(!recipientTemplateCopy.length){
    recipientTemplateCopy = JSON.parse(JSON.stringify(recipientList.value));
  }
  
  proxy.download(
    essPrefix + "/flowInfoBatch/exportByUser", // API路径
    {
      essTemplateId: props.templateInfo.essTemplateId, // 模板ID
      templateBatchUpload: recipientTemplateCopy.map((i) => i.list).flat(), // 扁平化处理签署方列表
    },
    `签署方模版模板.xlsx`, // 下载文件名
    { headers: { "Content-Type": "application/json" } } // 请求头设置
  )
}, 800) // 800ms内只能触发一次


/**
 * 根据recipientId合并签署方列表
 * @param {Array} arr 签署方列表
 * @returns {Array} 返回合并后的签署方列表
 */
function dedupeListAndMergeTemplateUsers(arr) {
  return arr.map(group => {
    const map = {};
    group.list.forEach(item => {
      const id = item.recipientId;
      if (!map[id]) {
        // 复制一份，避免污染原数据
        map[id] = { ...item, templateUsers: [...(item.templateUsers || [])] };
      } else {
        // 合并 templateUsers，不覆盖
        map[id].templateUsers = map[id].templateUsers.concat(item.templateUsers || []);
      }
    });
    return {
      ...group,
      list: Object.values(map)
    };
  });
}


/**
 * 计算已选择的签署方总人数
 * @returns {number} 总人数
 */
const computedSum = () => {
  let sum = 0;
  recipientTemplate.value.forEach((i) => {
    i.list.forEach((j) => {
      sum += j.templateUsers ? j.templateUsers.length : 0; // 累加每个签署方的人数
    });
  });
  return sum;
}


/**
 * 清空已选择的签署方
 * 将所有签署方的templateUsers数组清空
 */
const clearTemplateUsers = () => {
  // 遍历所有签署方类型和列表，清空templateUsers数组
  recipientTemplate.value.forEach((item) => {
    item.list.forEach((recipient) => {
      recipient.templateUsers = []; // 清空签署方列表
    });
  });
}

// 组件初始化时获取模板签署方信息
getTemplateRecipients()

// 向父组件暴露的方法
defineExpose({
  open, // 暴露open方法供父组件调用
})
</script>

<style scoped lang="scss">
/* 签署方容器样式 - 左右布局 */
.signatory-container {
  display: flex;
  justify-content: space-between;
  height: 80vh;
  /* 设置容器高度为视口高度的80% */

  /* 左侧表单区域样式 */
  .form-body {
    width: 36%;
    /* 左侧区域宽度 */
    height: 100%;
    overflow-y: auto;
    /* 内容过多时显示垂直滚动条 */
    padding-left: 10px;
  }
}

/* 导入表单样式 */
.import-form {

  /* 快速选择签署方区域样式 */
  .quick-select-users {
    margin-top: 10px;
    background-color: #f8f8f8;
    /* 浅灰色背景 */
    padding: 12px 15px;
    border-radius: 4px;
    /* 圆角边框 */
    margin-bottom: 20px;

    /* 已选择信息区域样式 */
    .selected-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      padding: 8px 12px;
      background-color: #FFFFFF;
      border-radius: 6px;
      border: 1px solid #F0F0F0;

      /* 已选择数量区域 */
      .selected-count {
        display: flex;
        align-items: center;
        gap: 6px;

        .selected-icon {
          color: #AAAAAA;
        }

        .selected-text {
          font-size: 12px;
          color: #333;
          font-weight: 400;

          /* 数字样式 */
          .number {
            font-size: 16px;
            color: #FF7B17;
            padding: 0 2px;
          }
        }
      }

      /* 清空按钮样式 */
      .clear-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;

        &:hover .clear-icon, &:hover .clear-text {
            color: red;
        }

        .clear-icon,.clear-text{
          display: flex;
          align-items: center;
          color: #AAAAAA;
          font-size: 12px;
        }

        .clear-icon {
          padding-top: 2px;
        }
        

        .clear-text {
          font-weight: 400;
          line-height: 1;
        }
      }
    }
  }
}
</style>