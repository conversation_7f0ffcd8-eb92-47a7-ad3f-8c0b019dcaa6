export default {
	runReject: {
		index: "#",
		importrunRejectTip: "import RunReject",
		id: "id",
		flowKey: "flowKey",
		fromFlowNodeId: "fromFlowNodeId",
		toFlowNodeId: "toFlowNodeId",
		handleUserId: "handleUserId",
		status: "status",
		createUser: "createUser",
		createTime: "createTime",
		defFlowId: "defFlowId",
		flowInstId: "flowInstId",
		fromRunNodeId: "fromRunNodeId",
		fromRunJobId: "fromRunJobId",
		toRunNodeId: "toRunNodeId",
		toRunJobId: "toRunJobId",
		remark: "remark",

		inputIdTip: "input id",
		inputFlowKeyTip: "input flowKey",
		inputFromFlowNodeIdTip: "input fromFlowNodeId",
		inputToFlowNodeIdTip: "input toFlowNodeId",
		inputHandleUserIdTip: "input handleUserId",
		inputStatusTip: "input status",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputDefFlowIdTip: "input defFlowId",
		inputFlowInstIdTip: "input flowInstId",
		inputFromRunNodeIdTip: "input fromRunNodeId",
		inputFromRunJobIdTip: "input fromRunJobId",
		inputToRunNodeIdTip: "input toRunNodeId",
		inputToRunJobIdTip: "input toRunJobId",
		inputRemarkTip: "input remark",

	},
}
