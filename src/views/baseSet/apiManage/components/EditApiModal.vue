<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="700px"
      :before-close="handleClose"
      top="10vh"
  >
    <el-form
        ref="formRef"
        :model="form"
        label-width="120px"
        :rules="rules"
        inline
        :disabled="formDisabled"
    >
      <el-form-item v-if="form.id" label="系统ID" prop="id" w-640px>
        <el-input v-model="form.id" disabled/>
      </el-form-item>
      <el-form-item label="系统名称" prop="sysName">
        <el-input
            v-model="form.sysName"
            placeholder="请输入系统名称"
            maxlength="200"
            clearable
        />
      </el-form-item>
      <el-form-item label="责任单位" prop="responsibleUnit">
        <el-input
            v-model="form.responsibleUnit"
            placeholder="请输入责任单位"
            maxlength="200"
            clearable
        />
      </el-form-item>
      <el-form-item label="接入时间" prop="acceptTime">
        <el-date-picker
            v-model="form.acceptTime"
            value-format="YYYY-MM-DD HH:mm"
            format="YYYY-MM-DD HH:mm"
            type="datetime"
            placeholder="请选择"
            style="width: 180px"
        />
      </el-form-item>
      <el-form-item label="联系人" prop="contacts">
        <el-input
            v-model="form.contacts"
            placeholder="请输入联系人"
            maxlength="200"
            clearable
        />
      </el-form-item>
      <el-form-item label="接入状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择">
          <el-option
              v-for="item in normal_disable"
              :key="item.value"
              :label="item.label"
              :value="+item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="联系电话" prop="phoneNumber">
        <el-input
            v-model="form.phoneNumber"
            placeholder="请输入联系电话"
            maxlength="20"
            clearable
        />
      </el-form-item>
      <div class="form-b-border"/>
      <div class="block-form-item">
        <el-form-item label="指定回调地址" prop="callbackUrl">
          <el-input
              v-model="form.callbackUrl"
              placeholder="请输入指定回调地址"
              clearable
          />
        </el-form-item>
        <el-form-item label="加密key" prop="secretKey">
          <div class="suffix-form-item" :class="{ block: formDisabled }">
            <el-input
                v-model="form.secretKey"
                placeholder="请输入加密key"
                clearable
                readonly
            />
            <el-button
                v-if="!formDisabled"
                plain
                type="success"
                :loading="keyLoading"
                @click="createKey"
            >
              {{ keyLoading ? "生成中" : "点击生成" }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="签名验证token" prop="token">
          <div class="suffix-form-item" :class="{ block: formDisabled }">
            <el-input
                v-model="form.token"
                placeholder="请输入签名验证token"
                clearable
                readonly
            />
            <el-button
                v-if="!formDisabled"
                plain
                type="success"
                :loading="tokenLoading"
                @click="createToken"
            >
              {{ tokenLoading ? "生成中" : "点击生成" }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="业务简介" prop="description">
          <el-input
              v-model="form.description"
              type="textarea"
              maxlength="500"
              :rows="6"
              placeholder="请输入业务简介"
              clearable
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 取消</el-button>
      <el-button
          v-if="!formDisabled"
          type="primary"
          :loading="loading"
          @click="submitForm"
      >
        保存
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup name="EditBusinessLineModal">
import {
  addInterface,
  updateInterface,
  generateKey,
  generateToken,
} from "@/api/ess/baseSet/apiManage"
import { rule } from "@/utils/validate"

const { proxy } = getCurrentInstance()

const props = defineProps({
  normal_disable: Array,
})
const title = ref("新增业务线")
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)
const form = ref({
  sysName: null,
  status: null,
  responsibleUnit: null,
  contacts: null,
})
const rules = ref({
  sysName: [
    { required: true, message: "请输入系统名称", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" },
  ],
  acceptTime: [{ required: true, message: "请选择接入时间", trigger: "change" }],
  status: [{ required: true, message: "请选择接入状态", trigger: "change" }],
  responsibleUnit: [
    { required: true, message: "请输入责任单位", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" },
  ],
  contacts: [
    { required: true, message: "请输入联系人", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  phoneNumber: [
    { required: true, message: "请输入联系电话", trigger: "blur" },
    {
      validator(rule, value, callback) {
        if (!/^1\d{10}$|^0\d{2,3}-?\d{7,8}$/.test(value)) {
          callback(new Error("请输入正确的手机号"))
        } else {
          callback()
        }
      },
      trigger: "blur",
    },
  ],
  secretKey: [{ required: true, message: "请输入加密key", trigger: "change" }],
  token: [{ required: true, message: "请输入签名验证token", trigger: "change" }],
  callbackUrl: [{ required: true, message: "请输入回调地址", trigger: "blur" }],
})
const emit = defineEmits(["change"])
const formDisabled = ref(false) // 表单禁用

const open = (item, disabled = false) => {
  dialogVisible.value = true
  nextTick(() => {
    if (item?.id) {
      form.value = { ...item }
    }
    formDisabled.value = disabled
    title.value = disabled ? "查看" : item?.id ? "编辑" : "新增"
  })
}
// 取消
const handleClose = () => {
  proxy.resetForm("formRef")
  form.value.id = null
  dialogVisible.value = false
}

// 确定
const submitForm = () => {
  proxy.$refs["formRef"].validate(async (valid) => {
    if (valid) {
      loading.value = true
      let actionUrl = form.value.id ? updateInterface : addInterface
      try {
        let res = await actionUrl(form.value)
        if (+res.code === 200) {
          proxy.$modal.msgSuccess(form.value.id ? "修改成功" : "新增成功")
          handleClose()
          form.value.id ? emit("change") : emit("refresh")
        }
        loading.value = false
      } catch (err) {
        loading.value = false
      }
    }
  })
}

const keyLoading = ref(false)
const createKey = async () => {
  keyLoading.value = true
  let res = await generateKey()
  form.value.secretKey = res.object
  keyLoading.value = false
}

const tokenLoading = ref(false)
const createToken = async () => {
  tokenLoading.value = true
  let res = await generateToken()
  form.value.token = res.object
  tokenLoading.value = false
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.block-form-item {
  :deep(.el-form-item) {
    width: 96%;
  }

  .suffix-form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 420px;
      flex-shrink: 0;
      margin-right: 15px;
    }

    .el-button.is-plain {
      border-color: transparent;
    }

    &.block {
      display: block;
      width: 100%;

      .el-input {
        min-width: 100% !important;
      }
    }
  }
}
</style>
