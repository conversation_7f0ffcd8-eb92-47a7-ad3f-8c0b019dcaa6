<template>
	<div class="dict-select">
		<el-select
			v-if="type == 'select'"
			v-model="model"
			:placeholder="placeholder"
			clearable
			v-bind="$attrs"
		>
			<el-option
				v-for="item in options"
				:key="item.value"
				:label="item.label"
				:value="formatterValue(item.value)"
			/>
		</el-select>
		<el-checkbox-group v-if="type == 'checkbox'" v-model="model" v-bind="$attrs">
			<el-checkbox v-for="item in options" :key="item.value" :value="formatterValue(item.value)">
				{{ item.label }}
			</el-checkbox>
		</el-checkbox-group>
		<el-radio-group v-if="type == 'radio'" v-model="model" v-bind="$attrs">
			<el-radio v-for="item in options" :key="item.value" :value="formatterValue(item.value)">
				{{ item.label }}
			</el-radio>
		</el-radio-group>
	</div>
</template>

<script setup>
const props = defineProps({
	type: {
		type: String,
		default: "select", // select | checkbox | radio
	},
	options: {
		type: Array,
		default: () => [],
	},
	modelValue: {
		type: [Number, String, Array],
		default: null,
	},
	placeholder: {
		type: String,
		default: "请选择",
	},
	valueType: {
		type: String,
		default: "number", // number | string
	},
})

const model = defineModel()
const formatterValue = e => {
	let result = props.valueType == "number" ? Number(e) : e
	return result
}
</script>

<style scoped lang="scss">
.dict-select{
  width: 100%;
}
</style>
