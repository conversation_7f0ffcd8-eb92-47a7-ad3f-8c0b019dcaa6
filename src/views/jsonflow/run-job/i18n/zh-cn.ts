export default {
	runJob: {
		index: "#",
		importrunJobTip: "导入任务记录",
		id: " 任务名称",
		sort: "排序值",
		defFlowId: "流程名称",
		parFlowInstId: "关联父流程",
		subFlowInstId: "关联子流程",
		flowNodeId: "节点名称",
		nodeJobId: "任务名称",
		jobType: "参与者类型",
		roleId: "参与者",
		userId: "审批人",
		startTime: "开始时间",
		endTime: "结束时间",
		timeLimit: "任务时限",
		useTime: "任务用时",
		userKey: "参与者KEY",
		jobName: "任务名称",
		distFlowNodeId: "待分配参与者节点",
		timeout: "任务时限",
		status: "任务状态",
		suspension: "是否挂起",
		suspensionReason: "挂起原因",
		invalidReason: "作废原因",
		flowStatus: "流程状态",
		isSkipRejected: "被驳回是否可跳过",
		createUser: "创建人",
		createTime: "创建时间",
		flowKey: "工单流程KEY",
		flowInstId: "流程实例ID",
		runNodeId: "节点名称",
		isNowRun: "被分配后是否立即运行",
		signatureType: "加签类型",
		isRead: "是否已阅",

		inputIdTip: "请输入任务名称",
		inputSortTip: "请输入排序值",
		inputDefFlowIdTip: "请输入流程名称",
		inputParFlowInstIdTip: "请输入关联父流程",
		inputSubFlowInstIdTip: "请输入关联子流程",
		inputFlowNodeIdTip: "请输入节点名称",
		inputNodeJobIdTip: "请输入任务名称",
		inputJobTypeTip: "请输入参与者类型",
		inputRoleIdTip: "请输入参与者",
		inputUserIdTip: "请输入审批人",
		inputStartTimeTip: "请输入开始时间",
		inputEndTimeTip: "请输入结束时间",
		inputUserKeyTip: "请输入参与者KEY",
		inputJobNameTip: "请输入任务名称",
		inputDistFlowNodeIdTip: "请输入待分配参与者节点",
		inputTimeoutTip: "请输入任务时限",
		inputStatusTip: "请输入任务状态",
		inputFlowStatusTip: "请输入流程状态",
		inputSuspensionTip: "请输入是否挂起",
		inputSuspensionReasonTip: "请输入挂起原因",
		inputIsSkipRejectedTip: "请输入被驳回是否可跳过",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputFlowKeyTip: "请输入工单流程KEY",
		inputFlowInstIdTip: "请输入流程实例ID",
		inputRunNodeIdTip: "请输入节点名称",
		inputIsNowRunTip: "请输入被分配后是否立即运行",
		inputSignatureTypeTip: "请输入加签类型",
		inputIsReadTip: "请输入是否已阅",
	},
}
