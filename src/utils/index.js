import { parseTime } from "./common"
import request from "@/utils/request"
import modal from "@/plugins/modal"
import useClipboard from 'vue-clipboard3';
import * as CryptoJS from 'crypto-js';
import {sm4} from 'sm-crypto'
import { cloneDeep } from "lodash-es"

const { toClipboard } = useClipboard();

/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
	if (cellValue == null || cellValue == "") { return "" }
	var date = new Date(cellValue)
	var year = date.getFullYear()
	var month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1
	var day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate()
	var hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours()
	var minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()
	var seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds()
	return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds
}

// 手机号脱敏
export function replacePhone(str) { 
 return str?.replace(/(\d{3})\d*(\d{4})/,"$1****$2") || ''
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
	if (("" + time).length === 10) {
		time = parseInt(time) * 1000
	}
	else {
		time = +time
	}
	const d = new Date(time)
	const now = Date.now()

	const diff = (now - d) / 1000

	if (diff < 30) {
		return "刚刚"
	}
	else if (diff < 3600) {
		// less 1 hour
		return Math.ceil(diff / 60) + "分钟前"
	}
	else if (diff < 3600 * 24) {
		return Math.ceil(diff / 3600) + "小时前"
	}
	else if (diff < 3600 * 24 * 2) {
		return "1天前"
	}
	if (option) {
		return parseTime(time, option)
	}
	else {
		return (
			d.getMonth() + 1 + "月" + d.getDate() + "日" + d.getHours() + "时" + d.getMinutes() + "分"
		)
	}
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
	url = url == null ? window.location.href : url
	const search = url.substring(url.lastIndexOf("?") + 1)
	const obj = {}
	const reg = /([^?&=]+)=([^?&=]*)/g
	search.replace(reg, (rs, $1, $2) => {
		const name = decodeURIComponent($1)
		let val = decodeURIComponent($2)
		val = String(val)
		obj[name] = val
		return rs
	})
	return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
	// returns the byte length of an utf8 string
	let s = str.length
	for (var i = str.length - 1; i >= 0; i--) {
		const code = str.charCodeAt(i)
		if (code > 0x7f && code <= 0x7ff) { s++ }
		else if (code > 0x7ff && code <= 0xffff) { s += 2 }
		if (code >= 0xdc00 && code <= 0xdfff) { i-- }
	}
	return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
	const newArray = []
	for (let i = 0; i < actual.length; i++) {
		if (actual[i]) {
			newArray.push(actual[i])
		}
	}
	return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
	if (!json) { return "" }
	return cleanArray(
		Object.keys(json).map(key => {
			if (json[key] === undefined) { return "" }
			return encodeURIComponent(key) + "=" + encodeURIComponent(json[key])
		}),
	).join("&")
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
	const search = decodeURIComponent(url.split("?")[1]).replace(/\+/g, " ")
	if (!search) {
		return {}
	}
	const obj = {}
	const searchArr = search.split("&")
	searchArr.forEach(v => {
		const index = v.indexOf("=")
		if (index !== -1) {
			const name = v.substring(0, index)
			const val = v.substring(index + 1, v.length)
			obj[name] = val
		}
	})
	return obj
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
	const div = document.createElement("div")
	div.innerHTML = val
	return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
	if (typeof target !== "object") {
		target = {}
	}
	if (Array.isArray(source)) {
		return source.slice()
	}
	Object.keys(source).forEach(property => {
		const sourceProperty = source[property]
		if (typeof sourceProperty === "object") {
			target[property] = objectMerge(target[property], sourceProperty)
		}
		else {
			target[property] = sourceProperty
		}
	})
	return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
	if (!element || !className) {
		return
	}
	let classString = element.className
	const nameIndex = classString.indexOf(className)
	if (nameIndex === -1) {
		classString += "" + className
	}
	else {
		classString
      = classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length)
	}
	element.className = classString
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
	let timeout, args, context, timestamp, result

	const later = function () {
		// 据上一次触发时间间隔
		const last = +new Date() - timestamp

		// 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
		if (last < wait && last > 0) {
			timeout = setTimeout(later, wait - last)
		}
		else {
			timeout = null
			// 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
			if (!immediate) {
				result = func.apply(context, args)
				if (!timeout) { context = args = null }
			}
		}
	}

	return function (...args) {
		context = this
		timestamp = +new Date()
		const callNow = immediate && !timeout
		// 如果延时不存在，重新设定延时
		if (!timeout) { timeout = setTimeout(later, wait) }
		if (callNow) {
			result = func.apply(context, args)
			context = args = null
		}

		return result
	}
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
	return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
	const timestamp = +new Date() + ""
	const randomNum = parseInt((1 + Math.random()) * 65536) + ""
	return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
	return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
	if (!hasClass(ele, cls)) { ele.className += " " + cls }
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
	if (hasClass(ele, cls)) {
		const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)")
		ele.className = ele.className.replace(reg, " ")
	}
}

export function makeMap(str, expectsLowerCase) {
	const map = Object.create(null)
	const list = str.split(",")
	for (let i = 0; i < list.length; i++) {
		map[list[i]] = true
	}
	return expectsLowerCase ? val => map[val.toLowerCase()] : val => map[val]
}

export const exportDefault = "export default "

export const beautifierConf = {
	html: {
		indent_size: "2",
		indent_char: " ",
		max_preserve_newlines: "-1",
		preserve_newlines: false,
		keep_array_indentation: false,
		break_chained_methods: false,
		indent_scripts: "separate",
		brace_style: "end-expand",
		space_before_conditional: true,
		unescape_strings: false,
		jslint_happy: false,
		end_with_newline: true,
		wrap_line_length: "110",
		indent_inner_html: true,
		comma_first: false,
		e4x: true,
		indent_empty_lines: true,
	},
	js: {
		indent_size: "2",
		indent_char: " ",
		max_preserve_newlines: "-1",
		preserve_newlines: false,
		keep_array_indentation: false,
		break_chained_methods: false,
		indent_scripts: "normal",
		brace_style: "end-expand",
		space_before_conditional: true,
		unescape_strings: false,
		jslint_happy: true,
		end_with_newline: true,
		wrap_line_length: "110",
		indent_inner_html: true,
		comma_first: false,
		e4x: true,
		indent_empty_lines: true,
	},
}

/**
 *
 * @param str 首字母大写
 * @returns 首字母大写
 */
export function titleCase(str) {
	return str.replace(/( |^)[a-z]/g, L => L.toUpperCase())
}

/**
 *
 * @param str 下划转驼峰
 * @returns 驼峰
 */
export function camelCase(str) {
	return str.replace(/_[a-z]/g, str1 => str1.substr(-1).toUpperCase())
}
/**
 *
 * @param str 驼峰转下划线
 * @returns 下划线
 */
export function toUnderline(str) {
	return str.replace(/([A-Z])/g, "_$1").toLowerCase()
}

export function isNumberStr(str) {
	return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str)
}

/**
 * 对象深克隆
 * @param obj 源对象
 * @returns 克隆后的对象
 */
export function deepClone(obj) {
  // return cloneDeep(obj)
	let newObj
	try {
		newObj = obj.push ? [] : {}
	}
	catch (error) {
		newObj = {}
	}
	for (let attr in obj) {
		if (obj[attr] && typeof obj[attr] === "object") {
			newObj[attr] = deepClone(obj[attr])
		}
		else {
			newObj[attr] = obj[attr]
		}
	}
	return newObj
}

/**
 * @description 获取不重复的id
 * @param length { Number } id的长度
 * @return { String } id
 */
export const getNonDuplicateID = (length = 8) => {
	let idStr = Date.now().toString(36)
	idStr += Math.random().toString(36)
		.substring(3, length)
	return idStr
}

/**
 * 判断是否是移动端
 */
export function isMobile() {
	if (
		navigator.userAgent.match(
			/('phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone')/i,
		)
	) {
		return true
	}
	else {
		return false
	}
}

/**
 *
 * @param url 目标下载接口
 * @param query 查询参数
 * @param fileName 文件名称
 * @returns {*}
 */
export function downBlobFile(url, query, fileName) {
	return request({
		url: url,
		method: "get",
		responseType: "blob",
		params: query,
	}).then(response => {
		handleBlobFile(response, fileName)
	})
}
/**
 * blob 文件刘处理
 * @param response 响应结果
 * @returns
 */
export function handleBlobFile(response, fileName) {
	// 处理返回的文件流
	const blob = response
	if (blob && blob.size === 0) {
		modal.msgError("内容为空，无法下载")
		return
	}
	const link = document.createElement("a")

	// 兼容一下 入参不是 File Blob 类型情况
	var binaryData = []
	binaryData.push(response)
	link.href = window.URL.createObjectURL(new Blob(binaryData))
	link.download = fileName
	document.body.appendChild(link)
	link.click()
	window.setTimeout(function () {
		// @ts-ignore
		URL.revokeObjectURL(blob)
		document.body.removeChild(link)
	}, 0)
}

/**
 * 列表结构转树结构
 * @param data
 * @param id
 * @param parentId
 * @param children
 * @param rootId
 * @returns {*}
 */
export function handleTree(data, id, parentId, children, rootId) {
	id = id || "id"
	parentId = parentId || "parentId"
	children = children || "children"
	rootId
        = rootId
        	|| Math.min.apply(
        		Math,
        		data.map(item => {
        			return item[parentId]
        		}),
        	)
        	|| 0
	// 对源数据深度克隆
	const cloneData = JSON.parse(JSON.stringify(data))
	// 循环所有项
	const treeData = cloneData.filter(father => {
		const branchArr = cloneData.filter(child => {
			// 返回每一项的子级数组
			return father[id] === child[parentId]
		})
		branchArr.length > 0 ? (father[children] = branchArr) : ""
		// 返回第一层
		return father[parentId] === rootId
	})
	return treeData !== "" ? treeData : data
}



/**
 * 打开小窗口
 */
export const openWindow = (url, title, w, h) => {
    // @ts-ignore
    const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : screen.left;
    // @ts-ignore
    const dualScreenTop = window.screenTop !== undefined ? window.screenTop : screen.top;

    const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
    const height = window.innerHeight
        ? window.innerHeight
        : document.documentElement.clientHeight
            ? document.documentElement.clientHeight
            : screen.height;

    const left = width / 2 - w / 2 + dualScreenLeft;
    const top = height / 2 - h / 2 + dualScreenTop;
    return window.open(
        url,
        title,
        'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=yes, copyhistory=no, width=' +
        w +
        ', height=' +
        h +
        ', top=' +
        top +
        ', left=' +
        left
    );
};

/**
 *加密处理
 */
export function encryption(src, keyWord) {
    const key = CryptoJS.enc.Utf8.parse(keyWord);
    // 加密
    var encrypted = CryptoJS.AES.encrypt(src, key, {
        iv: key,
        mode: CryptoJS.mode.CFB,
        padding: CryptoJS.pad.NoPadding,
    });
    return encrypted.toString();
}

/**
 *  解密
 * @param {*} params 参数列表
 * @returns 明文
 */
export function decryption(src, keyWord) {
    const key = CryptoJS.enc.Utf8.parse(keyWord);
    // 解密逻辑
    var decryptd = CryptoJS.AES.decrypt(src, key, {
        iv: key,
        mode: CryptoJS.mode.CFB,
        padding: CryptoJS.pad.NoPadding,
    });

    return decryptd.toString(CryptoJS.enc.Utf8);
}

/**
 * SM4加密处理
 */
export function sm4Encryption(src, keyWord) {
    return sm4.encrypt(src, keyWord);
}

/**
 * SM4解密处理
 * @param {*} params 参数列表
 * @returns 明文
 */
export function sm4Decryption(src, keyWord) {
    return sm4.decrypt(src, keyWord);
}

/**
 * Base64 加密
 * @param {*} src  明文
 * @returns 密文
 */
export function base64Encrypt(src) {
    const encodedWord = CryptoJS.enc.Utf8.parse(src);
    return CryptoJS.enc.Base64.stringify(encodedWord);
}

export function getNumberRadixNum(input) {
    let strings = input.toString().split(".");
    if (strings.length <= 1) {
        return 0;
    }
    return strings[1].toString().length;
};

/**
 * @description 生成唯一 uuid
 * @return string
 */
export function generateUUID() {
    if (typeof crypto === 'object') {
        if (typeof crypto.randomUUID === 'function') {
            return crypto.randomUUID();
        }
        if (typeof crypto.getRandomValues === 'function' && typeof Uint8Array === 'function') {
            const callback = (c) => {
                const num = Number(c);
                return (num ^ (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (num / 4)))).toString(16);
            };
            return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, callback);
        }
    }
    let timestamp = new Date().getTime();
    let performanceNow = (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0;
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        let random = Math.random() * 16;
        if (timestamp > 0) {
            random = (timestamp + random) % 16 | 0;
            timestamp = Math.floor(timestamp / 16);
        } else {
            random = (performanceNow + random) % 16 | 0;
            performanceNow = Math.floor(performanceNow / 16);
        }
        return (c === 'x' ? random : (random & 0x3) | 0x8).toString(16);
    });
}

export const copyText = (text) => {
		return new Promise((resolve, reject) => {
			try {
				//复制
				toClipboard(text);
				//下面可以设置复制成功的提示框等操作
				modal.msgSuccess('复制成功');
				resolve(text);
			} catch (e) {
				//复制失败
				modal.msgError('复制失败');
				reject(e);
			}
		});
	};

// 获取文件名称
export function getFileName(name) {
  // 先去掉URL参数
  const urlWithoutParams = name.split('?')[0]
  // 取最后的名字
  let fileName = urlWithoutParams.lastIndexOf("/") > -1 
    ? urlWithoutParams.slice(urlWithoutParams.lastIndexOf("/") + 1) 
    : urlWithoutParams

  // 查找 __ty__ 的位置
  const tyIndex = fileName.indexOf("__ty__")
  if (tyIndex > -1) {
    return fileName.slice(tyIndex + "__ty__".length)
  }
  return fileName
}

/**
 * 判断两数组字符串是否相同（用于按钮权限验证），数组字符串中存在相同时会自动去重（按钮权限标识不会重复）
 * @param news 新数据
 * @param old 源数据
 * @returns 两数组相同返回 `true`，反之则反
 */
export function judgementSameArr(newArr, oldArr) {
	const news = removeDuplicate(newArr)
	const olds = removeDuplicate(oldArr)
	let count = 0
	const leng = news.length
	for (let i in olds) {
		for (let j in news) {
			if (olds[i] === news[j]) { count++ }
		}
	}
	return count === leng ? true : false
}

/**
 * 判断两个对象是否相同
 * @param a 要比较的对象一
 * @param b 要比较的对象二
 * @returns 相同返回 true，反之则反
 */
export function isObjectValueEqual(a, b) {
	if (!a || !b) { return false }
	let aProps = Object.getOwnPropertyNames(a)
	let bProps = Object.getOwnPropertyNames(b)
	if (aProps.length != bProps.length) { return false }
	for (let i = 0; i < aProps.length; i++) {
		let propName = aProps[i]
		let propA = a[propName]
		let propB = b[propName]
		if (!b.hasOwnProperty(propName)) { return false }
		if (propA instanceof Object) {
			if (!isObjectValueEqual(propA, propB)) { return false }
		}
		else if (propA !== propB) {
			return false
		}
	}
	return true
}

/**
 * 数组、数组对象去重
 * @param arr 数组内容
 * @param attr 需要去重的键值（数组对象）
 * @returns
 */
export function removeDuplicate(arr, attr) {
	if (!Object.keys(arr).length) {
		return arr
	}
	else {
		if (attr) {
			const obj = {}
			return arr.reduce((cur, item) => {
				obj[item[attr]] ? "" : (obj[item[attr]] = true && item[attr] && cur.push(item))
				return cur
			}, [])
		}
		else {
			return [...new Set(arr)]
		}
	}
}
