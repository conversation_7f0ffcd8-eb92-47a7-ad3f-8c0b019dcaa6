<template>
  <div class="app-container">
    <div class="flex-x-between mb-10px">
      <StepsFill
        class="step-box"
        width="900px"
        :steps="stepOptions"
        v-model:current="stepIndex"
        :disabled="stepValue === 'finish'"
      />
      <div class="right-opt-box">
        <div
          class="btn-box"
          v-if="!['finish', 'setSignatory'].includes(stepValue)"
          @click="stepIndex = stepIndex - 1"
        >
          <el-icon><ArrowLeft /></el-icon>
        </div>
        <div class="btn-box close-btn" @click="handleClose">
          <el-icon><Close /></el-icon>
        </div>
      </div>
    </div>

    <div
      class="white-body-box relative main-create-box"
      v-loading="loading"
      :class="{ 'no-bg': stepValue === 'setFlowConfig' }"
    >
      <template v-if="templateInfo.id">
        <FillApplicationInfo
          ref="fillApplicationInfoRef"
          :templateInfo="templateInfo"
          :draftForm="draftForm"
          v-show="stepValue === 'setSignatory'"
        />
        <SetFlowConfig
          ref="setFlowConfigRef"
          :templateInfo="templateInfo"
          v-show="stepValue === 'setFlowConfig'"
          v-if="+templateInfo.flowFlag === 2"
        />
        <EditTemplateContent
          ref="editTemplateContentRef"
          :templateInfo="templateInfo"
          :flowId="form.flowId"
          @success="editTemplateSuccess"
          v-show="['templateContent', 'finish'].includes(stepValue)"
        />
      </template>
      <div
        class="table-handle-box"
        v-if="stepValue === 'setSignatory' || stepValue === 'setFlowConfig'"
      >
        <el-button
          type="primary"
          plain
          v-if="+templateInfo.flowFlag === 1"
          @click="showFlowModal"
        >
          固定审批流程
        </el-button>
        <el-button plain @click="stepIndex = stepIndex - 1" v-if="stepIndex !== 0" w-80px>
          上一步
        </el-button>
        <el-button type="primary" @click="handleStep" w-80px :loading="loading">
          下一步
        </el-button>
      </div>
    </div>

    <FlowViewModal ref="flowViewModalRef" />
  </div>
</template>

<script setup name="UseTemplate">
import { getTemplateInfo } from "@/api/ess/template/template"
import {
  saveDraftTemplateAPI,
  getTemplateDraftInfoAPI,
} from "@/api/ess/template/myTemplate"
import { removeDraft } from "@/api/ess/fileManagement/file"
import { updateDraftAPI } from "@/api/ess/fileManagement/file"
import mittBus from "@/utils/mitt"

const StepsFill = defineAsyncComponent(() => import("@/components/StepsFill/index.vue"))
const FlowViewModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowViewModal.vue")
)
const FillApplicationInfo = defineAsyncComponent(() =>
  import("./components/Steps/FillApplicationInfo.vue")
)
const SetFlowConfig = defineAsyncComponent(() =>
  import("./components/Steps/SetFlowConfig.vue")
)
const EditTemplateContent = defineAsyncComponent(() =>
  import("./components/Steps/EditTemplateContent.vue")
)
const { proxy } = getCurrentInstance()
const route = useRoute()
const templateId = ref(route.params.id)
const draftId = ref(route.query.draftId) // 草稿箱id
const stepIndex = ref(0)
const form = ref({}) // 保存草稿的数据
const draftForm = ref({}) // 回显草稿数据
const stepValue = computed(() => {
  return stepOptions.value[stepIndex.value]?.value
})
// 提取公共基础步骤
const BASE_STEPS = [
  { title: "设置签署方", value: "setSignatory" },
  { title: "完善模板内容", value: "templateContent" },
  { title: "办结", value: "finish" },
]

// 定义不同流程类型的步骤配置
const STEP_CONFIGS = {
  FLOW: [
    { title: "设置签署方", value: "setSignatory" },
    { title: "配置审批流程", value: "setFlowConfig" },
    { title: "完善模板内容", value: "templateContent" },
    { title: "办结", value: "finish" },
  ],
  APPROVAL: BASE_STEPS,
  NO_APPROVAL: BASE_STEPS,
}
const stepOptions = ref([])
const templateInfo = ref({}) // 模版信息
const loading = ref(true)

watch(
  () => draftId.value,
  (val) => {
    if (val) {
      draftId.value = val
      getDraftInfo()
    }
  }
)

// 获取草稿信息
async function getDraftInfo() {
  if (!draftId.value) return
  try {
    loading.value = true
    const res = await getTemplateDraftInfoAPI(draftId.value)
    draftForm.value = res.object
    form.value.flowId = res.object.id
  } finally {
    loading.value = false
  }
}

// 获取模版信息
async function getTempInfo() {
  try {
    loading.value = true
    if (!templateId.value) return
    const res = await getTemplateInfo(templateId.value)
    templateInfo.value = res.object
    await nextTick()
    // 根据流程类型选择步骤配置
    switch (+templateInfo.value.flowFlag) {
      case 2: // 发起人指定流程
        stepOptions.value = STEP_CONFIGS.FLOW
        break
      case 0: // 无须审批
        stepOptions.value = STEP_CONFIGS.NO_APPROVAL
        break
      default:
        // 默认审批流程
        stepOptions.value = STEP_CONFIGS.APPROVAL
    }
    getDraftInfo()
  } finally {
    loading.value = false
  }
}

// 展示固定审批流程
const flowViewModalRef = ref(null)
function showFlowModal() {
  flowViewModalRef.value.open(templateInfo.value.workId)
}

// 保存基础信息
async function submitBaseInfo(closed = false) {
  try {
    const data = await proxy.$refs.fillApplicationInfoRef?.validForm()
    form.value = { ...form.value, ...data }
    await saveDraft()
    !closed && stepIndex.value++
  } catch (error) {}
}

// 保存草稿
async function saveDraft() {
  try {
    loading.value = true
    const res = await saveDraftTemplateAPI(form.value)
    form.value.flowId = res.object
  } finally {
    loading.value = false
  }
}

// 下一步
async function handleStep() {
  if (stepIndex.value === 0) {
    await submitBaseInfo()
  } else if (stepValue.value === "setFlowConfig") {
    const flowRes = await proxy.$refs.setFlowConfigRef.submit()
    form.value.defFlowId = flowRes.defFlowId
    const updateRes = await updateDraftAPI(form.value.flowId, form.value.defFlowId, 2) // 更新发起人指定流程
    form.value.flowId = updateRes.object
    stepIndex.value++
  }
}

watch(stepValue, (val) => {
  if (val === "templateContent") {
    proxy.$refs.editTemplateContentRef?.getEditTemplateUrl()
  }
})

// 填写模版内容成功
function editTemplateSuccess() {
  stepIndex.value++
}

function handleClose() {
  if (stepValue.value === "finish") {
    proxy.$tab.closePage()
    mittBus.emit("refreshTemplateList")
    return
  }
  proxy.$modal
    .confirm(
      "是否将本次编辑内容保存为草稿",
      "可在文件管理-我发起的草稿箱里继续编辑",
      "info",
      true,
      "保存并退出",
      "不保存"
    )
    .then(async () => {
      await submitBaseInfo(true)
      proxy.$modal.msgSuccess("保存成功")
      mittBus.emit("refreshTemplateList")
      proxy.$tab.closePage()
    })
    .catch((action) => {
      if (action === "cancel") {
        if (form.value.flowId) {
          removeDraft(form.value.flowId)
        }
        proxy.$tab.closePage()
      }
    })
}

onMounted(() => {
  getTempInfo()
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;
.white-body-box {
  min-height: calc(#{$base-main-page-height} - 80px);
}
.step-box {
  :deep(.steps-title) {
    font-size: 15px !important;
  }
}

.table-handle-box {
  position: absolute;
  right: 10px;
  top: 10px;
}

.right-opt-box {
  flex-shrink: 0;
  display: flex;
  .btn-box {
    background: rgba(235, 236, 237, 0.4);
    border: 1px solid #d1d1d1;
    @apply flex-center text-14px cursor-pointer rounded-4px w-50px h-30px;
    margin-left: 10px;
  }
}

.main-create-box {
  &.no-bg {
    background: none;
    padding: 0;
  }
}
</style>
