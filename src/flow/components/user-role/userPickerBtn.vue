<template>
  <div class="user-picker-btn flex-x-center">
    <span v-if="selectedNames.length" class="m-r-10px">{{ selectedNames }}</span>
    <el-button type="primary" v-bind="$attrs" @click="openDialog" v-if="!disabled">
      {{ selectedNames.length ? "重新选择" : "选择人员" }}
    </el-button>

    <UserRolePicker
      ref="userPickerRef"
      :isOnlyOne="isOnlyOne"
      :izEssUser="izEssUser"
      :formCreateInject="formCreateInject"
      :deptId="deptId"
      :selectTypes="['user']"
      @onSelectItems="changeSelectItems"
    />
  </div>
</template>

<script setup>
const UserRolePicker = defineAsyncComponent(() =>
  import("@/flow/components/user-role/picker2.vue")
)
const modelValue = defineModel()

const selectedNames = computed(() => {
  return (modelValue?.value || []).map((item) => item.name || item.nickName).join(",")
})

const userPickerRef = ref(null)
const emit = defineEmits(["changePicker"])

const props = defineProps({
  formCreateInject: {
    type: Object,
    default: {},
  },
  isOnlyOne: {
    type: Boolean,
    default: true,
  },
  // 是否是 ess 用户
  izEssUser: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否存在部门id 如果存在---需要过滤部门
  deptId: {
    default: null,
  },
})
const changeSelectItems = (e) => {
  modelValue.value = e
  emit("changePicker", e)
}
const openDialog = () => {
  userPickerRef.value.onOpen(modelValue.value)
}
</script>

<style scoped lang="scss"></style>
