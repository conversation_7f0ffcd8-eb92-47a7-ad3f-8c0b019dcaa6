<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item :label="$t('runJob.flowInstId')" prop="flowInstId">
						<el-input
							v-model="state.queryForm.flowInstId"
							:placeholder="t('runJob.inputFlowInstIdTip')"
							style="max-width: 180px"
							@change="cascadeChange('flowInstId', ['runNodeId'])"
						/>
					</el-form-item>
					<el-form-item :label="$t('runJob.runNodeId')" prop="runNodeId">
						<el-select
							v-model="state.queryForm.runNodeId"
							:placeholder="t('runJob.inputRunNodeIdTip')"
							clearable
							filterable
							style="max-width: 180px"
							@change="cascadeChange('runNodeId', ['runJobId'])"
						>
							<el-option
								v-for="(item, index) in cascadeDic.runNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('runJob.id')" prop="id">
						<el-select
							v-model="state.queryForm.id"
							:placeholder="t('runJob.inputIdTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in cascadeDic.runJobId"
								:key="index"
								:label="item.jobName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('runJob.status')" prop="status">
						<el-select
							v-model="state.queryForm.status"
							:placeholder="t('runJob.inputStatusTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.NODE_STATUS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							{{ $t('common.queryBtn') }}
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							{{ $t('common.resetBtn') }}
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.addBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_runjob_add'"
							icon="Plus"
							type="primary"
							class="ml10"
							@click="formDialogRef.openDialog('add')"
						/>
					</el-tooltip>
					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.delBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_runjob_del'"
							plain
							:disabled="multiple"
							icon="Delete"
							type="primary"
							class="ml10"
							@click="handleDelete(selectObjs)"
						/>
					</el-tooltip>
					<right-toolbar
						v-model:show-search="showSearch"
						:export="'jsonflow_runjob_export'"
						class="ml10"
						style="float: right;margin-right: 20px"
						@export-excel="exportExcel"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" :label="t('runJob.index')" width="40" />
				<el-table-column prop="sort" :label="t('runJob.sort')" show-overflow-tooltip />
				<el-table-column prop="flowInstId" :label="t('runJob.flowInstId')" show-overflow-tooltip />
				<el-table-column prop="defFlowId" :label="t('runJob.defFlowId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.flowInstId"
							:value="scope.row.defFlowId"
							:value-key="'defFlowId'"
							:show-key="'flowName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="runNodeId" :label="t('runJob.runNodeId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.runNodeId"
							:value="scope.row.runNodeId"
							:value-key="'id'"
							:show-key="'nodeName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="jobName" :label="t('runJob.jobName')" show-overflow-tooltip />
				<el-table-column prop="jobType" :label="t('runJob.jobType')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.JOB_USER_TYPE" :value="scope.row.jobType" />
					</template>
				</el-table-column>
				<el-table-column prop="roleId" :label="t('runJob.roleId')">
					<template #default="scope">
						<convert-role-name
							:options="{users: dicData.users, roles: dicData.roles, posts: dicData.posts, depts: dicData.depts }"
							:value="scope.row"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="userId" :label="t('runJob.userId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.userId"
							:value="scope.row.userId"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="startTime" :label="t('runJob.startTime')" show-overflow-tooltip />
				<el-table-column prop="endTime" :label="t('runJob.endTime')" show-overflow-tooltip />
				<el-table-column prop="userKey" :label="t('runJob.userKey')" show-overflow-tooltip />
				<!--                <el-table-column prop="distFlowNodeId" :label="t('runJob.distFlowNodeId')"  show-overflow-tooltip>
                    <template #default="scope">
                        <convert-name :options="state.dicData.runNodeId" :value="scope.row.distFlowNodeId"
                                      :valueKey="'flowNodeId'" :showKey="'nodeName'"></convert-name>
                    </template>
                </el-table-column>-->
				<el-table-column prop="timeout" :label="t('runJob.timeout')" show-overflow-tooltip />
				<el-table-column prop="status" :label="t('runJob.status')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.NODE_STATUS" :value="scope.row.status" />
					</template>
				</el-table-column>
				<!--                <el-table-column prop="suspension" :label="t('runJob.suspension')"  show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.YES_OR_NO" :value="scope.row.suspension"></dict-tag>
                    </template>
                </el-table-column>-->
				<el-table-column prop="suspensionReason" :label="t('runJob.suspensionReason')" show-overflow-tooltip />
				<!--                <el-table-column prop="isSkipRejected" :label="t('runJob.isSkipRejected')"  show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.YES_OR_NO" :value="scope.row.isSkipRejected"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="isNowRun" :label="t('runJob.isNowRun')"  show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.YES_OR_NO" :value="scope.row.isNowRun"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="createUser" :label="t('runJob.createUser')"  show-overflow-tooltip>
                    <template #default="scope">
                        <convert-name :options="state.dicData.createUser" :value="scope.row.createUser"
                                      :valueKey="'userId'" :showKey="'name'"></convert-name>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" :label="t('runJob.createTime')"  show-overflow-tooltip/>-->
				<el-table-column :label="$t('common.action')" width="100">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.viewBtn') }}
							</template>
							<el-button
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								修改
							</template>
							<el-button
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.delBtn') }}
							</template>
							<el-button
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemRunJob">
import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchList, delObjs } from "@/api/jsonflow/run-job"
import { useMessage, useMessageBox } from "@/hooks/message"

import { useI18n } from "vue-i18n"
import { onCascadeChange, onLoadDicUrl, onLoaded } from "@/flow/components/convert-name/convert"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))
const { t } = useI18n()
// 定义查询字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "users" }, { key: "roles" }, { key: "posts" }, { key: "depts" })
const onCascade = onCascadeChange(cascadeDic, { key: "flowInstId", cascades: ["runNodeId"] }, { key: "runNodeId", cascades: ["runJobId"] })
onMounted(() => {
	onLoad(dicData)
})
function cascadeChange(key, cascades) {
	onCascade(state.queryForm, { key: key, cascades: cascades })
}

// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
	onLoaded: onLoaded({ key: "createUser" }, { key: "userId" }, { key: "flowInstId" }, { key: "runNodeId" }),
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	// 清空多选
	selectObjs.value = []
	getDataList()
}

// 导出excel
const exportExcel = () => {
	downBlobFile("/jsonflow/run-job/export", state.queryForm, "run-job.xlsx")
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t("common.delConfirmText"))
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess(t("common.delSuccessText"))
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
}
</script>
