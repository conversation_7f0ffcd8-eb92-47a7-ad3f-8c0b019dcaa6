<template>
  <div class="components-table">
    <el-form
      ref="queryRef"
      :model="queryParams"
      label-width="70px"
      class="white-form-box"
      inline
    >
      <el-form-item label="发起人" prop="createUserName">
        <el-input
          v-model="queryParams.createUserName"
          placeholder="请输入发起人"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="标题" prop="flowTitle">
        <el-input
          v-model="queryParams.flowTitle"
          placeholder="请输入标题"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="工单编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入工单编号"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <dict-select
          v-model="queryParams.status"
          :options="DIC_PROP.MINE_NODE_STATUS"
          value-type="string"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="queryParams.createTimeRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="common-input-width"
          clearable
          :default-time="dateDefaultTime"
        />
      </el-form-item>
      <!-- <el-form-item label="任务分类" prop="belongType">
        <dict-select
          v-model="queryParams.belongType"
          :options="DIC_PROP.BELONG_TYPE_STATUS"
          value-type="string"
          @change="handleBelongTypeChange"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      :has-toolbar="false"
      :data="tableData"
      :loading="loading"
      :total="total"
      :opt-width="180"
      @reload="getList"
    >
      <vxe-column type="seq" width="60" />
      <vxe-column title="工单编号" field="code" min-width="100" show-overflow />
      <vxe-column
        title="发起人"
        field="createUserName"
        min-width="120"
        show-overflow
        :formatter="formatterCreateUserName"
      />
      <vxe-column title="标题" field="flowTitle" min-width="140" show-overflow />
      <vxe-column title="任务状态" field="status" width="100" show-overflow>
        <template #default="scope">
          <dict-tag :options="DIC_PROP.NODE_STATUS" :value="scope.row.status"></dict-tag>
        </template>
      </vxe-column>
      <!-- <vxe-column title="任务分类" field="belongType" min-width="80" show-overflow>
        <template #default="scope">
          <dict-tag
            :options="DIC_PROP.BELONG_TYPE_STATUS"
            :value="scope.row.belongType"
          ></dict-tag>
        </template>
      </vxe-column> -->
      <vxe-column title="发起时间" field="receiveTime" min-width="120" show-overflow />
      <vxe-column title="当前节点" field="nodeName" min-width="100" show-overflow />
      <vxe-column title="当前节点停留时间" field="stayTime" width="140" show-overflow />
      <!-- <vxe-column title="挂起原因" field="suspensionReason" min-width="100" show-overflow /> -->
      <!-- <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '处理',
              click: () => handleJob(row),
              hidden: !(
                row.suspension !== '1' &&
                (row.status === '0' || row.status === '9') &&
                row.belongType !== '2'
              )
            },
            {
              text: '查看',
              click: () => handleJob(row, '0', '1'),
              hidden: !(row.suspension !== '1' && row.belongType === '2' && row.isRead === '0')
            },
            {
              text: '审批过程',
              click: () => handleComment(row),
              permission: ['todo:mineFlow:comment']
            },
            {
              text: '查看流程图',
              click: () => handleFlowPic(row),
              permission: ['todo:mineFlow:viewWorkflowPic']
            },
            {
              text: '签收任务',
              click: () => handleSignForJob(row, '1'),
              hidden: !(row.jobType !== DIC_PROP.JOB_USER_TYPE[0].value),
              permission: ['todo:mineFlow:signForJob']
            },
            {
              text: '激活任务',
              click: () => handleSuspension(row, '0'),
              hidden: row.suspension !== '1',
              permission: ['todo:mineFlow:suspension']
            },
            {
              text: '挂起任务',
              click: () => handleSuspension(row, '1'),
              hidden: row.suspension === '1',
              permission: ['todo:mineFlow:hangup']
            },
            {
              text: '转办任务',
              click: () => handleJobRoleUserId(row, '0'),
              hidden: row.suspension === '1',
              permission: ['todo:mineFlow:turnTo']
            },
            {
              text: '提前结束流程',
              click: () => handleEarlyComplete(row),
              permission: ['todo:mineFlow:cutThrough']
            },
            {
              text: '终止流程',
              click: () => handleTerminateFlow(row),
              permission: ['todo:mineFlow:stopFlow']
            },
            {
              text: '作废流程',
              click: () => handleInvalidFlow(row),
              permission: ['todo:mineFlow:invalid']
            }
          ]"
        />
      </template> -->
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '处理',
              click: () => handleJob(row),
              permission: ['todo:mineFlow:handle'],
              hidden: !(
                row.suspension !== '1' &&
                (row.status === '0' || row.status === '9') &&
                row.belongType !== '2'
              ),
            },
            {
              text: '审批过程',
              click: () => handleComment(row),
              permission: ['todo:mineFlow:comment'],
            },
          ]"
        />
      </template>
    </CustomTable>

    <!-- 查看审批过程 -->
    <el-dialog
      v-model="data.showComment"
      v-if="data.showComment"
      top="20px"
      width="600px"
      title="查看审批过程"
      append-to-body
    >
      <comment :curr-job="data.currJob"></comment>
    </el-dialog>

    <!-- 查看流程图 -->
    <!-- <el-drawer
      class="flow-overflow-drawer"
      direction="rtl"
      append-to-body
      size="90%"
      v-model="data.showFlowPic"
    >
      <flow-photo v-if="data.showFlowPic" :curr-job="data.currJob"></flow-photo>
    </el-drawer> -->

    <!-- <user-role-picker
      ref="userRolePicker"
      :isOnlyOne="true"
      @onSelectItems="onSelectItems"
    /> -->
  </div>
</template>

<script setup lang="ts">
import {
  fetchTodoPage,
  signForJob,
  flowSuspension,
  turnRunJob,
} from "@/api/jsonflow/do-job"
import * as runFlow from "@/api/jsonflow/run-flow"
import { handleFlowPreview } from "@/flow/support/extend"
import { DIC_PROP } from "@/flow/support/dict-prop"
import { useFlowJob } from "@/flow/stores/flowJob"
import useUserStore from "@/store/modules/user"

const { proxy } = getCurrentInstance()

// const UserRolePicker = defineAsyncComponent(
//   () => import("@/flow/components/user-role/picker2.vue")
// )

// const FlowPhoto = defineAsyncComponent(
//   () => import("@/views/jsonflow/flow-design/view.vue")
// )

const Comment = defineAsyncComponent(
  () => import("@/views/jsonflow/comment/timeline.vue")
)
const userInfo = useUserStore().userInfo
const flowJob = useFlowJob()
const $router = useRouter()

const tableData = ref([])
const loading = ref(false)
const total = ref(0)

const queryParams = reactive({
  current: 1,
  size: 10,
  belongType: "-1",
  descs: "receive_time",
})

const data = reactive({
  // 0、转办
  nodeUserType: undefined,
  currJob: undefined,
  showComment: false,
  showFlowPic: false,
  activeName: "-1",
})

async function getList() {
  try {
    loading.value = true
    let res = await fetchTodoPage(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 审批
function handleJob(row, isView?, isRead?) {
  handleFlowPreview($router, row, isView, isRead, true, flowJob, getList)
}

function handleComment(row) {
  data.currJob = row
  data.showComment = true
}
// function handleBelongTypeChange(e) {
//   queryParams.signatureType = null
//   queryParams.belongType = null
//   if (e !== "3") {
//     queryParams.belongType = e
//   } else {
//     queryParams.belongType = "0"
//     // 仅标识
//     queryParams.signatureType = "-1"
//   }
//   handleQuery()
// }

// function handleFlowPic(row) {
//   data.currJob = row
//   data.showFlowPic = true
// }
// // 转办任务
// function handleJobRoleUserId(row, type) {
//   data.currJob = row
//   openJobRoleUserId(type)
// }
// // 签收反签收任务
// function handleSignForJob(row, type) {
//   let obj = { id: row.id, signForType: type }
//   if (type === "0") {
//     // 判断反签收
//     onSignForJob(obj)
//     return
//   }
//   signForJob(obj).then(() => {
//     proxy.$modal.msgSuccess("操作成功")
//     getList()
//   })
// }
// // 反签收任务
// function onSignForJob(obj) {
//   let msg = "是否确认反签收当前任务, 将放回待认领列表?"
//   proxy.$modal
//     .confirm(msg)
//     .then(() => {
//       return signForJob(obj)
//     })
//     .then(() => {
//       proxy.$modal.msgSuccess("操作成功")
//       flowJob.delJobLen()
//       getList()
//     })
// }
// // 挂起激活任务
// function handleSuspension(row, suspension) {
//   if (suspension === "0") {
//     row.suspension = suspension
//     flowSuspension(row).then(() => {
//       proxy.$modal.msgSuccess("操作成功")
//       getList()
//     })
//     return
//   }
//   // 增加挂起原因
//   proxy.$modal
//     .prompt("请输入挂起原因")
//     .then(({ value }) => {
//       row.suspension = suspension
//       row.suspensionReason = value
//       return flowSuspension(row)
//     })
//     .then(() => {
//       proxy.$modal.msgSuccess("操作成功")
//       getList()
//     })
// }
// // 选择参与者
// function openJobRoleUserId(type) {
//   data.nodeUserType = type
//   proxy.$refs.userRolePicker.onOpen()
// }
// function onSelectItems(items) {
//   if (data.nodeUserType === "0") {
//     onTurnRunJob(items[0])
//   }
// }
// // 终止流程
// function handleTerminateFlow(row) {
//   proxy.$modal
//     .prompt("请输入终止理由")
//     .then(({ value }) => {
//       row.jobBtn = DIC_PROP.JOB_BTNS[15].value
//       row.invalidReason = value
//       row.flowStatus = "0"
//       return runFlow.terminateFlow(row)
//     })
//     .then(() => {
//       proxy.$modal.msgSuccess("操作成功")
//       flowJob.delJobLen()
//       getList()
//     })
// }
// // 作废流程
// function handleInvalidFlow(row) {
//   proxy.$modal
//     .prompt("请输入作废理由")
//     .then(({ value }) => {
//       row.jobBtn = DIC_PROP.JOB_BTNS[17].value
//       row.invalidReason = value
//       row.flowStatus = "0"
//       return runFlow.invalidFlow(row)
//     })
//     .then(() => {
//       proxy.$modal.msgSuccess("操作成功")
//       flowJob.delJobLen()
//       getList()
//     })
// }
// function onTurnRunJob(role) {
//   proxy.$modal
//     .confirm('是否确认转办名称为"' + data.currJob.jobName + '"的任务?')
//     .then(() => {
//       data.currJob.jobType = role.jobType
//       data.currJob.roleId = role.roleId
//       data.currJob.jobBtn = DIC_PROP.JOB_BTNS[14].value
//       return turnRunJob(data.currJob)
//     })
//     .then(() => {
//       proxy.$modal.msgSuccess("操作成功")
//       flowJob.delJobLen()
//       getList()
//     })
// }
// // 提前结束流程
// function handleEarlyComplete(row) {
//   proxy.$modal
//     .prompt("请输入提前结束流程理由")
//     .then(({ value }) => {
//       row.jobBtn = DIC_PROP.JOB_BTNS[16].value
//       row.invalidReason = value
//       row.flowStatus = "0"
//       return runFlow.earlyComplete(row)
//     })
//     .then(() => {
//       proxy.$modal.msgSuccess("操作成功")
//       flowJob.delJobLen()
//       getList()
//     })
// }

function formatterCreateUserName({ row }) {
  const deptName = row.deptName ? `(${row.deptName})` : ""
  const workNumber = row.workNumber ? `(${row.workNumber})` : ""
  return `${row.createUserName}${workNumber}${deptName}`
}

getList()
</script>

<style scoped lang="scss"></style>
