import request from "@/utils/request"
import {essPrefix} from "@/config/constant"

// 查询开发文档配置列表 带分页
export function listDevManualPage(query: object) {
    return request({
        url: essPrefix + "/docCfgInfo/listPage",
        method: "post",
        data: query,
    })
}

// 查询开发文档配置列表 带分页（无验证登录）
export function NoAuthListDevManualPage(query: object) {
    return request({
        url: essPrefix + "/docCfgInfo/listPageNoAuth",
        method: "post",
        data: query,
    })
}

// 获取开发文档配置详细信息 （无验证登录）
export function NoAuthGetDevManual(noticeId: string) {
    return request({
        url: essPrefix + `/docCfgInfo/getInfoNoAuth/${noticeId}`,
        method: "get",
    })
}

// 查询校内外访问地址
export function getLinkAddr() {
    return request({
        url: essPrefix + `/docCfgInfo/queryLink`,
        method: "get",
    })
}

// 获取开发文档配置详细信息
export function getDevManual(noticeId: string) {
    return request({
        url: essPrefix + `/docCfgInfo/${noticeId}`,
        method: "get",
    })
}

// 新增开发文档配置
export function addDevManual(data: object) {
    return request({
        url: essPrefix + "/docCfgInfo/add",
        method: "post",
        data: data,
    })
}

// 修改开发文档配置
export function updateDevManual(data: object) {
    return request({
        url: essPrefix + "/docCfgInfo/edit",
        method: "post",
        data: data,
    })
}

// 删除开发文档配置
export function delDevManual(ids: Array) {
    return request({
        url: essPrefix + "/docCfgInfo/remove",
        method: "post",
        data: ids
    })
}
