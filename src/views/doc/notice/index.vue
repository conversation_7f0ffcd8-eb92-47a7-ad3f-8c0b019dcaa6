<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      class="white-form-box"
      label-width="80px"
    >
      <el-form-item label="公告标题" prop="noticeTitle">
        <el-input
          v-model="queryParams.noticeTitle"
          placeholder="请输入公告标题"
          clearable
          @keyup.enter="handleQuery"
          :maxlength="100"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createBy">
        <user-select v-model="queryParams.createBy" />
      </el-form-item>
      <el-form-item label="类型" prop="noticeType">
        <el-select v-model="queryParams.noticeType" placeholder="公告类型" clearable>
          <el-option
            v-for="dict in sys_notice_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" :loading="loading" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :data="tableData"
      custom
      :hasToolbar="false"
      :loading="loading"
      :total="total"
      @reload="getList"
      :opt-width="80"
    >
      <vxe-column type="seq" width="80" />
      <vxe-column field="noticeTitle" title="公告标题" min-width="180" />
      <vxe-column field="noticeType" title="公告类型" width="100">
        <template #default="{ row }">
          <dict-tag :options="sys_notice_type" :value="row.noticeType" />
        </template>
      </vxe-column>
      <vxe-column field="createName" title="创建人" width="100" show-overflow />
      <vxe-column field="createTime" title="发布时间" width="160" />
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '查看',
              click: () => handleView(row),
            },
          ]"
        />
      </template>
    </CustomTable>

    <ViewNoticeModal ref="viewNoticeRef" />
  </div>
</template>

<script setup name="Notice">
import { listNotice } from "@/api/ess/doc/noticeManagementApi"

const ViewNoticeModal = defineAsyncComponent(() =>
  import("./components/ViewNoticeModal.vue")
)

const { proxy } = getCurrentInstance()
const { sys_notice_type } = proxy.useDict("sys_notice_type")

const tableData = ref([])
const loading = ref(true)
const total = ref(0)

const customTableRef = ref(null)

const queryParams = ref({
  page: 1,
  limit: 10,
  noticeTitle: null,
})

/** 查询公告列表 */
function getList() {
  loading.value = true
  listNotice(queryParams.value)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
      loading.value = false
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 查看 */
function handleView(row) {
  proxy.$refs.viewNoticeRef.open(row)
}

getList()
</script>
