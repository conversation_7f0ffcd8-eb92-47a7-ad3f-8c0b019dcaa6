import hasRole from "./permission/hasRole"
import copyText from "./common/copyText"
import realImg from "./common/realImg"
import tooltip from "./common/tooltip"
import preventReclick from "./common/preventReclick"
import { authDirective } from "./permission/authDirective"

export default function directive(app) {
	app.directive("hasRole", hasRole)
	app.directive("copyText", copyText)
	app.directive("realImg", realImg)
	app.directive("tooltip", tooltip)
	app.directive("preventReclick", preventReclick)
	// 用户权限指令
	authDirective(app)
}
