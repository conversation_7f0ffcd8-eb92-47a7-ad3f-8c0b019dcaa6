import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-jsonflow/run-flow/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/run-flow",
		method: "post",
		data: obj,
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-flow/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/cloud-jsonflow/run-flow/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/cloud-jsonflow/run-flow/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-jsonflow/run-flow",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/run-flow",
		method: "put",
		data: obj,
	})
}

// 提前结束流程
export function earlyComplete(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-flow/early-complete",
		method: "put",
		data: obj,
	})
}

// 终止流程
export function terminateFlow(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-flow/terminate",
		method: "put",
		data: obj,
	})
}

// 通过id作废流程管理
export function invalidFlow(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-flow/invalid",
		method: "delete",
		data: obj,
	})
}

// 撤回或重置当前流程
export function recallReset(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-flow/recall-reset",
		method: "put",
		data: obj,
	})
}

// 催办流程
export function remind(data: any) {
	return request({
		url: "/cloud-jsonflow/run-flow/remind",
		method: "put",
		data,
	})
}
// 我的待办 - 进行中
export function onGoingFlowPage(params: any) {
	return request({
		url: "/cloud-jsonflow/runFlowExt/onGoingFlowPage",
		method: "get",
		params,
	})
}
// 我的待办 - 已完结
export function flowFinishPage(params: any) {
	return request({
		url: "/cloud-jsonflow/runFlowExt/finishPage",
		method: "get",
		params,
	})
}
