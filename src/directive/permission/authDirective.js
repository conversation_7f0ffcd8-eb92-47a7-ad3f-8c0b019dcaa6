/**
 * 用户权限指令
 * @directive 单个权限验证（v-auth="xxx"）
 * @directive 多个权限验证，满足一个则显示（v-auths="[xxx,xxx]"）
 * @directive 多个权限验证，全部满足则显示（v-auth-all="[xxx,xxx]"）
 */
// 导入放在文件顶部，避免使用前未定义的问题
import useUserStore from "@/store/modules/user"
import { judgementSameArr } from "@/utils/index"
const all_permission = "*:*:*"
export function authDirective(app) {
	// 单个权限验证（v-auth="xxx"）

	app.directive("auth", {
		mounted(el, binding) {
			const permissions = useUserStore().permissions
			if (permissions.includes(all_permission) || permissions.includes(binding.value)) { return }
			el.parentNode.removeChild(el)
		},
	})

	// 多个权限验证，满足一个则显示（v-auths="[xxx,xxx]"）
	app.directive("auths", {
		mounted(el, binding) {
			const { value } = binding
			const permissions = useUserStore().permissions
			if (value && value instanceof Array && value.length > 0) {
				// 如果是超级管理员则显示
				const hasPermissions = permissions.some(permission => {
					return all_permission === permission || value.includes(permission)
				})
				if (!hasPermissions) {
					el.parentNode && el.parentNode.removeChild(el)
				}
			}
		},
	})

	// 多个权限验证，全部满足则显示（v-auth-all="[xxx,xxx]"）
	app.directive("auth-all", {
		mounted(el, binding) {
			const permissions = useUserStore().permissions
			if (permissions.includes(all_permission) || judgementSameArr(binding.value, permissions)) { return }
			el.parentNode.removeChild(el)
		},
	})
}
