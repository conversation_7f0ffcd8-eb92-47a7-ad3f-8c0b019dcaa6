<template>
  <div class="upload-file">
    <div v-if="!props.disabled">
      <div class="upload-file-box">
        <el-upload
          ref="fileUpload"
          multiple
          :action="uploadFileUrl"
          :accept="accept"
          :before-upload="handleBeforeUpload"
          :file-list="fileList"
          :limit="limit"
          :on-error="handleUploadError"
          :on-exceed="handleExceed"
          :on-success="handleUploadSuccess"
          :show-file-list="false"
          :headers="headers"
          :data="formData"
          class="upload-file-uploader"
          :drag="uploadDragger"
        >
          <!-- 上传按钮 -->
          <el-button v-if="!uploadDragger" class="upload-btn" type="primary" plain>
            {{ btnText }}
          </el-button>
          <!-- 拖拽上传 -->
          <div v-if="uploadDragger" class="upload-drag-box">
            <img src="@/assets/images/file/drag-icon.png" alt="" />
            <div class="upload-drag-text">
              <p>
                将文件拖拽至此区域，或
                <el-link type="primary" underline="always" class="m-t-[-2px] m-l-4px">
                  点击上传
                </el-link>
              </p>
              <p class="tip">
                支持格式{{ fileType.join("/") }}，文件大小在{{ fileSize }}M以内
              </p>
            </div>
          </div>
        </el-upload>
        <!-- 上传提示 -->
        <div v-if="showTip && !uploadDragger" class="el-upload__tip">
          请上传<template v-if="fileSize"
            >大小不超过
            <span :style="highlightTip ? 'color: #f56c6c' : ''">{{ fileSize }}MB</span>
          </template>
          <template v-if="fileType">
            格式为
            <span :style="highlightTip ? 'color: #f56c6c' : ''">{{
              fileType.join("/")
            }}</span>
          </template>
          的文件
        </div>
      </div>
    </div>

    <!-- 文件列表 -->
    <transition-group
      v-if="(showList || props.disabled) && showListType === 'list'"
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        v-for="(file, index) in fileList"
        :key="file.uid"
        class="el-upload-list__item ele-upload-list__item-content"
      >
        <el-link :href="getFullUrl(file.baseUrl)" underline="never" target="_blank">
          <span class="el-icon-document">{{ getFileName(file.name) }}</span>
        </el-link>
        <div class="ele-upload-list__item-content-action" v-if="!props.disabled">
          <el-link underline="never" type="danger" @click="handleDelete(index)">
            删除
          </el-link>
        </div>
      </li>
    </transition-group>

    <div
      class="file-card-list"
      v-if="(showList || props.disabled) && showListType === 'card'"
    >
      <div
        v-for="(file, index) in fileList"
        :key="file.uid"
        class="file-card-list__item"
        :title="getFileName(file.name)"
        :class="{ 'w-60px h-60px': isImage(file.name) }"
      >
        <el-image
          v-if="isImage(file.name)"
          :src="imageApi + getFullUrl(file.baseUrl)"
          :preview-src-list="[imageApi + getFullUrl(file.baseUrl)]"
          fit="contain"
        ></el-image>
        <el-icon v-else class="text-60px! m-l-10px"><Document /></el-icon>
        <el-icon
          v-if="!props.disabled"
          class="el-icon-close"
          @click="handleDelete(index)"
        >
          <CircleCloseFilled />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth"
import { getFileName } from "@/utils/index"
import { uploadBaseUrl, imageApi } from "@/config/constant"
import { genFileId } from "element-plus"

const props = defineProps({
  modelValue: [String, Object, Array],
  // 数量限制
  limit: {
    type: Number,
    default: 1,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => [
      "png",
      "jpg",
      "jpeg",
      "doc",
      "xls",
      "ppt",
      "txt",
      "pdf",
      "docx",
      "xlsx",
      "pptx",
    ],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
  // 是否高亮提示
  highlightTip: {
    type: Boolean,
    default: true,
  },
  showList: {
    type: Boolean,
    default: true,
  },
  btnText: {
    type: String,
    default: "选取文件",
  },
  // 是否可拖拽上传
  uploadDragger: {
    type: Boolean,
    default: false,
  },
  // 上传文件服务器地址
  uploadFileUrl: {
    type: String,
    default: uploadBaseUrl,
  },
  dir: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 展示是否卡片式
  showListType: {
    type: String,
    default: "list", // list / card
  },
})

const { proxy } = getCurrentInstance()
const emit = defineEmits()
const number = ref(0)
const fileUpload = ref(null)
// 添加一个 ref 来存储完整 URL 的映射
const fullUrlMap = ref(new Map())
const uploadList = ref([])
const headers = ref({ Authorization: "Bearer " + getToken() })
const fileList = ref([])
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize))

const accept = computed(() => {
  return props.fileType.length ? props.fileType.map((i) => "." + i).join(",") : "*"
})

// 请求参数处理
const formData = computed(() => {
  return Object.assign(props.data, { dir: props.dir })
})

// 修改 watch 部分
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      let temp = 1
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(",")

      // 然后将数组转为对象数组
      fileList.value = list.map((item) => {
        if (typeof item === "string") {
          const isFullUrl = item.includes("?")
          const baseUrl = isFullUrl ? item.split("?")[0] : item

          // 存储完整URL到映射中
          if (isFullUrl) {
            fullUrlMap.value.set(baseUrl, item)
          }
          item = {
            name: baseUrl,
            url: isFullUrl ? item : baseUrl, // 用于预览的完整URL
            baseUrl: baseUrl, // 用于modelValue的基础URL
          }
        }
        item.uid = item.uid || new Date().getTime() + temp++
        return item
      })

      // 立即更新父组件的值为基础URL（不包含查询参数）
      const baseUrls = fileList.value.map((file) => file.baseUrl).join(",")
      emit("update:modelValue", baseUrls)
    } else {
      fileList.value = []
      fullUrlMap.value.clear()
      return []
    }
  },
  { deep: true, immediate: true }
)

// 判断文件是否为图片类型
function isImage(fileName) {
  const imageExtensions = ["png", "jpg", "jpeg", "gif", "bmp", "webp"]
  const extension = fileName.split(".").pop().toLowerCase()
  return imageExtensions.includes(extension)
}

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split(".")
    const fileExt = fileName[fileName.length - 1]
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0
    if (!isTypeOk) {
      proxy.$modal.msgError(`文件格式不正确，请上传${props.fileType.join("/")}格式文件!`)
      return false
    }
  }
  // 校检文件名是否包含特殊字符
  if (file.name.includes(",")) {
    proxy.$modal.msgError("文件名不正确，不能包含英文逗号!")
    return false
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`)
      return false
    }
  }
  proxy.$modal.loading("正在上传文件，请稍候...")
  number.value++
  return true
}

// 文件个数超出
function handleExceed(files) {
  if (props.limit === 1) {
    uploadList.value = []
    fileUpload.value.clearFiles()
    const file = files[0]
    fileList.value = [file]
    file.uid = genFileId()
    fileUpload.value.handleStart(file)
    fileUpload.value.submit()
    proxy.$modal.msgWarning(`上传文件数量不能超过 ${props.limit} 个! 已默认选中第一个文件。`)
  } else {
    proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
  }
}

// 上传失败
function handleUploadError(err) {
  proxy.$modal.msgError("上传文件失败")
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (+res.code === 200) {
    const fullUrl = res.object.presignedUrl
    const baseUrl = fullUrl.split("?")[0]

    // 存储完整URL到映射中
    fullUrlMap.value.set(baseUrl, fullUrl)
    uploadList.value.push({
      name: res.object.filename,
      url: fullUrl, // 用于预览的完整URL
      baseUrl: baseUrl, // 用于modelValue的基础URL
      fullUrl: res.object.imageUrl, // 保存完整URL用于回显
    })
    uploadedSuccessfully()
  } else {
    number.value--
    proxy.$modal.closeLoading()
    proxy.$modal.msgError(res.msg || res.message)
    proxy.$refs.fileUpload.handleRemove(file)
    uploadedSuccessfully()
  }
}

// 删除文件
function handleDelete(index) {
  const file = fileList.value[index]
  fullUrlMap.value.delete(file.baseUrl)
  fileList.value.splice(index, 1)
  // 发送基础URL给父组件
  const baseUrls = fileList.value.map((file) => file.baseUrl).join(",")
  emit("update:modelValue", baseUrls)
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value
      .filter((f) => f.url !== undefined)
      .concat(uploadList.value)

    uploadList.value = []
    number.value = 0

    // 发送基础URL给父组件
    const baseUrls = fileList.value.map((file) => file.baseUrl).join(",")
    const fileNames = fileList.value.map((file) => file.name).join(",")
    emit("update:modelValue", baseUrls)
    emit("update:fileName", fileNames)
    proxy.$modal.closeLoading()
  }
}

// 优化获取完整URL的方法
function getFullUrl(baseUrl) {
  // 如果已经是完整URL，直接返回
  if (baseUrl.includes("?")) {
    return baseUrl
  }

  // 首先从 fullUrlMap 中获取
  const fullUrl = fullUrlMap.value.get(baseUrl)
  if (fullUrl) {
    return fullUrl
  }

  // 如果 fullUrlMap 中没有，从 fileList 中查找
  const file = fileList.value.find((f) => f.baseUrl === baseUrl)
  if (file?.url) {
    // 找到后存储到 fullUrlMap 中
    fullUrlMap.value.set(baseUrl, file.url)
    return file.url
  }

  return baseUrl
}

const getFullUrlForParent = (baseUrl) => {
  return getFullUrl(baseUrl)
}

// 暴露方法给父组件
defineExpose({
  getFullUrlForParent,
})
</script>

<style scoped lang="scss">
.upload-file {
  width: 100%;
}
.upload-file-uploader {
  margin-bottom: 5px;
  :deep(.el-upload-dragger) {
    padding: 20px 0;
  }
}
.upload-file-list {
  .el-upload-list__item {
    margin-bottom: 10px;
    position: relative;
    border-bottom: 1px dashed #eee;
    padding: 2px 10px;
  }
  .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }
}

.file-card-list {
  display: flex;
  flex-wrap: wrap;
  &__item {
    width: 80px;
    height: 80px;
    margin-right: 10px;
    margin-bottom: 10px;
    position: relative;
    .el-image {
      width: 100%;
      height: 100%;
    }
    .el-icon-close {
      position: absolute;
      top: -5px;
      right: -5px;
      color: red;
      font-size: 20px;
      cursor: pointer;
      background: #fff;
      border-radius: 50%;
    }
  }
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}

.upload-btn {
  min-height: 32px !important;
}

.el-upload__tip {
  color: #aaaaaa;
  font-size: 13px;
  margin-bottom: 5px;
  margin-top: 0;
}

.upload-drag-box {
  display: flex;
  align-items: center;
  height: 100%;
  padding-left: 50px;

  img {
    width: 70px;
  }
  .upload-drag-text {
    font-size: 14px;
    text-align: left;
    p {
      line-height: 20px;
      margin: 0;
      display: flex;
      align-items: center;
    }
    .tip {
      color: #9b9b9b;
      font-size: 12px;
      margin-top: 8px;
    }
  }
  .el-link {
    line-height: 18px;
  }
}
</style>
