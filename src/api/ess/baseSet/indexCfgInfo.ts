import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询首页管理配置列表 带分页
export function indexCfgInfoPage(data: Object) {
	return request({
		url: essPrefix + "/indexCfgInfo/listPage",
		method: "post",
		data,
	})
}
// 查询首页管理配置列表
export function getIndexCfgInfoList() {
	return request({
		url: essPrefix + "/indexCfgInfo/list",
		method: "get",
	})
}

//  新增首页
export function addIndexCfgInfoAPI(data: Object) {
	return request({
		url: essPrefix + "/indexCfgInfo/add",
		method: "post",
		data,
	})
}
// 修改首页
export function updateIndexCfgInfoAPI(data: Object) {
	return request({
		url: essPrefix + "/indexCfgInfo/edit",
		method: "post",
		data,
	})
}

// 删除
export function delIndexCfgInfo(ids: string) {
	return request({
		url: essPrefix + "/indexCfgInfo/" + ids,
		method: "delete",
	})
}
// 查询首页管理配置详细
export function getIndexCfgInfo(id: string) {
	return request({
		url: essPrefix + "/indexCfgInfo/" + id,
		method: "get",
	})
}

// 查询首页配置列表 带分页
export function getIndexCfgListPage(data: Object) {
	return request({
		url: essPrefix + "/indexCfgInfo/listPage",
		method: "post",
		data,
	})
}

