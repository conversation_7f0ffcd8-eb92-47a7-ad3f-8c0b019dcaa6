<template>
  <div class="flow-node-menu">
    <el-menu style="border-right: 0">
      <template v-for="(node, index) in props.menuList" :key="index">
        <el-menu-item
          v-if="isShowNode(node) && node.type !== HighNodeType.CHILD_FLOW"
          style="padding-top: 4px; padding-left: 14px"
          :index="node.name"
        >
          <el-tooltip :content="getNodeName(node)" placement="right">
            <div
              class="el-node-item"
              draggable="true"
              @dragstart="dragNode(node.type, props.type)"
            >
              <img :src="node.icon" alt="node" />
            </div>
          </el-tooltip>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>
<script setup lang="ts" name="NodeMenu">
import { HighNodeType } from "../config/type"
import { validateNull } from "@/utils/validate"

const $emit = defineEmits(["setDragInfo"])
const props = defineProps({
  menuList: {
    type: Array,
    default: () => [],
  },
  flowData: {
    type: Object,
    default: {},
  },
  type: {
    type: String,
    default: null,
  },
})

function isShowNode(node) {
  if (validateNull(props.flowData)) return true
  if (node.type !== HighNodeType.JOB && node.type !== HighNodeType.VIRTUAL) {
    return true
  }
  let isJobSeparated = props.flowData.attrs.isJobSeparated
  return isJobSeparated === "1"
}

function getNodeName(node) {
  return node.nodeName + (node.nodeDesc ? node.nodeDesc : "")
}

// 开始拖拽
function dragNode(type, belongTo) {
  $emit("setDragInfo", {
    type,
    belongTo,
  })
}
</script>
<style scoped lang="scss">
/*菜单间距*/
.flow-node-menu {
  .el-menu-item {
    padding: 11px !important;
    height: 40px !important;
  }

  .el-node-item {
    height: 32px;
    width: 32px;
    color: #fff;
    border-radius: 5px;
    line-height: 30px;
    text-align: center;
    cursor: move;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #0960bd;
      outline: 1px dashed #0960bd;
    }
  }
}
</style>
