<template>
  <div class="app-container home">
    <div class="card-flex">
      <div class="left-box">
        <FilesService v-if="+zsUserInfo.izRealAuth === 1" />
        <TodoNav />
        <ModuleNav title="常用流程" :module-type="0" />
        <ModuleNav title="工具箱" :module-type="2" />
        <ModuleNav title="实体印章" :module-type="1" />
      </div>
      <div class="right-box">
        <NoticeMsgList />
        <CommonCard title="使用情况">
          <UseCondition />
        </CommonCard>
      </div>
    </div>
  </div>
</template>

<script setup name="Index">
import FilesService from "@/views/home/<USER>"
import TodoNav from "@/views/home/<USER>"
import ModuleNav from "@/views/home/<USER>"
import NoticeMsgList from "@/views/home/<USER>"
import UseCondition from "@/views/home/<USER>"
// import PieGraphChart from "@/views/dashboard/PieGraphChart.vue"
import CommonCard from "@/views/components/CommonCard.vue"
import useUserStore from "@/store/modules/user"

const { zsUserInfo } = useUserStore()
</script>

<style scoped lang="scss">
.home {
  min-width: 1100px;
}

.card-flex {
  display: flex;
  justify-content: space-between;
  .left-box {
    flex: auto;
    width: calc(70% - 16px);
  }
  .right-box {
    width: 30%;
    max-width: 460px;
    min-width: 400px;
    flex-shrink: 0;
    margin-left: 16px;
  }
}
</style>
