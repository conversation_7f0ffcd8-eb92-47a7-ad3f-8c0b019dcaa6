<template>
  <div class="app-container">
    <el-tag
        v-for="(item, index) in linkList"
        class="link-tab"
        round
        size="large"
        @click="handleCopyLinkAddr(item)"
    >
      <SvgIcon class="link-icon mr-5px" name="local-link-url" :size="20" />
      {{ item.linkName }}
      <span>{{ item.linkAddr }}</span>
      <el-tooltip
          content="复制网址"
          placement="bottom"
          effect="dark"
      >
        <SvgIcon class="link-icon ml-30px! icon-copy" name="local-copy" :size="20" />
      </el-tooltip>
    </el-tag>
    <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
        class="white-form-box"
        label-width="90"
    >
      <el-form-item label="文档名称" prop="docName">
        <el-input
            v-model="queryParams.docName"
            placeholder="请输入名称"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作人员" prop="createBy">
        <user-select v-model="queryParams.createBy"/>
      </el-form-item>
      <el-form-item label="类型" prop="status">
        <dict-select
            v-model="queryParams.status"
            :options="sys_normal_disable"
            value-type="string"
        />
      </el-form-item>
      <el-form-item label="是否对外" prop="foreignFlag">
        <dict-select
            v-model="queryParams.foreignFlag"
            :options="ess_yes_no"
            value-type="string"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" :loading="loading" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="Refresh" :loading="loading" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <CustomTable
        ref="customTableRef"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :has-toolbar="false"
        :data="noticeList"
        :loading="loading"
        :total="total"
        :opt-width="100"
        @reload="getList"
    >
      <template #actions>
        <el-button
            type="primary"
            icon="plus"
            @click="handleAdd"
            v-auths="['ess:docCfgInfo:add']"
        >
          新增
        </el-button
        >
        <el-button
            v-auths="['ess:docCfgInfo:remove']"
            type="danger"
            plain
            @click="handleDelete"
            :disabled="!selections.length"
        >
          删除
        </el-button>
      </template>
      <vxe-column type="checkbox" width="60" fixed="left"/>
      <vxe-column type="seq" width="60"/>
      <vxe-column title="文档名称" field="docName" show-overflow/>
      <vxe-column title="创建时间" field="createTime" show-overflow width="150"/>
      <vxe-column title="文档附件" field="fileName" show-overflow>
        <template #default="{ row }">
          <span class="color-primary pointer" @click="downLoadDoc(row)">{{
              row.fileName
            }}</span>
        </template>
      </vxe-column>
      <vxe-column title="创建人" field="createName" show-overflow/>
      <vxe-column title="版本号" field="version" show-overflow/>
      <vxe-column title="状态" field="status" show-overflow>
        <template #default="{ row }">
          <dict-tag :options="sys_normal_disable" :value="row.status"/>
        </template>
      </vxe-column>
      <vxe-column title="是否对外" field="foreignFlag" show-overflow>
        <template #default="{ row }">
          <dict-tag :options="ess_yes_no" :value="row.foreignFlag"/>
        </template>
      </vxe-column>
      <vxe-column title="排序" field="sort" show-overflow/>
      <template #opts="{ row }">
        <TableColOptBtn
            :key="Math.random()"
            :buttons="[
            {
              text: '编辑',
              click: () => handleUpdate(row),
              permission: ['ess:docCfgInfo:edit'],
            },
            {
              text: '删除',
              type: 'danger',
              click: () => handleDelete(row),
              permission: ['ess:docCfgInfo:remove'],
            },
          ]"
        />
      </template>
    </CustomTable>

    <!--    编辑、新增-->
    <EditDevManualModal
        ref="editDevManualRef"
        :sys_normal_disable="sys_normal_disable"
        :ess_yes_no="ess_yes_no"
        @change="handleQuery"
        @refresh="getList"
    />
  </div>
</template>

<script setup name="DevManual">
import EditDevManualModal from "./components/EditDevManualModal.vue"
import { listDevManualPage, delDevManual,getLinkAddr } from "@/api/ess/doc/devManualApi.ts"
import { startDownload } from "@/api/ess/downloadCenter/downloadCenter.ts"
import { handleBlobFile } from "@/utils"

const {proxy} = getCurrentInstance()
const {ess_yes_no, sys_normal_disable} = proxy.useDict(
    "ess_yes_no",
    "sys_normal_disable"
)

const noticeList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const editDevManualRef = ref(null)

const linkList = ref([
  {
    linkName: "校内链接地址：",
    linkAddr: "http://127.0.0.1:32767/123",
  },
  {
    linkName: "校外链接地址：",
    linkAddr: "http://127.0.0.1:32767/456",
  }
])
const queryParams = ref({
  page: 1,
  limit: 10,
  docName: null,
  createName: null,
  status: null,
})

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

/** 查询公告列表 */
function getList() {
  loading.value = true
  listDevManualPage(queryParams.value).then((response) => {
    noticeList.value = response.object.records
    total.value = response.object.total
    loading.value = false
  })
}

/** 查询校内网地址 */
async function getLinkAddress() {
  const res = await getLinkAddr()
  linkList.value[0].linkAddr = res.object.insideUrl
  linkList.value[1].linkAddr = res.object.outsideUrl
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
  getLinkAddress()
}

/** 新增按钮操作 */
function handleAdd() {
  editDevManualRef.value.open()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const id = row && row.id !== undefined ? row.id : selections.value[0].id
  editDevManualRef.value.open(row && row.id !== undefined ? row : {id})
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row?.id ? [row.id] : selections.value.map((item) => item.id)
  const titles = row.docName || selections.value.map((item) => item.docName).join(",")
  proxy.$modal
      .confirm('是否确认删除开发手册"' + titles + '"的数据项？')
      .then(function () {
        return delDevManual(ids)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {
      })
}

/** 开发文档 */
function handleCopyLinkAddr(item) {
  const textarea = document.createElement('textarea');
  textarea.value = item.linkAddr;
  textarea.style.position = 'fixed';
  document.body.appendChild(textarea);
  textarea.focus();
  textarea.select();
  try {
    document.execCommand('copy');
    proxy.$modal.msgSuccess("复制成功")
  } catch (err) {
    console.error('Fallback copy failed:', err);
    proxy.$modal.msgError("复制失败")
  }
  document.body.removeChild(textarea);
}


/** 点击文档下载 */
function downLoadDoc(row) {
  startDownload(row.fileUrl).then((res) => {
    handleBlobFile(res.data, row.fileName)
  })
}

getList()
getLinkAddress()
</script>
<style lang="scss" scoped>
.link-tab {
  @apply cursor-pointer;
  border: none;
  font-size: 15px;
  background-color: #efefef;
  margin: 10px 10px 10px 0;

  span {
    color: #000000;
  }

  .link-icon {
    @apply ml-5px w-17px! h-17px!;
    fill: var(--el-color-primary);
    outline: none;
  }

  .icon-copy {
    fill: #bcbcbc !important;
  }

  &:hover {
    background-color: var(--el-color-primary);
    color: #FFFFFF;

    .link-icon {
      fill: #FFFFFF !important;
    }

    span {
      color: #FFFFFF;
    }

  }
}

:deep(.el-tag__content) {
  display: flex;
  align-items: center;
}
</style>
