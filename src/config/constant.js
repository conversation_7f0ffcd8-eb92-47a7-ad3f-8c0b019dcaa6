/*
 * @Description: 接口配置
 * @Autor: ljn
 * @Date: 2022-06-15 08:59:34
 * @LastEditors: ljn
 * @LastEditTime: 2025-06-18 17:31:48
 */

const {
  VITE_BUCKET_NAME,
  VITE_IMAGE_PATH,
  VITE_APP_BASE_API
} = import.meta.env

export const eduUser = "/edu-user"
export const eduMinio = "/edu-minio"
export const auth = "/edu-auth"
export const essPrefix = "/electronic-seal"
export const orderPrefix = "/cloud-order"
export const flowPrefix = "/cloud-jsonflow"
export const eduJob = "/edu-job"
export const bucketName = VITE_BUCKET_NAME // 桶名
export const imageApi = `${VITE_IMAGE_PATH}/` // 图片路径
// 文件上传地址
export const uploadBaseUrl = VITE_APP_BASE_API + eduMinio + "/minio/getFileUrl"
// 文件下载地址
export const downloadBaseUrl = eduMinio + "/minio/download?filename="

// 流程用的key维护
export const flowConfigKey = {
  SEAL_ENABLE_FLOW_KEY: "SEAL_ENABLE_FLOW_KEY", // 印章启用
  SEAL_DISABLE_FLOW_KEY: "SEAL_DISABLE_FLOW_KEY", // 印章停用
  FILE_FLOW_STABLE: "FILE_FLOW_STABLE_ID", // 创建模版固定流程
  FILE_FLOW_STABLE: "FILE_FLOW_STABLE_ID", // 创建模版固定流程
  TEMPLATE_ENABLE_FLOW: "TEMPLATE_ENABLE_FLOW_ID", // 模版启用流程
  FILE_FLOW_STABLE_ID: "FILE_FLOW_STABLE_ID", // 发起文件的固定流程
}
