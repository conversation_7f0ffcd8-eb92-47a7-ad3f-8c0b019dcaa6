<!---->
<template>
  <div class="index-menu-select">
    <el-popover
      v-model:visible="showPopover"
      placement="bottom-start"
      title="选择图标"
      width="500px"
      trigger="click"
      :disabled="disabled"
    >
      <template #reference>
        <div class="opt-box">
          <img
            v-if="modelValue && !customize"
            class="select flex-shrink-0 block"
            :src="iconUrl"
            :style="{ height: height + 'px' }"
          />
          <el-button v-if="!disabled" type="primary" plain>
            {{ iconUrl ? "更改图标" : "选择图标" }}
          </el-button>
        </div>
      </template>
      <div class="popover-icon-box">
        <img
          v-for="item in list"
          :key="item.iconName"
          class="item"
          :class="{ active: item.iconName == modelValue }"
          :src="item && item.icon"
          alt=""
          @click="handleSelect(item)"
        />
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import iconList from "./requireIcon"
import iconFlowList from "./requireFlowIcon"

const props = defineProps({
  modelValue: {
    default: "",
  },
  disabled: {
    type: Boolean,
    default: false, // 是否禁用
  },
  type: {
    type: String,
    default: "menu", // 图标类型  menu | flow
  },
  customize: {
    type: Boolean,
    default: false, // 是否为自定义
  },
  height: {
    type: Number,
    default: 50,
  },
})

const emit = defineEmits(["update:modelValue", "change"])

const showPopover = ref(false)

const list = computed(() => {
  return props.type === "menu" ? iconList : iconFlowList
})

const iconUrl = computed(() => {
  return props.modelValue && list.value.find((i) => i.iconName == props.modelValue)?.icon
})

const handleSelect = (e) => {
  emit("update:modelValue", e.iconName)
  emit("change", e.iconName)
  showPopover.value = false
}
</script>

<style lang="scss" scoped>
.opt-box {
  display: flex;
  align-items: center;

  img {
    margin-right: 20px;
    width: 50px;
    height: 50px;
    object-fit: contain;
  }
}
</style>

<style lang="scss">
.popover-icon-box {
  display: grid;
  grid-template-columns: repeat(7, 50px);
  grid-gap: 20px;
  width: 505px;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px 0;

  .item {
    width: 50px;
    height: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.5s;
    object-fit: contain;

    &:hover,
    &.active {
      transform: scale(1.2);
    }
  }
}
</style>
