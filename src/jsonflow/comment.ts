import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/jsonflow/comment/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/jsonflow/comment",
		method: "post",
		data: obj,
	})
}

export function getObj(id?: string) {
	return request({
		url: "/jsonflow/comment/" + id,
		method: "get",
	})
}

export function tempStore(obj?: any) {
	return request({
		url: "/jsonflow/comment/temp-store",
		method: "post",
		data: obj,
	})
}

export function delObj(id?: any) {
	return request({
		url: "/jsonflow/comment/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/jsonflow/comment",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/jsonflow/comment",
		method: "put",
		data: obj,
	})
}

export function fetchComment(query?: any) {
	return request({
		url: "/jsonflow/comment/comment",
		method: "get",
		params: query,
	})
}
