<template>
  <div class="history-wrapper">
    <div class="history-wrapper__title ml-10px">
      <span class="relative z20">历史配置</span>
    </div>
    <div class="ml-10px text-14px">
      <CustomTabs
        v-model="currentTab"
        :options="historyTabs"
        tabType="solid"
        @tab-click="tabClick"
      />
    </div>
    <div class="history-wrapper__content" v-loading="loading">
      <div
        class="card-item"
        :class="{ active: item.defFlowId == activeId }"
        v-for="item in list"
        :key="item.defFlowId"
        @click="changeDefFlowId(item)"
      >
        <img src="@/assets/images/icon/flow-his-icon.png" alt="" />
        <div class="name" v-tooltip>{{ item.templateName }}</div>
        <div class="date">{{ item.flowOperateTime }}</div>
      </div>
      <div v-if="!list.length && !loading">
        <CustomEmpty />
      </div>
    </div>

    <FlowViewModal ref="flowViewModalRef" :title="flowInfo.templateName">
      <template #footer>
        <div class="flex-x-between">
          <div>创建时间：{{ flowInfo.flowOperateTime }}</div>
          <div>
            <el-button w-80px @click="handleClose">取消</el-button>
            <el-button w-80px type="primary" @click="handleConfirm">引用</el-button>
          </div>
        </div>
      </template>
    </FlowViewModal>
  </div>
</template>

<script setup>
const FlowViewModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowViewModal.vue")
)
defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  historyAPI: {
    type: Function,
  },
  hisExtData: {
    type: Object,
  },
})
// 定义历史列表数据结构
const historyTabs = computed(() => [
  { label: "我的历史配置", value: 0 },
  { label: "他人为我配置", value: 1 },
])
const currentTab = ref(0)
const loading = ref(true)
const list = ref([])
const flowInfo = ref({}) // 当前选择的历史配置信息
const activeId = ref(null)
async function getList() {
  loading.value = true
  const res = await props.historyAPI({
    mine: currentTab.value,
    ...props.hisExtData,
  })
  list.value = res.object
  loading.value = false
}

// tabClick切换
async function tabClick() {
  list.value = []
  await nextTick()
  getList()
}

const emit = defineEmits(["change"])
// 切换流程
const flowViewModalRef = ref(null)
function changeDefFlowId(item) {
  flowInfo.value = item
  flowViewModalRef.value.open(item.defFlowId)
}
// 关闭
function handleClose() {
  flowViewModalRef.value.handleClose()
}

function handleConfirm() {
  emit("change", flowInfo.value.defFlowId)
  activeId.value = flowInfo.value.defFlowId
  handleClose()
}

getList()
</script>

<style scoped lang="scss">
.history-wrapper {
  background: #fff;
  border-radius: 10px;
  padding: 15px 10px;
  width: 400px;
  flex-shrink: 0;
  height: 100%;
  margin-right: 15px;
  &__title {
    @apply title-line-b  before:(bg-[var(--el-color-primary-light-6)] bottom-0px) mb-15px;
  }
  &__content {
    min-height: 60vh;
    margin-top: -5px;
    .card-item + .card-item {
      border-top: 1px solid rgba(112, 112, 112, 0.06);
    }
    .card-item {
      display: flex;
      font-size: 14px;
      padding: 10px 12px;
      cursor: pointer;
      border-radius: 5px;
      justify-content: space-between;
      align-items: center;
      &:hover,
      &.active {
        background: #eff8f5;
        position: relative;
        &::after {
          content: "";
          position: absolute;
          top: 0;
          width: 100%;
          height: 1px;
          background-color: #fff;
          top: -1px;
          left: 0;
        }
      }
      img {
        width: 14px;
        flex-shrink: 0;
        object-fit: contain;
        margin-right: 5px;
      }
      .name {
        flex: auto;
        max-width: 205px;
      }
      .date {
        color: #9b9b9b;
        flex-shrink: 0;
      }
    }
  }
}
</style>
