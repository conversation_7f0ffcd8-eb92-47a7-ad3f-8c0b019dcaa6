<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="850px"
      :before-close="handleClose"
      top="8vh"
  >
    <el-form
        ref="formRef"
        v-loading="loading"
        :model="form"
        label-width="120px"
        :rules="rules"
    >
      <el-form-item label="文档名称" prop="docName">
        <el-input v-model="form.docName" placeholder="请输入公告标题" clearable/>
      </el-form-item>
      <el-row>
        <el-col :span="7">
          <el-form-item label="是否对外开放" prop="foreignFlag">
            <dict-select
                v-model="form.foreignFlag"
                :options="ess_yes_no"
                value-type="string"
                type="radio"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <dict-select
                v-model="form.status"
                :options="sys_normal_disable"
                value-type="string"
                type="radio"
            />
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="版本号" prop="version">
            <el-input
                v-model="form.version"
                placeholder="请输入版本号"
                clearable
                :maxlength="100"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort" :min="0"/>
      </el-form-item>
      <el-form-item label="文档内容" prop="docContent">
        <editor v-model="form.docContent" :min-height="192"/>
      </el-form-item>
      <el-form-item label="文档附件" prop="fileUrl">
        <DocFileUpload v-model="form.fileUrl" v-model:fileName="form.fileName"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 取消</el-button>
      <el-button type="success" :loading="loading" @click="submitAndAdd">
        保存并新建
      </el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  getDevManual,
  addDevManual,
  updateDevManual,
} from "@/api/ess/doc/devManualApi.ts"
import Editor from "@/components/Editor/index.vue"
import { deleteMinio } from "@/api/file/file.ts"
import { rule } from "@/utils/validate.js";

const DocFileUpload = defineAsyncComponent(() =>
    import("@/views/doc/components/DocFileUpload.vue")
)

const { proxy } = getCurrentInstance()

const title = ref("新增")
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)

const props = defineProps({
  ess_yes_no: Array,
  sys_normal_disable: Array,
})

const form = ref({
  docName: null,
  foreignFlag: null,
  status: null,
  fileUrl: null,
  sort: 0,
  fileName: null,
  version: null,
})

const rules = ref({
  docName: [
    { required: true, message: "请输入文档名称", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  foreignFlag: [{ required: true, message: "请选择是否对外开放", trigger: "change" }],
  docContent: [{ required: true, message: "请输入文档内容", trigger: "change" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  version: [{ required: true, message: "请输入版本号", trigger: "change" }],
  sort: [{ required: true, message: "请填入排序", trigger: "change" }],
  fileUrl: [{ required: true, message: "请上传文档附件", trigger: "change" }],
})
const emit = defineEmits(["change", "refresh"])

const open = (item) => {
  dialogVisible.value = true
  title.value = item?.id ? "编辑" : "新增"
  nextTick(() => {
    if (item?.id) {
      form.value = { ...form, ...item, oldUrl: item.fileUrl }
      getDevManualInfo(item.id)
    }
  })
}

function getDevManualInfo(id) {
  getDevManual(id)
      .then((res) => {
        form.value = { ...form, ...res.object, oldUrl: res.object.fileUrl }
      })
      .finally(() => {
        loading.value = false
      })
}

// 取消
const handleClose = () => {
  proxy.resetForm("formRef")
  dialogVisible.value = false
}

// 保存并新建
const submitAndAdd = () => {
  submitForm("add")
}

// 确定
const submitForm = (type = "save") => {
  proxy.$refs["formRef"].validate(async (valid) => {
    if (!valid) {
      return
    }

    loading.value = true

    // 防止循环序列化报错
    const submitData = {
      docName: form.value.docName,
      foreignFlag: form.value.foreignFlag,
      docContent: form.value.docContent,
      status: form.value.status,
      fileUrl: form.value.fileUrl,
      sort: form.value.sort,
      fileName: form.value.fileName,
      version: form.value.version,
    }

    if (form.value.id) {
      submitData.id = form.value.id
    }

    const actionUrl = form.value.id ? updateDevManual : addDevManual
    if (form.value.id && form.value.fileUrl !== form.value.oldUrl) {
      deleteOldFile() // 删除旧文件
    }
    try {
      const res = await actionUrl(submitData)
      if (+res.code === 200) {
        handleSuccess(type)
      }
    } catch (error) {
      console.error("提交表单时出错:", error)
    } finally {
      loading.value = false
    }
  })
}

const handleSuccess = (type) => {
  const message = form.value.id ? "修改成功" : "新增成功"
  proxy.$modal.msgSuccess(message)
  // 保存并新增
  if (type === "add") {
    title.value = "新增"
    proxy.resetForm("formRef")
    form.value.id = null
    emit("change")
  } else {
    handleClose()
    form.value.id ? emit("change") : emit("refresh")
  }
}

const deleteOldFile = async () => {
  try {
    await deleteMinio({ filename: form.value.oldUrl })
  } catch (err) {
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
