<template>
  <div class="app-container">
    <CustomTabs v-model="currentTab" :options="tabs" />

    <CompanyContactTable v-if="currentTab === 0" />
    <PersonalContactTable v-if="currentTab === 1" />
  </div>
</template>

<script setup>
const CompanyContactTable = defineAsyncComponent(() =>import('@/views/baseSet/contactManage/components/CompanyContactTable.vue'))
const PersonalContactTable = defineAsyncComponent(() =>import('@/views/baseSet/contactManage/components/PersonalContactTable.vue'))
const currentTab = ref(0)
const tabs = [{ label: "企业联系人", value: 0 }, { label: "个人联系人", value: 1 }]
</script>

<style scoped lang="scss"></style>
