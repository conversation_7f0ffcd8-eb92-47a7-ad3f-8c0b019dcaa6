<template>
  <div class="app-container">
    <div class="components-table">
      <el-form
        ref="queryRef"
        :model="queryParams"
        label-width="100px"
        class="white-form-box"
        inline
      >
        <el-form-item label="用印材料名称" label-width="100" prop="sealMaterialName">
          <el-input
            v-model="queryParams.sealMaterialName"
            placeholder="请输入用印材料名称"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="申请人" prop="startUserName">
          <el-input
            v-model="queryParams.startUserName"
            placeholder="请输入申请人"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="印章名称" prop="sealName">
          <el-input
            v-model="queryParams.sealName"
            placeholder="请输入印章名称"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="用印时间" prop="stampTimeRange">
          <el-date-picker
            v-model="queryParams.stampTimeRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="common-input-width"
            clearable
            :default-time="dateDefaultTime"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <CustomTable
        ref="customTableRef"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :has-toolbar="false"
        :data="tableData"
        :loading="loading"
        :total="total"
        :opt-width="180"
        @reload="getList"
      >
        <template #actions>
          <el-button
            type="primary"
            v-auths="['ess:sealUsed:export']"
            @click="handleExport"
          >
            导出
          </el-button>
        </template>
        <vxe-column type="seq" width="60" />
        <vxe-column
          title="用印材料名称"
          field="sealMaterialName"
          min-width="140"
          show-overflow
        />
        <vxe-column title="申请人" field="startUserName" min-width="140" show-overflow />
        <vxe-column title="审批人" field="endUserName" min-width="140" show-overflow />
        <vxe-column title="审批时间" field="approvalTime" min-width="140" show-overflow />
        <vxe-column title="印章名称" field="sealName" min-width="140" show-overflow />
        <vxe-column title="用印时间" field="stampTime" min-width="100" show-overflow />
        <vxe-column title="申请原由" field="applyReason" min-width="100" show-overflow />
        >
      </CustomTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="UseDetail">
import { getSealUsedDetailListAPI } from "@/api/ess/dataStatistics/sealUsedAndDetail.ts"
import { essPrefix } from "@/config/constant"

const { proxy } = getCurrentInstance()
const { seal_type } = proxy.useDict("seal_type")

const tableData = ref([])
const loading = ref(false)
const total = ref(0)

const queryParams = reactive({
  page: 1,
  limit: 10,
  sealMaterialName: "",
  startUserName: "",
  sealName: "",
  stampTimeRange: [],
})

async function getList() {
  try {
    loading.value = true
    queryParams.stampTime = queryParams.stampTimeRange?.[0]
    queryParams.stampEndTime = queryParams.stampTimeRange?.[1]
    let res = await getSealUsedDetailListAPI(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    essPrefix + "/sealUsed/export",
    {
      ...queryParams,
    },
    `印章使用详情统计_${new Date().getTime()}.xlsx`
  )
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

getList()
</script>

<style scoped lang="scss"></style>
