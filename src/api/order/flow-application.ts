import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-order/flow-application/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-order/flow-application",
		method: "post",
		data: obj,
    headers:{
      hiddenError: true,
      repeatSubmit: false,
    }
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/cloud-order/flow-application/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/cloud-order/flow-application/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/cloud-order/flow-application/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-order/flow-application",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-order/flow-application",
		method: "put",
		data: obj,
	})
}

export function listByPerms(query: any) {
	return request({
		url: "/cloud-order/flow-application/list/perm",
		method: "get",
		params: query,
	})
}

// 通过配置key查询表单
export function fetchByConfig(configKey: any) {
	return request({
		url: "/cloud-order/flow-application/fetchByConfig?configKey=" + configKey,
		method: "get",
	})
}

// 通过流程id查询表单
export function queryByDefFlowId(defFlowId?: string) {
	return request({
		url: "/cloud-order/flowApplicationExt/queryByDefFlowId",
		method: "get",
		params: {defFlowId},
	})
}
