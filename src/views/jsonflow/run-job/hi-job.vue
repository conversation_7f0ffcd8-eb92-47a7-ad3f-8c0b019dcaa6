<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row v-show="showSearch">
        <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
          <el-form-item :label="$t('runFlow.initiatorId')" prop="initiatorId">
            <el-select
              v-model="state.queryForm.initiatorId"
              :placeholder="t('runFlow.inputInitiatorIdTip')"
              clearable
              filterable
              style="max-width: 180px;"
            >
              <el-option
                v-for="(item, index) in dicData.initiatorId"
                :key="index"
                :label="item.name"
                :value="item.userId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('runFlow.code')" prop="flowInstId">
            <el-select
              v-model="state.queryForm.flowInstId"
              :placeholder="t('runFlow.inputCodeTip')"
              clearable
              filterable
              @change="cascadeChange('flowInstId', ['runNodeId'])"
              style="max-width: 180px;"
            >
              <el-option
                v-for="(item, index) in dicData.flowInstId"
                :key="index"
                :label="item.code"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('runJob.runNodeId')" prop="runNodeId">
            <el-select
              v-model="state.queryForm.runNodeId"
              :placeholder="t('runJob.inputRunNodeIdTip')"
              clearable
              filterable
              style="max-width: 180px;"
              @change="cascadeChange('runNodeId', ['runJobId'])"
            >
              <el-option
                v-for="(item, index) in cascadeDic.runNodeId"
                :key="index"
                :label="item.nodeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('runJob.id')" prop="id">
            <el-select
              v-model="state.queryForm.id"
              :placeholder="t('runJob.inputIdTip')"
              clearable
              filterable
              style="max-width: 180px;"
            >
              <el-option
                v-for="(item, index) in cascadeDic.runJobId"
                :key="index"
                :label="item.jobName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('runJob.status')" prop="status">
            <el-select
              v-model="state.queryForm.status"
              :placeholder="t('runJob.inputStatusTip')"
              clearable
              filterable
              style="max-width: 180px;"
            >
              <el-option
                v-for="(item, index) in DIC_PROP.NODE_STATUS"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('runJob.flowStatus')" prop="status">
            <el-select
              v-model="state.queryForm.flowStatus"
              :placeholder="t('runJob.inputFlowStatusTip')"
              clearable
              filterable
              style="max-width: 180px;"
            >
              <el-option
                v-for="(item, index) in DIC_PROP.FLOW_STATUS"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="search" type="primary" @click="getDataList">
              {{ $t('common.queryBtn') }}
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="mb8" style="width: 100%;">
          <el-tabs v-model="data.activeName" class="demo-tabs" @tab-click="methods.handleTabClick">
            <el-tab-pane label="全部" name="-1"></el-tab-pane>
            <el-tab-pane label="已办理" name="0"></el-tab-pane>
            <el-tab-pane label="被抄送" name="1"></el-tab-pane>
            <el-tab-pane label="已阅" name="2"></el-tab-pane>
            <el-tab-pane label="被加签" name="3"></el-tab-pane>
          </el-tabs>
          <right-toolbar
            v-model:showSearch="showSearch"
            :export="'jsonflow_runjob_export'"
            @exportExcel="exportExcel"
            class="ml10"
            style="float: right; margin-right: 20px;"
            @queryTable="getDataList"
          ></right-toolbar>
        </div>
      </el-row>
      <el-table
        :data="state.dataList"
        v-loading="state.loading"
        style="width: 100%;"
        @sort-change="sortChangeHandle"
      >
        <el-table-column type="index" :label="t('runJob.index')" width="40" />
        <el-table-column prop="initiatorId" :label="t('runFlow.initiatorId')" show-overflow-tooltip>
          <template #default="scope">
            <convert-name
              :options="state.dicData.initiatorId"
              :value="scope.row.initiatorId"
              :valueKey="'userId'"
              :showKey="'name'"
            ></convert-name>
          </template>
        </el-table-column>
        <el-table-column prop="code" :label="t('runFlow.code')" show-overflow-tooltip />
        <el-table-column
          prop="flowName"
          :label="t('runJob.defFlowId')"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="orderId"
          :label="t('flowApplication.formName')"
          show-overflow-tooltip
        >
          <template #default="scope">
            <convert-name
              :options="state.dicData.orderId"
              :value="scope.row.orderId"
              :valueKey="'id'"
              :showKey="'formName'"
            ></convert-name>
          </template>
        </el-table-column>
        <!--                <el-table-column prop="runNodeId" :label="t('runJob.runNodeId')" show-overflow-tooltip>
                    <template #default="scope">
                        <convert-name :options="state.dicData.runNodeId" :value="scope.row.runNodeId"
                                      :valueKey="'id'" :showKey="'nodeName'"></convert-name>
                    </template>
                </el-table-column>-->
        <el-table-column prop="jobName" :label="t('runJob.jobName')" show-overflow-tooltip />
        <el-table-column prop="jobType" :label="t('runJob.jobType')" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="DIC_PROP.JOB_USER_TYPE" :value="scope.row.jobType"></dict-tag>
          </template>
        </el-table-column>
        <el-table-column prop="roleId" :label="t('runJob.roleId')">
          <template #default="scope">
            <convert-role-name
              :options="{
                users: dicData.users,
                roles: dicData.roles,
                posts: dicData.posts,
                depts: dicData.depts
              }"
              :value="scope.row"
            ></convert-role-name>
          </template>
        </el-table-column>
        <el-table-column prop="userId" :label="t('runJob.userId')" show-overflow-tooltip>
          <template #default="scope">
            <convert-name
              :options="state.dicData.userId"
              :value="scope.row.userId"
              :valueKey="'userId'"
              :showKey="'name'"
            ></convert-name>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" :label="t('runJob.startTime')" show-overflow-tooltip />
        <el-table-column prop="endTime" :label="t('runJob.endTime')" show-overflow-tooltip />
        <!--                <el-table-column prop="timeLimit" :label="t('runJob.timeLimit')" show-overflow-tooltip/>-->
        <el-table-column prop="useTime" :label="t('runJob.useTime')" show-overflow-tooltip />
        <!--                <el-table-column prop="suspensionReason" :label="t('runJob.suspensionReason')" show-overflow-tooltip/>
                <el-table-column prop="invalidReason" :label="t('runJob.invalidReason')" show-overflow-tooltip/>
                <el-table-column prop="status" :label="t('runJob.status')" show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.NODE_STATUS" :value="scope.row.status"></dict-tag>
                    </template>
                </el-table-column>-->
        <el-table-column prop="flowStatus" :label="t('runJob.flowStatus')" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="DIC_PROP.FLOW_STATUS" :value="scope.row.flowStatus"></dict-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="parFlowInstId"
          :label="t('runJob.parFlowInstId')"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-tooltip
              placement="top"
              content="点击可查看关联父流程工单信息"
              v-if="scope.row.parFlowInstId"
            >
              <convert-name
                :options="state.dicData.parFlowInstId"
                :value="scope.row.parFlowInstId"
                :valueKey="'id'"
                :showKey="'flowName'"
                :elTagType="'primary'"
                @click="methods.handleJobByFlowInstId(scope.row, '1')"
              ></convert-name>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="subFlowInstId"
          :label="t('runJob.subFlowInstId')"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-tooltip
              placement="top"
              content="点击可查看关联子流程工单信息"
              v-if="scope.row.subFlowInstId"
            >
              <convert-name
                :options="state.dicData.subFlowInstId"
                :value="scope.row.subFlowInstId"
                :valueKey="'id'"
                :showKey="'flowName'"
                :elTagType="'primary'"
                @click="methods.handleJobByFlowInstId(scope.row, '2')"
              ></convert-name>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.action')" width="120">
          <template #default="scope">
            <el-tooltip placement="top">
              <template #content>
                查看工单
              </template>
              <el-button
                text
                type="primary"
                icon="view"
                @click="methods.handleJob(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip placement="top">
              <template #content>
                查看审批过程
              </template>
              <el-button
                text
                type="primary"
                icon="Operation"
                @click="methods.handleComment(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip placement="top">
              <template #content>
                查看流程图
              </template>
              <el-button
                text
                type="primary"
                icon="Share"
                @click="methods.handleFlowPic(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              placement="top"
              v-if="scope.row.flowStatus === '0' && scope.row.belongType !== '2'"
            >
              <template #content>
                取回任务
              </template>
              <el-button
                text
                type="primary"
                icon="CirclePlus"
                @click="methods.handleRetakeJob(scope.row, '1')"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        v-bind="state.pagination"
      />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />

    <!-- 查看审批过程 -->
    <el-dialog
      v-model="data.showComment"
      v-if="data.showComment"
      top="20px"
      width="90%"
      title="查看审批过程"
      append-to-body
    >
      <comment :currJob="data.currJob"></comment>
    </el-dialog>

    <!-- 查看流程图 -->
    <el-drawer
      class="flow-overflow-drawer"
      direction="rtl"
      append-to-body
      size="90%"
      v-model="data.showFlowPic"
    >
      <flow-photo v-if="data.showFlowPic" :currJob="data.currJob"></flow-photo>
    </el-drawer>
  </div>
</template>

<script setup lang="ts" name="HiJob">
import { BasicTableProps, useTable } from '@/hooks/table'
import { fetchToDonePage, retakeJob } from '@/api/jsonflow/hi-job'
import { useMessage, useMessageBox } from '@/hooks/message'
import { useI18n } from 'vue-i18n'
import { onCascadeChange, onLoadDicUrl, onLoaded } from '@/flow/components/convert-name/convert'
import { openFlowPreview } from '@/flow/support/extend'

const { proxy } = getCurrentInstance()
const router = useRouter()
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'))
const Comment = defineAsyncComponent(() => import('../comment/timeline.vue'))
const FlowPhoto = defineAsyncComponent(() => import('../flow-design/view.vue'))
const { t } = useI18n()
// 定义查询字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl(
  { key: 'flowInstId' },
  { key: 'initiatorId' },
  { key: 'users' },
  { key: 'roles' },
  { key: 'posts' },
  { key: 'depts' }
)
const onCascade = onCascadeChange(
  cascadeDic,
  { key: 'flowInstId', cascades: ['runNodeId'] },
  { key: 'runNodeId', cascades: ['runJobId'] }
)
onMounted(() => {
  onLoad(dicData)
})
function cascadeChange(key, cascades) {
  onCascade(state.queryForm, { key: key, cascades: cascades })
}

// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)

const data = reactive({
  currJob: {},
  showComment: false,
  showFlowPic: false,
  activeName: '-1'
})

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: { queryJobType: '0', belongType: data.activeName },
  pageList: fetchToDonePage,
  onLoaded: onLoaded(
    { key: 'userId' },
    { key: 'initiatorId' },
    { key: 'parFlowInstId' },
    { key: 'subFlowInstId' },
    { key: 'orderId' },
    { key: 'runNodeId' }
  ),
  descs: ['end_time']
})

// table hook
const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
  // 清空搜索条件
  queryRef.value?.resetFields()
  getDataList()
}

// 导出excel
const exportExcel = () => {
  downBlobFile('/jsonflow/run-job/export', state.queryForm, 'run-job.xlsx')
}

const methods = {
  handleTabClick(tab) {
    if (data.activeName === tab.paneName) {
      return
    }
    state.queryForm.signatureType = null
    state.queryForm.belongType = null
    if (tab.paneName !== '3') {
      state.queryForm.belongType = tab.paneName
    } else {
      state.queryForm.belongType = '0'
      // 仅标识
      state.queryForm.signatureType = '-1'
    }
    getDataList(true)
  },
  handleJobByFlowInstId(row, type) {
    let find
    if (type === '1') {
      find = state.dicData.parFlowInstId.find((f) => f.id === row.parFlowInstId)
    } else {
      find = state.dicData.subFlowInstId.find((f) => f.id === row.subFlowInstId)
    }
    methods.handleJob({ flowInstId: find.id })
  },
  handleJob(row) {
    openFlowPreview(router, row)
  },
  handleRetakeJob(row) {
    retakeJob(row).then(() => {
      getDataList()
    })
  },
  handleComment(row) {
    data.currJob = row
    data.showComment = true
  },
  handleFlowPic(row) {
    data.currJob = row
    data.showFlowPic = true
  }
}
</script>

<style lang="scss">
@use "../../../flow/components/style/flow-drawer.scss" as *;
</style>
