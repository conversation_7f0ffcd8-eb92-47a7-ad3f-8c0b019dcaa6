import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询印章信息列表 带分页
export function getSealListPage(data: Object) {
	return request({
		url: essPrefix + "/seal/listPage",
		method: "post",
		data,
	})
}

// 修改印章归属
export function updateSealBelong(data: Object) {
	return request({
		url: essPrefix + "/seal/belong",
		method: "post",
		data,
	})
}

// 新增印章
export function addSeal(data: Object) {
	return request({
		url: essPrefix + "/seal/add",
		method: "post",
		data,
	})
}

// 修改印章
export function updateSeal(data: Object) {
	return request({
		url: essPrefix + "/seal/edit",
		method: "post",
		data,
	})
}

// 查看印章
export function getSealInfo(id: string) {
	return request({
		url: essPrefix + "/seal/" + id,
		method: "get",
	})
}

// 超管-启用印章
export function adminEnableSeal(id: string) {
	return request({
		url: essPrefix + "/seal/enable/" + id,
		method: "get",
	})
}
// 超管-禁用印章
export function adminDisableSeal(id: string) {
	return request({
		url: essPrefix + "/seal/disable/" + id,
		method: "get",
	})
}

// 查询印章授权信息列表 带分页
export function getSealAuthList(data: Object) {
	return request({
		url: essPrefix + "/sealAuth/listPage",
		method: "post",
		data,
	})
}

// 查询我持有的印章信息列表 带分页
export function getMySealList(data: Object) {
	return request({
		url: essPrefix + "/seal/mySealPage",
		method: "post",
		data,
	})
}

// ------- 签署人员查询 ----------
// 获取校级持章人
export function getSchoolLevelStaffAPI() {
	return request({
		url: essPrefix + "/signatory/schoolLevelStaff",
		method: "get",
	})
}
// 获取二级持章人
export function getSecondLevelStaffAPI() {
	return request({
		url: essPrefix + "/signatory/secondLevelStaff",
		method: "get",
	})
}
// 持章人  0-校级 1-二级单位
export function getHoldSealAPI(staffType: number) {
	return request({
		url: essPrefix + "/signatory/holdSealStaff/" + staffType,
		method: "get",
	})
}
