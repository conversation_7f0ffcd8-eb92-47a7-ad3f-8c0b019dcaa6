<template>
  <div>
    <div
      v-for="(tab, index) in options"
      :key="index"
      :class="[{ active: tab[valueKey] === modelValue }, tabType]"
      class="tab"
      @click="activateTab(tab[valueKey])"
    >
      <span :class="{ active: tab[valueKey] === modelValue }">
        {{ tab[labelKey] }}
      </span>
    </div>
  </div>
</template>

<script>
import { debounce } from "@/utils/index"
export default {
  props: {
    options: Array,
    modelValue: [String, Number],
    labelKey: {
      type: String,
      default: "label",
    },
    valueKey: {
      type: String,
      default: "value",
    },
    tabType: {
      type: String,
      default: "plain", // plain 浅色 | solid 实色
    },
  },
  methods: {
    activateTab(tab) {
      debounce(this.update(tab), 500)
    },

    update(tab) {
      this.$emit("tab-click", tab)
      this.$emit("update:modelValue", tab)
    },
  },
}
</script>

<style lang="scss" scoped>
.tab {
  @apply mr-40px inline-block cursor-pointer font-16px mb-15px;
  height: 22px;
  line-height: 22px;
  span {
    @apply relative z20;
  }
  &.plain {
    &.active,
    &:hover {
      @apply title-line-b  before:(bg-[var(--el-color-primary-light-6)] bottom-1px);
    }
  }
  &.solid {
    position: relative;
    height: 30px;
    &.active,
    &:hover {
      color: var(--el-color-primary);
      font-weight: 600;
      &::after {
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 2px;
        width: 50%;
        margin: auto;
        border-radius: 20px;
        background-color: var(--el-color-primary);
      }
    }
  }
}
</style>
