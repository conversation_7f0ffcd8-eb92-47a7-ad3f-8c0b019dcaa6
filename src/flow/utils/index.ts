import * as orderVue from '@/api/order/order-key-vue'
import { isMobile } from '@/utils/index'
import { dynamicRoutesFlow } from '@/flow/support/extend'
import { listFlowApplicationByFlowKey } from '@/flow/designer/api/jsonflow'
import { utils } from '@/flow/designer/utils/common'
import { setPropsNull } from '@/flow/support/common'
import { ElMessageBox } from 'element-plus'
import { validateNull } from '@/utils/validate'
import { validateNodeType } from '@/flow/designer/components'
import { notifyLeft } from '@/flow'

/**
 * 常用工具类
 *
 */
export const formWidgetDesignHeight = (browserHeight) => {
  try {
    let initClientHeight = document.getElementById('initClientHeight')
    let height = browserHeight - (initClientHeight?.offsetTop || 0) + 'px'
    initClientHeight.style.height = height
    setTimeout(() => {
      let sidePanel = document.getElementsByClassName('el-aside _fc-l')[0]
      if (sidePanel) {
        sidePanel.style.height = height
      }
      // TODO 预览时不保存默认formData
      // let formWidgetMain = document.getElementsByClassName("el-container is-vertical _fc-m")[0];
      // if (formWidgetMain) formWidgetMain.style.height = height;
    }, 2000)
  } catch (error) {

  }

}

// 可视窗口默认无高度
export const initFlowDesignHeight = ($route, browserHeight) => {
  let initClientHeight = document.getElementById('initClientHeight')
  if (isMobile()) {
    // 判断是否在APP中访问
    if (initClientHeight.parentNode.id !== 'app') {
      browserHeight = browserHeight - 187
      if ($route.path !== dynamicRoutesFlow[0].children[0].path) {
        browserHeight = browserHeight + 120
      }
    }
  } else {
    if ($route.path === dynamicRoutesFlow[0].children[0].path) {
      browserHeight = browserHeight - 120
    }
    browserHeight = browserHeight - initClientHeight.offsetTop
  }
  initClientHeight.style.height = browserHeight + 'px'
}

// 隐藏弹出框滚动条
export const initHandleJobHeight = (data) => {
  let handleJob = document.getElementById('handle_job')
  if (handleJob) {
    handleJob.style.overflowX = 'hidden'
    if (data.currElTab.path === orderVue.vueKey.FlowDesignView) {
      handleJob.style.overflowY = 'hidden'
    } else {
      handleJob.style.overflowY = 'auto'
    }
  }
}

// 隐藏流程图弹出框
export const hideVueContextmenuName = () => {
  let element = document.querySelector('.vue-contextmenuName-node-menu')
  if (element) {
    element.style.display = 'none'
  }
  let element2 = document.querySelector('.vue-contextmenuName-link-menu')
  if (element2) {
    element2.style.display = 'none'
  }
  let element3 = document.querySelector('.vue-contextmenuName-node-connect-menu')
  if (element3) {
    element3.style.display = 'none'
  }
  let element4 = document.querySelector('.vue-contextmenuName-flow-menu')
  if (element4) {
    element4.style.display = 'none'
  }
}

// 确认窗体
export const confirmCancelAndClose = (confirmObj, cancelObj, msg) => {
  // 无法await
  ElMessageBox.confirm(msg, '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: confirmObj.text,
    cancelButtonText: cancelObj.text,
    type: 'warning'
  })
    .then(() => {
      if (confirmObj.callback) {
        confirmObj.callback()
      }
    })
    .catch((err) => {
      if (err === 'cancel') {
        if (cancelObj.callback) {
          cancelObj.callback()
        }
      }
    })
}

export const handleUpgradeVersion = async (props) => {
  if (!props.currFlowForm) return
  if (props.currFlowForm.isNew) return
  props.currFlowForm.isNew = true
  // TODO 只有模板才能升版本
  let oldFormId = props.currFlowForm.id
  setPropsNull(props.currFlowForm, 'id', 'createUser', 'createTime')
  let newFormId = utils.getId()
  props.currFlowForm.id = newFormId
  let resp = await listFlowApplicationByFlowKey(props.currFlowForm.flowKey)
  if (validateNull(resp.object)) return
  props.currFlowForm.version = resp.object[0].version + 1
  replaceFlowNodeFormId(newFormId, oldFormId)
}

// 替换节点旧表单ID
export function replaceFlowNodeFormId(newFormId, oldFormId, isNotified?) {
  let models = window._jfGraph.getElements()
  if (validateNull(models)) return
  let isExistReplace = false
  models.forEach((model) => {
    if (!validateNodeType(model, null, true)) return
    let pcTodoUrl = model.attributes.attrs.cdata.attrs.pcTodoUrl
    if (validateNull(pcTodoUrl)) return
    const index = pcTodoUrl.findIndex((item) => item === oldFormId)
    if (index !== -1) {
      model.attributes.attrs.cdata.attrs.pcTodoUrl.splice(index, 1, newFormId)
      let formId = model.attributes.attrs.cdata.attrs.formId
      if (formId === oldFormId) model.attributes.attrs.cdata.attrs.formId = newFormId
      isExistReplace = true
    }
  })
  if (!isNotified) return
  if (isExistReplace) {
    notifyLeft('替换成功')
  } else {
    notifyLeft('节点中不存在该旧的页面', 'warning')
  }
}
