import { ref, computed, watch, nextTick, getCurrentInstance } from "vue"

export function useAgendaTabs(tabsConfig, tableRefs) {
  const { proxy } = getCurrentInstance()
  const currentTab = ref(0)
  const radioValue = ref(0)

  // 权限过滤后的 tabs
  const authTabs = computed(() =>
    tabsConfig.filter((item) => proxy?.$auth.hasPermiOr(item.permissions))
  )

  // 当前 tab 下 radio 选项
  const authRadioOptions = computed(() => {
    const currentTabData = authTabs.value[currentTab.value]
    return (
      currentTabData?.children?.filter((item) =>
        proxy?.$auth.hasPermiOr(item.permissions)
      ) || []
    )
  })

  // 初始化赋值
  function useInitValue(source, refValue) {
    let stop
    stop = watch(
      source,
      (val) => {
        if (val.length > 0) {
          refValue.value = val[0].value
          if (typeof stop === 'function') stop() // 只在stop为函数时调用
        }
      },
      { immediate: true }
    )
  }
  useInitValue(authTabs, currentTab)
  useInitValue(authRadioOptions, radioValue)

  // tab 切换
  const handleTabClick = () => {
    nextTick(() => {
      let hasRadioValue = authRadioOptions.value.findIndex(
        (i) => i.value == radioValue.value
      )
      if (hasRadioValue == -1) radioValue.value = authRadioOptions.value[0].value
      resetCurrentTable()
    })
  }

  // 重置表格
  const resetCurrentTable = () => {
    if (radioValue.value === 0) {
      tableRefs[0]?.value?.resetQuery()
    } else {
      tableRefs[1]?.value?.resetQuery()
    }
  }

    watch(radioValue, () => {
    nextTick(() => {
      resetCurrentTable()
    })
  })


  return {
    currentTab,
    radioValue,
    authTabs,
    authRadioOptions,
    handleTabClick,
    resetCurrentTable,
  }
} 
