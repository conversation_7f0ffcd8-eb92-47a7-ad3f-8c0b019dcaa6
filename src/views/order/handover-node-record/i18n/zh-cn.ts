export default {
	handoverNodeRecord: {
		index: "#",
		importhandoverNodeRecordTip: "导入交接任务记录",
		id: "ID",
		flowKey: "业务类型",
		initiatorId: "交接申请人",
		nodeJobId: "任务名称",
		startTime: "任务开始时间",
		todoList: "待办事项",
		status: "交接状态",
		createUser: "创建人",
		createTime: "创建时间",
		orderId: "交接流程表ID",
		flowInstId: "流程实例ID",
		retStatus: "驳回状态",
		runJobId: "任务名称",
		runNodeId: "运行节点ID",
		updateUser: "修改人",
		updateTime: "修改时间",

		inputIdTip: "请输入ID",
		inputFlowKeyTip: "请输入业务类型",
		inputInitiatorIdTip: "请输入交接申请人",
		inputNodeJobIdTip: "请输入任务名称",
		inputStartTimeTip: "请输入任务开始时间",
		inputTodoListTip: "请输入待办事项",
		inputStatusTip: "请输入交接状态",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputOrderIdTip: "请输入交接流程表ID",
		inputFlowInstIdTip: "请输入流程实例ID",
		inputRetStatusTip: "请输入驳回状态",
		inputRunJobIdTip: "请输入任务名称",
		inputRunNodeIdTip: "请输入运行节点ID",
		inputUpdateUserTip: "请输入修改人",
		inputUpdateTimeTip: "请输入修改时间",

	},
}
