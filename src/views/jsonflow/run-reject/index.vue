<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item :label="$t('runReject.flowInstId')" prop="flowInstId">
						<el-input
							v-model="state.queryForm.flowInstId"
							:placeholder="t('runReject.inputFlowInstIdTip')"
							style="max-width: 180px"
						/>
					</el-form-item>
					<el-form-item :label="$t('runReject.status')" prop="status">
						<el-select
							v-model="state.queryForm.status"
							:placeholder="t('runReject.inputStatusTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.REJECT_STATUS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							{{ $t('common.queryBtn') }}
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							{{ $t('common.resetBtn') }}
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<right-toolbar
						v-model:show-search="showSearch"
						class="ml10"
						style="float: right;margin-right: 20px"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" :label="t('runReject.index')" width="40" />
				<el-table-column prop="flowInstId" :label="t('runReject.flowInstId')" show-overflow-tooltip />
				<el-table-column prop="flowKey" :label="t('runReject.flowKey')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.flowInstId"
							:value="scope.row.flowKey"
							:value-key="'flowKey'"
							:show-key="'flowName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="fromRunNodeId" :label="t('runReject.fromRunNodeId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.fromRunNodeId"
							:value="scope.row.fromRunNodeId"
							:value-key="'id'"
							:show-key="'nodeName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="toRunNodeId" :label="t('runReject.toRunNodeId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.toRunNodeId"
							:value="scope.row.toRunNodeId"
							:value-key="'id'"
							:show-key="'nodeName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="status" :label="t('runReject.status')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.REJECT_STATUS" :value="scope.row.status" />
					</template>
				</el-table-column>
				<el-table-column prop="remark" :label="t('runReject.remark')" show-overflow-tooltip />
				<el-table-column prop="createUser" :label="t('runReject.createUser')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.createUser"
							:value="scope.row.createUser"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" :label="t('runReject.createTime')" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="80">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.viewBtn') }}
							</template>
							<el-button
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								修改
							</template>
							<el-button
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemRunReject">
import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchList, delObjs } from "@/api/jsonflow/run-reject"
import { useMessage, useMessageBox } from "@/hooks/message"

import { useI18n } from "vue-i18n"
import { onLoadDicUrl, onLoaded } from "@/flow/components/convert-name/convert"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))
const { t } = useI18n()

// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
	onLoaded: onLoaded({ key: "createUser" }, { key: "flowInstId" }, { key: "fromRunNodeId" }, { key: "toRunNodeId" }),
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	getDataList()
}

</script>
