import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询首页模块配置
export function getHomeModuleList(data: Object) {
	return request({
		url: essPrefix + "/indexCfgInfo/queryModuleIcon",
		method: "post",
		data,
	})
}
// 文件用印情况
export function getFileIndexCountAPI() {
	return request({
		url: essPrefix + "/flow/listIndex",
		method: "get",
	})
}
// 印章使用情况
export function getSealIndexCountAPI() {
	return request({
		url: essPrefix + "/seal/listIndex",
		method: "get",
	})
}
// 我的待办（首页统计）
export function getTodoCountAPI() {
	return request({
		url: essPrefix + "/todo/todoIndex",
		method: "post",
	})
}
