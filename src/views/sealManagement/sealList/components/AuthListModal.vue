<template>
  <el-dialog
    v-model="dialogVisible"
    title="角色用户"
    :before-close="handleClose"
    width="1050px"
    append-to-body
    class="small-form-dialog"
  >
    <el-form ref="queryRef" :model="queryParams" inline label-width="50px">
      <el-form-item label="netId" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入netId"
          clearable
          :maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="workNumber">
        <el-input
          v-model="queryParams.workNumber"
          placeholder="请输入工号"
          clearable
          :maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入姓名"
          clearable
          :maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item class="search-btns">
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="u-m-b-8">
      <el-col :span="1.5">
        <el-button v-auths="['system:role:add']" type="primary" @click="openSelectUser">
          新增用户
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-auths="['system:role:remove']"
          type="danger"
          :disabled="multiple"
          @click="cancelAuthUserAll"
        >
          删除用户
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      class="table-empty"
      :data="userList"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="netId" prop="userName" :show-overflow-tooltip="true" />
      <el-table-column label="工号" prop="workNumber" :show-overflow-tooltip="true" />
      <el-table-column label="姓名" prop="nickName" :show-overflow-tooltip="true" />
      <el-table-column
        label="所属部门"
        prop="dept.deptName"
        :show-overflow-tooltip="true"
      />
      <!-- <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" /> -->
      <!-- <el-table-column label="手机" prop="phonenumber" :show-overflow-tooltip="true" /> -->
      <!-- <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        v-if="$auth.hasPermiOr(['system:role:remove'])"
      >
        <template #default="scope">
          <el-button
            v-auths="['system:role:remove']"
            link
            type="danger"
            @click="cancelAuthUser(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script setup name="AuthListModal">
import { getSealAuthList } from "@/api/ess/seal/seal"

defineOptions({
  inheritAttrs: false,
})

const { proxy } = getCurrentInstance()

const userList = ref([])
const loading = ref(true)
const dialogVisible = ref(false)
const multiple = ref(true)
const total = ref(0)
const userIds = ref([])

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  roleId: null,
  userName: null,
  work: null,
  nickName: null,
})

const open = (row) => {
  dialogVisible.value = true
  resetQuery()
}

const handleClose = () => {
  dialogVisible.value = false
}

/** 查询授权用户列表 */
function getList() {
  loading.value = true
  getSealAuthList(queryParams).then((response) => {
    userList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  userIds.value = selection.map((item) => item.userId)
  multiple.value = !selection.length
}

/** 取消授权按钮操作 */
function cancelAuthUser(row) {
  // proxy.$modal
  //   .confirm('确认要取消该用户"' + row.userName + '"角色吗？')
  //   .then(function () {
  //     return authUserCancel({ userId: row.userId, roleId: queryParams.roleId })
  //   })
  //   .then(() => {
  //     getList()
  //     proxy.$modal.msgSuccess('取消授权成功')
  //   })
  //   .catch(() => {})
}

defineExpose({
  open,
})
</script>
