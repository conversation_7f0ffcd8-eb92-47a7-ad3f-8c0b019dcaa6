<template>
  <div class="template-iframe" v-loading="loading" :style="{ height: height }">
    <iframe :src="previewUrl" frameborder="0" width="100%" height="100%"></iframe>
  </div>
</template>
<script setup name="TemplateIframe">
import { templatePreviewUrl } from "@/api/ess/template/template"

const props = defineProps({
  essTemplateId: {
    type: String,
    default: "",
  },
  height: {
    type: String,
    default: "100%",
  },
})

const route = useRoute()
const essTemplateId = ref(route.query.essTemplateId || props.essTemplateId)
const previewUrl = ref(null)
const loading = ref(true)

const getTemplatePreviewUrl = async () => {
  try {
    const res = await templatePreviewUrl(essTemplateId.value)
    previewUrl.value = res.object.url
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  window.addEventListener("message", handleMessage)
  // 获取iframe传过来的信息
})

onBeforeUnmount(() => {
  window.removeEventListener("message", handleMessage)
})

function handleMessage(event) {
  const { messageType, action, embedParams } = event.data
  if (messageType === "NOT_LOGIN") {
    // 监听登录失效
    previewUrl.value = null
    getTemplatePreviewUrl()
  }
}

getTemplatePreviewUrl()

defineExpose({
  getTemplatePreviewUrl,
})
</script>
<style scoped>
.template-iframe {
  width: 100%;
  height: 100%;
}
</style>
