<template>
	<el-form
		ref="dataFormRef"
		v-loading="loading"
		:model="form"
		:rules="dataRules"
		label-width="90px"
		:disabled="operType === 'view'"
	>
		<el-row :gutter="24">
			<el-col :span="12" class="mb-1">
				<el-form-item :label="t('askLeave.type')" prop="type">
					<el-input v-model="form.type" :placeholder="t('askLeave.inputTypeTip')" />
				</el-form-item>
			</el-col>

			<el-col :span="12" class="mb-1">
				<el-form-item :label="t('askLeave.startTime')" prop="startTime">
					<el-date-picker
						v-model="form.startTime"
						type="datetime"
						:placeholder="t('askLeave.inputStartTimeTip')"
						:value-format="dateTimeStr"
					/>
				</el-form-item>
			</el-col>

			<el-col :span="12" class="mb-1">
				<el-form-item :label="t('askLeave.endTime')" prop="endTime">
					<el-date-picker
						v-model="form.endTime"
						type="datetime"
						:placeholder="t('askLeave.inputEndTimeTip')"
						:value-format="dateTimeStr"
					/>
				</el-form-item>
			</el-col>

			<el-col :span="12" class="mb-1">
				<el-form-item :label="t('askLeave.days')" prop="days">
					<el-input v-model="form.days" :placeholder="t('askLeave.inputDaysTip')" />
				</el-form-item>
			</el-col>

			<el-col :span="12" class="mb-1">
				<el-form-item :label="t('askLeave.carbonCopyPerson')" prop="carbonCopyPerson">
					<el-select
						v-model="form.carbonCopyPerson"
						:placeholder="t('askLeave.inputCarbonCopyPersonTip')"
						clearable
						filterable
						multiple
					>
						<el-option
							v-for="(item, index) in dicData.carbonCopyPerson"
							:key="index"
							:label="item.name"
							:value="item.userId"
						/>
					</el-select>
				</el-form-item>
			</el-col>

			<el-col :span="12" class="mb-1">
				<el-form-item :label="t('askLeave.remark')" prop="remark">
					<el-input v-model="form.remark" type="textarea" :placeholder="t('askLeave.inputRemarkTip')" />
				</el-form-item>
			</el-col>

			<el-col :span="12" class="mb-1">
				<el-form-item :label="t('askLeave.imgUrls')" prop="imgUrls">
					<el-input v-model="form.imgUrls" :placeholder="t('askLeave.inputImgUrlsTip')" />
				</el-form-item>
			</el-col>
		</el-row>
	</el-form>
</template>

<script setup lang="ts" name="CustomAskLeave">

import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj, tempStore } from "@/api/order/ask-leave"
import { useI18n } from "vue-i18n"
import { rule } from "@/utils/validate"
import { onLoadDicUrl } from "@/flow/components/convert-name/convert.ts"
import * as common from "@/flow/support/common"
import { DIC_PROP } from "@/flow/support/dict-prop"

const { t } = useI18n()

const props = defineProps({
	currJob: {
		type: Object,
		default: null,
	},
})

// 定义变量内容
const dataFormRef = ref()
const loading = ref(false)
const operType = ref(false)
// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "carbonCopyPerson" })
onMounted(() => {
	onLoad(dicData)
	openForm(props.currJob.operType, props.currJob.id)
	props.currJob.onSubmit = onSubmit
	props.currJob.onTemp = onTemp
})

// 提交表单数据
const form = reactive({
	type: "",
	startTime: "",
	endTime: "",
	days: "",
	carbonCopyPerson: [],
	remark: "",
	imgUrls: "",
})

// 定义校验规则
const dataRules = ref({
	startTime: [{ required: true, message: "开始时间不能为空", trigger: "blur" }],
	days: [{ required: true, message: "请假天数不能为空", trigger: "blur" }],
	remark: [{ required: true, message: "请假事由不能为空", trigger: "blur" }],
})

// 打开表单
const openForm = (type: string, id: string) => {
	operType.value = type
	form.id = ""

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取AskLeave信息
	if (id) {
		form.id = id
		getAskLeaveData(id)
	}
}

// 暂存
const onTemp = async () => {
	let clone = { operType: operType.value, form: form }
	common.handleCloneSubmit(clone)
	try {
		loading.value = true
		await tempStore(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		return true
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	let clone = { operType: operType.value, form: form }
	common.handleCloneSubmit(clone)
	try {
		loading.value = true
		form.status !== DIC_PROP.ORDER_STATUS[0].value ? await addObj(form) : await putObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		return true
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getAskLeaveData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
		let clone = { operType: operType.value, form: form }
		common.handleClone(clone)
	})
		.finally(() => {
			loading.value = false
		})
}

</script>
