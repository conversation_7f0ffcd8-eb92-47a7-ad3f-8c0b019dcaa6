import request from "@/utils/request"
import { eduUser } from "@/config/constant"

// 查询部门列表
export function listDept (query) {
  return request({
    url: eduUser + "/system/dept/list",
    method: "get",
    params: query,
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild (deptId) {
  return request({
    url: eduUser + "/system/dept/list/exclude/" + deptId,
    method: "get",
  })
}

// 查询部门详细
export function getDept (deptId) {
  return request({
    url: eduUser + "/system/dept/" + deptId,
    method: "get",
  })
}

// 新增部门
export function addDept (data) {
  return request({
    url: eduUser + "/system/dept",
    method: "post",
    data: data,
  })
}

// 修改部门
export function updateDept (data) {
  return request({
    url: eduUser + "/system/dept",
    method: "put",
    data: data,
  })
}

// 删除部门
export function delDept (deptId) {
  return request({
    url: eduUser + "/system/dept/" + deptId,
    method: "delete",
  })
}

// 查询当前部门tree结构人员信息
export function selectZsDeptTreeInfo (data) {
  return request({
    url: eduUser + "/zsDeptInfoApi/selectZsDeptTreeInfoV3",
    method: "post",
    data,
    headers:{
      repeatSubmit: false
    }
  })
}
// 查询当前部门tree结构
export function fetchDeptTreeAPI (data) {
  return request({
    url: eduUser + "/zsDeptInfoApi/selectZsDeptTreeInfo",
    method: "post",
    data: {...data, izZsUsersOpen: false},
  })
}
