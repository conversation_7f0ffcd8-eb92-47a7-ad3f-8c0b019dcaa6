<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="110px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.code')" prop="code">
						<el-select
							v-model="form.code"
							:placeholder="t('distPerson.inputCodeTip')"
							clearable
							filterable
							@change="cascadeChange('code', ['flowInstId'])"
						>
							<el-option
								v-for="(item, index) in dicData.code"
								:key="index"
								:label="item.code"
								:value="item.code"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.flowKey')" prop="flowInstId">
						<el-select
							v-model="form.flowInstId"
							:placeholder="t('runJob.inputFlowKeyTip')"
							clearable
							filterable
							@change="cascadeChange('flowInstId', ['runNodeId'])"
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowInstId"
								:key="index"
								:label="item.flowName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.runNodeId')" prop="runNodeId">
						<el-select
							v-model="form.runNodeId"
							:placeholder="t('distPerson.inputRunNodeIdTip')"
							clearable
							filterable
							@change="cascadeChange('runNodeId', ['runJobId'])"
						>
							<el-option
								v-for="(item, index) in cascadeDic.runNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.runJobId')" prop="runJobId">
						<el-select
							v-model="form.runJobId"
							:placeholder="t('distPerson.inputRunJobIdTip')"
							clearable
							filterable
							@change="runJobIdChange"
						>
							<el-option
								v-for="(item, index) in cascadeDic.runJobId"
								:key="index"
								:label="item.jobName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.userKey')" prop="userKey" disabled>
						<el-input v-model="form.userKey" :placeholder="t('distPerson.inputUserKeyTip')" disabled />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.jobType')" prop="jobType">
						<el-select
							v-model="form.jobType"
							:placeholder="t('distPerson.inputJobTypeTip')"
							clearable
							filterable
							@change="handleRoleType"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.JOB_USER_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.roleId')" prop="roleId">
						<el-select
							v-model="form.roleId"
							:placeholder="t('distPerson.inputRoleIdTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in dicData.users"
								v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[0].value"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
							<el-option
								v-for="(item, index) in dicData.roles"
								v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[1].value"
								:key="index"
								:label="item.roleName"
								:value="item.roleId"
							/>
							<!-- <el-option
								v-for="(item, index) in dicData.posts"
								v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value"
								:key="index"
								:label="item.postName"
								:value="item.postId"
							/> -->
							<el-option
								v-for="(item, index) in dicData.depts"
								v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value"
								:key="index"
								:label="item.name"
								:value="item.deptId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.jobName')" prop="jobName">
						<el-input v-model="form.jobName" :placeholder="t('distPerson.inputJobNameTip')" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.sort')" prop="sort">
						<el-input-number
							v-model="form.sort"
							:min="1"
							:max="1000"
							:placeholder="t('distPerson.inputSortTip')"
						/>
					</el-form-item>
				</el-col>

				<!--                <el-col :span="12" class="mb-1">
                    <el-form-item :label="t('distPerson.isNowRun')" prop="isNowRun" disabled>
                        <el-radio-group disabled v-model="form.isNowRun">
                            <el-radio v-for="(item, index) in DIC_PROP.YES_OR_NO" :key="index" :label="item.value">
                                {{ item.label }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>-->
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="DistPersonDialog">

import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/jsonflow/dist-person"
import { useI18n } from "vue-i18n"
import { rule } from "@/utils/validate"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
import { setPropsDataValue, setPropsNull } from "@/flow/support/common"
import { handleChangeJobType } from "@/flow"
import { DIC_PROP } from "@/flow/support/dict-prop"
const emit = defineEmits(["refresh"])

const { t } = useI18n()

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "code" }, { key: "users" }, { key: "roles" }, { key: "posts" }, { key: "depts" })
const onCascade = onCascadeChange(cascadeDic, { key: "code", cascades: ["flowInstId"] }, { key: "flowInstId", cascades: ["runNodeId"] }, { key: "runNodeId", cascades: ["runJobId"] })
onMounted(() => {
	onLoad(dicData)
})

function cascadeChange(key, cascades) {
	onCascade(form, { key: key, cascades: cascades })
}

function runJobIdChange(newVal) {
	let fields = ["flowKey", "flowNodeId", "nodeJobId", "jobType", "userKey", "valType", "isNowRun"]
	if (!newVal) {
		setPropsNull(form, fields)
		return
	}
	const runJob = cascadeDic.runJobId.find(f => f.id === newVal)
	setPropsDataValue(form, runJob, fields)
	if (form.valType !== DIC_PROP.VAL_TYPE[1].value) {
		form.runJobId = null
		proxy.$modal.info("当前节点任务为非分配类型的任务，不允许单独分配参与者")
	}
}
// 提交表单数据
const form = reactive({
	code: "",
	runNodeId: "",
	runJobId: "",
	roleId: "",
	jobType: "",
	userKey: "",
	flowInstId: "",
	flowKey: "",
})

// 定义校验规则
const dataRules = ref({
	code: [{ required: true, message: "工单编号不能为空", trigger: "blur" }],
	flowInstId: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
	// runNodeId: [{required: true, message: '节点名称不能为空', trigger: 'blur'}],
	// runJobId: [{required: true, message: '任务名称不能为空', trigger: 'blur'}],
	jobType: [{ required: true, message: "参与者类型不能为空", trigger: "blur" }],
})

function handleRoleType() {
	handleChangeJobType(dicData, form)
}

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取DistPerson信息
	if (id) {
		form.id = id
		getDistPersonData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true

		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getDistPersonData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
		onCascade(form)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
