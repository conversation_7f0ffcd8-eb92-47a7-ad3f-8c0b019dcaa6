export default {
	createTable: {
		index: "#",
		importcreateTableTip: "import CreateTable",
		id: "id",
		tableName: "tableName",
		comments: "comments",
		comment: "comment",
		databaseType: "databaseType",
		pkPolicy: "pkPolicy",
		createUser: "createUser",
		createTime: "createTime",
		columnInfo: "columnInfo",

		inputIdTip: "input id",
		inputTableNameTip: "input tableName",
		inputCommentsTip: "input comments",
		inputCommentTip: "input comment",
		inputDatabaseTypeTip: "input databaseType",
		inputPkPolicyTip: "input pkPolicy",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputColumnInfoTip: "input columnInfo",

		name: "name",
		typeName: "typeName",
		precision: "precision",
		scale: "scale",
		defaultValue: "defaultValue",
		primaryKey: "primaryKey",
		nullable: "nullable",
	},
}
