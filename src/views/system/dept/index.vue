<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      inline
      class="white-form-box"
    >
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="部门状态" clearable>
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <CustomTable
      ref="customTableRef"
      :data="deptList"
      custom
      :loading="loading"
      :row-config="{
        keyField: 'id',
        isHover: true,
      }"
      :tree-config="{
        childrenField: 'children',
        rowField: 'id',
        parentField: 'parentId',
        reserve: true,
      }"
      @reload="getList"
      :row-class-name="rowClassName"
    >
      <template #actions>
        <el-button plain type="primary" class="is-deep" @click="toggleExpandAll">
          展开/折叠
        </el-button>
      </template>
      <template #toolbar>
        <el-button
          v-auths="['system:dept:dataSync']"
          type="primary"
          icon="Refresh"
          class="is-trans"
          plain
          @click="handleDataSync"
          :loading="syncLoading"
        >
          {{ syncLoading ? "同步中" : "数据同步" }}
        </el-button>
      </template>
      <vxe-column type="seq" width="90" />
      <vxe-column
        field="name"
        title="部门名称"
        min-width="210"
        tree-node
        header-class-name="tree-node"
        show-overflow
      />
      <vxe-column field="orderNum" title="排序" min-width="60" show-overflow />
      <vxe-column title="状态" width="100">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </vxe-column>
      <vxe-column field="createTime" title="创建时间" min-width="150" show-overflow />
    </CustomTable>

    <!-- 添加或修改部门对话框 -->
    <EditDeptModal
      ref="editDeptModalRef"
      :sys_normal_disable="sys_normal_disable"
      @change="getList"
    />
  </div>
</template>

<script setup name="Dept">
import { delDept, fetchDeptTreeAPI } from "@/api/system/dept"
import { useDeptStore } from "@/store/modules/dept"
import { syncDepartmentInfos } from "@/api/syncdata/syncdata"
import EditDeptModal from "./components/EditDeptModal.vue"
const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

const deptList = ref([])
const loading = ref(true)
const syncLoading = ref(false)
const showSearch = ref(true)
const isExpandAll = ref(false)
const editDeptModalRef = ref(null)

const queryParams = ref({
  deptName: undefined,
  status: undefined,
})

/** 查询部门列表 */
function getList() {
  loading.value = true
  fetchDeptTreeAPI(queryParams.value).then((response) => {
    deptList.value = response.object.map((item) => ({
      ...item,
      children:
        item.children?.map((child, index, array) => ({
          ...child,
          isLast: index === array.length - 1,
        })) || [],
    }))
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd(row) {
  editDeptModalRef.value.open(row, "新增部门")
}
const customTableRef = ref()
/** 展开/折叠操作 */
function toggleExpandAll() {
  isExpandAll.value = !isExpandAll.value
  handleExpand()
}

function handleExpand() {
  if (isExpandAll.value) {
    customTableRef.value?.tableRef?.setAllTreeExpand(true)
  } else {
    customTableRef.value?.tableRef?.clearTreeExpand()
  }
}

/** 修改按钮操作 */
function handleUpdate(row) {
  editDeptModalRef.value.open(row, "修改部门")
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.deptName + '"的数据项?')
    .then(function () {
      return delDept(row.deptId)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

function rowClassName({ row, rowIndex }) {
  if (row.parentId && +row.parentId !== 0) {
    return row.isLast ? "last-child-row" : ""
  }
  return ""
}

// 数据同步
const handleDataSync = () => {
  loading.value = true
  syncLoading.value = true
  syncDepartmentInfos().then((res) => {
    if (+res.code == 200) {
      getList()
      useDeptStore().fetchZsDeptTreeInfo({}, true) // 更新缓存中的数据
      useDeptStore().fetchDeptTreeSelect({}, true) // 更新缓存中的数据
      syncLoading.value = false
    }
    loading.value = false
  })
}

getList()
</script>

<style lang="scss" scoped>
:deep(*) {
  .vxe-table--render-default .vxe-body--row.row--stripe > .vxe-body--column {
    background-color: #fff;
  }
  .vxe-body--row.row--level-0:nth-child(even) .vxe-body--column {
    background-color: #f7f8f9;
  }
  .vxe-table--render-default .vxe-body--row.row--stripe.is--expand-tree,
  .vxe-body--row[class*="row--level-"]:not(.row--level-0) {
    .vxe-table-icon-caret-right {
      color: var(--el-color-primary);
    }
    .vxe-body--column {
      background-color: var(--el-color-primary-light-9) !important;
      &:first-child {
        border-left: 1px solid var(--el-color-primary-light-5);
      }
      &:last-child {
        border-right: 1px solid var(--el-color-primary-light-5);
      }
    }
  }
  .last-child-row {
    .vxe-body--column {
      border-bottom: 1px solid var(--el-color-primary-light-5);
    }
  }
  .vxe-table--render-default .vxe-body--row.row--stripe.is--expand-tree {
    .vxe-body--column {
      border-top: 1px solid var(--el-color-primary-light-5);
    }
  }
  .tree-node {
    padding-left: 20px;
  }
}
</style>
