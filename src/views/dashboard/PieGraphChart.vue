<template>
  <Echart ref="chartContainer" :option="options" :width="width" :height="height" />
</template>

<script setup>
import Echart from "./Echart.vue"

const props = defineProps({
  chartData: {
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "297px",
  },
  pieColors: {
    type: Array,
    default: () => {
      return ["#277AFF", "#FF9B12", "#1396F9", "#22FFCC"]
    },
  },
})
const chartContainer = ref(null)
const options = ref({})

const setOptions = () => {
  const richColor = {}
  const total = props.chartData.reduce((acc, cur) => acc + cur.value, 0)
  let pieColors = [...props.pieColors]
  pieColors.forEach((item, idx) => {
    richColor[`color${idx}`] = {
      fontSize: 20,
      color: item,
    }
  })
  const chartData = props.chartData
  options.value = {
    color: pieColors,
    tooltip: {
      trigger: "item", // 悬浮显示对比
    },
    title: {
      text: total,
      left: "center",
      top: "40%",
      itemGap: 8,
      textStyle: {
        fontSize: 32,
        fontWeight: "bold",
        color: "#222",
      },
      subtext: props.title,
      padding: 0,
      subtextStyle: {
        color: "#5F5F5F",
        fontSize: 14,
      },
    },
    series: [
      {
        type: "pie",
        radius: ["46%", "65%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        startAngle: -30, // 设置起始角度为-75度，即左侧
        label: {
          formatter: (params) => {
            return "{name|" + params.name + "} {value|" + params.value + "}"
          },
          rich: {
            value: {
              color: "inherit",
              fontSize: 20,
              fontWeight: "bold",
            },
            name: {
              color: "#141A26",
              fontSize: 14,
            },
          },
        },
        labelLine: {
          lineStyle: {
            width: 1,
          },
        },
        data: chartData,
      },
      {
        type: "pie",
        radius: ["39%", "46%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        startAngle: -30, // 设置起始角度为-75度，即左侧
        itemStyle: {
          opacity: 0.5,
        },
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: chartData,
      },
    ],
  }
}

watch(
  () => props.chartData,
  () => {
    console.log(props.chartData)
    setOptions()
  },
  {
    deep: true, // 监听对象属性的变
    immediate: true,
  }
)
</script>
