<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      :disabled="formDisabled"
    >
      <el-form-item label="任务名称" prop="jobName">
        <el-input v-model="form.jobName" placeholder="请输入任务名称" clearable />
      </el-form-item>

      <el-form-item label="任务分组" prop="jobGroup">
        <dict-select
          v-model="form.jobGroup"
          :options="sys_job_group"
          value-type="string"
        />
      </el-form-item>

      <el-form-item prop="invokeTarget">
        <template #label>
          调用方法
          <el-tooltip placement="top">
            <template #content>
              Bean调用示例：testTask.testParams('test')
              <br />Class类调用示例：com.towere.cloud.job.task.TestTask.testParams('test')
              <br />参数说明：支持字符串，布尔类型，长整型，浮点型，整型
            </template>
            <el-icon class="ml-5px"><QuestionFilled /></el-icon>
          </el-tooltip>
        </template>
        <el-input
          v-model="form.invokeTarget"
          placeholder="请输入调用目标字符串"
          clearable
        />
      </el-form-item>

      <el-form-item label="cron表达式" prop="cronExpression">
        <el-input
          v-model="form.cronExpression"
          placeholder="请输入cron执行表达式"
          clearable
        />
      </el-form-item>

      <el-form-item label="是否并发" prop="concurrent">
        <el-radio-group v-model="form.concurrent">
          <el-radio-button value="0">允许</el-radio-button>
          <el-radio-button value="1">禁止</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="错误策略" prop="misfirePolicy">
        <el-radio-group v-model="form.misfirePolicy">
          <el-radio-button value="1">立即执行</el-radio-button>
          <el-radio-button value="2">执行一次</el-radio-button>
          <el-radio-button value="3">放弃执行</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <dict-select
          v-model="form.status"
          :options="sys_job_status"
          value-type="string"
          type="radio"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose"> 取消 </el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 确定 </el-button>
    </template>
  </el-dialog>
</template>

<script setup name="EditJobForm">
import { addJob, updateJob } from "@/api/monitor/job"

defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  sys_job_status: Array,
  sys_job_group: Array,
})

const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)
const loading = ref(false)
const title = ref("新增任务")

const formDisabled = ref(false)
const form = ref({})
const rules = {
  jobName: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
  invokeTarget: [{ required: true, message: "调用目标字符串不能为空", trigger: "blur" }],
  cronExpression: [
    { required: true, message: "cron执行表达式不能为空", trigger: "blur" },
  ],
}
const formRef = ref(null)
function open(row, type) {
  formDisabled.value = type === "详情"
  title.value = "新增任务"
  if (row?.jobId) {
    form.value = { ...row }
    title.value = type ? "详情" : "修改任务"
  }
  dialogVisible.value = true
}

function handleClose() {
  formRef.value.resetFields()
  form.value = {}
  dialogVisible.value = false
}
const emit = defineEmits(["refresh", "change"])
function submitForm() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        const actionUrl = form.value.jobId ? updateJob : addJob
        const res = await actionUrl(form.value)
        if (+res.code === 200) {
          proxy.$modal.msgSuccess("操作成功")
          handleClose()
          emit(form.value.jobId ? "change" : "refresh")
        }
      } finally {
        loading.value = false
      }
    }
  })
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
