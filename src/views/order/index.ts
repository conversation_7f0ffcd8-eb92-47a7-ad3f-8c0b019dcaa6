import { flowConfig } from '@/flow/designer/config/flow-config'
import { validateNull } from '@/utils/validate'
import modal from '@/plugins/modal'
import { DIC_PROP } from '@/flow/support/dict-prop'
import * as runApplication from '@/api/order/run-application'
import { disabledAllFormFields } from '@/flow/utils/form-perm'

export function validateApp($route) {
  let appdata = flowConfig.mobileConfig.mobilePrefix
  return !validateNull($route.query[appdata])
}

export function initJobDataByApp($route, handleGetObj, rowEditInitData) {
  // 兼容移动端
  let appdata = flowConfig.mobileConfig.mobilePrefix
  let app = !validateNull($route.query[appdata])
  if (app) {
    let split = $route.query[appdata].split('-')
    let query = { id: split[1] }
    handleGetObj(query.id)
  } else {
    if (rowEditInitData) rowEditInitData()
  }
}

export async function doInitData(formCreateRef, saveInitData) {
  let state = await formCreateRef.value.design.fApi.validate()
  let formData = formCreateRef.value.design.formData
  if (!state || validateNull(formData)) {
    modal.msgWarning('请输入表单信息')
    return
  }
  return saveInitData(formData)
}

export async function doInitiateForm(
  loading,
  props,
  data,
  $route,
  formCreateRef,
  $emit,
  saveInitData
) {
  try {
    loading.value = true
    let formJson = await doInitData(formCreateRef, saveInitData)
    formJson.id = formJson.id || data.currFlowForm?.id
    props.currFlowForm.status !== DIC_PROP.ORDER_STATUS[0].value
      ? await runApplication.addObj(formJson)
      : await runApplication.putObj(formJson)
    // 兼容移动端
    let app = validateApp($route)
    if (app) {
      modal.msgSuccess('发起成功，请在【我的申请】查看')
      formCreateRef.value.design.fApi.disabled(true)
      data.submitBtn = false
    } else {
      props.showSuccessMsg && modal.msgSuccess(formJson.id ? '修改成功' : '提交成功')
      $emit('success', formJson.id ? '修改成功' : '提交成功')
      $emit('handleInitiateOrder', false)
    }
  } catch (err) {
  } finally {
    loading.value = false
  }
}

export function doTempStore(loading, data, $route, formCreateRef, $emit, saveInitData, props) {
  try {
    loading.value = true
    let formData = formCreateRef.value.design.formData
    if (validateNull(formData)) {
      modal.msgWarning('请输入表单信息')
      return
    }
    let formJson = saveInitData(formData)
    runApplication.tempStore(formJson).then((resp) => {
      props.showSuccessMsg && modal.msgSuccess('暂存成功，请在【草稿箱】查看')
      // 兼容移动端
      let app = validateApp($route)
      if (app) {
        formCreateRef.value.design.fApi.disabled(true)
        data.submitBtn = false
      } else {
        $emit('handleInitiateOrder', false)
        $emit('success', '暂存成功，请在【草稿箱】查看')
      }
    })
  } catch (err) {
    modal.msgError(err)
  } finally {
    loading.value = false
  }
}

function handleDisabledFields(disabledFields, disabled) {
  if (disabledFields) {
    Object.keys(disabledFields).forEach((key) => {
      disabledFields[key] = disabled
    })
  }
}
export function initCustomFormMethods(data, disabledFields, operType?) {
  return {
    disableForm(isAll) {
      // 流程中页面，因发起页面可copy
      if (isAll) operType.value = 'view'
      else handleDisabledFields(disabledFields, true)
    },
    enableForm(isAll) {
      if (isAll) operType.value = 'flow'
      else handleDisabledFields(disabledFields, false)
    },
    disableSubmit() {
      data.submitBtn = false
    },
    enableSubmit() {
      data.submitBtn = true
    }
  }
}
export function initFormMethods(formCreateRef, data) {
  return {
    hiddenForm(isAll, widgetList) {
      if (isAll) {
        nextTick(async () => {
          formCreateRef.value.design.fApi.hidden(true)
        })
      } else {
        disabledAllFormFields(widgetList, '-1')
      }
    },
    disableForm(isAll, widgetList) {
      if (isAll) {
        // TODO 目前调用会早于规则赋值无效
        nextTick(async () => {
          formCreateRef.value.design.fApi.disabled(true)
        })
      } else {
        disabledAllFormFields(widgetList, '0')
      }
    },
    enableForm(isAll, widgetList) {
      if (isAll) {
        nextTick(async () => {
          formCreateRef.value.design.fApi.disabled(false)
        })
      } else {
        disabledAllFormFields(widgetList, '1')
      }
    },
    disableSubmit() {
      data.submitBtn = false
    },
    enableSubmit() {
      data.submitBtn = true
    }
  }
}
