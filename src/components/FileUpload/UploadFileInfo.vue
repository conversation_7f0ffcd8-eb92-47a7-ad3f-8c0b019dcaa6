<template>
  <div class="flex pl-[0.5%] leading-20px file-info-box">
    <FileIcon :fileName="file.name" class="w-22px h-22px mr-10px" />
    <div class="file-info">
      <div class="flex-y-center">
        <el-text class="max-w-200px! mr-10px! text-16px" truncated>
          {{ file.name }}
        </el-text>
        <el-text type="primary" class="flex-y-center">
          <el-icon size="18" class="mr-3px"><CircleCheckFilled /></el-icon>
          上传成功
        </el-text>
      </div>
      <div class="text-left color-#6B6B6B text-14px mt-6px leading-20px" v-if="file.raw">
        文件大小：{{ formatFileSize(file.size) }}<br />上传日期：{{
          parseTime(file.raw.lastModifiedDate)
        }}
      </div>
      <div class="flex-y-center color-primary mt-12px">
        <el-icon><RefreshLeft /></el-icon>
        <el-link
          type="primary"
          underline="always"
          class="ml-3px"
          @click="$emit('reupload')"
        >
          重新上传
        </el-link>
        <el-tooltip
          popper-class="whitespace-pre-line"
          effect="dark"
          :content="acceptText"
          placement="bottom-start"
          v-if="acceptText"
        >
          <el-icon class="text-info ml-5px"><Warning /></el-icon>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup>
const FileIcon = defineAsyncComponent(() => import("@/components/SvgIcon/FileIcon.vue"))

defineProps({ file: Object, acceptText: String })
defineEmits(["reupload"])

function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i]
}
</script>

<style scoped>
.file-info-box {
  align-items: flex-start;
}
.file-info {
  flex: 1;
}
</style>
