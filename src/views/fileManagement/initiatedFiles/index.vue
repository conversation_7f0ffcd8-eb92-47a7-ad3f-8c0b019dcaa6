<template>
  <div>
    <EssFilesTable
      ref="essFilesTableRef"
      v-model:queryParams="queryParams"
      :tableData="tableData"
      :loading="loading"
      :total="total"
      @query="handleQuery"
    >
      <template #actions>
        <el-button
          v-auths="['file:initiatedFiles:lunchAudit']"
          type="primary"
          @click="handleLunch(0, '文件发起(审批)')"
        >
          文件发起(审批)
        </el-button>
        <el-button
          v-auths="['file:initiatedFiles:lunch']"
          type="success"
          @click="handleLunch(1, '文件发起(无需审批)')"
        >
          文件发起(无需审批)
        </el-button>
        <el-button
          v-auths="['file:initiatedFiles:download']"
          type="primary"
          @click="handleBatchDownload"
          class="is-deep"
        >
          批量下载
        </el-button>
      </template>
      <template #toolbar>
        <el-button plain type="primary" class="is-trans is-white" @click="toDrafts">
          <svg-icon name="local-edit-fill" class="u-m-r-5" />
          草稿箱({{ draftTotal }})
        </el-button>
      </template>
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '详情',
              click: () => handleViewDetails(row),
            },
            {
              text: '签署',
              click: () => handleSign(row),
              permission: ['file:initiatedFiles:sign'],
              hidden: !row.showSignBtn,
            },
            {
              text: '解除',
              click: () => handleRemove(row),
              permission: ['file:initiatedFiles:release'],
              hidden: !row.showReleaseBtn || true,
            },
            {
              text: '撤销',
              click: () => handleRevocation(row),
              permission: ['file:initiatedFiles:cancel'],
              hidden: !row.showCancelBtn,
            },
            {
              text: '审批撤销',
              click: () => handleConclude(row),
              permission: ['file:initiatedFiles:conclude'],
              hidden: +row.flowStatus !== 30,
            },
            {
              text: '催办',
              click: () => handlePress(row),
              permission: ['file:initiatedFiles:urging'],
              hidden: !row.showUrgingBtn,
            },
          ]"
        />
      </template>
    </EssFilesTable>

    <!-- 草稿箱 -->
    <DraftListModal ref="draftListRef" @updateTotal="updateDraftTotal" />
  </div>
</template>

<script setup name="InitiatedFiles">
import { getInitiatedFiles } from "@/api/ess/fileManagement/initiatedFiles"
import { useEssFilesTable } from "@/views/fileManagement/fileList/components/useEssFilesTable"

const EssFilesTable = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileList/components/EssFilesTable.vue")
)
const DraftListModal = defineAsyncComponent(() =>
  import("./components/DraftListModal.vue")
)
const router = useRouter()
const { proxy } = getCurrentInstance()

const essFilesTableRef = ref(null)
const {
  handleViewDetails,
  handlePress,
  handleSign,
  handleRevocation,
  handleConclude,
} = useEssFilesTable(essFilesTableRef)

const tableData = ref([])
const loading = ref(true)
const total = ref(0)
const draftTotal = ref(0) //草稿箱总数
const queryParams = ref({
  page: 1,
  limit: 10,
})

/** 查询列表 */
async function getList() {
  loading.value = true
  try {
    const res = await getInitiatedFiles(queryParams.value)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery(data) {
  Object.assign(queryParams.value, data)
  getList()
}

/** 文件发起合同 */
function handleLunch(flowFlag, title) {
  proxy.$tab.openPage(title, `/file/fileLunch/add/${flowFlag}`)
}

/** 批量下载 */
const handleBatchDownload = () => {
  const essFilesId = essFilesTableRef.value.selections.map((item) => {
    return item?.essFlowId
  })
  const formData = new FormData()
  formData.append("essFileIds", essFilesId.join(","))
  multipleDownloadFile(formData).then((res) => {
    if (+res.code === 200) {
      proxy.$modal.msgWarning("正在下载文件，请前往下载中心查看")
    }
  })
}

/** 草稿箱 */
function toDrafts() {
  proxy.$refs.draftListRef.open()
}

// 更新草稿箱数量
function updateDraftTotal(total) {
  draftTotal.value = total
}

getList()
</script>
