import { selectZsDeptTreeInfo } from '@/api/system/dept'
import { deptTreeSelect } from '@/api/system/user'

export const useDeptStore = defineStore('dept', {
  state: () => ({
    deptTreeSelectData: [],
    zsDeptTreeInfo: []
  }),
  actions: {
    // 缓存部门下拉树结构
    async fetchDeptTreeSelect(params = {}, refresh = false) {
      if (this.deptTreeSelectData.length && !refresh) {
        return this.deptTreeSelectData
      } else {
        const res = await deptTreeSelect(params)
        this.deptTreeSelectData = res.data
        return this.deptTreeSelectData
      }
    },
    // 获取部门树
    async fetchZsDeptTreeInfo(data = {}, refresh = false) {
      if ((isNullOrEmpty(data) && this.zsDeptTreeInfo.length) && !refresh ) {
        return this.zsDeptTreeInfo
      } else {
        // 如果 Pinia 中没有缓存数据，调用 API 获取数据并缓存
        const res = await selectZsDeptTreeInfo(data)
        if(isNullOrEmpty(data)){
          this.zsDeptTreeInfo = res.object
          return this.zsDeptTreeInfo
        }else{
          return res.object
        }
      }
    },

  }
})

function isNullOrEmpty(obj) {
  return !obj || 
         Object.keys(obj).length === 0 || 
         Object.values(obj).every(val => val === null)
}
