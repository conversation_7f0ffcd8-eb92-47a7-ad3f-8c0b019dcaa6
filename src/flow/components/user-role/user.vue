<template>
  <div v-if="data.updateMultiple" style="width: 100%" v-loading="initLoading">
    <el-select-v2
      v-model="modelValue"
      placeholder="请选择用户"
      clearable
      filterable
      remote
      :options="users"
      :multiple="props.multiple"
      :disabled="props.disabled"
      @change="changeModelValue"
      v-bind="$attrs"
      :loading="loading"
      :remote-method="remoteMethod"
      ref="selectRef"
      :props="{ label: 'nickName', value: 'userId' }"
      @clear="handleClear"
    >
      <template #default="{ item }">
        <span>{{ item.nickName }}{{ item.deptName ? `(${item.deptName})` : "" }}</span>
        <span>{{ item.workNumber ? `(${item.workNumber})` : "" }}</span>
      </template>
    </el-select-v2>
  </div>
</template>

<script setup name="UserSelect">
import { listUsersPage } from "@/api/jsonflow/common"
import { debounce } from "lodash-es"

const props = defineProps({
  // modelValue: {
  //   type: [Number, String, Array],
  //   default: null,
  // },
  nickName: {
    type: [Number, String, Array],
    default: null,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  addOptions: {
    type: Object,
    default: () => ({}),
  },
})

const modelValue = defineModel()
const selectRef = ref(null)
const loading = ref(false)
const isInit = ref(true)
const queryParams = reactive({
  limit: 20,
  page: 1,
})

const emits = defineEmits(["update:nickName"])

const users = ref([])
const defaultList = ref([]) // 首次默认20条数据
const initLoading = ref(true) // 首次加载

const data = reactive({
  updateMultiple: true,
})

const getList = debounce(async () => {
  try {
    loading.value = true
    const userId = isInit.value ? modelValue.value : null // 用于首次回显
    const res = await listUsersPage({ ...queryParams, userId })
    const resultArr = props.addOptions?.userId
      ? [props.addOptions, ...res.object.records]
      : [...res.object.records]
    users.value = resultArr
    if (isInit.value) {
      defaultList.value = resultArr
      const selectedUser = resultArr.find((item) => item.userId === modelValue.value)
      if (selectedUser?.realNameUser) {
        emits("update:nickName", selectedUser.realNameUser) // 如果存在id，首次获取用户姓名
      }
      isInit.value = false
    }
  } finally {
    loading.value = false
    initLoading.value = false
  }
}, 500)

const remoteMethod = (query) => {
  queryParams.nickName = query
  if (query) {
    getList()
  } else {
    // users.value = [...defaultList.value]
  }
}

const changeModelValue = (value) => {
  let name = selectRef.value.selectedLabel
  emits("update:nickName", name)
}

const handleClear = () => {
  emits("update:nickName", "")
  getList()
}

// 监听双向绑定
watch(
  () => props.multiple,
  (val) => {
    data.updateMultiple = false
    changeModelValue(null)
    // 防止切换时报错
    setTimeout(() => {
      data.updateMultiple = true
    })
  }
)

getList()

defineExpose({
  users,
})
</script>
