<template>
  <CommonCard title="我的待办">
    <div class="todo-nav">
      <div
        v-for="(item, idx) in todoList"
        :key="item.label"
        class="todo-nav-item"
        :class="{ 'cursor-pointer': $auth.hasPermi(item.perm) }"
        :style="{ backgroundImage: `url(${item.bg})` }"
        @click="handleClick(item)"
      >
        <p>{{ item.label }}</p>
        <span>{{ todoData[item.countKey] }}</span>
      </div>
    </div>
  </CommonCard>
</template>

<script setup>
import CommonCard from "@/views/components/CommonCard.vue"
import { getTodoCountAPI } from "@/api/ess/home"

import todoBg1 from "@/assets/images/index/todo-bg-1.png"
import todoBg2 from "@/assets/images/index/todo-bg-2.png"
import todoBg3 from "@/assets/images/index/todo-bg-3.png"
import todoBg4 from "@/assets/images/index/todo-bg-4.png"

const { proxy } = getCurrentInstance()

const todoData = ref({
  myEnableFileCount: 0,
  overTimeCount: 0,
  mySigningCount: 0,
  myAuditingCount: 0,
})

const todoList = [
  {
    label: "待我签署",
    perm: "agenda:mine:list",
    route: "/agenda/mine?tab=1",
    countKey: "mySigningCount",
    bg: todoBg1,
  },
  {
    label: "待我审批",
    perm: "runJob:todo:page",
    route: "/agenda/mine",
    countKey: "myAuditingCount",
    bg: todoBg2,
  },
  {
    label: "我发起的文件",
    perm: "file:initiatedFiles:list",
    route: "/file/initiatedFiles",
    countKey: "myEnableFileCount",
    bg: todoBg3,
  },
  {
    label: "即将超时",
    perm: "agenda:overtime:list",
    route: "/agenda/overtime",
    countKey: "overTimeCount",
    bg: todoBg4,
  },
]
const router = useRouter()
function handleClick(item) {
  if (proxy.$auth.hasPermi(item.perm)) {
    router.push(item.route)
  }
}

async function getTodoCount() {
  const res = await getTodoCountAPI()
  if (Number(res.code) === 200) {
    todoData.value = res.object
  }
}

getTodoCount()
onActivated(getTodoCount)
</script>

<style lang="scss" scoped>
.todo-nav {
  display: flex;
  justify-content: space-between;
  .todo-nav-item {
    width: 24%;
    height: 105px;
    color: #fff;
    padding: 20px;
    border-radius: 10px;
    background-size: cover;
    background-position: center right;
    background-repeat: no-repeat;
  }
  .todo-nav-item p {
    margin: 0 0 10px;
    white-space: nowrap;
  }
  .todo-nav-item span {
    font-weight: 500;
    font-size: 30px;
    font-family: "BAHNSCHRIFT";
  }
}
</style>
