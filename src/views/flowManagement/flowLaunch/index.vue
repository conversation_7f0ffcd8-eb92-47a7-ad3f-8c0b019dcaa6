<template>
  <div class="app-container">
    <div class="type-card" v-for="(tabs, tabIndex) in data.tabsData" :key="tabIndex">
      <div class="type-card__title">{{ tabs.groupName }}</div>
      <div class="type-card-body">
        <template
          v-for="(item, index) in data.tableData.filter(
            (f) => f.groupName === tabs.groupName
          )"
        >
          <div class="item" :key="item.id" v-if="item.status === '1'">
            <div class="flow-icon-wrapper">
              <ShowImageIcon :src="item.icon" height="50" />
            </div>
            <div class="flow-info-desc">
              <div class="flow-info-desc__title">{{ item.formName }}</div>
              <div class="item-opt">
                <div @click="handleInitiateOrder(item, index)">发起申请</div>
                <div @click="viewFlowDraftList(item)">草稿箱</div>
                <div @click="viewFlowPhoto(item)">流程图</div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 发起申请 -->
    <FlowInitiateModal ref="flowInitiateModalRef" @success="handleSuccess" />

    <!-- 流程图 -->
    <FlowPhotoModal ref="flowPhotoModalRef" />

    <!-- 草稿箱 -->
    <FlowDraftListModal ref="flowDraftListModalRef" @edit="editDraft" />
  </div>
</template>

<script setup name="FlowLaunch">
import * as flowApplication from "@/api/order/flow-application"
const ShowImageIcon = defineAsyncComponent(() =>
  import("@/components/ImageSelector/ShowImageIcon.vue")
)
// 引入组件
const FlowInitiateModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowInitiateModal.vue")
)
// 流程图
const FlowPhotoModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowPhotoModal.vue")
)
// 草稿箱
const FlowDraftListModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowDraftListModal.vue")
)

const CustomForm = defineAsyncComponent(() =>
  import("@/flow/components/custom-form/handle.vue")
)
const flowInitiateModalRef = ref(null)
const data = reactive({
  tableData: [],
  tabsData: [],
  currFlowForm: {},
})

// 列表查询
function getList() {
  flowApplication.listByPerms({}).then((response) => {
    data.tableData = response.object
    data.tabsData = data.tableData.filter(
      (value, i, arr) => arr.findIndex((x) => x.groupName === value.groupName) === i
    )
  })
}

// 发起流程
function handleInitiateOrder(row) {
  flowInitiateModalRef.value.open(row)
}

const flowPhotoModalRef = ref(null)
// 查看流程图
function viewFlowPhoto(row) {
  flowPhotoModalRef.value.open(row)
}
const flowDraftListModalRef = ref(null)

// 查看草稿箱
function viewFlowDraftList(row) {
  flowDraftListModalRef.value.open(row)
}

// 编辑草稿
function editDraft(row) {
  flowInitiateModalRef.value.open(row, false)
}
// 编辑草稿
function handleSuccess() {
  flowDraftListModalRef?.value.getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.type-card {
  @apply rounded-lg  mb-30px;
  &__title {
    @apply text-lg font-bold mb-10px title-line;
  }
  .type-card-body {
    @apply flex flex-wrap gap-16px;
    .item {
      @apply flex p-18px rounded-8px;
      width: 390px;
      border: 1px solid var(--el-color-primary-light-8);
      background: #fff;
      color: #0c1433;

      .flow-icon-wrapper {
        @apply flex-shrink-0 mr-15px;
      }
      .flow-info-desc {
        font-size: 14px;
        &__title {
          @apply text-overflow max-w-260px font-600;
        }
        .item-opt {
          @apply flex flex-items-center mt-14px;
          div {
            @apply bg-#fff mr-10px cursor-pointer rounded-4px px-12px py-4px;
            color: var(--el-color-primary);
            background: var(--el-color-primary-light-9);
            flex-shrink: 0;
          }
        }
      }
      &:hover {
        color: #fff;
        background: var(--el-color-primary);
        border-color: var(--el-color-primary);
        .flow-info-desc .item-opt {
          div {
            color: #fff;
            background: var(--el-color-primary-light-4);
            transition: all 0.3s;
            &:hover {
              transform: scale(1.05);
            }
          }
        }
      }
    }
  }
}
</style>
