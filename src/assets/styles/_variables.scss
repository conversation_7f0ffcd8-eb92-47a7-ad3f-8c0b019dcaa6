$main-color: #24a87e;
// 兼容手动改主题色的时候被其他插件覆盖element-plus的主题色

// 默认菜单主题风格
$base-menu-color: #bfcbd9;
$base-menu-color-active: $main-color;
$base-menu-background: #e8f3f0;
$base-logo-title-color: #ffffff;

$base-menu-light-color: #000;
$base-menu-light-background: #e8f3f0;
$base-logo-light-title-color: #001529;

$base-sub-menu-background: #e8f3f0;
$base-sub-menu-hover: #001528;

// 自定义暗色菜单风格
/**
$base-menu-color:hsla(0,0%,100%,.65);
$base-menu-color-active:#fff;
$base-menu-background:#001529;
$base-logo-title-color: #ffffff;

$base-menu-light-color:rgba(0,0,0,.70);
$base-menu-light-background:#ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background:#000c17;
$base-sub-menu-hover:#001528;
*/
$base-sidebar-width: 205px;
$base-nav-height: 60px;
$base-main-page-background: #f7f8f9;
$base-collapse-sidebar-width: 65px;
/* 主屏幕高度 navbar + tags-view + padding = 60 + 46 + 5 */
$base-main-page-height: calc(100vh - #{$base-nav-height} - 46px - 5px);
