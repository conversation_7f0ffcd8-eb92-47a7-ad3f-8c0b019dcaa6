<template>
  <!-- 导入对话框 -->
  <el-dialog
    v-model="upload.open"
    :title="title"
    width="560px"
    append-to-body
    :before-close="handleCancel"
  >
    <div class="u-m-b-8">请上传文件</div>
    <div class="import-file-box">
      <el-upload
        ref="uploadRef"
        v-model:file-list="fileList"
        :limit="1"
        :accept="fileType"
        :headers="upload.headers"
        :action="actionUrl"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-error="handleFileError"
        :auto-upload="false"
        :on-change="handleChange"
        :data="extraData"
        drag
        class="upload-file-box"
      >
        <div class="upload-file-box-btn">
          <!-- <img src="@/assets/images/file/upload-file-icon.png" alt="" /> -->
          <svg-icon name="local-folder-open" />
          <div class="upload-file-box-btn__desc">
            <div>
              将文件拖拽至此区域，或
              <span>点击上传</span>
            </div>
            <div>仅允许导入xls、xlsx格式文件</div>
          </div>
        </div>
      </el-upload>
      <div class="download-temp" @click="importTemplate">
        <div>
          <!-- <img src="@/assets/images/file/download-temp-icon.png" alt="" /> -->
          <svg-icon name="local-temp-download" />
          <div>下载模版</div>
        </div>
      </div>
    </div>

    <div v-if="showUpdateSupport" class="el-upload__tip">
      <el-checkbox
        v-model="upload.updateSupport"
        :true-value="1"
        :false-value="0"
        @click.stop
      />
      是否更新已经存在的数据
    </div>

    <template #footer>
      <el-button @click="handleCancel"> 取 消 </el-button>
      <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm">
        {{ upload.isUploading ? "正在上传..." : "确定" }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { getToken } from "@/utils/auth"
import { throttle } from "lodash-es"
const props = defineProps({
  limit: {
    type: Number,
    default: 1,
  },
  envUrl: {
    default: import.meta.env.VITE_APP_BASE_API,
  },
  // 上传地址
  uploadUrl: {
    type: String,
    default: "/system/user/importData",
  },
  // 弹出层标题
  title: {
    type: String,
    default: "用户导入",
  },
  // 下载模板地址
  tempUrl: {
    type: String,
    default: "/system/user/importTemplate",
  },
  // 是否展示更新原有数据
  showUpdateSupport: {
    type: Boolean,
    default: true,
  },
  fileType: {
    type: String,
    default: ".xlsx, .xls",
  },
  // 是否展示上传成功提示
  showUploadSuccessMsg: {
    type: Boolean,
    default: true,
  },
  // 额外携带参数
  extraData: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(["change"])
const uploadRef = ref(null)
const fileList = ref([])
const upload = reactive({
  // 是否显示弹出层（导入）
  open: false,
  // 是否禁用上传
  isUploading: false,
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
})

const actionUrl = computed(() => {
  const linkSymbol = props.uploadUrl.includes("?") ? "&" : "?"
  return `${props.envUrl}${props.uploadUrl}${linkSymbol}updateSupport=${upload.updateSupport}`
})

const { proxy } = getCurrentInstance()
const handleImport = () => {
  upload.open = true
}
const importTemplate = throttle(function () {
  proxy.download(props.tempUrl, {}, `${props.title}模板_${new Date().getTime()}.xlsx`)
}, 800)

const handleChange = (file) => {
  if (file.status !== "ready") {
    return
  }
  const suffName = "." + file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
  const fileTypeArr = props.fileType.split(",")
  const extension = fileTypeArr.includes(suffName)
  if (!extension) {
    proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType}格式文件!`)
    fileList.value = []
    return false
  }
}

// 文件上传中处理
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true
}
// 文件上传成功处理
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false
  upload.isUploading = false
  uploadRef.value.clearFiles()
  const msg = response.message || response.msg
  if (props.showUploadSuccessMsg) {
    proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        msg +
        "</div>",
      "导入结果",
      { dangerouslyUseHTMLString: true, customClass: "message-box-alert" }
    )
  }
  emit("change", response)
}
// 文件上传失败处理
const handleFileError = (err, file, fileList) => {
  upload.isUploading = false
  const msg = err.message || err.msg
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  )
}
const handleCancel = () => {
  uploadRef.value.clearFiles()
  fileList.value = []
  upload.open = false
}
// 提交上传文件
const submitFileForm = () => {
  if (uploadRef.value && !fileList.value.length) {
    proxy.$modal.msgWarning("您还没有添加文件！")
    return
  }
  uploadRef.value.submit()
}

defineExpose({
  handleImport,
})
</script>

<style>
/**单词强制换行 */
.message-box-alert {
  word-break: break-all !important;
}
</style>

<style lang="scss" scoped>
.el-upload__tip {
  line-height: 20px;
  display: flex;
  align-items: center;
  .el-checkbox {
    margin-right: 10px;
  }
}
.import-file-box {
  display: flex;
  justify-content: space-between;
  .upload-file-box {
    flex: auto;
    margin-right: 10px;
    .upload-file-box-btn {
      display: flex;
      align-items: center;
      height: 100%;
      padding-left: 50px;
      svg {
        font-size: 40px;
        color: var(--el-color-primary-light-5);
        margin-right: 10px;
      }
      img {
        width: 45px;
        object-fit: contain;
        margin-right: 10px;
        flex-shrink: 0;
      }
      &__desc {
        text-align: left;
        span {
          text-decoration: underline;
          color: var(--el-color-primary);
        }
        div:last-child {
          color: #9b9b9b;
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }
  }

  :deep(*) {
    .el-upload {
      width: 100%;
    }

    .el-upload-dragger {
      width: 100%;
      height: 110px;
      border-radius: 10px;
    }
  }
  .download-temp {
    flex-shrink: 0;
    width: 110px;
    height: 110px;
    border: 1px dashed var(--el-color-primary-light-5);
    background: var(--el-color-primary-light-9);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-sizing: border-box;
    cursor: pointer;
    &:hover {
      border-color: var(--el-color-primary);
    }
    svg {
      color: var(--el-color-primary);
      font-size: 35px;
      margin-bottom: 5px;
    }
    img {
      width: 30px;
      margin: auto;
      margin-bottom: 5px;
    }
  }
}
</style>
