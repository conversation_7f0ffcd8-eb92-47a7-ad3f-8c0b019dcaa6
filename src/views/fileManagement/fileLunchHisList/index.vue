<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" inline class="white-form-box" label-width="90px">
      <el-form-item label="模板名称" prop="templateName">
        <el-input v-model.trim="queryParams.templateName" placeholder="请输入模板名称" clearable maxlength="100"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="所属业务线" prop="businessLineId">
        <BusinessSelect v-model="queryParams.businessLineId" />
      </el-form-item>
      <el-form-item label="发起时间" prop="publishTimeRange">
        <el-date-picker v-model="queryParams.publishTimeRange" value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm" type="datetimerange" range-separator="-" start-placeholder="开始时间"
          end-placeholder="结束时间" class="common-input-width" clearable :default-time="dateDefaultTime" />
      </el-form-item>
      <el-form-item label="任务状态" prop="jobStatus">
        <dict-select v-model="queryParams.jobStatus" :options="file_job_status" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <CustomTable ref="customTableRef" v-model:page="queryParams.page" v-model:limit="queryParams.limit"
      :has-toolbar="false" :data="tableData" :loading="loading" :total="total" @reload="getList" :opt-width="70">
      <vxe-column type="seq" width="70" fixed="left" />
      <vxe-column field="publishTime" title="发起时间" width="180" :formatter="formatterTime" />
      <vxe-column field="templateName" title="模板名称" min-width="150" />
      <vxe-column field="businessLineName" title="所属业务线" min-width="120" />
      <vxe-column field="templateFlowFlag" title="使用模板流程设置" width="160">
        <template #default="{ row }">
          <div class="flex-y-center">
            {{ selectDictLabel(template_flow_flag, row.templateFlowFlag) }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="jobStatus" title="任务状态" width="90">
        <template #default="{ row }">
          <dict-tag :options="file_job_status" :value="row.jobStatus" />
        </template>
      </vxe-column>
      <vxe-column field="totalCount" title="总份数" min-width="160">
        <template #default="{ row }">
          <div class="flex-y-center">
            {{ row.totalCount }}
            <el-link underline="never" type="primary" class="ml-5px" v-if="+row.jobStatus === 1 || +row.jobStatus === 2"
              @click="downLoadSignatoryInfo(row)">
              <svg-icon name="local-download-line" :size="18" />下载签署方
            </el-link>
            <el-link underline="never" type="primary" class="ml-5px" @click="viewDetails(row)" v-else>
              <svg-icon name="local-documentation" :size="18" />
            </el-link>
          </div>
        </template>
      </vxe-column>
      <vxe-column field="publishedCount" title="已发起" min-width="120" />
      <vxe-column field="readyToPublishCount" title="待发起" min-width="120" />
      <vxe-column field="publishFailCount" title="发起失败" min-width="120">
        <template #default="{ row }">
          {{row.publishFailCount}}
          <el-button type="primary" class="min-h-30px! px-10px!" @click="handleReplenish(row)" v-if="+row.publishFailCount > 0">补充发起</el-button>
          <!-- &&
              +row.jobStatus === 4 &&
              !row.templateFlowFlag === '0' -->
        </template>
      </vxe-column>
      <vxe-column field="signedCount" title="已签署" min-width="80" />
      <vxe-column field="signingCount" title="签署中" min-width="80" />
      <!-- <template #opts="{ row }"> </template> -->
    </CustomTable>

    <!-- 发起详情 -->
    <FileLunchDetailsModal ref="fileLunchDetailsRef" />
    <!-- 补充详情 -->
    <FileReplenishLunchModal ref="fileReplenishLunchRef" />
  </div>
</template>

<script setup name="FileLunchHisList">
//防抖
import { throttle } from "lodash-es"
// 引入API前缀常量
import { essPrefix } from "@/config/constant"
import { getFileBatchHisListAPI } from "@/api/ess/fileManagement/fileBatchLunch"
const BusinessSelect = defineAsyncComponent(() =>
  import("@/views/components/BusinessSelect.vue")
)

const FileLunchDetailsModal = defineAsyncComponent(() =>
  import("./components/FileLunchDetailsModal.vue")
)
const FileReplenishLunchModal = defineAsyncComponent(() =>
  import("./components/FileReplenishLunchModal.vue")
)
const { proxy } = getCurrentInstance()
const { template_type, template_flow_flag, file_job_status } = proxy.useDict(
  "template_type",
  "template_flow_flag",
  "file_job_status"
)

const queryParams = reactive({
  templateName: null,
  templateStatus: null,
  businessLineId: null,
  publishTimeRange: [],
  limit: 10,
  page: 1,
})

const tableData = ref([])
const loading = ref(true)
const total = ref(0)

/** 查询列表 */
async function getList() {
  loading.value = true
  try {
    queryParams.publishTimeStart = queryParams.publishTimeRange?.[0]
    queryParams.publishTimeEnd = queryParams.publishTimeRange?.[1]
    const res = await getFileBatchHisListAPI(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}
//弹窗refs
const fileLunchDetailsRef = ref()

// 查看详情
async function viewDetails(row) {
  // 打开弹窗
  fileLunchDetailsRef.value.open(row)
}

const fileReplenishLunchRef = ref(null)
/** 补充发起 */
function handleReplenish(row) {
  fileReplenishLunchRef.value.open(row)
}

function formatterTime({ cellValue }) {
  return cellValue ? proxy.parseTime(cellValue, "{y}-{m}-{d} {h}:{i}") : "-"
}

/**
 * 下载签署方信息
 */
const downLoadSignatoryInfo = throttle(async (row) => {
  try {
    await proxy.download(
      essPrefix + `/flowInfoBatch/exportOriginData?flowBatchPublishId=${row.id}`,
      {},
      `签署方信息.xlsx`,
    )
  } catch (e) {
    proxy.$modal.msgError('下载失败，请稍后重试')
  }
}, 800)

getList()
</script>

<style scoped lang="scss">
.app-container__title {
  @apply title-line text-20px mb-10px;
}
</style>
