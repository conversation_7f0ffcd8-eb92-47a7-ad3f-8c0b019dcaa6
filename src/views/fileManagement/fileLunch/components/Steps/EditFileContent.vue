<template>
  <div class="preview-container" v-loading="loading">
    <iframe
      v-if="previewUrl"
      :src="previewUrl"
      width="100%"
      height="100%"
      class="preview"
      frameborder="0"
    />
  </div>
</template>

<script setup>
import useWebSocket from "@/hooks/websocket"
import { getFileInitiateUrlAPI } from "@/api/ess/fileManagement/file"
const props = defineProps({
  templateInfo: {
    type: Object,
    default: () => ({}),
  },
  flowId: {
    type: String,
    default: "",
  },
})
const previewUrl = ref(null)
const loading = ref(false)

const emit = defineEmits(["success"])

const { onConnect, wsClose } = useWebSocket({
  // 接收消息回调
  onMessage: (data) => {
    if (data === "SUCCESS") {
      loading.value = false
      emit("success")
    } else if (data === "PRE-SUCCESS") {
      loading.value = true
    }
  },
  onClose: () => {
    loading.value = false
  },
})
// 获取嵌入页面编辑
async function getFileInitiateUrl() {
  if (!props.flowId) return
  loading.value = true
  const res = await getFileInitiateUrlAPI(props.flowId)
  previewUrl.value = res.object.url
  loading.value = false
  onConnect(res.object.uniqueId)
}

onActivated(() => {
  getFileInitiateUrl() // 腾讯内嵌页切换回来会显示登录异常要重新请求
})

onDeactivated(() => {
  wsClose()
  previewUrl.value = null // 腾讯内嵌页切换回来会显示登录异常要重新请求
})

onUnmounted(wsClose)

defineExpose({
  getFileInitiateUrl,
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.preview-container {
  height: calc(#{$base-main-page-height} - 100px);
}
</style>
