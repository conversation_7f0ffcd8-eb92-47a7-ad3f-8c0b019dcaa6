<template>
  <div style="width: 100%">
    <TagUserList
      :list="modelValue || []"
      :show-remove="false"
      :itemSecondarySize="itemSecondarySize"
      :gridItems="gridItems"
      emptyText="暂无数据"
      itemBg
    />
  </div>
</template>

<script setup lang="ts" name="TagShowList">
const TagUserList = defineAsyncComponent(
  () => import("@/views/components/UserTreeTransfer/TagUserList.vue")
)

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  itemSecondarySize: {
    type: Number,
    default: 95,
  },
  gridItems: {
    type: Number,
    default: 4,
  },
})
</script>
