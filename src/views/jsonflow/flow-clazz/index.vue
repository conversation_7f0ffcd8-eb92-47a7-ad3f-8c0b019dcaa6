<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item label="流程名称" prop="defFlowId">
						<el-select
							v-model="state.queryForm.defFlowId"
							placeholder="请输入流程名称"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.defFlowId"
								:key="index"
								:label="item.flowName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							查询
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<el-tooltip placement="top">
						<template #content>
							新增
						</template>
						<el-button
							v-auth="'jsonflow_flowclazz_add'"
							icon="Plus"
							type="primary"
							class="ml10"
							@click="formDialogRef.openDialog('add')"
						/>
					</el-tooltip>
					<el-tooltip placement="top">
						<template #content>
							删除
						</template>
						<el-button
							v-auth="'jsonflow_flowclazz_del'"
							plain
							:disabled="multiple"
							icon="Delete"
							type="primary"
							class="ml10"
							@click="handleDelete(selectObjs)"
						/>
					</el-tooltip>
					<right-toolbar
						v-model:show-search="showSearch"
						:export="'jsonflow_flowclazz_export'"
						class="ml10"
						style="float: right;margin-right: 20px"
						@export-excel="exportExcel"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" label="序号" width="40" />
				<el-table-column prop="sort" label="排序" show-overflow-tooltip />
				<el-table-column prop="defFlowId" label="流程名称" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="dicData.defFlowId"
							:value="scope.row.defFlowId"
							:value-key="'id'"
							:show-key="'flowName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="flowNodeId" label="节点名称" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.cascadeDic[scope.row.defFlowId]"
							:value="scope.row.flowNodeId"
							:value-key="'id'"
							:show-key="'nodeName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="clazz" label="监听类" show-overflow-tooltip />
				<el-table-column prop="httpUrl" label="Http请求地址" show-overflow-tooltip />
				<el-table-column prop="httpMethod" label="Http请求类型" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.HTTP_METHODS" :value="scope.row.httpMethod" />
					</template>
				</el-table-column>
				<el-table-column prop="methods" label="方法名称" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.FLOW_METHODS" :value="scope.row.methods" />
					</template>
				</el-table-column>
				<el-table-column prop="valType" label="条件模式" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.VAL_TYPE.slice(2, 5)" :value="scope.row.valType" />
					</template>
				</el-table-column>
				<el-table-column prop="varKeyVal" label="表单字段" show-overflow-tooltip />
				<el-table-column prop="operator" label="运算符" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.OPERATOR" :value="scope.row.operator" />
					</template>
				</el-table-column>
				<el-table-column prop="varVal" label="校验值" show-overflow-tooltip />
				<el-table-column prop="type" label="事件类型" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.CLAZZ_TYPE" :value="scope.row.type" />
					</template>
				</el-table-column>
				<el-table-column prop="remark" label="备注" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="100">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								查看
							</template>
							<el-button
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								编辑
							</template>
							<el-button
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								删除
							</template>
							<el-button
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemFlowClazz">
import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchList, delObjs } from "@/api/jsonflow/flow-clazz"
import { useMessage, useMessageBox } from "@/hooks/message"

import { onCascaded, onLoadDicUrl, onLoaded } from "@/flow/components/convert-name/convert"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))
// 定义查询字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "defFlowId" })
onMounted(() => {
	onLoad(dicData)
})
// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
	onLoaded: onLoaded({ key: "createUser" }),
	onCascaded: onCascaded({ key: "defFlowId", cascades: ["flowNodeId"] }),
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	// 清空多选
	selectObjs.value = []
	getDataList()
}

// 导出excel
const exportExcel = () => {
	downBlobFile("/jsonflow/flow-clazz/export", state.queryForm, "flow-clazz.xlsx")
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm("确定要删除吗？")
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess("删除成功")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
}
</script>
