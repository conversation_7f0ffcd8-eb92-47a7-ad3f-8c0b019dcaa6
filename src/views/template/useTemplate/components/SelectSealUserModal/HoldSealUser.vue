<template>
  <div class="search-input w-300px mb-10px">
    <el-input
      v-model="searchValue"
      placeholder="请输入姓名/工号"
      prefix-icon="Search"
      clearable
      :maxlength="100"
      @clear="searchValue = ''"
    />
  </div>
  <div class="select-user-list" v-loading="loading">
    <div class="list-type">
      <div
        class="list-type-item"
        :class="{ active: ascription === item.value }"
        @click="changeStaffType(item.value)"
        v-for="item in staffTypeOptions"
        :key="item.value"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="list-content">
      <!-- 多选 -->
      <el-checkbox-group v-model="checked" @change="handleChange" v-if="multiple">
        <el-checkbox
          class="list-content-item"
          v-for="item in filterTreeData"
          :key="item.userId"
          :value="item.userId"
        >
          <UserNameItem :item="item" class="list-content-item-name" />
        </el-checkbox>
      </el-checkbox-group>
      <!-- 单选 -->
      <el-radio-group v-model="checked" @change="handleChange" v-else>
        <el-radio
          class="list-content-item"
          v-for="item in filterTreeData"
          :key="item.userId"
          :value="item.userId"
        >
          <UserNameItem :item="item" class="list-content-item-name" />
        </el-radio>
      </el-radio-group>
      <template v-if="!filterTreeData.length">
        <CustomEmpty class="p-y-20px!" />
      </template>
    </div>
  </div>
</template>

<script setup>
import { getHoldSealAPI } from "@/api/ess/seal/seal"

const UserNameItem = defineAsyncComponent(() => import("./UserNameItem.vue"))

const props = defineProps({
  multiple: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
})

const ascription = ref(0)
const treeData = ref([])
const loading = ref(false)
const checked = ref()
const searchValue = ref("") // 搜索关键字
const previousCheckedList = ref([]) // 上次数据

const staffTypeOptions = ref([
  { label: "校级", value: 0 },
  { label: "二级单位", value: 1 },
])

watch(
  () => props.modelValue,
  (newValue) => {
    if (!props.multiple) {
      checked.value = newValue[0]?.userId || ""
      return
    }
    checked.value = newValue?.map((item) => item.userId) || []
  },
  { immediate: true, deep: true }
)

// 获取数据
async function getList() {
  try {
    loading.value = true
    const res = await getHoldSealAPI(ascription.value)
    treeData.value = res.object
  } catch (error) {
    console.error("Error fetching tree data:", error)
  } finally {
    loading.value = false
  }
}

// 筛选后的数据
const filterTreeData = computed(() => {
  if (!searchValue.value) {
    return treeData.value
  }
  return treeData.value.filter((item) => {
    return (
      item.userName.includes(searchValue.value) ||
      item.workNumber.includes(searchValue.value)
    )
  })
})
const emit = defineEmits(["on-select", "on-cancel"])
function changeStaffType(e) {
  ascription.value = e
  getList()
}
function handleChange(e) {
  if (props.multiple) {
    const cancelItems = previousCheckedList.value.filter((i) => !e.includes(i))
    if (cancelItems.length) {
      emit("on-cancel", cancelItems[0]) // 只处理第一个取消项
    }
    const selectedUsers = treeData.value.reduce((acc, item) => {
      if (e.includes(item.userId)) {
        acc.push(item)
      }
      return acc
    }, [])
    emit("on-select", selectedUsers)
    previousCheckedList.value = [...e]
  } else {
    const result = treeData.value.find((item) => item.userId === e)
    emit("on-select", [result])
  }
}
// 清空选择
function clearSelect() {
  checked.value = null
}

getList()

defineExpose({
  clearSelect,
})
</script>

<style scoped lang="scss">
.select-user-list {
  display: flex;
  height: calc(100% - 50px);
  .list-type {
    width: 110px;
    flex-shrink: 0;
    text-align: center;
    padding-right: 10px;
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    &-item {
      cursor: pointer;
      border: 1px solid #d8d8d8;
      border-radius: 5px;
      padding: 5px;
      margin-bottom: 10px;

      &.active,
      &:hover {
        background: var(--el-color-primary);
        border-color: var(--el-color-primary);
        color: #fff;
      }
    }
  }
  .list-content {
    flex: auto;
    background: #fff;
    border-radius: 5px;
    height: 100%;
    padding: 15px 5px 15px 15px;
    overflow-y: auto;
    .list-content-item-name {
      width: 120px;
    }
    .el-radio {
      margin-right: 10px;
    }
  }
}
</style>
