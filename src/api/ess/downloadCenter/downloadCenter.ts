import request from "@/utils/request";
import {eduMinio, essPrefix} from "@/config/constant";

// 查询下载列表（分页）
export function getDownloadListPage(data: object) {
    return request({
        url: essPrefix + "/download/listPage",
        method: "post",
        data: data
    })
}

// 删除
export function deleteAllDownload(ids: Array) {
    return request({
        url: essPrefix + `/download/delete/${ids}`,
        method: "get"
    })
}

//取消下载
export function cancelDownload(id: string) {
    return request({
        url: essPrefix + `/download/cancel/${id}`,
        method: "get"
    })
}

//下载
export function startDownload(filename: string) {
    return request({
        url: eduMinio + "/minio/download",
        method: "post",
        responseType: "blob",
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        data: {filename: filename}
    })
}
