<template>
  <div class="signatory-table">
    <!-- <div>鼠标选中按住可上下拖动顺序</div> -->
    <el-table
      :data="modelValue"
      :stripe="false"
      row-key="id"
      ref="tableRef"
      class="draggable"
    >
      <!-- <el-table-column prop="index" width="60" :label="+order === 0 ? '顺序' : '序号'">
        <template #default="{ row, $index }">{{
          +order === 0 ? row.signOrder : $index + 1
        }}</template>
      </el-table-column> -->
      <el-table-column
        type="index"
        prop="index"
        width="60"
        :label="+order === 1 ? '序号' : '顺序'"
      />
      <el-table-column prop="organizationName" label="涉及组织机构" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.organizationName">
            {{ row.organizationName }}
          </span>
          <span v-else-if="row.insideFlag == 1 && !row.organizationName">个人</span>
        </template>
      </el-table-column>
      <el-table-column prop="approveName" label="经办人姓名" :formatter="nameFormatter" />
      <el-table-column
        prop="approveMobile"
        label="经办人手机号"
        :formatter="formatterPhoneNumber"
      ></el-table-column>
      <el-table-column prop="signAttributes" label="属性"></el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { replacePhone } from "@/utils/index"
import { cloneDeep } from "lodash-es"
import Sortable from "sortablejs"

const props = defineProps({
  order: {
    type: Number,
    default: 0, // 0有序 1无序
  },
})

// 手机号脱敏
const formatterPhoneNumber = (row) => {
  return replacePhone(row.approveMobile)
}

const modelValue = defineModel()
const tableRef = ref(null)

const rowDrop = () => {
  // 要拖拽元素的父容器 tbody
  const tbody = document.querySelector(".draggable .el-table__body-wrapper tbody")
  if (!tbody) return
  Sortable.create(tbody, {
    //  可被拖拽的子元素
    draggable: ".draggable .el-table__row",
    onEnd({ newIndex, oldIndex }) {
      let arr = cloneDeep(modelValue.value)
      const currRow = arr.splice(oldIndex, 1)[0]
      arr.splice(newIndex, 0, currRow)
      arr.forEach((element, index) => {
        element.signOrder = index + 1
      })
      modelValue.value = arr
    },
  })
}

function nameFormatter(row) {
  const workNumber = row.workNumber ? ` (${row.workNumber})` : ""
  return row.approveName + workNumber
}

onMounted(() => {
  // rowDrop()
})
</script>

<style scoped lang="scss">
.signatory-table {
  width: 100%;
  :deep(*) {
    td.el-table__cell {
      background: #fafafb;
    }
  }
}
</style>
