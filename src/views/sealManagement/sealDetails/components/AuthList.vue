<template>
	<div class="auth-list">
		<CustomTable
			ref="customTableRef"
			v-model:page="queryParams.page"
			v-model:limit="queryParams.limit"
			:data="tableData"
			:has-toolbar="false"
			:loading="loading"
			:total="total"
			@reload="getList"
		>
			<vxe-column type="seq" width="70" />
			<vxe-column
				title="持有人"
				field="holdUserName"
				min-width="100"
				show-overflow
			/>
			<vxe-column
				title="持有人归属部门"
				field="holdDeptName"
				min-width="100"
				show-overflow
			/>
			<vxe-column
				title="授权人"
				field="createUser"
				min-width="100"
				show-overflow
			/>
			<vxe-column
				title="授权时间"
				field="createTime"
				min-width="140"
				show-overflow
			/>
			<vxe-column
				title="授权结束时间"
				field="limitTime"
				min-width="100"
				show-overflow
			/>
		</CustomTable>
	</div>
</template>

<script setup>
import { getSealAuthListPage } from "@/api/ess/seal/sealAuth"

const route = useRoute()
const sealId = route.params.id
const loading = ref(true)
const total = ref(0)
const tableData = ref([])

const queryParams = reactive({
	sealId,
	page: 1,
	limit: 10,
})

async function getList() {
	try {
		loading.value = true
		let res = await getSealAuthListPage(queryParams)
		tableData.value = res.object.records
		total.value = res.object.total
	}
	finally {
		loading.value = false
	}
}

getList()
</script>

<style scoped lang="scss">
.auth-list{
  :deep(.white-body-box) {
    padding: 0;
  }
}
</style>
