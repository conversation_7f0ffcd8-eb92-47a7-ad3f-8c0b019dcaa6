import {
	defineConfig,
	presetUno,
	presetAttributify,
	transformerDirectives,
	transformerVariantGroup,
} from "unocss"

export default defineConfig({
	presets: [presetUno(), presetAttributify()],
	theme: {
		colors: {
			primary: "var(--el-color-primary)",
		},
		breakpoints: {
			xxl: "1536px",
		},
	},
	rules: [
		[
			/^dot-(\d+)-(.+)$/, // 匹配类名：dot-{size}-{color}
			([, size, color]) => ({
				"width": `${size}px`,
				"height": `${size}px`,
				"background-color": color.startsWith("#") ? color : "var(--el-color-primary)", // 支持颜色名或 hex 值
			}),
		],
		[
			/^title-line-(\d+)-(.+)$/, // 匹配类名：title-line-{size}-{color}
			([, size, color]) => ({
				"height": `${size}px`,
				"background-color": color.startsWith("#") ? color : "var(--el-color-primary)", // 支持颜色名或 hex 值
			}),
		],
	],
	shortcuts: {
		"flex-center": "flex justify-center items-center",
		"flex-x-center": "flex justify-center",
		"flex-y-center": "flex items-center",
		"flex-x-between": "flex items-center justify-between",
		"flex-x-end": "flex items-center justify-end",
		"text-overflow": "truncate",
		"bg-no-repeat-cover": "bg-no-repeat bg-cover",
		"abs-x-center": "absolute left-50% top-0 translate-x--1/2",
		"abs-y-center": "absolute left-0 top-50% translate-y--1/2",
		"abs-center": "absolute left-50% top-50% translate-x--1/2 translate-y--1/2",
		"dot": "relative pl-12px before:(content-empty absolute left-0 top-1/2 -translate-y-1/2 rounded-full dot-6-primary)",
		"title-line-b": "relative font-bold inline-block leading-20px before:(content-empty absolute left-0 bottom-[-1px] w-100% h-7px block)",
		"title-line":
      "relative font-bold pl-12px before:(content-empty absolute left-0 top-1/2 -translate-y-1/2 rounded-5px w-4px title-line-15-primary)",
	},
	transformers: [
		// 启用 @apply 功能
		transformerDirectives(),
		// 支持css class组合，eg: `<div class="hover:(bg-gray-400 font-medium) font-(light mono)">测试 unocss</div>`
		transformerVariantGroup(),
	],
})
