import request from "@/utils/request"
import { eduUser } from "@/config/constant"

// 查询角色列表
export function listRole(query) {
	return request({
		url: eduUser + "/system/role/list",
		method: "get",
		params: query,
	})
}

// 查询角色详细
export function getRole(roleId) {
	return request({
		url: eduUser + "/system/role/" + roleId,
		method: "get",
	})
}

// 新增角色
export function addRole(data) {
	return request({
		url: eduUser + "/system/role",
		method: "post",
		data: data,
	})
}

// 修改角色
export function updateRole(data) {
	return request({
		url: eduUser + "/system/role",
		method: "put",
		data: data,
	})
}

// 角色数据权限
export function dataScope(data) {
	return request({
		url: eduUser + "/system/role/dataScope",
		method: "put",
		data: data,
	})
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
	const data = {
		roleId,
		status,
	}
	return request({
		url: eduUser + "/system/role/changeStatus",
		method: "put",
		data: data,
	})
}

// 删除角色
export function delRole(roleId) {
	return request({
		url: eduUser + "/system/role/" + roleId,
		method: "delete",
	})
}

// 查询角色已授权用户列表
export function allocatedUserList(query) {
	return request({
		url: eduUser + "/system/role/authUser/allocatedList",
		method: "get",
		params: query,
	})
}

// 查询角色未授权用户列表
export function unallocatedUserList(query) {
	return request({
		url: eduUser + "/system/role/authUser/unallocatedList",
		method: "get",
		params: query,
	})
}

// 取消用户授权角色
export function authUserCancel(data) {
	return request({
		url: eduUser + "/system/role/authUser/cancel",
		method: "put",
		data: data,
	})
}

// 批量取消用户授权角色
export function authUserCancelAll(data) {
	return request({
		url: eduUser + "/system/role/authUser/cancelAll",
		method: "put",
		params: data,
	})
}

// 授权用户选择
export function authUserSelectAll(params) {
	return request({
		url: eduUser + "/system/role/authUser/selectAll",
		method: "put",
		params,
	})
}

// 根据角色ID查询部门树结构
export function deptTreeSelectForRoleId(roleId) {
	return request({
		url: eduUser + "/system/dept/roleDeptTreeselect/" + roleId,
		method: "get",
	})
}

// ess
// 查询角色信息列表 带分页
export function zsRoleListPage(data) {
	return request({
		url: eduUser + "/zsRoleInfoApi/zsRoleListPage",
		method: "post",
		data,
    headers:{
      repeatSubmit: false
    }
	})
}
// 新增子角色
export function addZsRoleInfo(data) {
	return request({
		url: eduUser + "/zsRoleInfoApi/addZsRoleInfo",
		method: "post",
		data,
	})
}
// 修改子角色
export function editZsRoleInfo(data) {
	return request({
		url: eduUser + "/zsRoleInfoApi/modifyZsRoleInfo",
		method: "post",
		data,
	})
}
// 删除子角色
export function delZsRoleInfo(roleId) {
	return request({
		url: eduUser + "/zsRoleInfoApi/deleteZsRoleInfo/" + roleId,
		method: "get",
	})
}
// 角色用户列表
export function roleUserPageList(data) {
	return request({
		url: eduUser + "/system/user/selectPageList",
		method: "post",
		data,
	})
}
// 批量新增用户信息绑定角色信息
export function batchAddZsUsersBindRole(data) {
	return request({
		url: eduUser + "/zsRoleInfoApi/batchAddZsUsersBindRole",
		method: "post",
		data,
	})
}
// 批量删除用户信息绑定角色信息
export function batchDelZsUsersBindRole(data) {
	return request({
		url: eduUser + "/zsRoleInfoApi/batchDelZsUsersBindRole",
		method: "post",
		data,
	})
}

// 批量修改角色部门
export function batchModifyZsRoleDeptInfo(data) {
	return request({
		url: eduUser + "/zsRoleInfoApi/batchModifyZsRoleDeptInfo",
		method: "post",
		data,
	})
}
// 修改数据权限
export function updateRoleInfoData(data) {
	return request({
		url: eduUser + "/zsRoleInfoApi/updateRoleInfoData",
		method: "post",
		data,
	})
}
// 获取指定类型角色信息
export function getExcludeRolesInfosAPI(data) {
	return request({
		url: eduUser + "/zsRoleInfoApi/getExcludeRolesInfos",
		method: "post",
		data,
	})
}
// 查询角色下的用户数量
export function queryUserCountByRoleId(params) {
	return request({
		url: eduUser + "/system/user/queryUserCountByRoleId",
		method: "get",
		params,
	})
}
