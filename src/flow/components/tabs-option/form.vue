<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="80%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="130px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item label="表单名称" prop="formName">
						<el-input v-model="form.formName" placeholder="请输入表单名称" clearable />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="图标" prop="icon">
						<IconSelector v-model="form.icon" placeholder="请输入图标" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="分组名称" prop="groupName">
						<el-select
							v-model="form.groupName"
							placeholder="请输入分组名称"
							style="width: 80%!important;"
							clearable
							filterable
							allow-create
							default-first-option
						>
							<el-option
								v-for="(item, index) in dicData.groupName"
								:key="index"
								:label="item.groupName"
								:value="item.groupName"
							/>
						</el-select>

						<el-button
							type="primary"
							size="small"
							round
							style="margin-left: 10px"
							@click="handleAddGroupName"
						>
							新增分组
						</el-button>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="表单类型" prop="type">
						<el-select
							v-model="form.type"
							placeholder="请选择表单类型"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.FORM_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col v-if="form.type === DIC_PROP.FORM_TYPE[1].value" :span="12" class="mb-1">
					<el-form-item label="PC端路径" prop="path">
						<el-tooltip content="当表单类型为系统表单时，可输入本地Vue组件路径，或者外部系统http或https开头的页面路径" placement="top">
							<el-input v-model="form.path" placeholder="请输入PC端路径" clearable />
						</el-tooltip>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="表单备注" prop="remark">
						<el-input v-model="form.remark" type="textarea" placeholder="请输入表单备注" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="排序值" prop="sort">
						<el-input-number
							v-model="form.sort"
							:min="1"
							:max="1000"
							placeholder="请输入排序值"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="版本" prop="version">
						<el-input-number
							v-model="form.version"
							:min="1"
							:max="1000"
							placeholder="请输入版本"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="默认展示" prop="isActive">
						<el-radio-group v-model="form.isActive">
							<el-radio v-for="(item, index) in DIC_PROP.YES_OR_NO" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="提交时自动审批" prop="isAutoAudit">
						<el-radio-group v-model="form.isAutoAudit">
							<el-radio v-for="(item, index) in DIC_PROP.YES_OR_NO" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="状态" prop="status">
						<el-radio-group v-model="form.status">
							<el-radio v-for="(item, index) in DIC_PROP.TEMP_STATUS" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="TabsOptionDialog">
import { useMessage, useMessageBox } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/order/flow-application"
import { onLoadDicUrl, onUpdateDicData } from "@/flow/components/convert-name/convert"
import { DIC_PROP } from "../../support/dict-prop"
import { notifyLeft } from "../../index"
import { validateFormTypeSave } from "@/api/order/order-key-vue"
const emit = defineEmits(["refresh"])
// 引入组件
const IconSelector = defineAsyncComponent(() => import("@/components/IconSelector/index.vue"))

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "groupName" })
const onUpdate = onUpdateDicData({ key: "groupName" })
onMounted(() => {
	onLoad(dicData)
})

// 提交表单数据
const form = reactive({
	icon: "",
	formName: "",
	groupName: "",
	path: "",
	prop: "",
	isActive: "1",
	isAutoAudit: "0",
	status: "-1",
	version: 1,
	sort: 1,
})

// 定义校验规则
const dataRules = ref({
	icon: [{ required: true, message: "图标不能为空", trigger: "blur" }],
	formName: [{ required: true, message: "表单名称不能为空", trigger: "blur" }],
	groupName: [{ required: true, message: "分组名称不能为空", trigger: "blur" }],
	prop: [{ required: true, message: "tab标识不能为空", trigger: "blur" }],
	isActive: [{ required: true, message: "默认展示不能为空", trigger: "blur" }],
	isAutoAudit: [{ required: true, message: "提交时自动审批不能为空", trigger: "blur" }],
	sort: [{ required: true, message: "排序值不能为空", trigger: "blur" }],
	type: [{ required: true, message: "表单类型不能为空", trigger: "blur" }],
	status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
	version: [{ required: true, message: "版本不能为空", trigger: "blur" }],
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap: any = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取TabsOption信息
	if (id) {
		form.id = id
		getTabsOptionData(id)
	}
}

function handleAddGroupName() {
	useMessageBox().prompt("请输入新的分组名称")
		.then(({ value }) => {
			form.groupName = value
			onUpdate(dicData, form)
		})
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	if (form.type === DIC_PROP.FORM_TYPE[1].value) {
		if (!form.path) {
			notifyLeft("当前表单类型为系统表单，【PC端路径】不能为空", "warning")
			return false
		}
	}
	let isDesign = form.type !== DIC_PROP.FORM_TYPE[1].value
	if (isDesign && !form.formInfo) {
		notifyLeft("当前表单类型为设计表单，请先完善第二项设计的表单内容", "warning")
		return false
	}
	validateFormTypeSave(form)
	try {
		loading.value = true
		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getTabsOptionData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
