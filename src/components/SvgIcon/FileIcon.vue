<script setup lang="ts">
// 引入阿里云字体图标css
import "@/assets/iconfont/iconfont.css"
import "@/assets/iconfont/iconfont.js"
interface Props {
  fileName?: string
}

const props = withDefaults(defineProps<Props>(), {
  fileName: "",
})

// 获取文件图标
function getFileIcon(): string {
  if (!props.fileName) {
    return "icon-file"
  }
  const fileType = props.fileName.split(".").pop()?.toLowerCase() || "file"
  return `icon-${fileType}`
}
</script>

<template>
  <svg aria-hidden="true" class="w-20px h-20px mr-5px flex-shrink-0">
    <use :xlink:href="'#' + getFileIcon()"></use>
  </svg>
</template>
