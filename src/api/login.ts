import request from "@/utils/request"
import { auth, eduUser } from "@/config/constant"
import qs from "qs"

// 登录方法
export function login(username: string, password: string, code: string, uuid: string) {
	return request({
		url: auth + "/auth/login",
		headers: {
			isToken: false,
			repeatSubmit: false,
		},
		method: "post",
		data: { username, password, code, uuid },
	})
}

// 刷新方法
export function refreshToken() {
	return request({
		url: auth + "/auth/refresh",
		method: "post",
	})
}

// 获取用户详细信息
export function getInfo() {
	return request({
		url: eduUser + "/system/user/getInfo",
		method: "get",
	})
}

// 退出方法
export function logout(params: Object) {
	return request({
		url: auth + "/auth/logout?" + qs.stringify(params),
		method: "get",
	})
}

// 获取验证码
export function getCodeImg() {
	return request({
		url: "/code",
		headers: {
			isToken: false,
		},
		method: "get",
		timeout: 20000,
	})
}

// 单点登录方法
export function casLoginAPI(params: Object) {
	return request({
		// url: auth + "/auth/caszsLogin",
		url: auth + "/auth/zsCasLogin",
		headers: {
			isToken: false,
		},
		method: "post",
		params: params,
	})
}

// ess 登录 zsLogin
export function zsLogin(data: Object) {
	return request({
		url: auth + "/auth/zsLogin",
		headers: {
			isToken: false,
		},
		method: "post",
		data,
	})
}
