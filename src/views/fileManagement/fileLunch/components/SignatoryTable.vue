<template>
  <div class="signatory-table">
    <div v-if="!unordered">鼠标选中按住可上下拖动顺序</div>
    <el-table
      :data="modelValue"
      :stripe="false"
      row-key="id"
      ref="tableRef"
      class="draggable"
      :class="{ 'is-order': !unordered }"
    >
      <!-- <el-table-column width="30" label="" v-if="!unordered">
        <template>
          <svg-icon name="local-drag-dot" />
        </template>
      </el-table-column> -->
      <el-table-column
        type="index"
        prop="index"
        width="60"
        :label="unordered ? '序号' : '顺序'"
      />
      <el-table-column prop="organizationName" label="涉及组织机构" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.organizationName">
            {{ row.organizationName }}
          </span>
          <span v-else-if="row.insideFlag == 1 && !row.organizationName">个人</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="approveName"
        label="经办人姓名"
        show-overflow-tooltip
        :formatter="nameFormatter"
      />
      <el-table-column
        prop="approveMobile"
        label="经办人手机号"
        width="120"
        :formatter="formatterPhoneNumber"
      ></el-table-column>
      <el-table-column prop="signAttributes" label="属性" width="100">
        <template #default="{ row }">
          <span v-if="row.insideFlag == 1 && row.organizationName">盖章</span>
          <span v-else-if="row.insideFlag == 1 && !row.organizationName">签字</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { replacePhone } from "@/utils/index"
import { cloneDeep } from "lodash-es"
import Sortable from "sortablejs"

const { proxy } = getCurrentInstance()

const props = defineProps({
  unordered: {
    type: Boolean,
    default: false, // 签署顺序 false有序 true-无序
  },
})

// 手机号脱敏
const formatterPhoneNumber = (row) => {
  return replacePhone(row.approveMobile)
}

const modelValue = defineModel()
const tableRef = ref(null)

const rowDrop = () => {
  // 要拖拽元素的父容器 tbody
  const tbody = document.querySelector(".draggable .el-table__body-wrapper tbody")
  if (!tbody) return
  Sortable.create(tbody, {
    //  可被拖拽的子元素
    ghostClass: "sortable-ghost",
    draggable: ".draggable .el-table__row",
    onEnd({ newIndex, oldIndex }) {
      let arr = cloneDeep(modelValue.value)
      const currRow = arr.splice(oldIndex, 1)[0]
      arr.splice(newIndex, 0, currRow)
      modelValue.value = arr
    },
  })
}

function nameFormatter(row) {
  const workNumber = row.workNumber ? ` (${row.workNumber})` : ""
  return row.approveName + workNumber
}

onMounted(() => {
  rowDrop()
})
</script>

<style scoped lang="scss">
.signatory-table {
  width: 100%;
  .is-order {
    :deep(tbody tr) {
      cursor: move;
    }
  }

  :deep(*) {
    td.el-table__cell {
      background: #fafafb;
    }
    tr.el-table__row.sortable-ghost {
      background: linear-gradient(to right, var(--el-color-primary-light-8), #fff);
      position: relative;
      &::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        display: block;
        height: 100%;
        width: 100%;
        background: transparent;
        border: 1px solid var(--el-color-primary);
        z-index: 99;
        box-sizing: border-box;
      }
      & > td.el-table__cell {
        background: transparent !important;
      }
    }
    tbody .el-table__row:not(:last-child) {
      td.el-table__cell {
        border-bottom: 1px solid #f4f4f5;
      }
    }
  }
}
</style>
