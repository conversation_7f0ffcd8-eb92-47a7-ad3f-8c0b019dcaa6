<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item :label="$t('defFlow.flowName')" prop="flowName">
						<el-input
							v-model="state.queryForm.flowName"
							:placeholder="t('defFlow.inputFlowNameTip')"
							style="max-width: 180px"
						/>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							{{ $t('common.queryBtn') }}
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							{{ $t('common.resetBtn') }}
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.addBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_defflow_add'"
							icon="Plus"
							type="primary"
							class="ml10"
							@click="formDialogRef.openDialog('add')"
						/>
					</el-tooltip>

					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.delBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_defflow_del'"
							plain
							:disabled="multiple"
							icon="Delete"
							type="primary"
							class="ml10"
							@click="handleDelete(selectObjs)"
						/>
					</el-tooltip>

					<right-toolbar
						v-model:show-search="showSearch"
						:export="'jsonflow_defflow_export'"
						class="ml10"
						style="float: right;margin-right: 20px"
						@export-excel="exportExcel"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" :label="t('defFlow.index')" width="40" />
				<el-table-column prop="flowName" :label="t('defFlow.flowName')" show-overflow-tooltip />
				<el-table-column prop="flowKey" :label="t('defFlow.flowKey')" show-overflow-tooltip />
				<el-table-column prop="groupName" :label="t('defFlow.groupName')" show-overflow-tooltip />
				<!--                <el-table-column prop="isIndependent" :label="t('defFlow.isIndependent')"  show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.YES_OR_NO" :value="scope.row.isIndependent"></dict-tag>
                    </template>
                </el-table-column>-->
				<el-table-column
					prop="remark"
					:label="t('defFlow.remark')"
					width="300"
					show-overflow-tooltip
				/>
				<el-table-column prop="status" :label="t('defFlow.status')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.TEMP_STATUS" :value="scope.row.status" />
					</template>
				</el-table-column>
				<el-table-column prop="version" :label="t('defFlow.version')" show-overflow-tooltip />
				<!--                <el-table-column prop="createUser" :label="t('defFlow.createUser')"  show-overflow-tooltip>
                    <template #default="scope">
                        <convert-name :options="state.dicData.createUser" :value="scope.row.createUser"
                                      :valueKey="'userId'" :showKey="'name'"></convert-name>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" :label="t('defFlow.createTime')"  show-overflow-tooltip/>-->
				<el-table-column :label="$t('common.action')" width="120">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.viewBtn') }}
							</template>
							<el-button
								v-auth="'jsonflow_defflow_view'"
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								修改
							</template>
							<el-button
								v-auth="'jsonflow_defflow_edit'"
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.delBtn') }}
							</template>
							<el-button
								v-auth="'jsonflow_defflow_del'"
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
						<el-tooltip v-if="scope.row.fromType === '1'" placement="top">
							<template #content>
								{{ $t('jfI18n.designFlow') }}
							</template>
							<el-button
								v-auth="'jsonflow_defflow_edit'"
								text
								type="primary"
								icon="Share"
								@click="handleEditPhoto(scope.row)"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemDefFlow">
import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchList, delObjs } from "@/api/jsonflow/def-flow"
import { useMessage, useMessageBox } from "@/hooks/message"

import { useI18n } from "vue-i18n"
import { onLoaded } from "@/flow/components/convert-name/convert"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))
const { t } = useI18n()
// 定义查询字典

// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: { fromType: "1" },
	pageList: fetchList,
	onLoaded: onLoaded({ key: "createUser" }),
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	// 清空多选
	selectObjs.value = []
	getDataList()
}

// 导出excel
const exportExcel = () => {
	downBlobFile("/jsonflow/def-flow/export", state.queryForm, "def-flow.xlsx")
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t("common.delConfirmText"))
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess(t("common.delSuccessText"))
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
}

const router = useRouter()
const handleEditPhoto = (row: any) => {
	let src = "/jsonflow/flow-design/index"
	router.push({
		path: src,
		query: {
			currFlowId: row.id,
			flowType: "0",
		},
	})
}
</script>
