<template>
  <el-dialog
    v-model="dialogVisible"
    width="1500px"
    title="签署方"
    :before-close="handleClose"
    top="2vh"
  >
    <div class="signatory-container">
      <div class="form-body">
        <div class="form-body-item" v-for="item in recipientList" :key="item.value">
          <div class="recipient-title">
            {{ selectDictLabel(recipient_type, item.key) }}
          </div>
          <el-table :data="item.list">
            <el-table-column label="签署信息" prop="componentNames">
              <template #default="{ row }">
                <div class="whitespace-pre-wrap">
                  {{ filterComponentNames(row.componentInfoDtos) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="单位名称"
              prop="organizationName"
              v-if="item.key === 'ENTERPRISE_2_E'"
            ></el-table-column>
            <el-table-column
              :label="item.key === 'INDIVIDUAL_P' ? '姓名' : '经办人'"
              prop="approveName"
            ></el-table-column>
            <el-table-column
              :label="item.key === 'INDIVIDUAL_P' ? '手机号' : '经办人手机号'"
              prop="approveMobile"
              :formatter="formatterPhoneNumber"
            ></el-table-column>
            <el-table-column label="操作" prop="opt" width="90">
              <template #default="{ row, $index }">
                <el-button
                  text
                  icon="Switch"
                  type="primary"
                  class="opt-btn"
                  v-if="row.approveName"
                  @click="openDialog(row, $index, item.key)"
                >
                  更改
                </el-button>
                <el-button
                  text
                  icon="Plus"
                  type="primary"
                  class="opt-btn"
                  v-else
                  @click="openDialog(row, $index, item.key)"
                >
                  添加
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="w-57% flex-shrink-0">
        <TemplateIframe class="h-full" :template-info="templateInfo" title="模版预览" />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>

    <!-- 选择全部 -->
    <UserRolePicker
      ref="userPickerRef"
      title="选择校内签字人（注：外国国籍成员选择功能受限，暂不开放）"
      izChineseGj="1"
      :isOnlyOne="true"
      :selectTypes="['user']"
      @onSelectItems="selectUser"
    />
    <!-- 选择企业 -->
    <EnterpriseUserModal ref="enterpriseUserRef" hiddenAdd @select="changeSelectItems" />
    <!-- 选择联系人 -->
    <ExtUserModal ref="extUserModalRef" hiddenAdd @select="changeSelectItems" />
    <!-- 印章管理员 -->
    <SelectSealUserModal ref="selectSealUserRef" @select="changeSelectItems" />
  </el-dialog>
</template>

<script setup>
import { getTemplateRecipientsAPI } from "@/api/ess/template/myTemplate"
import { replacePhone } from "@/utils/index"

const UserRolePicker = defineAsyncComponent(() =>
  import("@/flow/components/user-role/picker2.vue")
)
const EnterpriseUserModal = defineAsyncComponent(() =>
  import("./EnterpriseUserModal.vue")
)
const ExtUserModal = defineAsyncComponent(() => import("./ExtUserModal.vue"))

const SelectSealUserModal = defineAsyncComponent(() =>
  import("./SelectSealUserModal/index.vue")
)
const TemplateIframe = defineAsyncComponent(() =>
  import("@/views/template/useTemplate/components/TemplateIframe.vue")
)

const { proxy } = getCurrentInstance()

// seal_belong_subject归属主体=中大/中大深圳
const { seal_belong_subject, recipient_type } = proxy.useDict(
  "seal_belong_subject",
  "recipient_type"
)
const organizationId = ref(null)

watchEffect(() => {
  if (seal_belong_subject.value?.length && !organizationId.value) {
    organizationId.value = seal_belong_subject.value[0].value
  }
})

const props = defineProps({
  templateInfo: {
    type: Object,
    default: () => ({}),
  },
})
const dialogVisible = ref(false)
const userPickerRef = ref(null)
const recipientList = ref([])
const handleClose = () => {
  dialogVisible.value = false
}

const open = (data) => {
  dialogVisible.value = true
  if (data.length) {
    setDraftData(data)
  }
}

// 回显草稿数据
const setDraftData = (signData) => {
  signData.forEach((signItem) => {
    // 在recipientList中查找匹配的recipientId
    recipientList.value.forEach((recipient) => {
      const matchedItem = recipient.list.find(
        (item) => item.recipientId === signItem.recipientId
      )
      if (matchedItem) {
        // 合并数据
        Object.assign(matchedItem, {
          ...signItem,
          approveName: signItem.approveName || signItem.userName, // 将userName赋值给approveName
          ...(signItem.componentInfoDtos && {
            componentInfoDtos: signItem.componentInfoDtos,
          }),
          ...(signItem.signAttributes && { signAttributes: signItem.signAttributes }),
        })
      }
    })
  })
}

// 获取模版签署方信息
const getTemplateRecipients = async () => {
  const res = await getTemplateRecipientsAPI(props.templateInfo.essTemplateId)
  recipientList.value = Object.entries(res.object).map(([key, value]) => ({
    key,
    list: value,
  }))
}

const emit = defineEmits(["confirm"])
const handleSubmit = () => {
  // 合并所有类型的 list 为一个数组
  const allSignatories = recipientList.value.flatMap((recipient) => recipient.list)
  // 检查是否所有签署方都有 userId
  const allHaveUserId = allSignatories.every((item) => item.approveName)

  if (!allHaveUserId) {
    proxy.$modal.msgError("请完善所有签署方信息")
    return
  }
  emit("confirm", allSignatories)
  handleClose()
}

const updateRow = ref(null)

/**
 * @param {Array<{name: string, phonenumber: string, userId: string}>} selectedItems
 */
const changeSelectItems = (selectedItems) => {
  if (!selectedItems?.length || !updateRow.value) return

  const [user] = selectedItems // 直接解构获取首个选中项
  const { key, rowIndex, approveType, signAttributes } = updateRow.value

  // 使用 find 替代 findIndex + 直接索引访问
  const targetRecipient = recipientList.value.find((item) => item.key === key)
  if (!targetRecipient) return

  // 安全访问并更新数据
  const targetRow = targetRecipient.list[rowIndex]
  if (targetRow) {
    Object.assign(targetRow, {
      ...user,
      approveName: user.name || user.operatorName || user.userName,
      approveIdCardType: user.certificateType,
      approveIdCardNumber: user.certificateNo,
      approveMobile: user.phonenumber || user.phoneNumber,
      organizationName: user.enterpriseName || user.organizationName,
      approveType,
      signAttributes: signAttributes || ["签字"],
      insideFlag: user.userId ? 0 : 1, // 0-校内签署方 1-校外
    })
  }
}

// 打开弹窗
const openDialog = (row, index, key) => {
  updateRow.value = { ...row, rowIndex: index, key }
  if (key === "ENTERPRISE_1_E") {
    // 个人签名/印章
    if (
      row.componentInfoDtos?.[0].componentType === "SIGN_SIGNATURE" &&
      row.componentInfoDtos.length === 1
    ) {
      updateRow.value.approveType = 0
      userPickerRef.value.onOpen() //
    } else {
      updateRow.value.approveType = 0
      proxy.$refs.selectSealUserRef.open()
    }
  } else if (key === "ENTERPRISE_2_E") {
    updateRow.value.approveType = 0
    proxy.$refs.enterpriseUserRef.open()
  } else {
    // 校外联系人
    updateRow.value.approveType = 1
    proxy.$refs.extUserModalRef.open()
  }
}

// 选择个人签署方
const selectUser = (e) => {
  const orgName = proxy.selectDictLabel(seal_belong_subject.value, organizationId.value)
  e = e.map((i) => {
    return {
      ...i,
      organizationId: organizationId.value,
      organizationName: orgName,
    }
  })
  changeSelectItems(e)
}

const filterComponentNames = (componentInfoDtos) => {
  if (!componentInfoDtos || !Array.isArray(componentInfoDtos)) {
    return []
  }
  return componentInfoDtos.map((i) => i.componentName).join("\n")
}

// 手机号脱敏
const formatterPhoneNumber = (row) => {
  return replacePhone(row.approveMobile)
}
getTemplateRecipients()

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
@use "sass:list";
.signatory-container {
  display: flex;
  justify-content: space-between;
  height: 80vh;
  .form-body {
    width: 42%;
    height: 100%;
    overflow-y: auto;
    padding-left: 10px;
  }
}

.form-body-item {
  margin-bottom: 30px;
  position: relative;
  padding-top: 30px;
  .recipient-title {
    display: inline-block;
    color: #fff;
    height: 40px;
    line-height: 30px;
    width: 150px;
    text-align: center;
    background-size: 100% 100%;
    font-size: 14px;
    position: absolute;
    top: 0;
    left: -12px;
    z-index: 99;
  }
  $colors: #f2f8f7, #f2f6fc, #fbf6f3;
  $btnColors: #24a87e, #2e89ff, #ff7b17;
  @for $i from 1 through 3 {
    &:nth-child(#{$i}) {
      .recipient-title {
        background-image: url(@/assets/images/type-title-#{$i}.png);
      }
      :deep(.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell) {
        background-color: list.nth($colors, $i);
      }
      .opt-btn {
        color: list.nth($btnColors, $i);
        padding-left: 0;
        :deep(span) {
          margin-left: 2px;
        }
        &:hover {
          background: transparent;
        }
      }
    }
  }
  :deep(.el-table) {
    tr {
      background-color: #fbfbfc;
    }
    tbody .el-table__row:not(:last-child) {
      td.el-table__cell {
        border-bottom: 1px solid #f4f4f5;
      }
    }
  }
}

.belong-radio-box {
  margin-bottom: 10px;
  :deep(.el-radio) {
    background: var(--el-color-primary-light-9);
    border: none;
    height: 40px;
    min-width: 200px;
    padding-left: 15px;
    border-radius: 6px;
    .el-radio__inner {
      background: none;
      border-color: #b2b2b2;
    }
    &.is-checked {
      background: var(--el-color-primary);
      .el-radio__label {
        color: #fff;
      }
      .el-radio__inner {
        border-color: #fff;
        &::before {
          border-color: var(--el-color-primary);
        }
        &::after {
          background: #fff !important;
          width: 7px;
          height: 7px;
        }
      }
    }
  }
}
</style>
