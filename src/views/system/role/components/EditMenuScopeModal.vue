<template>
  <el-dialog
    v-model="dialogVisible"
    title="菜单权限"
    width="400px"
    :before-close="handleClose"
  >
    <div class="u-m-l-10">
      <el-checkbox
        v-model="menuExpand"
        class="u-m-l-12"
        @change="handleCheckedTreeExpand"
      >
        展开/折叠
      </el-checkbox>
      <template v-if="isEdit">
        <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll">
          全选/全不选
        </el-checkbox>
        <el-checkbox v-model="form.menuCheckStrictly" @change="handleCheckedTreeConnect">
          父子联动
        </el-checkbox>
      </template>
      <div class="border-tree">
        <el-tree
          ref="menuRef"
          class="tree-border"
          :data="menuOptions"
          show-checkbox
          node-key="id"
          :check-strictly="!form.menuCheckStrictly"
          empty-text="加载中，请稍候"
          :props="defaultProps"
        />
      </div>
    </div>
    <template #footer v-if="isEdit">
      <el-button type="primary" @click="submitForm" :loading="loading">确定</el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>

<script setup name="EditMenuScopeModal">
import { roleMenuTreeselect, treeselect as menuTreeselect } from "@/api/system/menu"
import { updateRole } from "@/api/system/role"
const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const loading = ref(false)
const formDisabled = ref(false) // 禁用
const form = ref({})
const menuOptions = ref([])
const menuNodeAll = ref(false)
const menuExpand = ref(false)
const menuRef = ref(null)

const isEdit = ref(import.meta.env.VITE_SYS_EDIT === "true") // 是否可编辑

const defaultProps = ref({
  children: "children",
  label: "label",
  disabled: () => {
    return !isEdit.value
  },
})

const emit = defineEmits(["change"])

const open = (item) => {
  dialogVisible.value = true
  form.value = item
  formDisabled.value = !!item.parentId
  getRoleMenuTreeselect()
}
// 取消
const handleClose = () => {
  menuNodeAll.value = false
  menuExpand.value = false
  dialogVisible.value = false
  menuOptions.value = []
}
// 确定
const submitForm = () => {
  loading.value = true
  const menuIds = getMenuAllCheckedKeys()
  updateRole({ ...form.value, menuIds })
    .then((res) => {
      proxy.$modal.msgSuccess("保存成功")
      handleClose()
      emit("change")
    })
    .finally(() => (loading.value = false))
}

/** 根据角色ID查询菜单树结构 */
function getRoleMenuTreeselect() {
  loading.value = true
  roleMenuTreeselect(form.value.roleId)
    .then((res) => {
      nextTick(() => {
        menuOptions.value = res.menus
        let checkedKeys = res.checkedKeys
        checkedKeys.forEach((v) => {
          nextTick(() => {
            menuRef.value.setChecked(v, true, false)
          })
        })
      })
    })
    .finally(() => (loading.value = false))
}

/** 树权限（展开/折叠） */
function handleCheckedTreeExpand(value) {
  let treeList = menuOptions.value
  for (let i = 0; i < treeList.length; i++) {
    menuRef.value.store.nodesMap[treeList[i].id].expanded = value
  }
}

/** 树权限（全选/全不选） */
function handleCheckedTreeNodeAll(value) {
  menuRef.value.setCheckedNodes(value ? menuOptions.value : [])
}

/** 树权限（父子联动） */
function handleCheckedTreeConnect(value) {
  form.value.menuCheckStrictly = value ? true : false
}

/** 所有菜单节点数据 */
function getMenuAllCheckedKeys() {
  // 目前被选中的菜单节点
  let checkedKeys = menuRef.value.getCheckedKeys()
  // 半选中的菜单节点
  let halfCheckedKeys = menuRef.value.getHalfCheckedKeys()
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
  return checkedKeys
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.border-tree {
  background: #f7f8f9;
  border-radius: 12px;
  padding: 12px;
  margin-top: 10px;
  .el-tree {
    background: transparent;
  }
}
</style>
