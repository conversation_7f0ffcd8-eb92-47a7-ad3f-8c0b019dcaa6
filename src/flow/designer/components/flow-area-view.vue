<template>
  <div id="jfDragWrapPaper">
    <div id="jfWrapPaper" class="jf-wrap-paper">
      <div id="jsonflowMainPaper"/>
    </div>
    <div id="minimapPaperView" class="jsonflow-navigator"/>
    <div class="container-scale status-tip">
      <el-button
          type="success"
          size="small"
          class="flow-tip-btn executed"
      />
      已执行
      <el-button
          type="primary"
          size="small"
          class="flow-tip-btn executing"
      />
      执行中
      <el-button
          type="info"
          size="small"
          class="flow-tip-btn unexecuted"
      />
      未执行
    </div>
    <div class="container-scale btn-zoom">
      <el-button
          icon="ZoomIn"
          circle
          size="small"
          type="default"
          @click="methods.enlargePaper"
      />
      <span>{{ data.container.scaleShow }}% </span>
      <el-button
          icon="ZoomOut"
          circle
          size="small"
          type="default"
          @click="methods.narrowPaper"
      />
    </div>
    <vue-context-menu :context-menu-data="data.nodeContMenuData"/>
    <vue-context-menu :context-menu-data="data.linkContMenuData"/>
  </div>
</template>

<script setup name="FlowAreaView">
import { flowConfig } from "../config/flow-config"
import { utils } from "../utils/common"
import { deepClone } from "@/utils/index"
import { validateNull } from "@/utils/validate"
import { hideVueContextmenuName } from "@/flow/utils"

// 引入组件
const VueContextMenu = defineAsyncComponent(() =>
    import("../../components/contextmenu/index.vue")
)
const $emit = defineEmits(["initJsonFlowView"])
const props = defineProps({
  currSelect: {
    type: Object,
    default: {},
  },
})
const data = reactive({
  container: {
    scaleShow: utils.mul(flowConfig.defaultStyle.containerScale.init, 100),
  },
  linkContMenuData: deepClone(flowConfig.contextMenu.linkView),
  nodeContMenuData: deepClone(flowConfig.contextMenu.nodeView),
})

const methods = {
  // 画布放大
  enlargePaper() {
    data.container.scaleShow = window._jfOperate.zoomOut()
  },
  // 画布缩小
  narrowPaper() {
    let zoomIn = window._jfOperate.zoomIn()
    data.container.scaleShow = parseInt(zoomIn)
  },
  // 节点click事件
  showNodeClickMenu(currSelect, e) {
    // TODO 业务侧替换其他内容
    methods.showNodeContMenu(currSelect, e)
  },
  // 节点hover事件
  showNodeContMenu(currSelect, e) {
    if (validateNull(currSelect)) currSelect = props.currSelect

    if (Object.keys(currSelect).length === 0) return
    // 计算节点信息
    methods.updateNodeContMenuData(currSelect)
    let event = window.event || e
    event.preventDefault()
    hideVueContextmenuName()
    let x = event.clientX
    let y = event.clientY
    data.nodeContMenuData.axis = {x, y}
  },
  updateNodeContMenuData(currSelect) {
    let nodeNameBtnName = flowConfig.contextMenu.nodeView.menulists[0].btnName
    let text
    if (!currSelect.attributes.attrs.label) text = currSelect.attrs.label.text
    else text = currSelect.attributes.attrs.label.text
    data.nodeContMenuData.menulists[0].btnName = nodeNameBtnName + text
    data.nodeContMenuData.menulists[1].btnName = currSelect.attributes.startTime
    data.nodeContMenuData.menulists[2].btnName = currSelect.attributes.userRoleName
    data.nodeContMenuData.menulists[3].btnName = currSelect.attributes.userName
    data.nodeContMenuData.menulists[4].btnName = currSelect.attributes.remark
  },
  // 连接click事件
  showLinkClickMenu(currSelect, e) {
    // TODO 业务侧替换其他内容
    methods.showLinkContMenu(currSelect, e)
  },
  // 连接线hover事件
  showLinkContMenu(currSelect, e) {
    if (validateNull(currSelect)) currSelect = props.currSelect

    if (Object.keys(currSelect).length === 0) return
    // 计算连线条件
    let varKeyVal = currSelect.attributes.attrs.cdata.attrs.varKeyVal
    hideVueContextmenuName()
    if (!varKeyVal) return
    let btnName = flowConfig.contextMenu.linkView.menulists[0].btnName
    data.linkContMenuData.menulists[0].btnName = btnName + varKeyVal

    let event = window.event || e
    event.preventDefault()
    event.stopPropagation()
    let x = event.clientX
    let y = event.clientY
    data.linkContMenuData.axis = {x, y}
  },
}

onMounted(() => {
  nextTick(() => {
    $emit("initJsonFlowView")
  })
})

// 暴露变量
defineExpose({
  showNodeContMenu: methods.showNodeContMenu,
  showNodeClickMenu: methods.showNodeClickMenu,
  showLinkContMenu: methods.showLinkContMenu,
  showLinkClickMenu: methods.showLinkClickMenu,
})
</script>

<style lang="scss">
@use "../assets/style/flow-area.scss" as *;
@use "../assets/style/flow-paper.css" as *;
</style>
