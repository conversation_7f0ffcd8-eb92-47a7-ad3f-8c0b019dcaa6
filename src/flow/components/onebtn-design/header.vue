<template>
  <div>
    <div class="header m-t-10px">
      <Steps
        class="step-box"
        width="500px"
        :steps="tabsOptions"
        :current="stepIndex"
        @change="methods.activeMenu"
      />

      <div v-if="form.active !== 'flowDesign'" class="btn-publish">
        <el-button
          icon="CaretRight"
          type="primary"
          @click="methods.activeMenu(form.active === 'formSetting' ? 2 : 3)"
        >
          下一步
        </el-button>
      </div>

      <div v-if="form.active === 'flowDesign'" class="btn-publish">
        <template v-if="!validateRunFlow(props)">
          <!-- <el-button icon="Upload" type="primary" @click="methods.publish(null, true)">
						升版本
					</el-button> -->
          <el-button icon="CirclePlus" type="primary" @click="methods.publish('-1')">
            暂存
          </el-button>
        </template>
        <el-button icon="Promotion" type="primary" @click="methods.publish('1')">
          发布
        </el-button>
      </div>
      <div class="btn-back">
        <el-button icon="Back" circle @click="methods.exitFlowForm" />
        <span style="margin-left: 20px">
          <i :class="props.currFlowForm.icon" />
          <span>{{ props.currFlowForm.formName }} V{{ props.currFlowForm.version }}</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="FlowDesignHeader">
import { notifyLeft } from "@/flow"
import { validateNull } from "@/utils/validate"

const Steps = defineAsyncComponent(() => import("@/components/Steps/index.vue"))

const { proxy } = getCurrentInstance()
const $emit = defineEmits(["handleDesignFlow", "syncCurrFlowForm", "publish"])
const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null,
  },
})
const form = reactive({
  active: "formSetting",
  interval: {},
  intervalTime: 2,
})

const tabsOptions = ref([
  {
    title: "表单设置",
    value: "formSetting",
  },
  {
    title: "表单设计",
    value: "formDesign",
  },
  {
    title: "流程设计",
    value: "flowDesign",
  },
])

const stepIndex = computed(() => {
  return tabsOptions.value.findIndex((item) => item.value === form.active) + 1
})

const methods = {
  publish(status, version) {
    $emit(
      "publish",
      form.active,
      () => {
        props.currFlowForm[form.active] = true
      },
      status,
      version
    )
  },
  async exitFlowForm() {
    let text = ""
    // 判断是否都保存
    if (props.currFlowForm.formSetting !== true) text += "【表单设置】,"
    if (props.currFlowForm.formDesign !== true) text += "【表单设计】,"
    if (props.currFlowForm.flowDesign !== true) text += "【流程设计】"
    if (!text) {
      $emit("handleDesignFlow", false)
      return
    }
    try {
      await proxy.$modal.confirm(text + "未保存，是否继续退出?")
    } catch {
      return
    }
    $emit("handleDesignFlow", false)
  },
  activeMenu(tab) {
    let menu = tabsOptions.value[tab - 1].value
    let active = form.active

    // 判断是否在操作中
    if (props.currFlowForm.flowDesigning === true) {
      if (!validateNull(form.interval)) clearInterval(form.interval)
      let intervalTime = 0
      form.interval = setInterval(() => {
        methods.validateFlowDesigning(menu, active, intervalTime)
        intervalTime++
      }, 1000)
      return true
    }
    methods.doActiveMenu(menu, active)
  },
  validateFlowDesigning(menu, active, intervalTime = 0) {
    notifyLeft("流程设计操作中，请稍后", "warning")
    if (intervalTime >= form.intervalTime) props.currFlowForm.flowDesigning = false

    if (props.currFlowForm.flowDesigning === true) return
    methods.doActiveMenu(menu, active)
    clearInterval(form.interval)
  },
  doActiveMenu(menu, active) {
    $emit("syncCurrFlowForm", menu, active, (preSave) => {
      if (!preSave) menu = active
      props.currFlowForm[menu] = false
      form.active = menu
      props.currFlowForm.active = menu
    })
  },
  // 关闭提示
  listenPage() {
    window.onbeforeunload = function (e) {
      e = e || window.event
      if (e) e.returnValue = "关闭提示"

      return "关闭提示"
    }
  },
}

onMounted(() => {
  methods.listenPage()
})
</script>
<style lang="scss" scoped>
.step-box {
  @apply absolute left-0 right-0  inline-flex justify-center font-size-14px m-auto;
}
.header-steps {
  @apply absolute left-0 right-0  inline-flex justify-center font-size-14px m-auto;
  width: 400px;
  padding: 10px 0;
  background: transparent;
}
.header {
  min-width: 980px;

  .el-tabs {
    position: absolute;
    top: 15px;
    z-index: 999;
    display: flex;
    justify-content: center;
    width: 100%;
    :deep(.el-tabs__header) {
      margin: 0 auto 15px;
    }
  }

  .btn-publish {
    position: absolute;
    top: 20px;
    z-index: 1000;
    right: 40px !important;

    i {
      margin-right: 6px;
    }

    button {
      height: 28px;
    }
  }

  .btn-back {
    position: absolute;
    top: 20px;
    z-index: 1000;
    left: 20px !important;
    font-size: 18px;

    i {
      margin-right: 6px;
    }

    button {
      width: 44px;
      height: 28px;
      border-radius: 15px;
    }
  }
}
</style>
