<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="850px"
    :before-close="handleClose"
    top="8vh"
  >
    <el-form ref="formRef" :model="form" label-width="80px" disabled>
      <el-form-item label="文档名称" prop="docName">
        <el-input v-model="form.docName" show-word-limit />
      </el-form-item>
      <el-col :span="9">
        <el-form-item label="文档内容" prop="fileUrl">
          <DocFileUpload
            v-model="form.fileUrl"
            v-model:fileName="form.fileName"
            :readonly="true"
          />
        </el-form-item>
      </el-col>
      <el-form-item label="所属端" prop="belongType">
        <dict-select
          v-model="form.belongType"
          :options="dictOptions.home_app_type"
          type="checkbox"
          valueType="string"
        />
      </el-form-item>
      <el-col :span="9">
        <el-form-item label="状态" prop="status">
          <dict-select
            v-model="form.status"
            :options="dictOptions.sys_normal_disable"
            valueType="string"
          />
        </el-form-item>
      </el-col>
      <el-col :span="9">
        <el-form-item label="版本号" prop="version">
          <el-input v-model="form.version" />
        </el-form-item>
      </el-col>
      <el-form-item prop="vis" label-position="top">
        <div class="selected-user-box">
          <h4>可见范围</h4>
          <TagUserList :list="form.vis" :show-remove="false" />
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
import { detailManualCfgInfo } from "@/api/ess/doc/manualCfgInfo"
const DocFileUpload = defineAsyncComponent(() =>
  import("@/views/doc/components/DocFileUpload.vue")
)

const TagUserList = defineAsyncComponent(() =>
  import("@/views/components/UserTreeTransfer/TagUserList.vue")
)

const { proxy } = getCurrentInstance()

const props = defineProps({
  dictOptions: Object,
})

const title = ref("查看详情")
const dialogVisible = ref(false)
const formRef = ref(null)

const form = ref({
  docName: null,
  version: null,
  status: null,
  belongType: null,
})

const open = (item) => {
  dialogVisible.value = true
  nextTick(() => {
    if (item?.id) {
      form.value = { ...item, oldUrl: item.fileUrl }
      getDetail(item)
    }
  })
}

// 获取详情
const getDetail = (item) => {
  detailManualCfgInfo(item.id).then((res) => {
    form.value = { ...form, ...res.object }
  })
}

// 取消
const handleClose = () => {
  proxy.resetForm("formRef")
  dialogVisible.value = false
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.selected-user-box {
  @apply mt-5px;
  width: 100%;
  border-top: 1px dashed #f1f1f1;

  h4 {
    margin: 10px 0;
  }
}
</style>
