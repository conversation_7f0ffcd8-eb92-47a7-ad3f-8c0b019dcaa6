<template>
  <div class="avatar-container">
    <el-dropdown
      class="right-menu-item hover-effect"
      trigger="click"
      @command="handleCommand"
    >
      <div class="avatar-wrapper">
        <img
          v-realImg="imageApi + userStore.avatar"
          src="@/assets/images/default-user-icon.png"
          class="user-avatar"
        />
        <span>{{ userInfo.nickName }}</span>
        <el-icon><caret-bottom /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <router-link to="/user/profile" v-if="!disabled">
            <el-dropdown-item v-if="!disabled">个人中心</el-dropdown-item>
          </router-link>
          <!-- <el-dropdown-item command="setLayout" v-if="settingsStore.showSettings">
                <span>布局设置</span>
              </el-dropdown-item> -->
          <el-dropdown-item :divided="!disabled" command="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import useUserStore from "@/store/modules/user"
import { imageApi } from "@/config/constant"

const userStore = useUserStore()
const userInfo = useUserStore().userInfo
const { proxy } = getCurrentInstance()

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
})
function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout()
      break
    case "logout":
      logout()
      break
    default:
      break
  }
}

function logout() {
  proxy.$modal
    .confirm("确定注销并退出系统吗？", "", "info")
    .then(function () {
      userStore.logOut()
    })
    .catch(() => {})
}
const emits = defineEmits(["setLayout"])
function setLayout() {
  emits("setLayout")
}
</script>

<style scoped lang="scss">
.avatar-container {
  margin-right: 20px;
  display: flex;
  align-items: center;

  .avatar-wrapper {
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 16px;
    cursor: pointer;

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    span {
      margin: 0 5px;
    }

    i {
      font-size: 12px;
    }
  }
}
</style>
