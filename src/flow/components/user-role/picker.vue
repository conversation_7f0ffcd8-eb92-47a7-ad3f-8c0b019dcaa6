<template>
	<div>
		<el-dialog
			v-model="visible"
			append-to-body
			title="选择参与者"
			width="680px"
			:close-on-click-modal="false"
			draggable
		>
			<span> 注：可任意选择人员、角色、部门作为参与者 </span>
			<div class="toBody" v-loading="loading">
				<div class="toBody-left">
					<div class="mt-4" style="margin: 10px">
						<el-input
							v-model="searchKey"
							style="max-width: 600px"
							placeholder="搜索"
							@keyup="onListenSearch"
						>
							<template #prepend>
								<el-select v-model="data.jobType" style="width: 80px" @change="changeJobType">
									<el-option
										v-for="(item, index) in DIC_PROP.JOB_USER_TYPE"
										:key="index"
										:label="item.label"
										:value="item.value"
									/>
								</el-select>
							</template>
							<template #append>
								<el-button icon="Search" />
							</template>
						</el-input>
					</div>
					<div v-show="searchKey" class="content">
						<div class="group" style="margin-top: 0; overflow: auto; height: 514px">
							<span v-for="(item, index) in data.searchUsers" :key="index" class="group-item">
								<el-checkbox v-model="item.isChecked" @change="setCheckboxChange(item)">
									<template #default>
										<div class="selectLabel">
											<img v-if="item.avatar" :src="item.avatar" class="image">
											<div v-else class="mock">
												{{ showKeyName(item) }}
											</div>
											<span class="text"> {{ showKeyName(item) }} </span>
										</div>
									</template>
								</el-checkbox>
								<!-- <span class="tips"> 下级 </span> -->
							</span>
						</div>
					</div>

					<div v-show="!searchKey" v-if="data.jobType === DIC_PROP.JOB_USER_TYPE[1].value" class="content">
						<div class="bcrumb">
							<span class="bcrumb-item">
								<span class="name line-1">{{ DIC_PROP.JOB_USER_TYPE.find(f => data.jobType === f.value).label }}</span>
								<el-icon style="color: #a1a2a4; width: 16px; height: 16px"><ArrowRight /></el-icon>
							</span>
						</div>

						<div class="group" style="margin-top: 0; overflow: auto; height: 494px">
							<span v-for="(item, index) in data.jobTypeUsers" :key="index" class="group-item">
								<el-checkbox v-model="item.isChecked" @change="setCheckboxChange(item)">
									<template #default>
										<div class="selectLabel">
											<img v-if="item.avatar" :src="item.avatar" class="image">
											<div v-else class="mock">
												{{ showKeyName(item) }}
											</div>
											<span class="text"> {{ showKeyName(item) }} </span>
										</div>
									</template>
								</el-checkbox>
								<!-- <span class="tips"> 下级 </span> -->
							</span>
						</div>
					</div>

					<div v-show="!searchKey" v-if="data.jobType === DIC_PROP.JOB_USER_TYPE[0].value || data.jobType === DIC_PROP.JOB_USER_TYPE[2].value" class="content">
						<div class="bcrumb">
							<span
								v-for="(item, index) in data.treeLevels"
								:key="index"
								class="bcrumb-item"
								@click="goOrganiseLevel(item)"
							>
								<span class="name line-1">{{ item.name }}</span>
								<el-icon style="color: #a1a2a4; width: 16px; height: 16px"><ArrowRight /></el-icon>
							</span>
						</div>

						<div class="group">
							<span class="group-item">
								<el-checkbox
									v-model="checkAll"
									:disabled="isOnlyOne"
									label="全选"
									@change="setCheckboxAllChange"
								/>

								<span class="tips" @click="goParentLevel()">上一级</span>
							</span>
						</div>
						<div class="group" style="margin-top: 0; overflow: auto; height: 450px">
							<span
								v-for="(child, index) in data.currMembersDepts.children"
								:key="index"
								:class="{ 'group-item': true, disabled: child.isChecked }"
							>
								<el-checkbox v-model="child.isChecked" :disabled="!allowDept" @change="setCheckboxChange(child)">
									<template #default>
										<div class="selectLabel">
											<div class="icon">
												<el-icon><HomeFilled /></el-icon>
											</div>
											<span class="text"> {{ showKeyName(child) }}</span>
										</div>
									</template>
								</el-checkbox>
								<span class="tips" @click="setCurrentChild(child)"> 下级 </span>
							</span>
							<span v-for="(child, index) in data.currMembersDepts.users" :key="index" class="group-item">
								<el-checkbox v-model="child.isChecked" @change="setCheckboxChange(child)">
									<template #default>
										<div class="selectLabel">
											<img v-if="child.avatar" :src="child.avatar" class="image">
											<div v-else class="mock">
												{{ showKeyName(child) }}
											</div>
											<span class="text"> {{ showKeyName(child) }} </span>
										</div>
									</template>
								</el-checkbox>
								<!-- <span class="tips"> 下级 </span> -->
							</span>
						</div>
						<!-- </el-checkbox-group> -->
					</div>
				</div>
				<div class="toBody-line" />
				<div class="toBody-right">
					<div class="behaver">
						<span>已选 {{ data.isCheckedItems.length }} 项</span>
						<span style="float: right;color: #c75450;cursor: pointer;" @click="onRemoveAllItem()">清空</span>
					</div>
					<div class="selectGroup">
						<span
							v-for="(item, index) in data.isCheckedItems"
							:key="index"
							class="selectGroup-item"
						>
							<div v-if="item.jobType !== DIC_PROP.JOB_USER_TYPE[2].value" class="selectLabel">
								<img v-if="item.avatar" :src="item.avatar" class="image">
								<div v-else class="mock">
									{{ showKeyName(item) }}
								</div>
								<span class="text"> {{ showKeyName(item) }}</span>
							</div>
							<div v-else class="selectLabel">
								<div class="icon">
									<el-icon><HomeFilled /></el-icon>
								</div>
								<span class="text"> {{ showKeyName(item) }}</span>
							</div>

							<div class="close" @click="onRemoveItem(item, index)">
								<el-icon style="width: 16px; height: 16px"><Close /></el-icon>
							</div>
						</span>
					</div>
				</div>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button theme="default" variant="outline" @click="onClose">
						取消
					</el-button>
					<el-button theme="primary" @click="onSave">
						确定
					</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="UserRolePicker">
const { proxy } = getCurrentInstance()
import { fetchUserRolePicker } from "@/api/jsonflow/common"
import { listDept } from "@/api/system/dept"
import { validateNull } from "@/utils/validate"
import { DIC_PROP } from "@/flow/support/dict-prop"
import { onLoadDicUrl } from "../convert-name/convert"

const visible = ref<Boolean>(false)
const searchKey = ref<string>("")
const checkAll = ref(false)

const props = defineProps({
	isOnlyOne: {
		type: Boolean,
		default: false,
	},
	allowDept: {
		type: Boolean,
		default: false,
	},
})
const loading = ref(true)
const emits = defineEmits(["onSelectItems"])
const data = reactive({
	// 存储所有数据
	membersAllInfoObj: { children: [] },
	// 只存储所有人员 用于搜索
	membersQueryList: [],
	deptsQueryList: [],
	// 通过搜索条件，模糊查询得出结果
	searchUsers: [],
	currMembersDepts: { children: [], users: [] },
	// 存储层级菜单面包屑
	treeLevels: [],
	// 将已选中的对象全部加入
	isCheckedItems: [],
	jobTypeUsers: [],
	jobType: "0",
})

// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "roles" })

// 初始化数据
const initMemberDeptsData = () => {
	data.membersAllInfoObj = { children: [] }
	data.membersQueryList = []
	data.searchUsers = []
	data.currMembersDepts = { children: [], users: [] }
	data.treeLevels = []
	data.isCheckedItems = []
}

// 获取成员列表树状组件
const getMemberInfoList = async () => {
	await onLoad(dicData)
	// const res = await fetchUserRolePicker()
	const res = await listDept()
  loading.value = false
	switchIsCheckedData(dicData)
	switchIsChecked(res.data)

	// 存储所有的人
	data.membersAllInfoObj = res.data
	data.currMembersDepts.children = res.data
	data.membersQueryList = getAllPerson(res.data)
	data.deptsQueryList = getAllDept(res.data)

  console.log(data.deptsQueryList)

	data.jobTypeUsers = data.membersQueryList

	// 存储层级菜单面包屑
	data.treeLevels = [data.membersAllInfoObj]
}

// 属性isChecked
const switchIsCheckedData = dicData => {
	data.deptsQueryList.forEach(each => {
		each.isChecked = false
	})
	if (!validateNull(dicData.roles)) {
		dicData.roles.forEach(each => {
			each.isChecked = false
		})
	}
	if (!validateNull(dicData.posts)) {
		dicData.posts.forEach(each => {
			each.isChecked = false
		})
	}
}
const switchIsChecked = obj => {
	if (obj.users && obj.users.length > 0) {
		obj.users.forEach(user => {
			user.isChecked = false
		})
	}
	if (obj.children && obj.children.length > 0) {
		obj.children.forEach(child => {
			child.isChecked = false
			switchIsChecked(child)
		})
	}
}

// 获取全部人员
const getAllPerson = obj => {
	let arr = []
	if (obj.users && obj.users.length > 0) {
		arr = arr.concat(obj.users)
	}
	if (obj.children && obj.children.length > 0) {
		obj.children.forEach(child => {
			arr = arr.concat(getAllPerson(child))
		})
	}
	return arr
}

// 获取全部部门
const getAllDept = obj => {
	let arr = []
	if (obj.children && obj.children.length > 0) {
		arr = arr.concat(obj.children)
	}
	if (obj.children && obj.children.length > 0) {
		obj.children.forEach(child => {
			arr = arr.concat(getAllDept(child))
		})
	}
	return arr
}

// 设置当前层级对象
const goOrganiseLevel = child => {
	data.currMembersDepts = child
	const index = data.treeLevels.findIndex(e => e.id === child.id)
	data.treeLevels = data.treeLevels.splice(0, index + 1)
	updateItemChecked()
}

// 获取上一级对象
const goParentLevel = () => {
	let parentId = data.currMembersDepts.parentId
	if (!parentId) {
		proxy.$modal.msgWarning("当前无上级选项")
		return
	}
	const index = data.treeLevels.findIndex(e => e.id === parentId)
	data.treeLevels = data.treeLevels.splice(0, index + 1)
	data.currMembersDepts = data.treeLevels[index]
	updateItemChecked()
}

// 部门被选中，禁用方法
const setCurrentChild = child => {
	if (child.isChecked) {
		return
	}
	data.currMembersDepts = child
	// 设置层级菜单面包屑
	let arr = getParent(data.membersAllInfoObj.children, child.id)
	arr.unshift(data.membersAllInfoObj)
	data.treeLevels = arr
	updateItemChecked()
}

const getParent = (data2, nodeID2) => {
	let arrRes = []
	if (data2.length === 0) {
		if (nodeID2) {
			arrRes.unshift(data2)
		}
		return arrRes
	}
	const rev = (data, nodeID) => {
		for (let i = 0, { length } = data; i < length; i++) {
			const node = data[i]
			if (node.id == nodeID) {
				arrRes.unshift(node)
				// 处理父级
				rev(data2, node.parentId)
				break
			}
			else {
				// 处理子级
				if (node.children && node.children.length > 0) {
					rev(node.children, nodeID)
				}
			}
		}
		return arrRes
	}
	arrRes = rev(data2, nodeID2)
	return arrRes
}

// 设置全选和取消
const setCheckboxAllChange = bool => {
	// 人员
	data.currMembersDepts.users.map(ev => {
		ev.isChecked = bool
		const index = data.isCheckedItems.findIndex(v => v.jobType === DIC_PROP.JOB_USER_TYPE[0].value && (v.userId === ev.userId || v.roleId === ev.userId))
		if (index > -1) {
			if (!bool) {
				data.isCheckedItems.splice(index, 1)
			}
		}
		else if (bool) {
			ev.jobType = DIC_PROP.JOB_USER_TYPE[0].value
			data.isCheckedItems.push(ev)
		}
	})
}

// 选中或取消
const setCheckboxChange = e => {
	let index: number
	if (userJobType(e)) {
		e.jobType = DIC_PROP.JOB_USER_TYPE[0].value
		index = data.isCheckedItems.findIndex(v => v.jobType === e.jobType && (v.userId === e.userId || v.roleId === e.userId))
	}
	else if (roleJobType(e)) {
		e.jobType = DIC_PROP.JOB_USER_TYPE[1].value
		index = data.isCheckedItems.findIndex(v => v.jobType === e.jobType && v.roleId === e.roleId)
	}
	// else if (postJobType(e)) {
	// 	e.jobType = DIC_PROP.JOB_USER_TYPE[2].value
	// 	index = data.isCheckedItems.findIndex(v => v.jobType === e.jobType && (v.postId === e.postId || v.roleId === e.postId))
	// }
	else if (deptJobType(e)) {
		e.jobType = DIC_PROP.JOB_USER_TYPE[2].value
		index = data.isCheckedItems.findIndex(v => v.jobType === e.jobType && (v.id === e.id || v.roleId === e.id))
	}
	if (props.isOnlyOne && data.isCheckedItems.length > 0) {
		if (index === -1) {
			e.isChecked = false
			proxy.$modal.msgWarning("当前节点任务只允许选择一个选项")
			return
		}
	}
	if (index > -1) {
		if (!e.isChecked) {
			data.isCheckedItems.splice(index, 1)
		}
	}
	else if (e.isChecked) {
		data.isCheckedItems.push(e)
	}
	if (userJobType(e)) {
		isCheckedAllHandle(e.isChecked)
	}
}

// 多选的情况下使用
const isCheckedAllHandle = bool => {
	if (bool) {
		let isAll = false
		if (data.currMembersDepts.users.length > 0) {
			const userBool = data.currMembersDepts.users.every(v => v.isChecked === true)
			if (userBool) {
				isAll = true
			}
		}
		checkAll.value = isAll
	}
	else {
		checkAll.value = bool
	}
}

// 移除所有已选中项
const onRemoveAllItem = () => {
	data.isCheckedItems = []
	clearItemChecked()
}

// 移除已选中项
const onRemoveItem = (item, eindex) => {
	data.isCheckedItems.splice(eindex, 1)
	validateItemChecked(item, false)
}

function currUsersChecked(item, isChecked) {
	data.currMembersDepts.users.forEach(ele => {
		if (ele.userId === item.userId || ele.userId === item.roleId) {
			ele.isChecked = isChecked
		}
	})
	isCheckedAllHandle(true)
}

function allowDeptChecked(item, isChecked) {
	// 允许同时选择部门
	if (props.allowDept) {
		data.currMembersDepts.children.forEach(ele => {
			if (ele.id === item.id || item.roleId === ele.id) {
				ele.isChecked = isChecked
			}
		})
	}
}

function searchJobTypeUsers(item, key, isChecked) {
	if (!validateNull(data.jobTypeUsers) && data.jobType === item.jobType) {
		data.jobTypeUsers.forEach(ele => {
			if (ele[key] === item[key] || ele[key] === item.roleId) {
				ele.isChecked = isChecked
			}
		})
	}
	if (!validateNull(data.searchUsers) && data.jobType === item.jobType) {
		data.searchUsers.forEach(ele => {
			if (ele[key] === item[key] || ele[key] === item.roleId) {
				ele.isChecked = isChecked
			}
		})
	}
}

function validateItemChecked(item, isChecked) {
	if (item.jobType === DIC_PROP.JOB_USER_TYPE[0].value) {
		currUsersChecked(item, isChecked)
	}
	else if (item.jobType === DIC_PROP.JOB_USER_TYPE[1].value) {
		let key = item.jobType === DIC_PROP.JOB_USER_TYPE[1].value ? "roleId" : "postId"
		searchJobTypeUsers(item, key, isChecked)
	}
	else if (item.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
		searchJobTypeUsers(item, "id", isChecked)
		allowDeptChecked(item, isChecked)
	}
}

// 初始化数据进入复选框
const doSelectItemChecked = () => {
	data.isCheckedItems.forEach(item => {
		validateItemChecked(item, true)
	})
}

const setSelectItemChecked = items => {
	data.isCheckedItems = items
	doSelectItemChecked()
	// 重置数据信息
	let checkedItems = []
	data.isCheckedItems.forEach(ele => {
		if (ele.jobType === DIC_PROP.JOB_USER_TYPE[0].value) {
			data.membersQueryList.forEach(es => {
				if (ele.userId === es.userId || ele.roleId === es.userId) {
					es.jobType = ele.jobType
					checkedItems.push(es)
				}
			})
		}
		else if (ele.jobType === DIC_PROP.JOB_USER_TYPE[1].value) {
			dicData.roles.forEach(es => {
				if (ele.roleId === es.roleId) {
					es.jobType = ele.jobType
					checkedItems.push(es)
				}
			})
		}
		// else if (ele.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
		// 	dicData.posts.forEach(es => {
		// 		if (ele.postId === es.postId || ele.roleId === es.postId) {
		// 			es.jobType = ele.jobType
		// 			checkedItems.push(es)
		// 		}
		// 	})
		// }
		else if (ele.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
			data.deptsQueryList.forEach(es => {
				if (ele.id === es.id || ele.roleId === es.id) {
					es.jobType = ele.jobType
					checkedItems.push(es)
				}
			})
		}
	})
	data.isCheckedItems = checkedItems
}

const updateItemChecked = () => {
	if (validateNull(data.currMembersDepts)) {
		checkAll.value = false
		return
	}
	// 更新界面
	clearItemChecked()

	doSelectItemChecked()
}

const clearItemChecked = () => {
	data.currMembersDepts.users.forEach(es => {
		es.isChecked = false
	})
	// 允许同时选择部门
	if (props.allowDept) {
		data.currMembersDepts.children.forEach(es => {
			es.isChecked = false
		})
	}

	if (!validateNull(data.jobTypeUsers)) {
		data.jobTypeUsers.forEach(es => {
			es.isChecked = false
		})
	}
	if (!validateNull(data.searchUsers)) {
		data.searchUsers.forEach(es => {
			es.isChecked = false
		})
	}
	checkAll.value = false
}

const changeJobType = () => {
	if (data.jobType === DIC_PROP.JOB_USER_TYPE[0].value) {
		data.jobTypeUsers = data.membersQueryList
	}
	else if (data.jobType === DIC_PROP.JOB_USER_TYPE[1].value) {
		data.jobTypeUsers = dicData.roles
	}
	// else if (data.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
	// 	data.jobTypeUsers = dicData.posts
	// }
	else if (data.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
		data.jobTypeUsers = data.deptsQueryList
	}
	onListenSearch()
}

// 监听查询结果
const onListenSearch = () => {
	let name = searchKey.value.toLowerCase()
	if (data.jobType === DIC_PROP.JOB_USER_TYPE[0].value) {
		data.searchUsers = data.jobTypeUsers.filter(v => v.name.toLowerCase().includes(name))
	}
	else if (data.jobType === DIC_PROP.JOB_USER_TYPE[1].value) {
		data.searchUsers = data.jobTypeUsers.filter(v => v.roleName.toLowerCase().includes(name))
	}
	// else if (data.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
	// 	data.searchUsers = data.jobTypeUsers.filter(v => v.postName.toLowerCase().includes(name))
	// }
	else if (data.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
		data.searchUsers = data.jobTypeUsers.filter(v => v.name.toLowerCase().includes(name))
	}
	updateItemChecked()
}

function userJobType(item) {
	return item.jobType === DIC_PROP.JOB_USER_TYPE[0].value || (!item.jobType && item.hasOwnProperty("userId"))
}
function roleJobType(item) {
	return item.jobType === DIC_PROP.JOB_USER_TYPE[1].value || (!item.jobType && item.hasOwnProperty("roleId"))
}
function postJobType(item) {
	return item.jobType === DIC_PROP.JOB_USER_TYPE[2].value || (!item.jobType && item.hasOwnProperty("postId"))
}
function deptJobType(item) {
	return item.jobType === DIC_PROP.JOB_USER_TYPE[2].value || (!item.jobType && item.hasOwnProperty("id"))
}

function showKeyName(item) {
	if (userJobType(item)) { return item.deptName || item.nickName }
	else if (roleJobType(item)) { return item.roleName }
	// else if (postJobType(item)) { return item.postName }
	else if (deptJobType(item)) { return item.deptName }
}

const onSave = () => {
	if (data.isCheckedItems.length < 1) {
		proxy.$modal.msgWarning("请选择至少一个选项")
		return
	}
	let resItems = []
	data.isCheckedItems.forEach(item => {
		if (userJobType(item)) {
			item.roleId = item.userId
			item.jobType = DIC_PROP.JOB_USER_TYPE[0].value
			resItems.push(item)
		}
		else if (roleJobType(item)) {
			item.jobType = DIC_PROP.JOB_USER_TYPE[1].value
			resItems.push(item)
		}
		// else if (postJobType(item)) {
		// 	item.roleId = item.postId
		// 	item.jobType = DIC_PROP.JOB_USER_TYPE[2].value
		// 	resItems.push(item)
		// }
		else if (deptJobType(item)) {
			item.roleId = item.id
			item.jobType = DIC_PROP.JOB_USER_TYPE[2].value
			resItems.push(item)
		}
	})
	emits("onSelectItems", resItems)
	onClose()
}

const onOpen = async items => {
	searchKey.value = ""
	visible.value = true
	if (validateNull(items)) {
		initMemberDeptsData()
	}
	else {
		clearItemChecked()
	}
	// 判断初始化数据
	if (validateNull(data.membersQueryList)) {
		await getMemberInfoList()
	}
	if (!validateNull(items)) {
		setSelectItemChecked(items)
	}
	changeJobType()
}

const onClose = () => {
	visible.value = false
}

defineExpose({
	onOpen,
})
</script>

<style lang="scss" scoped>

  .el-alert--info {
    padding: 8px 16px;
  }
  .el-checkbox-group {
    width: 100%;
  }
  .toBody {
    border: 1px solid #e3e6eb;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    min-height: 372px;

    &-left {
      flex: 1;
      .bcrumb {
        margin: 0 16px;
        display: flex;
        flex-wrap: wrap;
        &-item {
          display: flex;
          align-items: center;
          max-width: 140px;
          .name {
            font-size: 14px;
            font-family: Microsoft YaHei, Microsoft YaHei-Regular;
            font-weight: 400;
            color: #13161b;
            max-width: 100px;
            cursor: pointer;
            user-select: none;
          }
          .icon {
            margin-left: 2px;
          }
          &:last-child {
            .name {
              color: #a1a2a4;
            }
            .icon {
              display: none;
            }
          }
        }
      }

      .group {
        padding: 0 8px;
        margin-top: 4px;
        display: flex;
        flex-direction: column;
        // justify-content: center;
        width: 100%;
        &-item {
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.2s linear;
          display: flex;
          justify-content: space-between;
          user-select: none;
          cursor: pointer;
          &:hover {
            background: #f0f8ff;
          }
          .selectLabel {
            display: flex;
            align-items: center;

            .image {
              width: 24px;
              height: 24px;
              border-radius: 5px;
            }
            .text {
              font-size: 14px;
              font-family: Microsoft YaHei, Microsoft YaHei-Regular;
              font-weight: 400;
              color: #13161b;
              margin-left: 2px;
            }
          }

          .tips {
            margin-top: 5px;
            font-size: 14px;
            font-family: Microsoft YaHei, Microsoft YaHei-Regular;
            font-weight: 400;
            cursor: pointer;
            user-select: none;
            color: #2069e3;
          }
        }
      }
      .disabled {
        .text {
          color: #a1a2a4 !important;
        }
        .tips {
          color: #a1a2a4 !important;
          cursor: not-allowed;
        }
      }
    }
    &-line {
      background: #e3e6eb;
      border-radius: 8px;
      width: 1px;
      margin: 8px 0;
    }
    &-right {
      flex: 1;
      .behaver {
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei-Regular;
        font-weight: 400;
        color: #13161b;
        margin: 16px;
        // background-color: #409EFF;
      }
      .selectGroup {
        margin: 0 8px;
        margin-top: 4px;
        &-item {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.25s linear;

          justify-content: space-between;
          &:hover {
            background: #f0f8ff;
          }
          .selectLabel {
            display: flex;
            align-items: center;

            .icon {
              background-color: #f0f8ff;
              width: 24px;
              height: 24px;
              border-radius: 4px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .image {
              width: 24px;
              height: 24px;
              border-radius: 5px;
            }
            .text {
              font-size: 14px;
              font-family: Microsoft YaHei, Microsoft YaHei-Regular;
              font-weight: 400;
              color: #13161b;
              margin-left: 2px;
            }
          }
          .close {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
  .radios-group {
    .font-name {
      height: 22px;
      font-size: 14px;
      font-weight: 400;
      color: #13161b;
      line-height: 22px;
      margin-bottom: 12px;
    }
  }
  .mock {
    width: 24px;
    height: 24px;
    background: var(--el-color-primary);
    border-radius: 4px;
    color: #ffffff;
    text-align: center;
    line-height: 24px;
    font-size: 10px;
  }

  .icon {
    background-color: #f0f8ff;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    &-svg {
      color: #fff;
      width: 16px;
      height: 16px;
    }
  }
</style>
