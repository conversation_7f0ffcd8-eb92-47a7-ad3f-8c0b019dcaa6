<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item label="流程实例ID" prop="flowInstId">
						<el-input v-model="state.queryForm.flowInstId" placeholder="请输入流程实例ID" />
					</el-form-item>
					<el-form-item label="用户ID" prop="userId">
						<el-select
							v-model="state.queryForm.userId"
							placeholder="请选择用户"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.users"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							查询
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<right-toolbar
						v-model:show-search="showSearch"
						:export="'jsonflow_comment_export'"
						class="ml10"
						style="float: right;margin-right: 20px"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" label="序号" width="40" />
				<el-table-column prop="flowInstId" label="流程实例ID" show-overflow-tooltip />
				<el-table-column prop="flowKey" label="流程标识" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.flowInstId"
							:value="scope.row.flowKey"
							:value-key="'flowKey'"
							:show-key="'flowName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="runNodeId" label="运行节点ID" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.runNodeId"
							:value="scope.row.runNodeId"
							:value-key="'id'"
							:show-key="'nodeName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="runJobId" label="运行任务ID" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.runJobId"
							:value="scope.row.runJobId"
							:value-key="'id'"
							:show-key="'jobName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="userId" label="用户ID" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.userId"
							:value="scope.row.userId"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="signName" label="签名">
					<template #default="scope">
						<img v-if="scope.row.signName" width="200px" :src="scope.row.signName">
					</template>
				</el-table-column>
				<el-table-column prop="remark" label="备注" show-overflow-tooltip />
				<el-table-column prop="createUser" label="创建人" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.createUser"
							:value="scope.row.createUser"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" label="创建时间" show-overflow-tooltip />
				<el-table-column label="操作" width="100">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								查看
							</template>
							<el-button
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								修改
							</template>
							<el-button
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								删除
							</template>
							<el-button
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemComment">
import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchList, delObjs } from "@/api/jsonflow/comment"
import { useMessageBox } from "@/hooks/message"
import { onLoadDicUrl, onLoaded } from "@/flow/components/convert-name/convert"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))

// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "users" })
onMounted(() => {
	onLoad(dicData)
})

// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
	onLoaded: onLoaded({ key: "createUser" }, { key: "userId" }, { key: "flowInstId" }, { key: "runNodeId" }, { key: "runJobId" }),
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	getDataList()
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm("确定要删除选中的数据吗？")
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess("删除成功")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
}
</script>
