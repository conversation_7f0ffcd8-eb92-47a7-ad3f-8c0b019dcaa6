export default {
	tabsOption: {
		index: "#",
		importtabsOptionTip: "import TabsOption",
		id: "id",
		icon: "icon",
		formName: "formName",
		groupName: "groupName",
		path: "path",
		sort: "sort",
		isActive: "isActive",
		isAutoAudit: "isAutoAudit",
		status: "status",
		version: "version",
		createTime: "createTime",
		createUser: "createUser",
		updateUser: "updateUser",
		formInfo: "formInfo",
		type: "type",
		updateTime: "updateTime",
		delFlag: "delFlag",

		inputIdTip: "input id",
		inputIconTip: "input icon",
		inputFormNameTip: "input formName",
		inputGroupNameTip: "input groupName",
		inputPathTip: "input path",
		inputSortTip: "input sort",
		inputIsActiveTip: "input isActive",
		inputIsAutoAuditTip: "input isAutoAudit",
		inputStatusTip: "input status",
		inputVersionTip: "input version",
		inputCreateTimeTip: "input createTime",
		inputCreateUserTip: "input createUser",
		inputUpdateUserTip: "input updateUser",
		inputFormInfoTip: "input formInfo",
		inputTypeTip: "input type",
		inputUpdateTimeTip: "input updateTime",
		inputDelFlagTip: "input delFlag",

	},
}
