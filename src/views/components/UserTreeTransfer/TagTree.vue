<template>
  <div>
    <el-input
      v-model="condition"
      placeholder="请输入关键字"
      clearable
      maxlength="100"
      suffix-icon="Search"
      class="filter-input"
      @input="onQueryChanged"
    >
      <slot name="input-prepend" :loading="loading"></slot>
    </el-input>
    <div v-loading="loading">
      <el-tree-v2
        class="user-tree"
        v-bind="treeCommonProps"
        :empty-text="loading.value ? '加载中...' : '暂无数据'"
        :data="treeData"
        :default-checked-keys="defaultCheckedKeys"
        :filter-method="filterTreeMethod"
        ref="treeRef"
      >
        <template #default="{ node, data }">
          <span class="user-tree-label">{{ node.label }}</span>
        </template>
      </el-tree-v2>
    </div>
  </div>
</template>

<script setup>
import { fetchTagFromTypeAPI } from "@/api/ess/baseSet/tagCfgInfo"
import { isNil } from "lodash-es"

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 业务线类型（传入就会筛选）
  businessLineType: {
    default: null,
  },
  // 是否只允许选择单个
  isOnlyOne: {
    type: Boolean,
    default: false,
  },
})
const treeRef = ref(null)
const loading = ref(true)

const treeProps = {
  value: "id",
  label: "name",
  children: "children",
  isLeaf: "isLeaf",
}
// 提取树组件公共配置
const treeCommonProps = ref({
  "node-key": "id",
  showCheckbox: true,
  props: treeProps,
  height: 350,
  "item-size": 30,
  onCheckChange: handleTreeCheckChange,
})

const condition = ref(null)
const treeData = ref([]) // 树节点数据

const defaultCheckedKeys = ref([])
const selectList = ref([])

const emit = defineEmits(["update:modelValue", "change"])

// 赋值已选数据
const updateCheckedKeys = () => {
  nextTick(() => {
    selectList.value = [...props.modelValue]
    defaultCheckedKeys.value = selectList.value.map((item) => item.id)
    treeRef.value?.setCheckedKeys(defaultCheckedKeys.value)
  })
}

watch(() => props.modelValue, updateCheckedKeys, { immediate: true })

watch(
  () => props.businessLineType,
  (val) => {
    getTagList()
  }
)

async function getTagList() {
  treeRef.value?.setExpandedKeys([])
  const res = await fetchTagFromTypeAPI(
    isNil(props.businessLineType) ? 3 : props.businessLineType
  )
  treeData.value = transformData(res.object || [])
  loading.value = false
}

function handleTreeCheckChange(data, checked) {
  if (props.isOnlyOne && selectList.value.length === 1) {
    proxy.$modal.msgWarning("当前只允许选择一个选项")
    treeRef.value.setChecked(data.id, false)
    return
  }
  const selectNodes = treeRef.value?.getCheckedNodes(false) || []
  selectList.value = selectNodes
  emit("update:modelValue", selectList.value)
  emit("change", selectList.value)
}

function transformData(arr) {
  return arr.map((item) => {
    return {
      ...item,
      name: item.name || item.tagName,
      rangeId: item.id,
      tagId: item.id,
      type: "tag",
      rangeName: item.name || item.tagName,
      rangeType: 3,
      jobType: 4, // 标签标识
      // children: transformData(item.children || []),
    }
  })
}

function onQueryChanged(query) {
  treeRef.value?.filter(query)
  if (!query) {
    treeRef.value?.setExpandedKeys([])
  }
}

// 统一过滤逻辑
function matchKeyword(data, keyword) {
  if (!keyword) return true
  return [data.tagName].some((item) =>
    item?.toLowerCase().includes(keyword.toLowerCase())
  )
}
// 过滤树
function filterTreeMethod(value, data) {
  return matchKeyword(data, value)
}

// 重置查询条件
function resetQuery() {
  condition.value = null
}

getTagList()

defineExpose({
  resetQuery,
})
</script>

<style scoped lang="scss">
.filter-input {
  margin-bottom: 10px;
  :deep(.el-input__wrapper) {
    padding-left: 80px;
  }
}
</style>
