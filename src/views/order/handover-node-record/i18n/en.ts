export default {
	handoverNodeRecord: {
		index: "#",
		importhandoverNodeRecordTip: "import HandoverNodeRecord",
		id: "id",
		flowKey: "flowKey",
		initiatorId: "initiatorId",
		nodeJobId: "nodeJobId",
		startTime: "startTime",
		todoList: "todoList",
		status: "status",
		createUser: "createUser",
		createTime: "createTime",
		orderId: "orderId",
		flowInstId: "flowInstId",
		retStatus: "retStatus",
		runJobId: "runJobId",
		runNodeId: "runNodeId",
		updateUser: "updateUser",
		updateTime: "updateTime",

		inputIdTip: "input id",
		inputFlowKeyTip: "input flowKey",
		inputInitiatorIdTip: "input initiatorId",
		inputNodeJobIdTip: "input nodeJobId",
		inputStartTimeTip: "input startTime",
		inputTodoListTip: "input todoList",
		inputStatusTip: "input status",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputOrderIdTip: "input orderId",
		inputFlowInstIdTip: "input flowInstId",
		inputRetStatusTip: "input retStatus",
		inputRunJobIdTip: "input runJobId",
		inputRunNodeIdTip: "input runNodeId",
		inputUpdateUserTip: "input updateUser",
		inputUpdateTimeTip: "input updateTime",

	},
}
