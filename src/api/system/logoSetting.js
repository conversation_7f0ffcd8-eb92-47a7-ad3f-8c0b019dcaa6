import request from "@/utils/request"
import { eduUser } from "@/config/constant"

// 新增logo
export function addLogo(data, baseRoute) {
	return request({
		url: eduUser + "/logoSetting/add",
		method: "post",
		data: data,
		params: {
			code: baseRoute,
		},
	})
}

// 查询logo
export function logoSettingList(baseRoute) {
	return request({
		url: eduUser + "/logoSetting/list",
		method: "get",
		params: {
			code: baseRoute,
		},
	})
}

// 修改logo
export function updateLogo(data) {
	return request({
		url: eduUser + "/logoSetting/edit",
		method: "put",
		data: data,
	})
}
