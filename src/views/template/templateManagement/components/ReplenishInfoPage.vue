<template>
  <DrawerPage v-model="show" :title="formDisabled ? '查看扩展信息' : '修改扩展信息'">
    <template v-if="!formDisabled" #actions>
      <el-button type="warning" :loading="btnLoading" @click="handleSave">
        保存并返回
      </el-button>
    </template>
    <CreateBaseInfoForm
      ref="baseInfoFormRef"
      :data="form"
      :form-disabled="formDisabled"
      :create-type="0"
    />
  </DrawerPage>
</template>

<script setup name="ReplenishInfoPage">
import {
  getTemplateInfo,
  updateTemplateExtend,
  getTemplateUserRef,
  getTemplateExtendInfo,
} from "@/api/ess/template/template"
import CreateBaseInfoForm from "./CreateBaseInfoForm.vue"

const { proxy } = getCurrentInstance()

const show = defineModel(false)
const loading = ref(true)
const btnLoading = ref(false)
const formDisabled = ref(false) // 是否禁用
const baseInfoFormRef = ref(null)

const form = ref({
  multipleFlag: null,
  businessLineId: null,
  templateUserDtos: [],
})

const open = (row, disabled = false) => {
  formDisabled.value = disabled
  show.value = true
  getTempInfo(row)
}

function getTempInfo(row) {
  getTemplateExtendInfo(row.id)
    .then((res) => {
      form.value = res.object
    })
    .finally(() => {
      loading.value = false
    })
}

const handleClose = () => {
  show.value = false
}

function handleSave() {
  proxy.$refs["baseInfoFormRef"].validate(async ({ valid, form }) => {
    if (!valid) {
      return
    }
    try {
      btnLoading.value = true
      let res = await updateTemplateExtend(form)
      if (+res.code === 200) {
        proxy.$modal.msgSuccess("保存成功！")
        handleClose()
      }
      btnLoading.value = false
    } catch (error) {
      btnLoading.value = false
    }
  })
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.tree-transfer {
  width: 85%;
  max-width: 900px;
}
.selected-user-box {
  background: #f7f8f9;
  border-radius: 12px;
  width: 85%;
  max-width: 900px;
  h3 {
    margin: 0;
    padding: 15px 20px 0 20px;
  }
}

.business-line-box {
  .business-line-box__item {
    display: flex;
    .title {
      margin-right: 10px;
      height: 32px;
      display: inline-block;
      width: 70px;
      text-align: right;
    }
  }
}
</style>
