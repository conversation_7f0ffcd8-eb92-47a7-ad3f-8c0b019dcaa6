<template>
  <el-dialog
    :title="title"
    v-model="visible"
    :width="isOnlyOne ? '550px' : '820px'"
    append-to-body
    :before-close="handleClose"
    top="8vh"
  >
    <slot name="prefix" />
    <div class="tree-select-wrapper">
      <div class="left-box" :class="{ 'w-100%!': isOnlyOne }">
        <div class="title">全部人员</div>
        <el-input
          v-model="condition"
          placeholder="请输入关键字"
          clearable
          maxlength="100"
          suffix-icon="Search"
          class="all-user-input"
          @input="onQueryChanged"
        >
          <template #prepend v-if="selectTypes.length > 1">
            <el-select
              v-model="jobType"
              suffix-icon="CaretBottom"
              :disabled="loading"
              @change="typeSelectChange"
            >
              <el-option label="人员" :value="0" />
              <el-option label="角色" :value="1" v-if="selectTypes.includes('role')" />
              <el-option
                label="角色"
                :value="2"
                v-if="selectTypes.includes('roleUser')"
              />
              <el-option label="标签" :value="3" v-if="selectTypes.includes('tagUser')" />
            </el-select>
          </template>
        </el-input>
        <el-tree-v2
          class="user-tree"
          v-bind="treeCommonProps"
          :data="treeData"
          :default-checked-keys="defaultCheckedKeys"
          :filter-method="filterTreeMethod"
          :check-strictly="jobType === 1"
        >
          <template #default="{ node, data }">
            <span class="user-tree-label">
              {{ node.label }}{{ data.workNumber ? `(${data.workNumber})` : "" }}
            </span>
          </template>
        </el-tree-v2>
      </div>
      <div class="right-box" v-if="!isOnlyOne">
        <div class="title flex-x-between p-12px">
          <div>选择人员</div>
          <div class="flex-y-center">
            <span class="font-400 color-#38383A mr-5px">已选{{ selectList.length }}</span>
            <el-button type="danger" link @click="clearAllChecked"> 清空 </el-button>
          </div>
        </div>
        <TagUserList :list="selectList" @remove="removeItem" />
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup name="UserRolePicker">
import { isEmpty } from "lodash-es"
import { getExcludeRolesInfosAPI } from "@/api/system/role"
import { useDeptStore } from "@/store/modules/dept"
import { queryTagUserRefList } from "@/api/ess/baseSet/tagCfgInfo"
import { uniqBy } from "lodash-es"

const TagUserList = defineAsyncComponent(() =>
  import("@/views/components/UserTreeTransfer/TagUserList.vue")
)

const { proxy } = getCurrentInstance()

const props = defineProps({
  formCreateInject: {
    type: Object,
    default: {},
  },
  // 是否单选
  isOnlyOne: {
    type: Boolean,
    default: false,
  },
  // 是否过滤ess用户
  izEssUser: {
    type: Boolean,
    default: false,
  },
  // 是否中国国籍 0否1是
  izChineseGj: {
    default: null,
  },
  // 业务线类型（传入就会筛选）
  businessLineType: {
    default: null,
  },
  selectTypes: {
    type: Array,
    default: () => ["user", "role"], // ['user','role','tagUser','roleUser']
  },
  title: {
    type: String,
    default: "选择人员",
  },
  // 是否存在部门id 如果存在---需要过滤部门
  deptId: {
    default: null,
  },
  // 是否展示所有的勾选框
  showCheckedBox: {
    type: Boolean,
    default: false,
  },
})

const defaultCheckedKeys = ref([])

const jobType = ref(0)
const visible = ref(false)
const loading = ref(false)
const condition = ref(null) // 左侧搜索关键字
const selectList = ref([]) // 选中节点
const treeData = ref([]) // 树级数据
const deptData = ref([]) // 树级数据
const rolesData = ref([]) // 角色
const roleUserData = ref([]) // 角色用户树
const tagData = ref([]) // 标签数据
const treeRef = ref(null)
const treeProps = {
  value: "id",
  label: "name",
  children: "children",
  isLeaf: "isLeaf",
  class: customNodeClass,
}
// 提取树组件公共配置
const treeCommonProps = {
  ref: treeRef,
  showCheckbox: true,
  props: treeProps,
  height: 400,
  "node-key": "id",
  "item-size": 30,
  "empty-text": loading.value ? "" : "暂无数据",
  onCheckChange: handleTreeCheckChange,
}

const emits = defineEmits(["onSelectItems"])

watch(
  () => props.businessLineType,
  (val) => {
    getList()
  }
)
watch(
  () => props.deptId,
  (val) => {
    getList()
  }
)

// 获取用户树
async function getList() {
  let reqData = {}
  reqData.izEssUser = props.izEssUser || null
  reqData.businessLineType = props.businessLineType ?? null
  const res = await useDeptStore().fetchZsDeptTreeInfo(reqData)
  const secondLevelNodes = res?.[0]?.children || []
  let tree = transformData(secondLevelNodes)
  if (props.deptId) {
    tree = filterTreeByDeptId(tree, props.deptId)
  }
  deptData.value = tree
  treeData.value = tree
}

// 新增：根据deptId过滤树节点，只返回目标节点及其子节点
function filterTreeByDeptId(tree, deptId) {
  for (const node of tree) {
    if (node.id == deptId) return [node]
    if (node.children) {
      const found = filterTreeByDeptId(node.children, deptId)
      if (found.length) return found
    }
  }
  return []
}

// 获取角色列表
async function getRoleList() {
  try {
    const { object } = await getExcludeRolesInfosAPI({})
    rolesData.value = transformRoleData(object)
  } catch (e) {
    console.log(e, "error")
  }
}

// 获取角色用户树
async function getRoleUserList() {
  try {
    const res = await getExcludeRolesInfosAPI({ izViewUsers: true })
    roleUserData.value = transformRoleUserData(res.object)
  } catch (e) {
    console.log(e, "error")
  }
}

// 获取标签用户树
async function getTagUserList() {
  const res = await queryTagUserRefList({})
  tagData.value = transformTagData(res.object)
}

// 通用人员节点生成函数
function createPersonNode(user, options = {}) {
  return {
    ...user,
    id: user.id || user.userId,
    name: user.nickName,
    parentId: options.deptId, // 仅部门树需要
    deptName: options.deptName, // 仅部门树需要
    isLeaf: true,
    type: "person",
    userOrRoleName: user.nickName,
    userOrRoleId: user.userId,
    jobType: options.jobType ?? 0, // 0:用户, 3:标签, 1:角色等
  }
}

function transformData(items) {
  return items.map((item) => {
    const { deptId, deptName, users = [], children } = item

    // 过滤中国国籍
    const filteredUsers = filterUsersByNationality(users)

    // 生成人员节点
    const personNodes = filteredUsers.map((user) =>
      createPersonNode(user, { deptId, deptName, jobType: 0 })
    )

    // 递归处理子节点
    const childNodes = children ? transformData(children) : []

    return {
      ...item,
      id: item.id || item.userId || item.deptId,
      name: item.name || item.nickName || item.deptName,
      isLeaf: item.hasOwnProperty("workNumber") || !children,
      children: [...childNodes, ...personNodes],
    }
  })
}

function transformRoleData(arr) {
  return arr.map((item) => {
    return {
      ...item,
      roleId: item.id,
      roleName: item.name,
      type: "role",
      userOrRoleId: item.id,
      userOrRoleName: item.name,
      jobType: 1, // 角色标识
      children: transformRoleData(item.children || []),
    }
  })
}

// 统一的用户国籍过滤逻辑
function filterUsersByNationality(users) {
  return props.izChineseGj == null
    ? users
    : users.filter((u) => u.izChineseGj === props.izChineseGj)
}

function transformTagData(arr) {
  return arr.map((item) => {
    const users = item.userList || []
    const filteredUsers = filterUsersByNationality(users)

    return {
      ...item,
      name: item.tagName || item.name,
      jobType: 3, // 标签标识
      children: filteredUsers.map((user) => createPersonNode(user, { jobType: 3 })),
    }
  })
}

function transformRoleUserData(items) {
  return items.map((item) => {
    const users = item.userList || []
    const filteredUsers = filterUsersByNationality(users)
    const personNodes = filteredUsers.map((user) =>
      createPersonNode(user, { jobType: 0 })
    )

    // 递归处理children并合并person节点
    return {
      ...item,
      children: [
        ...(item.children ? transformRoleUserData(item.children) : []),
        ...personNodes,
      ],
    }
  })
}

function customNodeClass({ type }) {
  return !type && !props.showCheckedBox ? "hidden-checked" : ""
}

function handleClose() {
  condition.value = ""
  treeRef.value?.scrollTo(0)
  visible.value = false
  clearAllChecked()
}

function handleConfirm() {
  emits("onSelectItems", selectList.value)
  handleClose()
}

const onOpen = async (items) => {
  visible.value = true
  await nextTick()
  if (isEmpty(items)) {
    clearAllChecked()
  } else {
    setSelectItemChecked(items)
  }
}

// 统一ID获取逻辑
function getUnifiedId(item) {
  return item.userOrRoleId || item.userId || item.roleId || item.id
}

async function setSelectItemChecked(items) {
  selectList.value = items.map((item) => ({
    ...item,
    id: getUnifiedId(item),
    userOrRoleId: getUnifiedId(item),
  }))

  defaultCheckedKeys.value = selectList.value.map(getUnifiedId)
  treeRef.value?.setCheckedKeys(defaultCheckedKeys.value)
}
// 统一过滤逻辑
function matchKeyword(data, keyword) {
  if (!keyword) return true
  return [data.name, data.workNumber].some((item) =>
    item?.toLowerCase().includes(keyword.toLowerCase())
  )
}
// 过滤树
function filterTreeMethod(value, data) {
  return matchKeyword(data, value)
}

// 节点点击
function handleTreeCheckChange(data, checked) {
  // 单选模式处理
  if (props.isOnlyOne) {
    if (selectList.value.length >= 1 && checked) {
      // 如果已有选中项且当前是选中操作，先清空再选中当前项
      treeRef.value?.setCheckedKeys([])
      treeRef.value?.setChecked(data.id, true)
      selectList.value = [data]
    } else if (!checked) {
      // 取消选中
      selectList.value = []
    } else {
      // 首次选中
      selectList.value = [data]
    }
    return
  }

  // 多选模式处理
  const selectNodes = treeRef.value?.getCheckedNodes(false) || []

  // 过滤有效节点：匹配搜索条件且有type属性的节点
  const validNodes = selectNodes.filter(
    (node) => matchKeyword(node, condition.value) && node.type
  )

  // 去重处理
  selectList.value = uniqBy(validNodes, "id")
}

function clearAllChecked() {
  selectList.value = []
  defaultCheckedKeys.value = []
  treeRef.value?.setCheckedKeys([])
  onQueryChanged()
}
// 数据映射配置
const DATA_TYPE_MAP = {
  0: () => deptData.value,
  1: () => rolesData.value,
  2: () => roleUserData.value,
  3: () => tagData.value,
}

// 类型切换
function typeSelectChange() {
  condition.value = ""
  treeRef.value?.scrollTo(0)

  const getDataFn = DATA_TYPE_MAP[jobType.value]
  if (getDataFn) {
    treeData.value = getDataFn()
  }
}
function onQueryChanged(query) {
  treeRef.value?.filter(query)
  if (!query) {
    treeRef.value?.setExpandedKeys([])
  }
}

// 移除已选
function removeItem(item) {
  const index = selectList.value.findIndex((i) => i.id === item.id)
  if (index > -1) {
    selectList.value.splice(index, 1)
    treeRef.value?.setChecked(item.id, false)
    updateModelValue()
  }
}

function updateModelValue() {}

defineExpose({
  onOpen,
})

// API请求映射配置
const API_ACTION_MAP = {
  user: getList,
  role: getRoleList,
  tagUser: getTagUserList,
  roleUser: getRoleUserList,
}

// 初始化数据
async function init() {
  loading.value = true
  try {
    // 过滤出需要的请求并并行执行
    const promises = props.selectTypes
      .filter((type) => API_ACTION_MAP[type])
      .map((type) => API_ACTION_MAP[type]())

    await Promise.all(promises)
  } catch (error) {
    console.error("初始化数据失败:", error)
  } finally {
    loading.value = false
  }
}

init()
</script>

<style scoped lang="scss">
.tree-select-wrapper {
  @apply flex items-center justify-between;
  .box {
    @apply w-49% bg-[#F7F8F9] h-500px rounded-10px p-12px;
    .title {
      @apply font-size-14px font-bold;
    }
  }
  .left-box {
    @extend .box;
    .user-tree {
      .user-tree-label {
        @apply text-sm;
      }
    }
    .all-user-input {
      margin-top: 10px;
      border: 1px solid #d8dde3;
      border-radius: 4px;
      margin-bottom: 8px;
      :deep(.el-input-group__prepend) {
        box-shadow: none;
      }
      :deep(.el-input__wrapper) {
        box-shadow: none;
      }
      :deep(.el-select) {
        width: 60px;
        .el-select__wrapper {
          background: #fff;
          box-shadow: none;
          padding-right: 0;
        }
      }
    }
  }
  .right-box {
    @extend .box;
    padding: 0;
  }
}

.user-tree {
  background: transparent;
  --el-checkbox-height: 30px;
  .user-tree-label {
    @apply text-ellipsis;
  }
  :deep(.el-vl__wrapper) {
    padding: 0 10px;
    box-sizing: border-box;
  }
  :deep(.el-tree-node.hidden-checked) {
    .el-checkbox {
      display: none;
    }
  }
  :deep(.el-tree-node__expand-icon) {
    color: #7c7c7c;
  }
  :deep(.el-checkbox) {
    --el-checkbox-input-border: 1px solid #969eac;
    --el-checkbox-input-height: 16px;
    --el-checkbox-input-width: 16px;
    .el-checkbox__inner {
      &:after {
        top: 2px;
        left: 5px;
      }
      &:before {
        top: 6px;
      }
    }
  }
}
</style>
