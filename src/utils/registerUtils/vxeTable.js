import {
	VxeUI,
	VxeButton,
	VxeButtonGroup,
	VxeDrawer,
	VxeForm,
	VxeFormGroup,
	VxeFormItem,
	VxeIcon,
	VxeLoading,
	VxeModal,
	VxePager,
	VxePrint,
	VxeTooltip,
	VxeUpload,
	VxeRadioGroup,
	VxeNumberInput,
} from "vxe-pc-ui"
import { VxeTable, VxeColumn, VxeColgroup, VxeGrid, VxeToolbar } from "vxe-table"
import zhCN from "vxe-table/lib/locale/lang/zh-CN"
VxeUI.setI18n("zh-CN", zhCN)
VxeUI.setLanguage("zh-CN")

// 全局默认参数
VxeUI.setConfig({
	table: {
		stripe: true,
		border: false,
	},
})

// 可选组件
export function lazyVxeUI(app) {
	app.use(VxeButton)
	app.use(VxeButtonGroup)
	app.use(VxeDrawer)
	app.use(VxeForm)
	app.use(VxeFormGroup)
	app.use(VxeFormItem)
	app.use(VxeIcon)
	app.use(VxeLoading)
	app.use(VxeModal)
	app.use(VxePager)
	app.use(VxePrint)
	app.use(VxeTooltip)
	app.use(VxeUpload)
	app.use(VxeRadioGroup)
	app.use(VxeNumberInput)
}

export function lazyVxeTable(app) {
	app.use(VxeTable)
	app.use(VxeColumn)
	app.use(VxeColgroup)
	app.use(VxeGrid)
	app.use(VxeToolbar)
}
