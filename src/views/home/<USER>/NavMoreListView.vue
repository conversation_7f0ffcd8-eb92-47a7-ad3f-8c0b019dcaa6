<template>
  <div class="nav-more-list-view" :class="{ show: modelValue }">
    <el-icon class="close-btn" @click="handleClose">
      <CircleCloseFilled />
    </el-icon>
    <CommonCard :title="title" class="list-content">
      <template #suffix>
        <el-input
          v-model="name"
          placeholder="请输入名称"
          suffix-icon="Search"
          maxlength="100"
          class="search-input"
        />
      </template>

      <div v-show="filterList.length" class="nav-box">
        <IconNav v-for="(item, index) in filterList" :key="index" :info="item" />
      </div>
      <el-empty v-show="filterList.length == 0" description="暂无搜索结果" />
    </CommonCard>
  </div>
</template>

<script setup>
import CommonCard from "@/views/components/CommonCard.vue"
import IconNav from "./IconNav.vue"
import { computed } from "vue"

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
  list: {
    type: Array,
    default: () => [],
  },
})

const name = ref("")

const filterList = computed(() => {
  if (!name.value) {
    return props.list
  }
  return props.list.filter((item) => item?.appName.includes(name.value))
})
const emit = defineEmits(["update:modelValue"])
const handleClose = () => {
  name.value = ""
  emit("update:modelValue", false)
}
</script>

<style scoped lang="scss">
.nav-more-list-view {
  position: absolute;
  z-index: 99;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  padding: 15px;
  min-width: 1100px;
  transform: translateY(100%);
  transition: opacity 0.5s, transform 0.5s;
  opacity: 0;
  &.show {
    transform: translateY(0);
    opacity: 1;
  }
  .list-content {
    height: 100%;
  }
  .close-btn {
    position: absolute;
    font-size: 26px;
    color: #999;
    cursor: pointer;
    top: 0;
    right: 10px;
    &:hover {
      color: var(--el-color-primary);
    }
  }
}

.search-input {
  width: 200px;
  height: 35px;
  :deep(.el-input__wrapper) {
    box-shadow: none;
    background: #f2f2f2;
  }
}

.nav-box {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
}
</style>
