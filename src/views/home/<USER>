<template>
  <CommonCard title="文件服务">
    <div class="file-service">
      <el-upload
        ref="uploadRef"
        :limit="1"
        :accept="acceptedFileTypes"
        :on-exceed="handleExceed"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleFileChange"
        class="upload-file-uploader"
        drag
      >
        <!-- 上传按钮 -->
        <div class="upload-drag-box" v-if="!fileRaw">
          <img src="@/assets/images/file/drag-icon.png" alt="" />
          <div class="upload-drag-text">
            将文件拖拽至此区域，或
            <el-link type="primary" underline="always"> 点击上传文件 </el-link>
            <p class="whitespace-pre-line">{{ acceptText }}</p>
          </div>
        </div>
        <div class="upload-file-list" v-else>
          <UploadFileInfo :acceptText="acceptText" :file="fileRaw" />
          <div class="opt-btn ml-5%">
            <el-button
              v-auths="['file:fileList:lunchAudit', 'file:initiatedFiles:lunchAudit']"
              type="primary"
              w-150px
              @click.stop="handleLunch(0, '文件发起(审批)')"
            >
              文件发起 (审批)
            </el-button>
            <el-button
              v-auths="['file:fileList:lunch', 'file:initiatedFiles:lunch']"
              type="success"
              w-155px
              @click.stop="handleLunch(1, '文件发起(无需审批)')"
            >
              文件发起 (无需审批)
            </el-button>
          </div>
        </div>
      </el-upload>
      <div
        class="template-upload-btn batch-btn"
        @click="$router.push('/template/myTemplate')"
        v-auths="['template:myTemplate:list']"
      >
        <img src="@/assets/images/file/batch-upload.png" alt="" />
        <p>模版发起</p>
      </div>
    </div>
  </CommonCard>
</template>

<script setup>
import useOtherStore from "@/store/modules/other"
import CommonCard from "@/views/components/CommonCard.vue"
import { genFileId } from "element-plus"

const UploadFileInfo = defineAsyncComponent(() =>
  import("@/components/FileUpload/UploadFileInfo.vue")
)

const { proxy } = getCurrentInstance()
// 文件上传相关配置
const acceptedFileTypes = ".pdf,.doc,.docx,.jpg,.png,.xls,.xlsx,.html,.jpeg"
const acceptText = `支持 .pdf/.docx/.doc/.jpg/.png/.xls/.xlsx/.html 文件;\n图片类型(png/jpg/jpeg)限制大小为5M以下,PDF/word/excel等其他格式限制大小为60M以下。`
const uploadRef = ref(null)
const fileRaw = ref(null)
// 文件大小限制常量
const FILE_SIZE_LIMITS = {
  IMAGE: 5 * 1024 * 1024, // 5MB
  DOCUMENT: 60 * 1024 * 1024, // 60MB
}
/**
 * 处理文件超出限制
 */
const handleExceed = (files) => {
  uploadRef.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  uploadRef.value.handleStart(file)
}
/**
 * 验证文件类型和大小
 */
const validateFile = (file) => {
  const allowedTypes = acceptedFileTypes
    .split(",")
    .map((type) => type.replace(".", ""))
    .filter(Boolean)

  const fileType = file.name.split(".").pop().toLowerCase()
  const isImage = ["jpg", "png", "jpeg"].includes(fileType)

  if (!allowedTypes.includes(fileType)) {
    proxy.$modal.msgError("不支持的文件格式，请上传指定类型文件")
    return false
  }

  const maxSize = isImage ? FILE_SIZE_LIMITS.IMAGE : FILE_SIZE_LIMITS.DOCUMENT
  if (file.size > maxSize) {
    const sizeText = isImage ? "5M" : "60M"
    proxy.$modal.msgError(`文件大小不能超过${sizeText}`)
    return false
  }

  return true
}
/**
 * 处理文件选择变化
 */
const handleFileChange = (file) => {
  if (!validateFile(file)) {
    uploadRef.value.clearFiles()
    return
  }
  if (file.status === "ready") {
    fileRaw.value = file
    console.log("file", file)
  }
}

function clearFile() {
  fileRaw.value = null
  uploadRef.value.clearFiles()
}

/** 文件发起合同 */
function handleLunch(flowFlag, title) {
  useOtherStore().setIndexFileData(fileRaw.value)
  clearFile()
  proxy.$tab.openPage(title, `/file/fileLunch/add/${flowFlag}`)
}
</script>

<style lang="scss" scoped>
.file-service {
  display: flex;
  height: 150px;
  .upload-file-uploader {
    flex: auto;
  }
  .el-upload {
    width: 100%;
  }
  :deep(.el-upload-dragger) {
    border-color: #4ec49f;
    height: 150px;
    width: 100%;
  }
}
.upload-drag-box {
  display: flex;
  align-items: center;
  height: 100%;
  padding-left: 30px;

  img {
    width: 70px;
  }
  .upload-drag-text {
    font-size: 14px;
    text-align: left;
    margin-left: 5px;
    p {
      color: #9b9b9b;
      margin: 0;
      margin-top: 3px;
    }
  }
  .el-link {
    line-height: 16px;
    margin-top: -2px;
  }
}

.template-upload-btn {
  @apply flex-center flex-col w-150px h-150px rounded-8px cursor-pointer flex-shrink-0;
  border: 1px solid #96cbff;
  background-color: rgba(59, 131, 193, 0.1);
  font-size: 14px;
  margin-left: 15px;
  &:hover {
    font-weight: 500;
  }
  img {
    width: 40px;
    height: 40px;
    object-fit: contain;
  }
  p {
    margin: 8px 0 0;
  }
  &.batch-btn {
    background: rgba(255, 209, 174, 0.21);
    border-color: rgba(255, 174, 87, 0.62);
  }
}

.upload-file-list {
  @apply flex-x-between h-100% p-x-20px;
}
</style>
