<template>
  <div class="components-table">
    <el-form
      ref="queryRef"
      :model="queryParams"
      label-width="90px"
      class="white-form-box"
      inline
    >
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input
          v-model="queryParams.enterpriseName"
          placeholder="请输入企业名称"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="经办人姓名" prop="operatorName">
        <el-input
          v-model="queryParams.operatorName"
          placeholder="请输入经办人姓名"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="queryParams.createTimeRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="common-input-width"
          clearable
          :default-time="dateDefaultTime"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" :loading="loading"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery" :loading="loading">重置</el-button>
      </el-form-item>
    </el-form>
    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :has-toolbar="false"
      :data="tableData"
      :loading="loading"
      :total="total"
      :opt-width="100"
      @reload="getList"
    >
      <template #actions>
        <el-button type="primary" icon="Plus" @click="handleAdd">新增</el-button>
        <el-button type="primary" plain @click="handleImport">导入</el-button>

        <el-button
          type="danger"
          w-80px
          plain
          @click="handleDelete"
          :disabled="!selections.length"
          >删除</el-button
        >
      </template>
      <vxe-column type="checkbox" width="60" fixed="left" />
      <vxe-column type="seq" width="60" />
      <vxe-column title="企业名称" field="enterpriseName" show-overflow />
      <vxe-column title="经办人姓名" field="operatorName" show-overflow />
      <vxe-column title="手机号" field="phoneNumber" show-overflow>
        <template #default="{ row }">
          {{ replacePhone(row.phoneNumber) }}
        </template>
      </vxe-column>
      <vxe-column title="添加时间" field="createTime" show-overflow />
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '编辑',
              click: () => handleEdit(row),
            },
            {
              text: '删除',
              type: 'danger',
              click: () => handleDelete(row),
            },
          ]"
        />
      </template>
    </CustomTable>

    <!-- 编辑、新增  -->
    <EditCompanyContactModal
      ref="editCompanyContactRef"
      @refresh="handleQuery"
      @change="getList"
    />
    <!-- 导入弹窗  -->
    <ImportFile
      ref="importFileRef"
      :tempUrl="essPrefix + '/enterpriseUser/importTemplate'"
      :uploadUrl="essPrefix + '/enterpriseUser/importMember'"
      title="企业联系人导入"
      @change="getList"
    />
  </div>
</template>

<script setup lang="ts">
import {
  getEnterpriseUserListAPI,
  deleteEnterpriseUserAPI,
} from "@/api/ess/baseSet/contacts"
import EditCompanyContactModal from "./EditCompanyContactModal.vue"
import { replacePhone } from "@/utils"
import { essPrefix } from "@/config/constant"

const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const editCompanyContactRef = ref()
const importFileRef = ref(null)

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

const queryParams = reactive({
  page: 1,
  limit: 10,
  enterpriseName: "",
  operatorName: "",
})

async function getList() {
  try {
    loading.value = true
    let res = await getEnterpriseUserListAPI(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  editCompanyContactRef.value.open()
}

/** 编辑按钮操作 */
function handleEdit(row: object) {
  editCompanyContactRef.value.open(row)
}

/** 删除按钮操作 */
function handleDelete(row: object) {
  const ids = row?.id ? row.id : selections.value.map((item) => item.id)
  const names = row?.id
    ? row.enterpriseName
    : selections.value.map((item) => item.enterpriseName)
  proxy.$modal
    .confirm("是否确认删除企业?", `当前已选企业(${names})`)
    .then(function () {
      return deleteEnterpriseUserAPI(ids)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功！")
    })
    .catch(() => {})
}

/** 导入按钮操作 */
function handleImport() {
  importFileRef.value.handleImport()
}

getList()
</script>

<style scoped lang="scss"></style>
