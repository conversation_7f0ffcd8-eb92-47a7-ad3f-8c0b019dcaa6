<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :before-close="handleClose"
    width="700px"
    append-to-body
  >
    <el-form ref="userRef" :model="form" :rules="rules" label-width="110px" inline>
      <el-form-item label="用户昵称" prop="nickName">
        <el-input
          v-model="form.nickName"
          placeholder="请输入用户昵称"
          maxlength="30"
          clearable
        />
      </el-form-item>
      <el-form-item label="归属部门" prop="deptId">
        <dept-picker v-model="form.deptId" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="form.phonenumber"
          placeholder="请输入手机号码"
          maxlength="11"
          clearable
        />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="form.email"
          placeholder="请输入邮箱"
          maxlength="50"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="!form.userId" label="用户名称" prop="userName">
        <el-input
          v-model="form.userName"
          placeholder="请输入用户名称"
          maxlength="30"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="!form.userId" label="用户密码" prop="password">
        <el-input
          v-model="form.password"
          placeholder="请输入用户密码"
          type="password"
          maxlength="20"
          show-password
          clearable
        />
      </el-form-item>
      <el-form-item label="用户性别" prop="sex">
        <el-select v-model="form.sex" placeholder="请选择" clearable>
          <el-option
            v-for="dict in sys_user_sex"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="岗位" class="block-form-item" prop="postIds">
        <el-select v-model="form.postIds" multiple placeholder="请选择" clearable>
          <el-option
            v-for="item in postOptions"
            :key="item.postId"
            :label="item.postName"
            :value="item.postId"
            :disabled="item.status == 1"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="角色" class="block-form-item" prop="roleIds">
        <el-select v-model="form.roleIds" multiple placeholder="请选择" clearable>
          <el-option
            v-for="item in roleOptions"
            :key="item.roleId"
            :label="item.roleName"
            :value="item.roleId"
            :disabled="item.status == 1"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" class="block-form-item" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入内容"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 取消 </el-button>
      <el-button type="primary" @click="submitForm"> 确定 </el-button>
    </template>
  </el-dialog>
</template>

<script setup name="EditUserModal">
import { getUser, updateUser, addUser } from "@/api/system/user"

const { proxy } = getCurrentInstance()
const { sys_normal_disable, sys_user_sex } = proxy.useDict(
  "sys_normal_disable",
  "sys_user_sex"
)
const emit = defineEmits(["change"])

const props = defineProps({
  deptOptions: {
    type: Array,
    default: () => [],
  },
})
const title = ref("新增用户")
const dialogVisible = ref(false)
const initPassword = ref(undefined)
const userRef = ref(null)
const postOptions = ref([])
const roleOptions = ref([])
const data = reactive({
  form: {},
  rules: {
    userName: [
      { required: true, message: "用户名称不能为空", trigger: "blur" },
      { min: 2, max: 20, message: "用户名称长度必须介于 2 和 20 之间", trigger: "blur" },
    ],
    nickName: [{ required: true, message: "用户昵称不能为空", trigger: "blur" }],
    password: [
      { required: true, message: "用户密码不能为空", trigger: "blur" },
      { min: 5, max: 20, message: "用户密码长度必须介于 5 和 20 之间", trigger: "blur" },
      {
        pattern: /^[^<>"'|\\]+$/,
        message: "不能包含非法字符：< > \" ' \\\ |",
        trigger: "blur",
      },
    ],
    email: [
      { type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] },
    ],
    phonenumber: [
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
  },
})

const { form, rules } = toRefs(data)
const open = (userId) => {
  dialogVisible.value = true
  reset()
  if (userId) {
    title.value = "修改用户"
    getUserInfo(userId)
  } else {
    getUserInfo()
    title.value = "新增用户"
  }
}

const getUserInfo = (userId) => {
  getUser(userId).then((response) => {
    postOptions.value = response.posts
    roleOptions.value = response.roles
    if (userId) {
      form.value = response.data
      form.value.postIds = response.postIds
      form.value.roleIds = response.roleIds
      form.value.password = ""
    }
  })
}

const handleClose = () => {
  reset()
  dialogVisible.value = false
}

/** 重置操作表单 */
function reset() {
  form.value = {
    userId: null,
    deptId: null,
    userName: null,
    nickName: null,
    password: null,
    phonenumber: null,
    email: null,
    sex: null,
    status: "0",
    remark: null,
    postIds: [],
    roleIds: [],
  }
  proxy.resetForm("userRef")
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["userRef"].validate((valid) => {
    if (valid) {
      let actionUrl = form.value.userId ? updateUser : addUser
      actionUrl(form.value).then((res) => {
        proxy.$modal.msgSuccess(form.value.userId ? "修改成功" : "新增成功")
        emit("change")
        handleClose()
      })
    }
  })
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.block-form-item {
  width: 620px;
}
</style>
