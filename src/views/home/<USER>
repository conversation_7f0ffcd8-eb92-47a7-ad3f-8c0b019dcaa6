<template>
  <div class="use-condition">
    <div class="condition-nav">
      <div
        class="condition-nav-item"
        :class="{ active: current === 0 }"
        @mouseover="changeCurrent(0)"
      >
        <img src="@/assets/images/index/seal-nav-icon.png" />
        <div class="condition-nav-item-title">
          <div class="title">印章使用总数</div>
          <div class="num">{{ sealTotal }}</div>
        </div>
      </div>
      <div
        class="condition-nav-item"
        :class="{ active: current === 1 }"
        @mouseover="changeCurrent(1)"
      >
        <img src="@/assets/images/index/file-nav-icon.png" />
        <div class="condition-nav-item-title">
          <div class="title">文件用印总数</div>
          <div class="num">{{ fileTotal }}</div>
        </div>
      </div>
    </div>

    <div class="use-table mt-15px">
      <el-table :data="sealChartData" :stripe="false" v-show="current === 0">
        <el-table-column type="index" width="60" label="排名" align="left" />
        <el-table-column label="印章名称" prop="sealName" show-overflow-tooltip />
        <el-table-column
          width="90"
          label="使用数量"
          prop="sealCount"
          show-overflow-tooltip
        />
        <template #empty>
          <CustomEmpty />
        </template>
      </el-table>
      <el-table :data="fileChartData" :stripe="false" v-show="current === 1">
        <el-table-column type="index" width="60" label="排名" />
        <el-table-column
          label="业务线类别"
          prop="businessLineName"
          show-overflow-tooltip
        />
        <el-table-column
          width="90"
          label="文件数量"
          prop="fileCount"
          show-overflow-tooltip
        />

        <template #empty>
          <CustomEmpty />
        </template>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { getFileIndexCountAPI, getSealIndexCountAPI } from "@/api/ess/home"
const sealChartData = ref([])
const fileChartData = ref([])

const current = ref(0)
const sealTotal = ref(0)
const fileTotal = ref(0)
function getFileCount() {
  getFileIndexCountAPI().then((res) => {
    fileChartData.value = res.object?.businessLineFiles || []
    fileTotal.value = res.object.total || 0
  })
}
function getSealCount() {
  getSealIndexCountAPI().then((res) => {
    sealChartData.value = res.object?.details || []
    sealTotal.value = res.object.total || 0
  })
}

function changeCurrent(index) {
  current.value = index
}

function init() {
  getFileCount()
  getSealCount()
}

init()

onActivated(() => {
  init()
})
</script>

<style scoped lang="scss">
.use-condition {
  height: 650px;
}

.condition-nav {
  display: flex;
  justify-content: space-between;
  .condition-nav-item {
    width: 48.8%;
    @apply flex cursor-pointer rounded-8px;
    background: linear-gradient(to top, #ecfaf5, #fff);
    padding: 15px 20px;
    border: 2px solid #e6f6f1;
    position: relative;
    img {
      width: 50px;
      object-fit: contain;
      margin-right: 10px;
    }
    .condition-nav-item-title {
      font-size: 14px;
      .title {
        white-space: nowrap;
      }
      .num {
        font-size: 26px;
        font-family: "BAHNSCHRIFT";
        margin-top: 5px;
        color: #38383a;
      }
    }
    &.active,
    &:hover {
      border-color: transparent;
      &::after {
        content: "";
        display: block;
        height: 115%;
        width: 102%;
        position: absolute;
        top: -2px;
        left: -2px;
        background: url(@/assets/images/index/use-nav-bg.png);
        background-size: 100% 100%;
      }
      .condition-nav-item-title {
        .title {
          font-weight: 600;
        }
        .num {
          color: var(--el-color-primary);
        }
      }
    }
  }
}

.use-table {
  :deep(.el-table thead th.el-table__cell) {
    padding: 9px 0;
    background: #f7f8f9 !important;
    border-bottom: 0;
  }
  :deep(.el-table__row:not(:last-child)) {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      height: 1px;
      width: 96%;
      margin: auto;
      bottom: 0;
      left: 0;
      right: 0;
      border-bottom: 1px dashed #f0f1f3;
    }
  }
  :deep(.el-table tbody .el-table__cell) {
    padding: 14px 0;
  }
}
</style>
