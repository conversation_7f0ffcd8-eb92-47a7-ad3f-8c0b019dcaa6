<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item label="类型" prop="type">
						<el-select
							v-model="state.queryForm.type"
							placeholder="请选择数据类型"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.FORM_DATA_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="表单来源" prop="formType">
						<el-select
							v-model="state.queryForm.formType"
							placeholder="请选择表单来源"
							clearable
							filterable
							@change="formTypeChange"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.FORM_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="表单名称" prop="formId">
						<el-select
							v-model="state.queryForm.formId"
							placeholder="请选择表单名称"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in dicData.formIdByType"
								:key="index"
								:label="item.formName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="流程名称" prop="defFlowId">
						<el-select
							v-model="state.queryForm.defFlowId"
							placeholder="请选择流程名称"
							clearable
							filterable
							@change="cascadeChange('defFlowId', ['flowNodeId'])"
						>
							<el-option
								v-for="(item, index) in dicData.defFlowId"
								:key="index"
								:label="item.flowName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="节点名称" prop="flowNodeId">
						<el-select
							v-model="state.queryForm.flowNodeId"
							placeholder="请选择节点名称"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							查询
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<el-button
						v-auth="'jsonflow_formoption_add'"
						icon="CirclePlus"
						type="primary"
						class="ml10"
						@click="openFormDefPerm('add', null, '0')"
					>
						字段定义
					</el-button>
					<el-button
						v-auth="'jsonflow_formoption_add'"
						icon="Setting"
						type="primary"
						class="ml10"
						@click="openFormDefPerm('add', null, '1')"
					>
						权限配置
					</el-button>
					<el-button
						v-auth="'jsonflow_formoption_add'"
						icon="Pointer"
						type="primary"
						class="ml10"
						@click="handlePrintTemplate"
					>
						打印设计
					</el-button>
					<el-button
						v-auth='jsonflow_formoption_del'
						plain
						:disabled="multiple"
						icon="Delete"
						type="primary"
						class="ml10"
						@click="handleDelete(selectObjs)"
					>
						删除
					</el-button>
					<right-toolbar
						v-model:show-search="showSearch"
						class="ml10"
						style="float: right;margin-right: 20px"
						export="jsonflow_formoption_export"
						@export-excel="exportExcel"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column
					fixed="left"
					type="index"
					label="#"
					width="40"
				/>
				<el-table-column prop="type" label="数据类型" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.FORM_DATA_TYPE" :value="scope.row.type" />
					</template>
				</el-table-column>
				<el-table-column prop="formType" label="表单来源" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.FORM_TYPE" :value="scope.row.formType" />
					</template>
				</el-table-column>
				<el-table-column prop="formName" label="表单名称" show-overflow-tooltip />
				<el-table-column prop="path" label="表单路径" show-overflow-tooltip />
				<el-table-column prop="defFlowId" label="流程名称" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="dicData.defFlowId"
							:value="scope.row.defFlowId"
							value-key="id"
							show-key="flowName"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="flowNodeId" label="节点名称" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="dicData.flowNodeId"
							:value="scope.row.flowNodeId"
							value-key="id"
							show-key="nodeName"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="propId" label="属性唯一ID" show-overflow-tooltip />
				<el-table-column prop="label" label="属性名称" show-overflow-tooltip />
				<el-table-column prop="prop" label="属性名" show-overflow-tooltip />
				<el-table-column prop="subForm" label="子表单属性名" show-overflow-tooltip />
				<el-table-column prop="propType" label="属性类型" show-overflow-tooltip />
				<el-table-column prop="permType" label="权限类型" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.FORM_PERM_TYPE" :value="scope.row.permType" />
					</template>
				</el-table-column>
				<el-table-column prop="printInfo" label="打印信息" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" fixed="right" width="100">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.viewBtn') }}
							</template>
							<el-button
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								修改
							</template>
							<el-button
								icon="edit-pen"
								text
								type="primary"
								@click="handleRowEdit(scope.row)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.delBtn') }}
							</template>
							<el-button
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />

		<!-- 打印模板设计器 -->
		<el-drawer
			v-model="data.showTinymceEditor"
			class="tinymce-print-drawer"
			append-to-body
			direction="rtl"
			size="100%"
			:title="data.tinymceTitle"
			@close="getDataList"
		>
			<tinymce-editor v-if="data.showTinymceEditor" :curr-flow-form="data.currFlowForm" />
		</el-drawer>

		<el-dialog
			v-if="data.formDefPermVisible"
			v-model="data.formDefPermVisible"
			title="新增或修改"
			width="80%"
			:close-on-click-modal="false"
			draggable
		>
			<form-def-perm ref="formDefPermRef" :form-def-perm="data.formDefPerm" @refresh="getDataList(false)" />
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="FormOption">
import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchList, delObjs } from "@/api/jsonflow/form-option"
import { useMessage, useMessageBox } from "@/hooks/message"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
import { DIC_PROP } from "@/flow/support/dict-prop"
import { deepClone } from "@/utils/index"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))
const FormDefPerm = defineAsyncComponent(() => import("./form-def-perm.vue"))
const TinymceEditor = defineAsyncComponent(() => import("@/flow/components/tinymce/TinymceEditor.vue"))

// 定义查询字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "defFlowId" }, { key: "flowNodeId" }, { key: "formId" })
const onCascade = onCascadeChange(cascadeDic, { key: "defFlowId", cascades: ["flowNodeId"] })
onMounted(() => {
	onLoad(dicData)
})
function cascadeChange(key, cascades) {
	onCascade(state.queryForm, { key: key, cascades: cascades })
}

// 定义变量内容
const formDialogRef = ref()
const formDefPermRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	queryRef.value.resetFields()
	// 清空多选
	selectObjs.value = []
	getDataList()
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

// 导出excel
const exportExcel = () => {
	downBlobFile("/jsonflow/form-option/export", state.queryForm, "formoption.xlsx")
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('是否确认删除')
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess('删除成功')
	}
	catch (err: any) {
		proxy.$modal.msgError(err)
	}
}

const formTypeChange = value => {
	if (value) { dicData.formIdByType = dicData.formId?.filter(f => f.type === value) }
	else { dicData.formIdByType = [] }
	state.queryForm.formId = null
}

function handleRowEdit(row) {
	if (row.type === DIC_PROP.FORM_DATA_TYPE[2].value) {
		handlePrintTemplate(row, true)
	}
	else {
		formDialogRef.value.openDialog("edit", row.id)
	}
}

const data = reactive({
	currFlowForm: {},
	showTinymceEditor: false,
	formDefPermVisible: false,
	formDefPerm: {},
	tinymceTitle: "",
})

const openFormDefPerm = async (type: string, id: string, dataType: string) => {
	data.formDefPermVisible = true
	data.formDefPerm = { type, id, dataType }
}

function handlePrintTemplate(row, isExist) {
	data.currFlowForm = {}
	if (isExist) {
		data.currFlowForm = deepClone(row)
		data.currFlowForm.isForm = true
	}
	data.tinymceTitle = "打印模板设计器（自定义打印格式）"
	data.showTinymceEditor = true
}

</script>

<style lang="scss">
  @use "../../../flow/components/style/flow-drawer.scss" as *;;
</style>
