<template>
  <div class="app-container">
    <CustomTable ref="customTableRef" :has-toolbar="false" :data="dataList" :loading="loading"
      v-model:page="getDownloadListParams.page" v-model:limit="getDownloadListParams.limit" :total="total">
      <template #actions>
        <el-button type="danger" class="btn-delete" :disabled="selections.length === 0" plain @click="handleDelete" v-auths="['download:center:delete']">
          批量删除
        </el-button>
      </template>
      <vxe-column type="checkbox" width="30" fixed="left" />
      <vxe-column type="seq" width="60" fixed="left" />
      <vxe-column title="文件名称" field="fileName" width="40%">
        <template #default="{ row }">
          <div class="flex items-center">
            <img src="@/assets/images/icon/download-file.png" width="15" />
            <div class="ml-10px">{{ row.fileName }}</div>
          </div>
        </template>
      </vxe-column>
      <vxe-column title="发起下载时间" field="createTime" show-overflow>
        <template #default="{ row }">
          <span class="fileInfo">{{ row.createTime ? row.createTime : "-" }}</span>
        </template>
      </vxe-column>
      <vxe-column title="下载完成时间" field="updateTime" show-overflow>
        <template #default="{ row }">
          <span class="fileInfo">{{ row.updateTime ? row.updateTime : "-" }}</span>
        </template>
      </vxe-column>
      <vxe-column field="opts" title="操作" fixed="right" width="180">
        <template #default="{ row }">
          <div class="download-opts">
            <div v-if="row.status === 1" class="flex items-center pr-15px color-#6B6B6B">
              <svg-icon name="local-download-circle" size="17" class="mr-5px" />
              <span class="mr-15px text-14px">下载中...</span>
              <el-button icon="Close" link type="info" @click="cancelDownloadTask(row)" v-auths="['download:center:cancel']"></el-button>
            </div>
            <el-button v-if="row.status === 0" icon="Download" link type="primary" class="text-14px"
              @click="startDownloadTask(row)">下载
            </el-button>

            <el-button icon="Delete" link type="danger" class="font-600! text-16px" v-auths="['download:center:delete']"

                       @click="handleDelete(row)" ></el-button>
          </div>
        </template>
      </vxe-column>
    </CustomTable>
  </div>
</template>

<script setup name="DownloadCenter">
import {
  getDownloadListPage,
  deleteAllDownload,
  cancelDownload,
  startDownload,
} from "@/api/ess/downloadCenter/downloadCenter.ts"
import { handleBlobFile } from "@/utils"

const { proxy } = getCurrentInstance()
const dataList = ref([])
const loading = ref(false)

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

//查询参数
const getDownloadListParams = ref({
  limit: 10,
  page: 1,
})
const total = ref(0)

//获取列表
const getDownloadList = () => {
  loading.value = true
  getDownloadListPage(getDownloadListParams.value)
    .then((res) => {
      dataList.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

// 删除按钮
const handleDelete = (row) => {
  const ids = row?.id ? row.id : selections.value.map((item) => item.id)
  const names = row?.id ? row.fileName : selections.value.map((item) => item.fileName)
  proxy.$modal
    .confirm("是否确认删除下载文件?", `当前已选文件(${names})`)
    .then(function () {
      return deleteAllDownload(ids)
    })
    .then(() => {
      getDownloadList()
      proxy.$modal.msgSuccess("删除成功！")
    })
    .catch(() => { })
}

//下载
const startDownloadTask = (row) => {
  proxy.$modal.msgWarning(`开始下载${row.fileName}，请稍等！`)
  startDownload(row.filePath).then((res) => {
    handleBlobFile(res.data, row.filePath)
  })
}

//取消下载
const cancelDownloadTask = (row) => {
  proxy.$modal
    .confirm(`是否确认取消下载${row.fileName}?`)
    .then(function () {
      return cancelDownload(row.id)
    })
    .then(() => {
      getDownloadList()
      proxy.$modal.msgWarning(`已取消文件${row.fileName}下载`)
    })
    .catch(() => { })
}

onMounted(() => {
  getDownloadList()
})
</script>

<style scoped lang="scss">
.download-opts {
  display: flex;
}
</style>
