export default {
	runNode: {
		index: "#",
		importrunNodeTip: "导入运行节点管理",
		id: "节点名称",
		sort: "排序值",
		defFlowId: "流程名称",
		flowNodeId: "节点名称",
		startTime: "开始时间",
		endTime: "结束时间",
		nodeType: "节点类型",
		nodeName: "节点名称",
		subDefFlowId: "关联子流程",
		nodeApproveMethod: "多节点审批方式",
		rejectType: "被驳回后再次提交时",
		timeout: "节点时限",
		status: "节点状态",
		isAutoNext: "是否自动流转下一节点",
		isContinue: "是否继续下一节点",
		suspension: "是否挂起",
		createUser: "创建人",
		createTime: "创建时间",
		flowInstId: "流程实例ID",
		flowKey: "业务KEY",

		inputIdTip: "请输入节点名称",
		inputSortTip: "请输入排序值",
		inputDefFlowIdTip: "请输入流程名称",
		inputFlowNodeIdTip: "请输入节点名称",
		inputStartTimeTip: "请输入开始时间",
		inputEndTimeTip: "请输入结束时间",
		inputNodeTypeTip: "请输入节点类型",
		inputNodeNameTip: "请输入节点名称",
		inputSubDefFlowIdTip: "请输入关联子流程",
		inputNodeApproveMethodTip: "请输入多节点审批方式",
		inputRejectTypeTip: "请输入被驳回后再次提交时",
		inputTimeoutTip: "请输入任务时限",
		inputStatusTip: "请输入节点状态",
		inputIsAutoNextTip: "请输入是否自动流转下一节点",
		inputIsContinueTip: "请输入是否继续下一节点",
		inputSuspensionTip: "请输入是否挂起",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputFlowInstIdTip: "请输入流程实例ID",
		inputFlowKeyTip: "请输入业务KEY",

	},
}
