<template>
  <el-dialog
      v-model="dialogVisible"
      title="详情"
      :before-close="handleClose"
      width="800px"
      append-to-body
      class="small-form-dialog"
  >
    <div class="min-h-300px" v-loading="loading">
      <div class="center text-18px font-600">{{ form.noticeTitle }}</div>
      <Editor class="p-20px" v-model="form.noticeContent" readOnly />
    </div>
  </el-dialog>
</template>

<script setup name="ViewNoticeModal">
import { getNotice } from "@/api/ess/doc/noticeManagementApi"
import Editor from "@/components/editor"

defineOptions({
  inheritAttrs: false,
})

const loading = ref(true)
const dialogVisible = ref(false)
const form = ref({})

const open = (row) => {
  dialogVisible.value = true
  getNoticeInfo(row.id)
}

const handleClose = () => {
  dialogVisible.value = false
  form.value = {}
}

async function getNoticeInfo(id) {
  try {
    loading.value = true
    const res = await getNotice(id)
    form.value = res.object
  } finally {
    loading.value = false
  }
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>

</style>
