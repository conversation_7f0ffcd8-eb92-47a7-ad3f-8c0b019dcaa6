const imageIsExist = url => {
	return new Promise(resolve => {
		var img = new Image()
		img.onload = function() {
			if (this.complete == true) {
				resolve(true)
				img = null
			}
		}
		img.onerror = function() {
			resolve(false)
			img = null
		}
		img.src = url
	})
}

const loadSucImg = async (el, binding) => {
	let imgURL = binding.value
	// 判断是否是相对路径
	if (imgURL) {
		// 如果 imgURL 是 URL 对象，获取其 href
		if (imgURL instanceof URL) {
			imgURL = imgURL.href
		}
		let exist = await imageIsExist(imgURL)
		if (exist) {
			el.src = imgURL
		}
	}
}

const loadImage = {
	async updated(el, binding, vnode) {
		// 检查值是否真的发生了变化
		if (binding.value !== binding.oldValue) {
			await loadSucImg(el, binding)
		}
	},
	async mounted(el, binding) {
		await loadSucImg(el, binding)
	},
	async beforeUpdate(el, binding) {
		// 检查值是否真的发生了变化
		if (binding.value !== binding.oldValue) {
			await loadSucImg(el, binding)
		}
	},
	// 优化 watch 实现
	watch: {
		value: {
			handler(newVal, oldVal) {
				// 更严格的值比较
				if (newVal !== oldVal && newVal !== undefined && newVal !== null) {
					// 使用 nextTick 确保 DOM 更新完成
					nextTick(() => {
						loadSucImg(this.el, { value: newVal })
					})
				}
			},
			immediate: true
		}
	}
}

export default loadImage
