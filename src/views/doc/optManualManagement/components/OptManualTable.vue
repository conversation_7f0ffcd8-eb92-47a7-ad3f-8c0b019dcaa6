<template>
  <div>
    <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
        class="white-form-box"
        label-width="85px"
    >
      <el-form-item label="文档名称" prop="docName">
        <el-input
            v-model="queryParams.docName"
            placeholder="请输入文档名称"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传时间" prop="updateTimeRange">
        <el-date-picker
            v-model="queryParams.updateTimeRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="common-input-width"
            clearable
            :default-time="dateDefaultTime"
        />
      </el-form-item>
      <el-form-item>
        <el-button
            type="primary"
            icon="Search"
            :loading="loading"
            @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="Refresh" :loading="loading" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <CustomTable
        ref="customTableRef"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :has-toolbar="false"
        :data="tableData"
        :loading="loading"
        :total="total"
        :opt-width="100"
        @reload="getList"
    >
      <template #actions>
        <el-button type="primary" icon="plus" @click="handleAdd" v-auths="['ess:manualCfgInfo:add']">新增</el-button>
        <el-button
            v-auths="['ess:manualCfgInfo:remove']"
            type="danger"
            plain
            @click="handleDelete"
            :disabled="!selections.length"
        >
          删除
        </el-button>
      </template>
      <vxe-column type="checkbox" width="60" fixed="left"/>
      <vxe-column type="seq" width="60"/>
      <vxe-column title="文档名称" field="docName" show-overflow/>
      <vxe-column title="文档内容" field="fileName" show-overflow/>
      <vxe-column title="上传时间" field="uploadTime" show-overflow width="200"/>
      <vxe-column title="上传人" field="uploadUserName" show-overflow/>
      <vxe-column title="所属端" field="belongType" show-overflow width="100">
        <template #default="{ row }">
          <dict-tag :options="home_app_type" :value="row.belongType"/>
        </template>
      </vxe-column>
      <vxe-column title="版本号" field="version" show-overflow/>
      <vxe-column title="状态" field="status" show-overflow>
        <template #default="{ row }">
          <dict-tag :options="sys_normal_disable" :value="row.status"/>
        </template>
      </vxe-column>
      <template #opts="{ row }">
        <TableColOptBtn
            :key="Math.random()"
            :buttons="[
            {
              text: '修改',
              click: () => handleUpdate(row),
              permission: ['ess:manualCfgInfo:edit'],
            },
            {
              text: '删除',
              type: 'danger',
              click: () => handleDelete(row),
              permission: ['ess:manualCfgInfo:remove'],
            },
            {
              text: '查看',
              click: () => handleView(row),
              permission: ['ess:manualCfgInfo:query']
            },
            {
              text: '下载',
              click: () => handleDownload(row),
              permission: ['ess:manualCfgInfo:download']
            },
          ]"
        />
      </template>
    </CustomTable>

    <!-- 新增/修改 -->
    <EditOptManualModal
        ref="editOptManualRef"
        :dict-options="{
				home_app_type: home_app_type,
				sys_normal_disable: sys_normal_disable
			}"
        @change="getList" @refresh="getList"
    />
    <!-- 详情 -->
    <DetailOptManualModal
        ref="detailOptManualRef"
        :dict-options="{
				home_app_type: home_app_type,
				sys_normal_disable: sys_normal_disable
			}"
    />
  </div>
</template>

<script setup name="OptManualTable">
import EditOptManualModal from "./EditOptManualModal.vue"
import DetailOptManualModal from "./DetailOptManualModal.vue"
import {
  getManualCfgListPage,
  delManualCfgInfo,
  downLoadManualCfgInfoUrl
} from "@/api/ess/doc/manualCfgInfo.ts"
import {
  startDownload
} from "@/api/ess/downloadCenter/downloadCenter.ts"
import {handleBlobFile} from "@/utils"

const {proxy} = getCurrentInstance()
const {sys_normal_disable, home_app_type} = proxy.useDict("sys_normal_disable", "home_app_type")

const editOptManualRef = ref(null)
const detailOptManualRef = ref(null)

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

const queryParams = reactive({
  docName: null,
  status: null,
  limit: 10,
  page: 1,
})

/** 查询列表 */
function getList() {
  loading.value = true
  queryParams.uploadTime = queryParams.updateTimeRange?.[0]
  queryParams.uploadEndTime = queryParams.updateTimeRange?.[1]
  getManualCfgListPage(queryParams)
      .then(res => {
        tableData.value = res.object.records
        total.value = res.object.total
      })
      .finally(() => {
        loading.value = false
      })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  editOptManualRef.value.open()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  editOptManualRef.value.open(row)
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row?.id ? [row.id] : selections.value.map((item) => item.id)
  const name = row.docName || selections.value.map(item => item.docName)
  proxy.$modal
      .confirm("是否确认删除文档\"" + name + "\"的数据项？")
      .then(function () {
        return delManualCfgInfo(ids)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {
      })
}

/** 查看按钮操作 */
function handleView(row) {
  detailOptManualRef.value.open(row)
}

/** 下载按钮操作 */
function handleDownload(row) {
  downLoadManualCfgInfoUrl(row.id).then(res => {
    startDownload(res.object).then(resp => {
      handleBlobFile(resp.data, row.docName + ".pdf")
    })
  })
}

getList()
</script>
