<template>
  <div class="sign-flow-table">
    <EssFilesTable
      ref="essFilesTableRef"
      v-model:queryParams="queryParams"
      :tableData="tableData"
      :loading="loading"
      :total="total"
      @query="handleQuery"
    >
      <template #actions>
        <el-button
          v-auths="['todo:mineFile:batchSign', 'file:fileList:BatchSign']"
          type="primary"
          @click="handleBatchSign"
        >
          批量签署
        </el-button>
      </template>
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '详情',
              click: () => handleViewDetails(row),
              permission: ['todo:mineFile:details'],
            },
            {
              text: '签署',
              click: () => handleSign(row),
              permission: ['todo:mineFile:sign'],
            },
            {
              text: '拒签',
              hidden: true,
              click: () => handleRejected(row),
              permission: ['todo:mineFile:rejected'],
            },
          ]"
        />
      </template>
    </EssFilesTable>
  </div>
</template>

<script setup>
import { mySigningPage } from "@/api/ess/todo/sign"

const EssFilesTable = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileList/components/EssFilesTable.vue")
)

import { useEssFilesTable } from "@/views/fileManagement/fileList/components/useEssFilesTable"

const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(true)

const total = ref(0)
const queryParams = ref({
  page: 1,
  limit: 10,
})

const essFilesTableRef = ref(null)

const {
  handleViewDetails,
  handleRejected,
  handleSign,
  handleBatchSign,
} = useEssFilesTable(essFilesTableRef)

/** 查询列表 */
function getList() {
  loading.value = true
  mySigningPage(queryParams.value)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery(data) {
  Object.assign(queryParams.value, data)
  getList()
}

getList()
</script>

<style lang="scss" scoped>
.sign-flow-table {
  :deep(.app-container) {
    padding: 0;
  }
}
</style>
