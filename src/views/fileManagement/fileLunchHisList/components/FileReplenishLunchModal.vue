<template>
  <el-dialog title="补充发起" v-model="dialogVisible" width="661px" :before-close="handleClose" top="5vh">

    <template #header v-if="!isShowTable">
      <div class="header-box">
        <span class="back-pre mt-3px" @click="isShowTable = true">
          <img src="@/assets/images/icon/back-pre.png" alt="">
        </span>
        <span class="title-text">
          补充发起
        </span>
      </div>
    </template>


    <div class="table-box" v-show="isShowTable">
      <div class="mb-10px">
        <el-button type="primary" class="w-80px" @click="downloadError">导出</el-button>
        <el-button type="primary" plain class="is-deep" @click="isShowTable = false">重新上传</el-button>
      </div>

      <CustomTable ref="customTableRef" v-model:page="queryParams.page" v-model:limit="queryParams.limit"
        :has-toolbar="false" :data="tableData" :loading="loading" :total="total" @reload="getList" :opt-width="40"
        noPadding>
        <vxe-column type="seq" width="70" fixed="left" />
        <vxe-column field="flowName" title="文件名称" min-width="180" show-overflow />
        <vxe-column field="flowUsers" title="签署方" min-width="150" show-overflow>
        </vxe-column>
        <vxe-column field="signStartTime" title="发起时间" min-width="100" show-overflow />
        <vxe-column field="errorInfo" title="失败原因" min-width="100" show-overflow />
      </CustomTable>
    </div>

    <div class="upload-box" v-show="!isShowTable">
      <div class="upload-box-header">
        <img src="@/assets/images/icon/wraning.png" alt="" class="w-16px h-16px">
        <p>本次任务失败条数为 <span class="text-danger">{{tableData.length}}</span> 条 ，请保持导入的条数一致！</p>
      </div>


      <div class="upload-box-content">
        <p class="upload-box-content-title">上传签署方信息:</p>
        <UploadBatchFileSign :essTemplateId="essTemplateId" :flowBatchPublishIds="queryParams.flowBatchPublishId" @exportPublishId="getPublishId"/>
      </div>
    </div>

    <template #footer v-if="!isShowTable">
      <!-- 镂空取消 -->
       <el-button  @click="handleClose">取消</el-button>
       <!-- primary确认导入 -->
       <el-button type="primary" @click="handleSubmit">确认导入</el-button>
    </template>

  </el-dialog>
</template>

<script setup>
import UploadBatchFileSign from "./UploadBatchFileSign.vue"

import { essPrefix } from "@/config/constant"
const { proxy } = getCurrentInstance()
import { throttle } from "lodash-es"
import { getFileBatchHisDetailAPI, confirmImportAPI } from "@/api/ess/fileManagement/fileBatchLunch"

const dialogVisible = ref(false)
const isShowTable = ref(true)

const queryParams = reactive({
  flowBatchPublishId: '',
  page: 1,
  limit: 10,
  publishStatus: -1,
})
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 获取列表
async function getList() {
  const res = await getFileBatchHisDetailAPI(queryParams)
  tableData.value = res.object.records.map(i => {
    // 判断userName是否为空，为空就不要join /
    i.flowUsers = i.flowUsers.map(j => j.userName)
    //userName为空的删除
    i.flowUsers = i.flowUsers.filter(j => j)
    return i
  })
}


const essTemplateId = ref('')
function open(row) {
  essTemplateId.value = row.essTemplateId
  isShowTable.value = true
  queryParams.flowBatchPublishId = row.id
  dialogVisible.value = true
  getList()
}

function handleClose() {
  dialogVisible.value = false
}

const downloadError = throttle(function () {
  proxy.download(
    `${essPrefix}/flowInfoBatch/exportFailReason?flowBatchPublishId=${queryParams.flowBatchPublishId}`,
    {},
    `不通过原因.xlsx`
  )
}, 800)


const uploadResult = ref(null);
// 上传文件成功后获取的响应值
const getPublishId = (res) => {
  uploadResult.value = res.object.flowBatchPublishId;
}


//确认导入
const handleSubmit = async () => {
  try {
    const res = await confirmImportAPI(uploadResult.value);
    if(+res.code === 200){
      proxy.$message.success('确认导入成功');
      handleClose();
    }
  } catch (error) {
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.grey-form-box {
  @apply pl-20px pt-10px bg-#f7f8f9 rounded-4px;

  :deep(.el-form-item) {
    margin-bottom: 10px;
  }
}
</style>


<style scoped lang="scss">
.upload-box-header {
  padding: 18px 20px;
  border-radius: 6px 6px 6px 6px;
  display: flex;
  background-color: #F7F8F9;
  width: 100%;
  height: 69px;
  align-items: center;

  img{
    width:32px;
    height: 32px;
    margin-right: 10px;
  }

  p {
    margin: 0;

    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #0C1433;
    text-align: left;
    font-style: normal;
    text-transform: none;


  }

  .text-danger {
    color: #F94E4F;
  }
}

.upload-box-content-title {
  font-size: 14px;
  color: #0C1433;
  line-height: 20px;
  margin-bottom: 10px;
}


.header-box {
  display: flex;
  align-items: center;

  .back-pre {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
    cursor: pointer;
    // padding-top: 3px;
    img{
      width: 22px;
      height: 22px;
    }
  }

  .title-text {
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #0C1433;
    font-style: normal;
    line-height: 22px;
  }

}
</style>
