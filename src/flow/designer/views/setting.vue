<template>
	<div>
		<el-drawer
			v-if="data.settingVisible"
			v-model="data.settingVisible"
			class="flow-header-drawer"
			title="画布设置"
			direction="rtl"
			append-to-body
			:size="600"
			style="z-index: 1001"
			@close="methods.close"
		>
			<el-form
				class="flow-drawer-form"
				:model="data.settingForm"
				label-position="right"
			>
				<el-divider content-position="left">
					画布
				</el-divider>
				<el-form-item label="网格大小">
					<el-slider
						v-model="data.gridConfig.gridSize"
						:min="1"
						:max="20"
						@change="methods.setGridSize"
					/>
				</el-form-item>
				<el-divider content-position="left">
					连线
				</el-divider>
				<!--                <el-form-item label="是否允许节点与任务分离显示">
                    <el-switch
                            v-model="data.globalConfig.isJobSeparated"
                            active-value="1"
                            inactive-value="0"
                            @change="methods.toggleJobSeparated"/>
                </el-form-item>-->
				<el-tooltip content="当节点与任务分离显示时，该设置在专业模式下生效" placement="left">
					<el-form-item label="是否允许任务连线到其他节点">
						<el-switch
							v-model="data.globalConfig.allowJobLink"
							active-value="1"
							inactive-value="0"
							@change="methods.toggleAllowJobLink"
						/>
					</el-form-item>
				</el-tooltip>
				<el-divider content-position="left">
					样式
				</el-divider>
				<el-form-item label="是否切换节点UI图形显示效果">
					<el-switch
						v-model="data.globalConfig.isJobSeparated"
						active-value="1"
						inactive-value="0"
						@change="methods.toggleJobSeparated"
					/>
				</el-form-item>
			</el-form>
		</el-drawer>
	</div>
</template>

<script setup lang="ts" name="FlowSetting">
import { flowConfig } from "../config/flow-config"
import * as nodeConfig from "@/flow/designer/config/node-config"

const $emit = defineEmits(["flowSeparated"])

const data = reactive({
	settingVisible: false,
	settingForm: {},
	globalConfig: flowConfig.globalConfig,
	gridConfig: flowConfig.gridConfig,
})

const methods = {
	open() {
		data.settingVisible = true
	},
	close() {
		data.settingVisible = false
	},
	toggleAllowJobLink(flag) {
		data.globalConfig.allowJobLink = flag
		flowConfig.globalConfig.allowJobLink = flag
	},
	toggleJobSeparated(flag) {
		data.globalConfig.isJobSeparated = flag
		flowConfig.globalConfig.isJobSeparated = flag
		$emit("flowSeparated", flag)
	},
	setGridSize(v) {
		data.gridConfig.gridSize = v
		window._jfOperate.resetGridSize(v)
	},
}

// 暴露变量
defineExpose({
	open: methods.open,
})
</script>

<style lang="scss">
  @use "../../../flow/components/style/flow-drawer.scss" as *;;

  .flow-drawer-form {
    /*属性面板*/
    .el-form-item {
      margin: 0 40px 10px;
    }

    .el-form-item .el-form-item__content {
      margin-left: 150px;
    }
  }
</style>
