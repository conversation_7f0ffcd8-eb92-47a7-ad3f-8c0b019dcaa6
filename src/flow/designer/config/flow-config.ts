import {nodeJobSvgIcons as nodeJobPath} from "@/flow/designer/assets/svges/path";

const clazzAttr = {
  clazzes: [],
}

export const flowAttr = {
  id: null,
  ...clazzAttr,
  isIndependent: '0',
  allowJobLink: '0',
  isJobSeparated: '1',
  isSimpleMode: '1',
  connector: null,
  router: null,
  queryOrder: null,
  updateOrder: null,
  queryMethod: null,
  updateMethod: null,
  orderParams: [],
  flowName: null,
  flowKey: null,
  groupName: null,
  formId: null,
  fromType: '1',
  version: 1,
  status: '-1',
  remark: null,
  isNew: true,
  sort: 1
}

export let flowConfig = {
  // ID类型
  // 1.uuid uuid 2.time_stamp 时间戳 3.sequence 序列 4.time_stamp_and_sequence 时间戳加序列 5.custom 自定义
  idType: ['time_stamp_and_sequence', 6],
  flowData: {
    // null时init
    graph: null,
    nodeList: [],
    linkList: [],
    attrs: flowAttr,
  },
  mobileConfig: {
    mobilePrefix: "appdata",
    mobileCode: "jf320920",
  },
  globalConfig: {
    isHideShortcut: '0',
    allowJobLink: '0',
    isJobSeparated: '1',
    isSimpleMode: '1', // 默认专业模式
    connectors: [{value: "normal", label: "简单"}, {value: "rounded", label: "圆角"}, {
      value: "jumpover",
      label: "跳线"
    }, {value: "smooth", label: "平滑"}],
    routers: [{value: "normal", label: "简单"}, {value: "manhattan", label: "智能正交"}, {
      value: "metro",
      label: "智能地铁线"
    }, {value: "orthogonal", label: "垂直直角"}, {value: "oneSide", label: "受限正交"}],
    rankDirs: [{value: "TB", label: "上下"}, {value: "BT", label: "下上"}, {value: "LR", label: "左右"}, {
      value: "RL",
      label: "右左"
    }],
    defaultAutoLayoutName: "TB",
    defaultConnectorName: "rounded",
    defaultRouterName: "normal",
    maxNodeJobNum: 50
  },
  gridConfig: {
    showGrid: false,
    showGridText: "显示网格",
    showGridIcon: "SwitchButton",
    gridSize: 1
  },
  defaultStyle: {
    containerScale: {
      init: 1
    }
  },
  viewShortcut: {
    paper: {
      codeName: '鼠标滚动或点击画布拖拽或小地图',
      shortcutName: '拖动画布'
    },
    flow: {
      codeName: '鼠标点击画布隐藏节点信息',
      shortcutName: '隐藏节点信息'
    },
    node: {
      codeName: '鼠标放节点上即可查看',
      shortcutName: '查看节点信息'
    },
    link: {
      codeName: '鼠标放连线上即可查看',
      shortcutName: '查看连线信息'
    },
    zoomInTool: {
      codeName: '画布右上角',
      shortcutName: '放大工具'
    },
    zoomOutTool: {
      codeName: '画布右上角',
      shortcutName: '缩小工具'
    }
  },
  shortcut: {
    note: {
      codeName: '图中节点、连线均可拖拽(专业模式)，缩放大小',
      shortcutName: '简约但不简单'
    },
    flow: {
      codeName: '左上角操作栏',
      shortcutName: '切换简单模式与专业模式'
    },
    flow2: {
      codeName: '左上角操作栏',
      shortcutName: '设置流程属性'
    },
    link: {
      codeName: '左上角操作栏',
      shortcutName: '设置连线、路由、布局'
    },
    paper: {
      codeName: '鼠标滚动或点击画布拖拽或小地图',
      shortcutName: '拖动画布'
    },
    node: {
      codeName: '从左侧节点菜单栏拖拽节点(专业模式)',
      shortcutName: '新增加点'
    },
    node1: {
      codeName: '鼠标点击节点拖动右下角灰色小圈',
      shortcutName: '缩放节点大小'
    },
    node2: {
      codeName: '鼠标放节点边缘显示(+)开始拖出连线',
      shortcutName: '节点连线'
    },
    node3: {
      codeName: '鼠标放节点文本上开始拖动节点',
      shortcutName: '拖拽节点'
    },
    node4: {
      codeName: '鼠标放节点上双击或右键菜单(专业模式)设置',
      shortcutName: '设置节点属性'
    },
    node41: {
      codeName: '鼠标放节点的任务项上单击(节点任务不分离时)设置',
      shortcutName: '设置任务属性'
    },
    node5: {
      codeName: '鼠标放节点上右键菜单删除(专业模式)或点击再点X删除',
      shortcutName: '删除节点'
    },
    link2: {
      codeName: '鼠标放连线上点击增加拖拽点拖动(专业模式)',
      shortcutName: '拖拽连线'
    },
    link3: {
      codeName: '鼠标放连线拖拽点上再次点击删除(专业模式)',
      shortcutName: '删除拖拽点'
    },
    link4: {
      codeName: '鼠标放连线上双击(简单模式)或右键菜单(专业模式)设置',
      shortcutName: '设置连线属性'
    },
    link5: {
      codeName: '鼠标放连线上点X删除或右键菜单(专业模式)删除',
      shortcutName: '删除连线'
    },
    zoomInTool: {
      codeName: '画布右上角',
      shortcutName: '放大工具'
    },
    zoomOutTool: {
      codeName: '画布右上角',
      shortcutName: '缩小工具'
    }
  },
  contextMenu: {
    container: {
      menuName: 'flow-menu',
      axis: {
        x: null,
        y: null
      },
      menulists: [
        {
          fnHandler: 'flowInfo',
          icoName: 'iconfont icon-xianshimima',
          btnName: '流程图信息'
        },
        {
          fnHandler: 'paste',
          icoName: 'iconfont icon-fuzhiyemian',
          btnName: '粘贴'
        }
      ]
    },
    node: {
      menuName: 'node-menu',
      axis: {
        x: null,
        y: null
      },
      menulists: [
        {
          fnHandler: 'setNodeAttr',
          icoName: 'iconfont icon-quanjushezhi_o',
          btnName: '设置属性'
        },
        {
          fnHandler: 'setConnectNode',
          icoName: 'iconfont icon-shuxingtu',
          btnName: '连接节点'
        },
        {
          fnHandler: 'copyNode',
          icoName: 'iconfont icon-fuzhiyemian',
          btnName: '复制节点'
        },
        {
          fnHandler: 'deleteNode',
          icoName: 'iconfont icon-yincangmima',
          btnName: '删除节点'
        }
      ]
    },
    nodeConnect: {
      menuName: 'node-connect-menu',
      axis: {
        x: null,
        y: null
      },
      menulists: [
        {
          fnHandler: 'setConnectNode',
          icon: nodeJobPath.linkIcon,
          icoName: 'iconfont icon-tuodong',
          btnName: '连接节点',
          nodeConnect: true,
        },
        {
          fnHandler: 'setSerialNode',
          icon: nodeJobPath.serial,
          icoName: 'iconfont icon-icon-',
          btnName: '串行节点',
          nodeConnect: true,
        },
        {
          fnHandler: 'setParallelNode',
          icon: nodeJobPath.parallel,
          icoName: 'iconfont icon-shuxingtu',
          btnName: '并行节点',
          nodeConnect: true,
        },
        {
          fnHandler: 'setSerialGate',
          icon: nodeJobPath.serialGate,
          icoName: 'iconfont icon-shuaxin',
          btnName: '串行网关',
          nodeConnect: true,
        },
        {
          fnHandler: 'setParallelGate',
          icon: nodeJobPath.parallelGate,
          icoName: 'iconfont icon-tuodong',
          btnName: '并行网关',
          nodeConnect: true,
        }
      ]
    },
    link: {
      menuName: 'link-menu',
      axis: {
        x: null,
        y: null
      },
      menulists: [
        {
          fnHandler: 'setLinkAttr',
          icoName: 'iconfont icon-quanjushezhi_o',
          btnName: '设置属性'
        },
        {
          fnHandler: 'modifySourceNode',
          icoName: 'iconfont icon-step',
          icoStyle: 'font-size: 13px',
          btnName: '修改起点'
        },
        {
          fnHandler: 'modifyTargetNode',
          icoName: 'iconfont icon-radio-off-full',
          icoStyle: 'font-size: 13px',
          btnName: '修改终点'
        },
        {
          fnHandler: 'deleteLink',
          icoName: 'iconfont icon-yincangmima',
          btnName: '删除连线'
        }
      ]
    },
    nodeView: {
      menuName: 'node-menu',
      axis: {
        x: null,
        y: null
      },
      menulists: [
        {
          fnHandler: '',
          icoName: 'iconfont icon-shuaxin',
          btnName: '节点名称: '
        },
        {
          fnHandler: '',
          icoName: 'iconfont icon-xianshimima',
          btnName: '审批开始时间: '
        },
        {
          fnHandler: '',
          icoName: 'iconfont icon-gerenzhongxin',
          btnName: '参与者: '
        },
        {
          fnHandler: '',
          icoName: 'iconfont icon-icon-',
          btnName: '审批人: '
        },
        {
          fnHandler: '',
          icoName: 'iconfont icon-tongzhi1',
          btnName: '审批备注: '
        }
      ]
    },
    linkView: {
      menuName: 'link-menu',
      axis: {
        x: null,
        y: null
      },
      menulists: [
        {
          fnHandler: '',
          icoName: 'iconfont icon-shuxingtu',
          btnName: '连线条件: '
        }
      ]
    }
  }
}
