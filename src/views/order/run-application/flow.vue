<template>
  <div>
    <div class="layout-padding-auto layout-padding-view">
      <form-render
        v-if="!validateNull(data.currFlowFormClone)"
        ref="formCreateRef"
        :curr-flow-form="data.currFlowFormClone"
        :init-form-perm-print="initFormPermPrint"
      />
      <footer v-if="data.submitBtn" class="center mb-20px">
        <el-button
          w-100px
          v-if="data.currFlowForm.printInfo"
          type="primary"
          @click="printForm"
        >
          打印
        </el-button>
        <el-button w-100px type="primary" :disabled="loading" @click="submitForm">
          提交
        </el-button>
      </footer>
      <footer v-else class="el-dialog__footer">
        <span class="dialog-footer">
          <el-button v-if="data.currFlowForm.printInfo" type="primary" @click="printForm">
            打印
          </el-button>
        </span>
      </footer>
    </div>

    <!-- 打印表单 -->
    <el-dialog
      v-model="data.showTinymceView"
      top="20px"
      width="700px"
      :title="data.tinymceTitle"
      append-to-body
      @close="closePrint"
    >
      <tinymce-view v-if="data.showTinymceView" :curr-flow-form="data.currFlowForm" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="RunApplicationForm">
import * as runApplication from "@/api/order/run-application"
import * as orderVue from "@/api/order/order-key-vue"
import { paramsFilter, parseWithFunctions } from "@/flow"
import { handleDesignFormPerm, handleFormPrint } from "@/flow/utils/form-perm"
import { validateNull } from "@/utils/validate"
import { deepClone } from "@/utils/index"
import { initJobDataByApp } from "@/flow/support/extend"

const { proxy } = getCurrentInstance()

const emits = defineEmits(["handleJob"])
const FormRender = defineAsyncComponent(
  () => import("@/flow/components/form-create/render.vue")
)
const formCreateRef = ref(null)
// 引入组件
const TinymceView = defineAsyncComponent(
  () => import("@/flow/components/tinymce/TinymceView.vue")
)

const loading = ref(false)
const props = defineProps({
  currJob: {
    type: Object,
    default: {},
  },
  showMsg: {
    type: Boolean,
    default: true,
  },
})
const data = reactive({
  // 兼容app端监听printInfo
  currFlowForm: {
    type: Object,
    default: {},
  },
  // 含可选表单formInfo
  currFlowFormClone: {},
  elTab: {},
  initFormData: {},
  showTinymceView: false,
  tinymceTitle: null,
  submitBtn: true,
})

const methods = {
  disableForm() {
    nextTick(() => {
      // 防止formCreateRef null
      setTimeout(() => {
        formCreateRef.value.design.fApi.disabled(true)
      }, 100)
    })
    data.submitBtn = false
  },
  enableForm() {
    nextTick(() => {
      setTimeout(() => {
        formCreateRef.value.design.fApi.disabled(false)
      }, 100)
    })
    data.submitBtn = true
  },
}

const $route = useRoute()

async function initJobData() {
  // 兼容移动端
  await initJobDataByApp($route, props)
  // 判断是否存在该表单
  let currElTab = orderVue.currElTabIsExist(props.currJob)
  if (!currElTab) return
  data.elTab = currElTab
  data.submitBtn =
    typeof props.currJob.showSubmitBtn === "boolean" ? props.currJob.showSubmitBtn : true
  handleGetObj(props.currJob.orderId)
}

function handleGetObj(id) {
  runApplication.getObj(id).then(async (resp) => {
    let form = resp.object ? resp.object : {}
    data.currFlowForm = form
    data.currFlowForm.runJobId = props.currJob.id
    rowEditInitData(form)
  })
}

function rowEditInitData(form: Object) {
  let formInfoStr = form.formInfo
  // 判断是否为可选表单
  if (data.elTab.id !== form.formId) {
    formInfoStr = validateNull(data.elTab.formInfo) ? form.formInfo : data.elTab.formInfo
  }
  data.currFlowFormClone = deepClone(form)
  data.currFlowFormClone.formInfo = formInfoStr
  // 记录原始值
  let formData = parseWithFunctions(form.formData)
  Object.assign(data.initFormData, formData)
}

async function initFormPermPrint(cloneFormInfo) {
  let elTab = data.elTab
  let form = data.currFlowForm
  // 优先采用elTab配置的模板
  let res = await handleDesignFormPerm(props, cloneFormInfo, elTab, elTab.type, elTab.id)
  if (validateNull(res.columns) && elTab.id !== form.formId) {
    res = await handleDesignFormPerm(props, cloneFormInfo, elTab, form.type, form.formId)
  }
  // 判断是否仅查看
  await orderVue.currElTabIsView(
    methods,
    props.currJob,
    submitForm,
    res.callback,
    res.widgetList
  )
  await handleFormPrint(form, elTab.type, elTab.id, "1")
  if (!form.printInfo && elTab.id !== form.formId) {
    await handleFormPrint(form, form.type, form.formId, "1")
  }
  return elTab
}

function printForm() {
  data.currFlowForm.formData = formCreateRef.value.design.formData
  data.currFlowForm.modelRefList = formCreateRef.value.design.fApi.children
  data.currFlowForm.rule = formCreateRef.value.design.rule
  data.tinymceTitle = data.currFlowForm.formName
  data.showTinymceView = true
}

function closePrint(isSave) {
  delete data.currFlowForm.modelRefList
  delete data.currFlowForm.rule
  if (isSave) {
    delete data.currFlowForm.printInfo
  }
}

async function submitForm() {
  try {
    loading.value = true
    let state = await formCreateRef.value.design.fApi.validate()
    let formData = formCreateRef.value.design.formData
    console.log(state, validateNull(formData))
    if (!state || validateNull(formData)) {
      proxy.$modal.msgWarning("请输入表单信息")
      return
    }
    let formJson = saveInitData(formData)
    await runApplication.putObj(formJson)
    orderVue.currElTabIsSave(props.currJob, true, emits)
    props.showMsg && proxy.$modal.msgSuccess(formJson.id ? "修改成功" : "新增成功")
    return true
  } catch (err) {
    console.log(err)
    return false
  } finally {
    loading.value = false
  }
}

function saveInitData(form) {
  closePrint(true)
  form = paramsFilter(form)
  data.currFlowForm.formData = validateNull(form) ? null : form
  let formJson = deepClone(data.currFlowForm)
  if (!validateNull(form)) {
    formJson.formData = Object.assign({}, data.initFormData, formJson.formData)
  }
  formJson.formData = JSON.stringify(formJson.formData)
  return formJson
}

// 监听双向绑定
watch(
  () => props.currJob.id,
  async () => {
    await initJobData()
  }
)

onMounted(async () => {
  await initJobData()
})
</script>
