<template>
  <div class="has-iframe-page">
    <div class="app-container" v-show="!showSignPage && !showPreviewPage">
      <EssFilesQueryForm
        :showSearch="showSearch"
        @query="handleQuery"
        :dictOptions="{
          flow_status,
          ess_yes_no,
        }"
        ref="essFilesQueryFormRef"
      />
      <CustomTable
        v-model:show-search="showSearch"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :data="tableData"
        custom
        :hasToolbar="false"
        :loading="loading"
        :total="total"
        @reload="getList"
        :opt-width="optWidth"
        ref="customTableRef"
      >
        <template #actions v-if="slotActions">
          <slot name="actions" />
        </template>
        <template #toolbar v-if="slotToolbar">
          <slot name="toolbar" />
        </template>
        <!-- <vxe-toolbar ref="toolbarRef" custom></vxe-toolbar> -->
        <vxe-column type="checkbox" width="60" fixed="left" />
        <vxe-column type="seq" width="70" fixed="left" />
        <vxe-column field="flowName" title="文件名称" fixed="left" min-width="200" />
        <vxe-column field="essFlowId" title="文件ID" min-width="200" show-overflow />
        <vxe-column field="startUserName" title="发起方" min-width="140" show-overflow />
        <vxe-column
          field="flowUserRefs"
          title="所有签署方"
          min-width="150"
          :formatter="formatterFlowUserRefs"
          show-overflow
        />
        <vxe-column
          field="businessLineName"
          title="所属业务线"
          min-width="100"
          show-overflow
        />
        <vxe-column field="sourceName" title="签署来源" min-width="100" show-overflow />
        <vxe-column field="flowStatusDesc" title="文件状态" min-width="140">
          <template #default="{ row }">
            <dict-tag :options="flow_status" :value="row.flowStatus">
              <template v-if="+row.flowStatus === 33" #suffix>
                ({{ row.signedTotal }}/{{ row.signTotal }})
              </template>
            </dict-tag>
          </template>
        </vxe-column>
        <vxe-column field="flowNodeName" title="审批节点" min-width="120" show-overflow />
        <vxe-column
          field="createTime"
          title="流程发起时间"
          min-width="150"
          :formatter="formatterTime"
        />
        <vxe-column
          field="signStartTime"
          title="签署发起时间"
          min-width="150"
          :formatter="formatterTime"
        />
        <vxe-column
          field="signLimitTime"
          title="签署截止时间"
          min-width="150"
          :formatter="formatterTime"
        />
        <vxe-column field="remainTime" title="剩余处理时间" min-width="150" />
        <vxe-column
          field="signEndTime"
          title="签署完成时间"
          min-width="150"
          :formatter="formatterTime"
        />
        <template #opts="{ row }">
          <slot name="opts" :row="row" />
        </template>
      </CustomTable>
    </div>

    <!-- 撤销 -->
    <RevocationFormModal ref="revocationFormRef" @refresh="getList" />

    <!-- 签署 -->
    <SignPage ref="signPageRef" v-model="showSignPage" @back="getList" />

    <!-- 文件详情 -->
    <FilePreviewPage ref="filePreviewRef" v-model="showPreviewPage" />
  </div>
</template>

<script setup>
import { urgingFlow } from "@/api/ess/fileManagement/file"
import { recallReset } from "@/api/jsonflow/run-flow"

const EssFilesQueryForm = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileList/components/EssFilesQueryForm.vue")
)
const RevocationFormModal = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileList/components/RevocationFormModal.vue")
)

const SignPage = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileList/components/SignPage.vue")
)
const FilePreviewPage = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileList/components/FilePreviewPage.vue")
)
const { proxy } = getCurrentInstance()

const slotActions = computed(() => !!useSlots().actions)
const slotToolbar = computed(() => !!useSlots().toolbar)

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  total: {
    type: Number,
    default: 0,
  },
  tableData: {
    type: Array,
    default: () => [],
  },
  optWidth: {
    type: Number,
    default: 200,
  },
})

const { ess_yes_no, flow_status } = proxy.useDict("ess_yes_no", "flow_status")

const queryParams = defineModel("queryParams", {
  default: {
    flowName: null,
    startDeptId: null,
    signDeptId: null,
    flowTimeRange: [],
    signQryTimeRange: [], //	签署发起时间
    signLimitQryTimeRange: [], // 签署截止时间
    signEndQryEndTimeRange: [], // 签署完成时间
    limit: 10,
    page: 1,
  },
})
const showSearch = ref(true)
const showSignPage = ref(false)
const showPreviewPage = ref(false)

const revocationFormRef = ref(null)

const $emit = defineEmits(["query"])

const customTableRef = ref()

// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

function queryTimeRange() {
  const timeRanges = {
    signEndQry: "signEndQryEndTimeRange", // 签署完成时间
    signLimitQry: "signLimitQryTimeRange", // 签署截止时间
    signQry: "signQryTimeRange", // 签署发起时间
    flowQry: "flowTimeRange", // 流程发起时间
  }

  Object.keys(timeRanges).forEach((key) => {
    const rangeKey = timeRanges[key]
    queryParams.value[`${key}StartTime`] = queryParams.value[rangeKey]?.[0] || null
    queryParams.value[`${key}EndTime`] = queryParams.value[rangeKey]?.[1] || null
  })
}
/** 查询列表 */
function handleQuery(data = {}) {
  Object.assign(queryParams.value, data)
  queryParams.value.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.$refs.essFilesQueryFormRef.resetQuery()
}

/** 查询列表 */
function getList() {
  queryTimeRange()
  $emit("query", queryParams.value)
}

/** 详情 */
function handleViewDetails(row) {
  showPreviewPage.value = true
  proxy.$refs.filePreviewRef.open(row)
}

/** 催办 */
async function handlePress(row) {
  await proxy.$modal.confirm("每个文件当天只能催办一次，是否确认催办？")
  try {
    const res = await urgingFlow(row.essFlowId)
    if (+res.code === 200) proxy.$modal.msgSuccess("催办成功")
  } catch (e) {}
}

/** 签署 */
function handleSign(row) {
  showSignPage.value = true
  proxy.$refs.signPageRef.open(row)
}

/** 批量签署 */
function handleBatchSign() {
  if (!selections.value.length) {
    proxy.$modal.msgWarning("请选择要签署的文件")
    return
  }
  showSignPage.value = true
  proxy.$refs.signPageRef.open(selections.value, true)
}

/** 撤销 */
function handleRevocation(row) {
  revocationFormRef.value.open(row, "撤销")
}
/** TODO: 拒签 */
function handleRejected(row) {}

/** 流程撤销 */
function handleConclude(row) {
  const { instId, flowKey, jsonFlowStatus } = row
  proxy.$modal
    .confirm(`确定撤销该“${row.flowName}”申请？`)
    .then(() => {
      const reqData = {
        id: instId,
        status: jsonFlowStatus,
        flowKey,
      }
      return recallReset(reqData)
    })
    .then((res) => {
      if (+res.code === 200) {
        proxy.$modal.alertCenter("撤销成功")
        getList()
      }
    })
}

// 所有签署方
function formatterFlowUserRefs({ cellValue }) {
  return cellValue?.map((item) => item.userName).join(",") || "-"
}
function formatterTime({ cellValue }) {
  return cellValue ? proxy.parseTime(cellValue, "{y}-{m}-{d} {h}:{i}") : "-"
}

defineExpose({
  getList,
  resetQuery,
  handlePress,
  handleConclude,
  handleRevocation,
  handleSign,
  handleBatchSign,
  handleViewDetails,
  handleRejected,
  selections,
})
</script>
