<template>
	<div style="width: 100%;">
		<el-input
			v-model="data.modelValue"
			:disabled="props.disabled"
			clearable
			@blur="changeModelValue"
		/>
	</div>
</template>

<script setup lang="ts" name="UserRolePickerIndex">
import { verifyIdCard } from "@/utils/toolsValidate"
const { proxy } = getCurrentInstance()
const props = defineProps({
	modelValue: {
		type: String,
		default: null,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
})

const emits = defineEmits(["update:modelValue"])

const $message = proxy.$modal
const data = reactive({
	modelValue: null,
})

const changeModelValue = () => {
	if (data.modelValue) {
		let boolean = verifyIdCard(data.modelValue)
		if (!boolean) {
			data.modelValue = null
			proxy.$modal.msgError("请输入正确的身份证号")
		}
		emits("update:modelValue", data.modelValue)
	}
	else {
		emits("update:modelValue", null)
	}
}

onMounted(() => {
	data.modelValue = props.modelValue
})
</script>
