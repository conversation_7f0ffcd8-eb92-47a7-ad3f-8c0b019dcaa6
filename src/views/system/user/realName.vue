<template>
  <div class="real-container">
    <Navbar disabled />
    <div class="real-main">
      <img src="@/assets/images/unregistered.png" alt="" />
      <div class="text-18px font-600">嗨，{{ nickName }}～</div>
      <div class="mt-10px">为确保您能正常使用平台，请先进行实名认证</div>
      <el-button
        class="w-160px min-h-40px! mt-50px"
        type="primary"
        @click="getRealName"
        round
        :loading="loading"
      >
        实名链接推送
      </el-button>
      <br />
      <el-button
        class="w-160px min-h-40px! is-trans"
        type="primary"
        round
        plain
        v-if="isSend"
        @click="$router.replace('/login')"
      >
        我已实名, 重新登录
      </el-button>
    </div>
  </div>
</template>

<script setup>
import Navbar from "@/layout/components/Navbar"
import { reminderUserRealAuth, selectZsRoleDeptInfos } from '@/api/system/user';
import useUserStore from "@/store/modules/user"
import Cookies from 'js-cookie';

const { proxy } = getCurrentInstance()
const loading = ref(false)
const isSend = ref(false)
const zsUserInfo = useUserStore().zsUserInfo
const nickName = ref(zsUserInfo.nickName)
async function getRealName() {
  try {
    loading.value = true
    const res = await reminderUserRealAuth(zsUserInfo.userId)
    if (+res.code === 200) {
      proxy.$modal.msgSuccess(`已成功推送实名链接，请注意查收！`)
    }
  } finally {
    loading.value = false
    isSend.value = true
  }
}

onMounted(async () => {
  const encodedNickName = Cookies.get("nickName");
  if (!encodedNickName.value) {
    nickName.value = decodeURIComponent(encodedNickName);
  }
})

</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.real-main {
  position: relative;
  background: url(@/assets/images/unregistered-bg.png) no-repeat;
  background-size: cover;
  height: calc(100vh - 20px);
  top: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
