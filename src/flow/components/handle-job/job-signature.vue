<template>
  <div>
    <el-form
      ref="dataFormRef"
      v-loading="loading"
      :model="form"
      :rules="dataRules"
      label-width="140px"
      :disabled="operType === 'view'"
    >
      <el-form-item label="在当前节点加签" prop="runNodeId">
        <el-select
          v-model="form.runNodeId"
          placeholder="默认当前节点"
          clearable
          filterable
          disabled
        >
          <el-option
            v-for="(item, index) in cascadeDic.runNodeId"
            :key="index"
            :label="item.nodeName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="任务名称" prop="jobName">
        <el-input v-model="form.jobName" placeholder="请输入任务名称" />
      </el-form-item>

      <!-- <el-form-item label="加签参与者类型" prop="jobType">
        <el-radio-group v-model="form.jobType" @change="handleRoleType">
          <el-radio
            v-for="(item, index) in DIC_PROP.JOB_USER_TYPE.filter((i) => +i.value !== 3)"
            :key="index"
            :value="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item
        label=" 指定加签人员/角色"
        label-position="top"
        label-width="140px"
        class="is-required"
      >
        <UserTreeTransfer
          ref="userTreeTransferRef"
          v-model:users="userList"
          v-model:ranges="roleList"
          isOnlyOne
          hiddenDeptCheckbox
          :selectTypes="['user', 'role']"
        />
      </el-form-item>

      <!-- <el-form-item
        label="指定加签人员"
        prop="roleId"
        v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[0].value"
      >
        <UserSelect v-model="form.roleId" />
      </el-form-item>

      <el-form-item
        label="指定加签角色"
        prop="roleId"
        v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[1].value"
      >
        <el-select
          v-model="form.roleId"
          placeholder="请选择加签角色"
          clearable
          filterable
        >
          <el-option
            v-for="(item, index) in dicData.roles"
            :key="index"
            :label="item.roleName"
            :value="item.roleId"
          />
        </el-select>
      </el-form-item> -->

      <!-- <el-col v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value" :span="12" class="mb-1">
					<el-form-item label="指定加签岗位" prop="roleId">
						<el-select
							v-model="form.roleId"
							placeholder="请选择加签岗位"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in dicData.posts"
								:key="index"
								:label="item.postName"
								:value="item.postId"
							/>
						</el-select>
					</el-form-item>
				</el-col> -->

      <!-- <el-form-item
        label="指定加签部门"
        prop="roleId"
        v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value"
      >
        <DeptPicker v-model="form.roleId" placeholder="请选择加签部门" />
      </el-form-item> -->
      <el-form-item  prop="comment" label="审批意见" label-position="top">
        <el-input v-model="form.comment" :autosize="{ minRows: 4, maxRows: 6 }" type="textarea" :maxlength="500" placeholder="请输入审批意见"/>
      </el-form-item>
    </el-form>
    <footer class="el-dialog__footer">
      <span class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="handleUpdate"
          >确定</el-button
        >
      </span>
    </footer>
  </div>
</template>

<script setup lang="ts" name="JobSignature">
import { onCascadeChange, onLoadDicUrl } from "../convert-name/convert"
import { getCurrentInstance, ref, reactive, onMounted, watch } from "vue"
import { handleChangeJobType } from "../../index"

const UserTreeTransfer = defineAsyncComponent(
  () => import("@/views/components/UserTreeTransfer/index.vue")
)
const { proxy } = getCurrentInstance()
const $emit = defineEmits(["onJobSignature"])

// 定义变量内容
const dataFormRef = ref()
const loading = ref(false)
const operType = ref(false)

// 定义字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "roles" })
const userList = ref([])
const roleList = ref([])
const onCascade = onCascadeChange(cascadeDic, {
  key: "flowInstId",
  cascades: ["runNodeId"],
})
onMounted(() => {
  onLoad(dicData)
})

// 提交表单数据
const form = reactive({
  runNodeId: "",
  jobType: "",
  roleId: null,
  jobName: "",
  nextUserRole: null,
})

// 定义校验规则
const dataRules = ref({
  jobType: [{ required: true, message: "加签参与者类型不能为空", trigger: "blur" }],
  roleId: [{ required: true, message: "加签参与者不能为空", trigger: "blur" }],
})

const props = defineProps({
  currJob: {
    type: Object,
    default: () => ({}),
  },
})

function initJobData() {
  if (props.currJob) {
    Object.assign(form, props.currJob)
    onCascade(form)
  }
}

function handleRoleType() {
  handleChangeJobType(dicData, form)
}

async function handleUpdate() {
  const valid = await dataFormRef.value.validate().catch(() => {})
  if (!valid) return false
  if (!userList.value.length && !roleList.value.length) {
    proxy.$modal.msgWarning("请指定加签人员/角色")
    return
  }

  form.nextUserRole = {
    jobType: userList.value.length ? 0 : 1,
    // roleId: form.roleId,
    roleId: userList.value?.[0]?.id || roleList.value?.[0]?.id,
    jobName: form.jobName,
  }

  props.currJob.nextUserRole = form.nextUserRole

  loading.value = true
  try {
    $emit("onJobSignature", props.currJob)
    setTimeout(() => {
      // 异步异常
      loading.value = false
    }, 3000)
  } catch (e) {
    loading.value = false
  }
}

// 监听双向绑定
watch(
  () => props.currJob?.id,
  (val) => {
    initJobData()
  }
)

onMounted(() => {
  initJobData()
})
</script>

<style lang="scss" scoped>


</style>
