import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询印章授权信息列表 带分页
export function getSealAuthListPage(data: Object) {
	return request({
		url: essPrefix + "/sealAuth/listPage",
		method: "post",
		data,
	})
}
// 添加授权
export function addAuthUserSeal(data: Object) {
	return request({
		url: essPrefix + "/sealAuth/add",
		method: "post",
		data,
    headers: {
      hiddenError: true
    }
	})
}
// 取消授权
export function removeAuthUserSeal(data: Object) {
	return request({
		url: essPrefix + "/sealAuth/remove",
		method: "post",
		data,
	})
}

// 查询印章授权操作日志列表 带分页
export function getSealAuthLog(data: Object) {
	return request({
		url: essPrefix + "/seal/authLog/listPage",
		method: "post",
		data,
	})
}
