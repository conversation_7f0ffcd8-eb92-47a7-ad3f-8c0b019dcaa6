<template>
  <div>
    <el-form
      ref="dataFormRef"
      v-loading="loading"
      :model="props.currFlowForm"
      :rules="dataRules"
      label-width="150px"
      label-position="left"
      :disabled="operType === 'view'"
    >
      <el-row :gutter="24">
        <el-col :span="12" class="mb-1" :offset="6">
          <el-form-item label="表单名称" prop="formName">
            <el-input
              v-model="props.currFlowForm.formName"
              placeholder="请输入表单名称"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1" :offset="6">
          <el-form-item label="图标" prop="icon">
            <IconSelector
              v-model="props.currFlowForm.icon"
              placeholder="请选择图标"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1" :offset="6">
          <el-form-item label="分组名称" prop="groupName">
            <el-select
              v-model="props.currFlowForm.groupName"
              style="width: 83% !important;"
              placeholder="请输入分组名称"
              clearable
              filterable
              allow-create
              default-first-option
            >
              <el-option
                v-for="(item, index) in dicData.groupName"
                :key="index"
                :label="item.groupName"
                :value="item.groupName"
              />
            </el-select>

            <el-button
              type="primary"
              size="small"
              round
              style="margin-left: 10px;"
              @click="handleAddGroupName"
            >
              新增分组
            </el-button>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1" :offset="6">
          <el-form-item label="表单类型" prop="type">
            <el-select
              v-model="props.currFlowForm.type"
              placeholder="请选择表单类型"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in DIC_PROP.FORM_TYPE"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col
          v-if="props.currFlowForm.type === DIC_PROP.FORM_TYPE[1].value"
          :span="12"
          class="mb-1"
          :offset="6"
        >
          <el-form-item label="PC端路径" prop="path">
            <el-tooltip
              content="当表单类型为系统表单时，可输入本地Vue组件路径，或者外部系统http或https开头的页面路径"
              placement="top"
            >
              <el-input
                v-model="props.currFlowForm.path"
                placeholder="请输入PC端路径"
                clearable
              />
            </el-tooltip>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1" :offset="6">
          <el-form-item label="表单备注" prop="remark">
            <el-input
              v-model="props.currFlowForm.remark"
              type="textarea"
              placeholder="请输入表单备注"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1" :offset="6">
          <el-form-item label="排序值" prop="sort">
            <el-input-number
              v-model="props.currFlowForm.sort"
              :min="1"
              :max="1000"
              placeholder="请输入排序值"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1" :offset="6">
          <el-form-item label="版本" prop="version">
            <el-input-number
              v-model="props.currFlowForm.version"
              :min="1"
              :max="1000"
              placeholder="请输入版本"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1" :offset="6">
          <el-tooltip
            content="在审批时默认展示的第1个页面。当存在多个默认展示页面时，优先展示排序值最小的"
            placement="top"
          >
            <el-form-item label="默认展示" prop="isActive">
              <el-radio-group v-model="props.currFlowForm.isActive">
                <el-radio
                  v-for="(item, index) in DIC_PROP.YES_OR_NO"
                  :key="index"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-tooltip>
        </el-col>

        <el-col :span="12" class="mb-1" :offset="6">
          <el-tooltip
            content="在审批时不会显示审批按钮，在页面点提交按钮会自动流转到下一步"
            placement="top"
          >
            <el-form-item label="提交时自动审批" prop="isAutoAudit">
              <el-radio-group v-model="props.currFlowForm.isAutoAudit">
                <el-radio
                  v-for="(item, index) in DIC_PROP.YES_OR_NO"
                  :key="index"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-tooltip>
        </el-col>

        <el-col :span="12" class="mb-1" :offset="6">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="props.currFlowForm.status">
              <el-radio
                v-for="(item, index) in DIC_PROP.TEMP_STATUS"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="TabsOptionForm">
import { useMessage, useMessageBox } from '@/hooks/message'
import { getObj, addObj, putObj } from '@/api/order/flow-application'
import { deepClone } from '@/utils/index'
import { notifyLeft, stringifyWithFunctions } from '@/flow'
import { useI18n } from 'vue-i18n'
import { onLoadDicUrl, onUpdateDicData } from '@/flow/components/convert-name/convert'
import { PROP_CONST } from '../../support/prop-const'
import { DIC_PROP } from '../../support/dict-prop'
import { validateFormTypeSave } from '@/api/order/order-key-vue'

const { proxy } = getCurrentInstance()
const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null
  }
})
// 引入组件
const IconSelector = defineAsyncComponent(() => import('@/components/IconSelector/index.vue'))

// 定义变量内容
const dataFormRef = ref()
const loading = ref(false)
const operType = ref(false)
// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: 'groupName' })
const onUpdate = onUpdateDicData({ key: 'groupName' })
onMounted(() => {
  onLoad(dicData)
  let isNoGroupName = !props.currFlowForm.groupName && dicData.groupName.length > 0
  if (isNoGroupName) {
    props.currFlowForm.groupName = dicData.groupName[0].groupName
  }
})

// 定义校验规则
const dataRules = ref({
  icon: [{ required: true, message: '图标不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '表单类型不能为空', trigger: 'blur' }],
  formName: [{ required: true, message: '表单名称不能为空', trigger: 'blur' }],
  groupName: [{ required: true, message: '分组名称不能为空', trigger: 'blur' }],
  prop: [{ required: true, message: 'tab标识不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序值不能为空', trigger: 'blur' }],
  isActive: [{ required: true, message: '默认展示不能为空', trigger: 'blur' }],
  isAutoAudit: [{ required: true, message: '提交时自动审批不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  version: [{ required: true, message: '版本不能为空', trigger: 'blur' }]
})

function handleAddGroupName() {
  useMessageBox()
    .prompt('请输入新的分组名称')
    .then(({ value }) => {
      props.currFlowForm.groupName = value
      onUpdate(dicData, props.currFlowForm)
    })
}

async function validatePreSave() {
  const valid = await dataFormRef.value.validate().catch(() => {})
  if (!valid) {
    notifyLeft('请先完善表单设置内容', 'warning')
    return false
  }
  let isSys = props.currFlowForm.type === DIC_PROP.FORM_TYPE[1].value
  if (isSys && !props.currFlowForm.path) {
    notifyLeft('当前表单类型为系统表单，【PC端路径】不能为空', 'warning')
    return false
  }
  return true
}

function validateOtherSave() {
  let isDesign = props.currFlowForm.type !== DIC_PROP.FORM_TYPE[1].value
  if (isDesign && !props.currFlowForm.formInfo) {
    notifyLeft('当前表单类型为设计表单，请先完善第二项设计的表单内容', 'warning')
    return false
  }
  return true
}

const handleSubmit = async (callback, status) => {
  if (status) {
    props.currFlowForm.status = status
  }
  let preSave = await validatePreSave()
  if (!preSave) {
    return
  }
  if (!validateOtherSave()) {
    return
  }
  let formJson = deepClone(props.currFlowForm)
  formJson.formInfo = stringifyWithFunctions(formJson.formInfo)

  validateFormTypeSave(formJson)
  try {
    loading.value = true
    await onUpdate(dicData, props.currFlowForm)
    await addObj(formJson)
    props.currFlowForm.tabsSetting = true
    notifyLeft('当前表单设置保存成功')
    if (callback) {
      callback()
    }
  } catch (err) {
    proxy.$modal.msgError(err)
  } finally {
    loading.value = false
  }
}

// 暴露变量
defineExpose({
  handleSubmit,
  validatePreSave,
  validateOtherSave
})
</script>
