<template>
	<div class="app-container">
		<el-form
			v-show="showSearch"
			ref="queryRef"
			:model="queryParams"
			:inline="true"
			class="white-form-box"
			label-width="110px"
		>
			<el-form-item label="业务线名称" prop="businessLineName">
				<el-input
					v-model="queryParams.businessLineName"
					placeholder="请输入业务线名称"
					clearable
					maxlength="50"
					@keyup.enter="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="归属" prop="ascription">
				<dict-select
					v-model="queryParams.ascription"
					:options="business_ascription"
					value-type="string"
				/>
			</el-form-item>
			<el-form-item label="是否允许解除" prop="relieveFlag">
				<dict-select
					v-model="queryParams.relieveFlag"
					:options="ess_yes_no"
				/>
			</el-form-item>
			<el-form-item label="解除是否需要走审批流程" prop="flowFlag">
				<dict-select v-model="queryParams.flowFlag" :options="ess_yes_no" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="Search" @click="handleQuery">
					搜索
				</el-button>
				<el-button icon="Refresh" @click="resetQuery">
					重置
				</el-button>
			</el-form-item>
		</el-form>

		<div class="table-handle-box">
			<div>
				<el-button
					v-auths="['baseSet:businessLine:add']"
					type="primary"
					icon="Plus"
					@click="handleAdd"
				>
					新增
				</el-button>
				<el-button
					v-auths="['baseSet:businessLine:remove']"
					type="danger"
					icon="Delete"
					:disabled="!selections.length"
					@click="handleDelete"
				>
					删除
				</el-button>
			</div>
			<right-toolbar
				v-model:show-search="showSearch"
				@query-table="getList"
			/>
		</div>

		<div class="white-body-box">
			<el-table
				v-loading="loading"
				class="table-empty"
				:data="tableData"
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="55" align="center" />
				<el-table-column type="index" label="序号" min-width="60" />
				<el-table-column
					label="业务线名称"
					prop="businessLineName"
					show-overflow-tooltip
				/>
				<el-table-column label="归属" prop="ascription">
					<template #default="scope">
						{{ selectDictLabel(business_ascription, scope.row.ascription) }}
					</template>
				</el-table-column>
				<el-table-column label="是否允许解除" prop="relieveFlag">
					<template #default="scope">
						<dict-tag :options="ess_yes_no" :value="scope.row.relieveFlag" />
					</template>
				</el-table-column>
				<el-table-column label="解除是否需要走审批流程" prop="flowFlag">
					<template #default="scope">
						<dict-tag :options="ess_yes_no" :value="scope.row.flowFlag" />
					</template>
				</el-table-column>
				<el-table-column label="操作" min-width="120">
					<template #default="scope">
						<el-button
							v-auths="['baseSet:businessLine:edit']"
							link
							type="primary"
							@click="handleUpdate(scope.row)"
						>
							修改
						</el-button>
						<el-button
							v-auths="['baseSet:businessLine:remove']"
							link
							type="danger"
							@click="handleDelete(scope.row)"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<pagination
			v-show="total > 0"
			v-model:page="queryParams.page"
			v-model:limit="queryParams.limit"
			:total="total"
			@pagination="getList"
		/>

		<!-- 新增修改业务线弹窗 -->
		<EditBusinessLineModal
			ref="businessLineModalRef"
			:ess_yes_no="ess_yes_no"
			:business_ascription="business_ascription"
			@refresh="handleQuery"
			@change="getList"
		/>
	</div>
</template>

<script setup name="BusinessLine">
import { businessLinePage, delBusinessLine } from "@/api/ess/baseSet/businessLine"
import EditBusinessLineModal from "./components/EditBusinessLineModal.vue"
const { proxy } = getCurrentInstance()
const { ess_yes_no, business_ascription } = proxy.useDict(
	"ess_yes_no",
	"business_ascription",
)

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const selections = ref([])
const total = ref(0)
const businessLineModalRef = ref(null)
const queryParams = reactive({
	businessLineName: null,
	flowFlag: null,
	relieveFlag: null,
	limit: 10,
	page: 1,
})

/** 查询列表 */
function getList() {
	loading.value = true
	businessLinePage(queryParams)
		.then(res => {
			tableData.value = res.object.records
			total.value = res.object.total
		})
		.finally(() => {
			loading.value = false
		})
}

/** 搜索按钮操作 */
function handleQuery() {
	queryParams.page = 1
	getList()
}

/** 重置按钮操作 */
function resetQuery() {
	proxy.resetForm("queryRef")
	handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
	ids.value = selection.map(item => item.id)
	selections.value = selection
}

/** 新增按钮操作 */
function handleAdd() {
	businessLineModalRef.value.open()
}

/** 修改按钮操作 */
function handleUpdate(row) {
	businessLineModalRef.value.open(row)
}
/** 删除按钮操作 */
function handleDelete(row) {
	const id = row.id || ids.value
	const name
    = row.businessLineName || selections.value.map(item => item.businessLineName)
	proxy.$modal
		.confirm("是否确认删除业务线\"" + name + "\"的数据项？")
		.then(function() {
			return delBusinessLine(id)
		})
		.then(() => {
			getList()
			proxy.$modal.msgSuccess("删除成功")
		})
		.catch(() => {})
}

getList()
</script>
