export default {
	jfcomment: {
		index: "#",
		importcommentTip: "import Comment",
		id: "id",
		flowKey: "flowKey",
		flowNodeId: "flowNodeId",
		nodeJobId: "nodeJobId",
		userId: "userId",
		subFlowStatus: "subFlowStatus",
		remark: "remark",
		signName: "signName",
		reSign: "re-sign",
		createUser: "createUser",
		createTime: "createTime",
		flowInstId: "flowInstId",
		runNodeId: "runNodeId",
		runJobId: "runJobId",
		tenantId: "tenantId",
		useTime: "useTime",
		inputIdTip: "input id",
		inputFlowKeyTip: "input flowKey",
		inputFlowNodeIdTip: "input flowNodeId",
		inputNodeJobIdTip: "input nodeJobId",
		inputUserIdTip: "input userId",
		inputRemarkTip: "input remark",
		inputSignNameTip: "input signName",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputFlowInstIdTip: "input flowInstId",
		inputRunNodeIdTip: "input runNodeId",
		inputRunJobIdTip: "input runJobId",
		inputTenantIdTip: "input tenantId",
	},
}
