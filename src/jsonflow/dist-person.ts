import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/jsonflow/dist-person/page",
		method: "get",
		params: query,
	})
}

export function delegatePage(query?: Object) {
	return request({
		url: "/jsonflow/dist-person/delegate/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/jsonflow/dist-person",
		method: "post",
		data: obj,
	})
}

export function delegate(obj?: Object) {
	return request({
		url: "/jsonflow/dist-person/delegate",
		method: "post",
		data: obj,
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/jsonflow/dist-person/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/jsonflow/dist-person/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/jsonflow/dist-person/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/jsonflow/dist-person",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/jsonflow/dist-person",
		method: "put",
		data: obj,
	})
}

// 流程中保存分配参与者
export function saveByFlowInstId(obj) {
	return request({
		url: "/jsonflow/dist-person/flow-inst-id",
		method: "put",
		data: obj,
	})
}

// 流程中获取
export function getByFlowInstId(obj) {
	return request({
		url: "/jsonflow/dist-person/flow-inst-id",
		method: "get",
		params: obj,
	})
}
