<template>
  <CommonCard title="消息通知">
    <template #suffix>
      <el-button
        type="info"
        link
        class="color-#AAAAAA! mr-[-5px]"
        v-if="total > 5"
        @click="handleMore"
      >
        更多
        <el-icon> <ArrowRight /> </el-icon>
      </el-button>
    </template>

    <el-skeleton :rows="5" v-if="loading" />
    <div class="notice-list" v-if="!loading && noticeList.length">
      <p v-for="item in noticeList" :key="item.id" @click="handleView(item)">
        <span class="title">{{ item.noticeTitle }}</span>
        <span class="time">{{ item.createTime }}</span>
      </p>
    </div>
    <div
      class="flex-center flex-col text-14px color-#AAAAAA h-150px"
      v-if="!loading && !noticeList.length"
    >
      <img class="mb-10px" src="@/assets/images/icon/message-empty.png" alt="" />
      暂无消息
    </div>

    <ViewNoticeModal ref="viewNoticeRef" />
  </CommonCard>
</template>

<script setup>
import CommonCard from "@/views/components/CommonCard.vue"
import { listNotice } from "@/api/ess/doc/noticeManagementApi"

const ViewNoticeModal = defineAsyncComponent(() =>
  import("@/views/doc/notice/components/ViewNoticeModal.vue")
)

const noticeList = ref([])
const queryParams = reactive({
  page: 1,
  limit: 5,
})
const router = useRouter()
const loading = ref(true)
const total = ref(0)
async function getList() {
  try {
    const res = await listNotice(queryParams)
    noticeList.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}
function handleMore() {
  router.push("/navUser/notice")
}

const viewNoticeRef = ref()
/** 查看 */
function handleView(row) {
  viewNoticeRef.value.open(row)
}

getList()

onActivated(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.notice-list {
  overflow-y: auto;
  height: 150px;
  p {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    cursor: pointer;
    margin: 0;
    .title {
      @apply text-overflow mr-10px;
    }
    .time {
      color: #6d7886;
      flex-shrink: 0;
    }
    &:not(:last-child) {
      margin-bottom: 12px;
    }
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
