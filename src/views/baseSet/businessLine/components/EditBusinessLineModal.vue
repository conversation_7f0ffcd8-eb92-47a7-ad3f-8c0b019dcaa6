<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="500px"
      :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="110px" :rules="rules">
      <el-form-item label="业务线名称" prop="businessLineName">
        <el-input
            v-model="form.businessLineName"
            placeholder="请输入业务线名称"
            maxlength="50"
            clearable
            show-word-limit
        />
      </el-form-item>
      <el-form-item label="归属" prop="ascription">
        <dict-select v-model="form.ascription" :options="business_ascription"/>
      </el-form-item>
      <el-form-item label="是否允许解除" prop="relieveFlag">
        <dict-select v-model="form.relieveFlag" :options="ess_yes_no" type="radio"/>
      </el-form-item>
      <el-form-item label="解除是否需要走审批流程" prop="flowFlag">
        <dict-select v-model="form.flowFlag" :options="ess_yes_no" type="radio"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 取消</el-button>
      <el-button type="success" :loading="loading" @click="submitForm('add')">
        保存并新建
      </el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup name="EditBusinessLineModal">
import { addBusinessLine, updateBusinessLine } from "@/api/ess/baseSet/businessLine"
import { rule } from "@/utils/validate"

const { proxy } = getCurrentInstance()
defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  ess_yes_no: Array,
  business_ascription: Array,
})
const title = ref("新增业务线")
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)
const form = ref({
  businessLineName: "",
  ascription: null,
  relieveFlag: 0,
  flowFlag: 0,
})

const rules = ref({
  businessLineName: [
    { required: true, message: "请输入业务线名称", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" },
  ],
  ascription: [{ required: true, message: "请选择归属", trigger: "change" }],
  relieveFlag: [{ required: true, message: "请选择是否允许解除", trigger: "change" }],
  flowFlag: [
    { required: true, message: "请选择解除是否需要走审批流程", trigger: "change" },
  ],
})
const emit = defineEmits(["change"])

const open = (item) => {
  dialogVisible.value = true
  nextTick(() => {
    if (item?.id) {
      form.value = { ...item }
    }
  })
  title.value = item?.id ? "编辑业务线" : "新增业务线"
}
// 取消
const handleClose = () => {
  reset()
  dialogVisible.value = false
}

const reset = () => {
  proxy.resetForm("formRef")
  form.value.id = null
  title.value = "新增业务线"
}

// 确定
const submitForm = async (type = "save") => {
  proxy.$refs["formRef"].validate(async (valid) => {
    if (valid) {
      loading.value = true
      let actionUrl = form.value.id ? updateBusinessLine : addBusinessLine
      try {
        let res = await actionUrl(form.value)
        if (+res.code === 200) {
          handleSuccess(type)
        }
      } catch (error) {
      } finally {
        loading.value = false
      }
    }
  })
}

const handleSuccess = (type) => {
  proxy.$modal.msgSuccess(form.value.id ? "修改成功" : "新增成功")
  if (type === "add") {
    reset()
    emit("change")
  } else {
    handleClose()
    form.value.id ? emit("change") : emit("refresh")
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
