import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-jsonflow/def-flow/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/def-flow",
		method: "post",
		data: obj,
    headers: {
      repeatSubmit: false,
    },
	})
}

export function getObj(id?: string) {
	return request({
		url: "/cloud-jsonflow/def-flow/" + id,
		method: "get",
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/cloud-jsonflow/def-flow/temp-store",
		method: "post",
		data: obj,
	})
}

export function delObj(id: any) {
	return request({
		url: "/cloud-jsonflow/def-flow/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-jsonflow/def-flow",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/def-flow",
		method: "put",
		data: obj,
	})
}

/**
 * 选择流程定义ID集合
 */
export function getDefFlowIds() {
	return request({
		url: "/cloud-jsonflow/def-flow/list",
		method: "get",
	})
}

/**
 * 根据流程名称获取信息
 *
 * @return AxiosPromise
 */
export function getByFlowName(flowName: string) {
	return request({
		url: "/cloud-jsonflow/def-flow/flow-name/" + flowName,
		method: "get",
	})
}
