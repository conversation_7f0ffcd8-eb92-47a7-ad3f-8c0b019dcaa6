@charset "UTF-8";
.flow-header-drawer {
  /*抽屉内容靠上*/
}
.flow-header-drawer .el-drawer__header {
  margin-bottom: 0;
}

.tinymce-print-drawer {
  /*抽屉内容靠上*/
}
.tinymce-print-drawer .el-drawer__header {
  margin-bottom: 0;
}

.flow-overflow-drawer {
  /*抽屉内容靠上*/
  /*隐藏滚动条*/
}
.flow-overflow-drawer .el-drawer__header {
  margin-bottom: 0;
}
.flow-overflow-drawer .el-drawer__body {
  overflow: hidden;
  padding: 10px 0;
}

.flow-dialog-drawer {
  /*隐藏滚动条*/
}
.flow-dialog-drawer .el-dialog__body {
  overflow: hidden;
}

.flow-attr-drawer {
  /*抽屉内容靠上*/
}
.flow-attr-drawer .el-drawer__header {
  margin-bottom: 0;
  margin-left: 15px;
}/*# sourceMappingURL=flow-drawer.css.map */