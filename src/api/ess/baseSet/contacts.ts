import request from "@/utils/request"
import {essPrefix} from "@/config/constant"

// ------------types--------------
export interface EnterpriseUserListParams {
    /** 企业名称 */
    enterpriseName?: string;
    /** 企业ID */
    id?: string;
    /** 综合搜索关键词 */
    keyWord?: string;
    /** 每页条数 */
    limit?: number;
    /** 经办人姓名 */
    operatorName?: string;
    /** 当前页码 */
    page?: number;
    /** 联系电话 */
    phoneNumber?: string;
}

// 添加企业用户请求参数类型
export interface AddEnterpriseUserParams {
    /** 企业名称 */
    enterpriseName: string;
    /** 经办人姓名 */
    operatorName?: string;
    /** 联系电话 */
    phoneNumber?: string;
}

export interface ExtUserListParams {
    /** 证件号码 */
    certificateNo?: string;
    /** 证件类型 0-身份证 */
    certificateType?: number;
    /** ID */
    id?: string;
    /** 综合搜索关键词 */
    keyWord?: string;
    /** 每页条数 */
    limit?: number;
    /** 当前页码 */
    page?: number;
    /** 联系电话 */
    phoneNumber?: string;
    /** 用户姓名 */
    userName?: string;
}

export interface AddExtUserParams {
    /** 证件号码 */
    certificateNo?: string;
    /** 证件类型 */
    certificateType?: string;
    /** 联系电话 */
    phoneNumber?: string;
    /** 用户姓名 */
    userName?: string;
}


// 查询校外企业信息列表 带分页
export function getEnterpriseUserListAPI(data: EnterpriseUserListParams) {
    return request({
        url: essPrefix + "/enterpriseUser/listPage",
        method: "post",
        data,
    })
}


// 新增校外企业信息
export function addEnterpriseUserAPI(data: AddEnterpriseUserParams) {
    return request({
        url: essPrefix + "/enterpriseUser/add",
        method: "post",
        data,
    })
}

// 编辑校外企业信息
export function updateEnterpriseUserAPI(data: AddEnterpriseUserParams) {
    return request({
        url: essPrefix + "/enterpriseUser/edit",
        method: "post",
        data,
    })
}

// 删除企业联系人
export function deleteEnterpriseUserAPI(ids: Array) {
    return request({
        url: essPrefix + `/enterpriseUser/${ids}`,
        method: "DELETE"
    })
}

// 查询校外用户列表 带分页
export function getExtUserListAPI(data: ExtUserListParams) {
    return request({
        url: essPrefix + "/extUser/listPage",
        method: "post",
        data,
    })
}


// 新增校外用户
export function addExtUserAPI(data: AddExtUserParams) {
    return request({
        url: essPrefix + "/extUser/add",
        method: "post",
        data,
    })
}

// 编辑校外企业信息
export function updateExtUserAPI(data: AddExtUserParams) {
    return request({
        url: essPrefix + "/extUser/edit",
        method: "post",
        data,
    })
}

// 删除个人联系人
export function deleteExtUserAPI(ids: Array) {
    return request({
        url: essPrefix + `/extUser/${ids}`,
        method: "DELETE"
    })
}
