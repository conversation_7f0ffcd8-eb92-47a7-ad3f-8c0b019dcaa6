<template>
  <div>
    <el-form
      ref="dataFormRef"
      v-loading="loading"
      :model="form"
      :rules="dataRules"
      label-width="120px"
      :disabled="operType === 'view'"
    >
      <el-form-item label="流程实例ID" prop="flowInstId">
        <el-input v-model="form.flowInstId" placeholder="请输入流程实例ID" disabled />
      </el-form-item>

      <el-form-item label="流程名称" prop="defFlowId">
        <el-select
          v-model="form.defFlowId"
          placeholder="请选择流程名称"
          clearable
          filterable
          disabled
        >
          <el-option
            v-for="(item, index) in cascadeDic.defFlowId"
            :key="index"
            :label="item.flowName"
            :value="item.defFlowId"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="达到节点" prop="toRunNodeId">
        <el-tooltip
          placement="top"
          content="若不选择具体的【指定被驳回人】，则默认会驳回到该节点下的全部审批人"
        >
          <el-select
            v-model="form.toRunNodeId"
            placeholder="请选择达到节点"
            clearable
            filterable
            @change="cascadeChange('toRunNodeId', ['handleUserId'])"
          >
            <el-option
              v-for="(item, index) in cascadeDic.toRunNodeId"
              :key="index"
              :label="item.nodeName"
              :value="item.id"
            />
          </el-select>
        </el-tooltip>
      </el-form-item>

      <el-form-item label="指定被驳回人" prop="handleUserId">
        <el-tooltip placement="top">
          <template #content>
            {{ validateNull(cascadeDic.handleUserId) ? userIdText : userIdText2 }}
          </template>
          <el-select
            v-model="form.handleUserId"
            placeholder="请选择被驳回人"
            clearable
            filterable
            @change="handleUserIdChange"
          >
            <el-option
              v-for="(item, index) in cascadeDic.handleUserId"
              :key="index"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-tooltip>
      </el-form-item>

      <el-form-item label="驳回原因" prop="remark">
        <el-input
          v-model="form.runRejectVO.remark"
          type="textarea"
          placeholder="请输入驳回原因"
        />
      </el-form-item>
    </el-form>
    <footer class="el-dialog__footer">
      <span class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="handleUpdate"
          >确定</el-button
        >
      </span>
    </footer>
  </div>
</template>

<script setup lang="ts" name="RunRejectForm">
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
import { validateNull } from "@/utils/validate"
const { proxy } = getCurrentInstance()

const $emit = defineEmits(["onRejectJob"])
// 定义变量内容
const dataFormRef = ref()
const loading = ref(false)
const operType = ref(false)

// 定义字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "userId" })
const onCascade = onCascadeChange(
  cascadeDic,
  { key: "flowInstId", cascades: ["defFlowId"] },
  { prefix: "runReject", key: "flowInstId", cascades: ["toRunNodeId"] },
  { key: "toRunNodeId", cascades: ["handleUserId"] }
)
onMounted(() => {
  onLoad(dicData)
})
function cascadeChange(key, cascades) {
  onCascade(form, { key: key, cascades: cascades })
  handleToFlowNodeId(form[key])
}

// 提交表单数据
const form = reactive({
  defFlowId: "",
  flowInstId: "",
  toRunNodeId: "",
  handleUserId: "",
  runRejectVO: {},
})

// 定义校验规则
const dataRules = ref({
  defFlowId: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
  flowInstId: [{ required: true, message: "流程实例ID不能为空", trigger: "blur" }],
  toRunNodeId: [{ required: true, message: "达到节点名称不能为空", trigger: "blur" }],
})

const props = defineProps({
  currJob: {
    type: Object,
    default: null,
  },
})

const userIdText = "注：当前被驳回节点下可候选被驳回人为空不可驳回，请使用【任意跳转】"
const userIdText2 = "注：当被驳回人为空时，则默认会驳回到当前节点显示的全部审批人"
const userRoleIdText =
  "注：被驳回节点下的任务需审批过存在审批人才可驳回，否则请使用【任意跳转】"

function handleToFlowNodeId(val) {
  if (!val) {
    return
  }
  const runNode = cascadeDic.toRunNodeId.find((f) => f.id === val)
  form.runRejectVO.toRunNodeId = runNode.id
  form.runRejectVO.toFlowNodeId = runNode.flowNodeId
  form.runRejectVO.nodeType = runNode.nodeType
}

function handleUserIdChange(val) {
  form.runRejectVO.handleUserId = val
}

function initJobData() {
  Object.assign(form, props.currJob)
  onCascade(form)
}

async function handleUpdate() {
  const valid = await dataFormRef.value.validate().catch(() => {})
  if (!valid) {
    return false
  }

  if (validateNull(cascadeDic.handleUserId)) {
    proxy.$modal.msgWarning(userIdText)
    return
  }
  props.currJob.runRejectVO = form.runRejectVO

  loading.value = true
  try {
    $emit("onRejectJob", props.currJob)
    setTimeout(() => {
      // 异步异常
      loading.value = false
    }, 3000)
  } catch (e) {
    loading.value = false
  }
}

// 监听双向绑定
watch(
  () => props.currJob.id,
  (val) => {
    initJobData()
  }
)

onMounted(() => {
  initJobData()
})
</script>

<style lang="scss" scoped></style>
