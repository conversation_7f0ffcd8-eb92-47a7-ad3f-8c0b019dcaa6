<template>
  <div>
    <div id="printInfo" style="word-break: break-all;" v-html="printInfoHtml" />
    <div>
      <footer class="el-dialog__footer" style="text-align: center;">
        <span class="dialog-footer">
          <el-button type="primary" @click="confirmClick">打印</el-button>
        </span>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts" name="TinymceView">
import { printHtml } from '@/flow'
import { validateNull } from '@/utils/validate'
import { onLoadDicUrl } from '../convert-name/convert'
import { PROP_CONST } from '../../support/prop-const'
import { handlePrintValue } from '../form-create'

const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null
  }
})

// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: 'createUser' })

const printInfoHtml = ref(null)

function buildPrintInfoHtml(printInfo) {
  let regTable = /<\/tbody>.*?/g
  let regTr = /<tr>.*?<\/tr>/g
  let regSubVar = /\$\{.*?\..*?\}/g
  let regVar = /\$\{.*?\}/g
  printInfo = printInfo.replaceAll('\n', '')
  let tables = printInfo.split(regTable)
  for (let j = 0; j < tables.length; j++) {
    let table = tables[j]
    if (regSubVar.test(table)) {
      let newTable = table.replace(regTr, function (str) {
        if (regSubVar.test(str)) {
          let propVar = str.substr(str.indexOf('${'), str.indexOf('}') - str.indexOf('${') + 1)
          let prop = propVar.substring(propVar.indexOf('${') + 2, propVar.indexOf('.'))
          // 获取表格数据
          let tableData = props.currFlowForm.formData[prop]
          // 构造动态数据行
          let dynamicRows = ''
          if (!validateNull(tableData)) {
            for (let i = 0; i < tableData.length; i++) {
              let dynStr = str.replace(regSubVar, function (str) {
                let propVar = str.substr(
                  str.indexOf('${'),
                  str.indexOf('}') - str.indexOf('${') + 1
                )
                let prop = propVar.substring(propVar.indexOf('.') + 1, propVar.indexOf('}'))
                let existSubForm = validateSubFormField(props.currFlowForm.modelRefList, prop)
                let formDatum = tableData[i][prop]
                if (existSubForm) {
                  // 判断自定义组件
                  let printValue = existSubForm.__fc__.prop.props.formCreateInject.printValue
                  if (!printValue) {
                    let optionItems = existSubForm.__fc__.prop.options
                    printValue = handlePrintValue(optionItems, formDatum, 'value', 'label')
                  }
                  if (printValue) return printValue
                }
                return formDatum ? formDatum : ''
              })
              dynamicRows += dynStr
            }
          }
          return dynamicRows
        } else {
          return str
        }
      })
      printInfo = printInfo.replace(table, newTable)
    }
  }
  // 替换外层变量值
  if (regVar.test(printInfo)) {
    let dynStr = printInfo.replace(regVar, function (str) {
      let propVar = str.substr(str.indexOf('${'), str.indexOf('}') - str.indexOf('${') + 1)
      let prop = propVar.substring(propVar.indexOf('${') + 2, propVar.indexOf('}'))
      let formDatum = props.currFlowForm.formData[prop]
      let isSysFields = false
      if (props.currFlowForm.rule) {
        let widgetRefVar = props.currFlowForm.rule.find((f) => f.field === prop)
        if (widgetRefVar) {
          let optionItems = props.currFlowForm.modelRefList[prop]?.__fc__.prop.options
          if (!validateNull(optionItems)) {
            if (Array.isArray(formDatum)) {
              let showKeys = ''
              for (let i = 0; i < formDatum.length; i++) {
                let item = optionItems.find((f) => f.value === formDatum[i])
                if (item) {
                  if (i === formDatum.length - 1) {
                    showKeys += item.label
                  } else {
                    showKeys += item.label + ','
                  }
                }
              }
              return showKeys
            } else {
              let item = optionItems.find((f) => f.value === formDatum)
              if (item) {
                return item.label
              }
            }
          }
        } else {
          isSysFields = true
        }
      } else {
        let optionItems = props.currFlowForm.dicData[prop]
        if (!validateNull(optionItems)) {
          if (Array.isArray(formDatum)) {
            let showKeys = ''
            for (let i = 0; i < formDatum.length; i++) {
              let item = optionItems.find(
                (f) => f[props.currFlowForm[prop + '.valueKey']] === formDatum[i]
              )
              if (item) {
                if (i === formDatum.length - 1) {
                  showKeys += item[props.currFlowForm[prop + '.showKey']]
                } else {
                  showKeys += item[props.currFlowForm[prop + '.showKey']] + ','
                }
              }
            }
            return showKeys
          } else {
            let item = optionItems.find(
              (f) => f[props.currFlowForm[prop + '.valueKey']] === formDatum
            )
            if (item) {
              return item[props.currFlowForm[prop + '.showKey']]
            }
          }
        } else {
          isSysFields = true
        }
      }
      if (isSysFields) {
        formDatum = props.currFlowForm[prop]
        if (!validateNull(dicData[prop])) {
          let field = PROP_CONST.SYS_FIELDS.find((f) => f.prop === prop)
          let find = dicData[prop].find((f) => f[field.valueKey] === formDatum)
          if (find) {
            return find[field.showKey]
          }
        }
      }
      return formDatum ? formDatum : ''
    })
    printInfo = printInfo.replace(printInfo, dynStr)
  }
  printInfoHtml.value = printInfo
}

function validateSubFormField(modelRefList, field) {
  if (validateNull(modelRefList)) return null
  let resExistField = null
  for (const each of modelRefList) {
    if (validateNull(each.model())) continue
    let existField = each.model()[field]
    if (existField) {
      resExistField = existField
      break
    }
  }
  return resExistField
}

function confirmClick() {
  printHtml('printInfo', props.currFlowForm.formName, null)
}

// 监听变化
watch(
  () => props.currFlowForm.id,
  () => {
    buildPrintInfoHtml(props.currFlowForm.printInfo)
  }
)

// 初始化
onMounted(async () => {
  await onLoad(dicData)
  buildPrintInfoHtml(props.currFlowForm.printInfo)
})
</script>

<style lang="scss">
// 排除tailwind.css全局样式的影响
/*border-width: 0;
  border-style: solid;*/
#printInfo {
  table,
  th,
  td {
    border: 1px solid; /* 设置边框样式 */
    border-collapse: collapse; /* 合并边框 */
  }
}
/*img { TODO 本地设置默认大小
    width: 660px;
    height: 150px;
  }*/
</style>
