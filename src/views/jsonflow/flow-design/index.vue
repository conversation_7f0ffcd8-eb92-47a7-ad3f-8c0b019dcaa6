<template>
  <div style="height: 100%" :class="props.currFlowForm ? '' : 'layout-padding'">
    <el-container class="flow-container" v-loading="loading">
      <el-aside class="select-area">
        <el-row>
          <div class="el-row-tab">基础</div>
          <node-menu
            :menu-list="data.menuList.commonNodes"
            type="commonNodes"
            @set-drag-info="methods.setDragInfo"
          />
        </el-row>
        <el-row>
          <div class="el-row-tab">高级</div>
          <node-menu
            :menu-list="data.menuList.highNodes"
            type="highNodes"
            @set-drag-info="methods.setDragInfo"
            :flowData="data.flowData"
          />
        </el-row>
        <el-row>
          <div class="el-row-tab">泳道</div>
          <node-menu
            :menu-list="data.menuList.laneNodes"
            type="laneNodes"
            @set-drag-info="methods.setDragInfo"
          />
        </el-row>
      </el-aside>
      <el-container>
        <el-header class="header-option">
          <div class="header-option__tools">
            <el-tooltip content="点击可切换简单模式与专业模式" placement="right">
              <el-button
                id="isSimpleMode"
                icon="Pointer"
                type="primary"
                plain
                @click="methods.showFlowMode"
              >
                {{ data.globalConfig.isSimpleMode === "1" ? "简单模式" : "专业模式" }}
              </el-button>
            </el-tooltip>
            <el-tooltip content="设置流程属性" placement="right">
              <el-button
                icon="Tools"
                type="primary"
                plain
                @click="methods.showAttrConfig(true, '1')"
              >
                流程属性
              </el-button>
            </el-tooltip>
            <el-select
              v-model="data.globalValue.connector"
              clearable
              placeholder="连线"
              @change="methods.changeConnector"
            >
              <el-option
                v-for="(item, index) in data.globalConfig.connectors"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="data.globalValue.router"
              clearable
              placeholder="路由"
              @change="methods.changeRouter"
            >
              <el-option
                v-for="(item, index) in data.globalConfig.routers"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="data.globalValue.rankDir"
              clearable
              placeholder="布局"
              @change="methods.layout"
            >
              <el-option
                v-for="(item, index) in data.globalConfig.rankDirs"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="header-option__buttons">
            <el-tooltip
              v-if="!props.currFlowForm && !validateRunFlow(data)"
              content="升版本"
              placement="bottom"
            >
              <el-button
                class="header-option-button"
                icon="Upload"
                @click="methods.upgradeVersion()"
              >
                升版本
              </el-button>
            </el-tooltip>
            <el-tooltip
              v-if="!props.currFlowForm && !validateRunFlow(data)"
              content="暂存"
              placement="bottom"
            >
              <el-button
                class="header-option-button"
                icon="CirclePlus"
                @click="methods.publishFlow(null, '-1')"
                :btnLoading="btnLoading"
              >
                暂存
              </el-button>
            </el-tooltip>
            <el-tooltip v-if="!props.currFlowForm" content="发布" placement="bottom">
              <el-button
                class="header-option-button"
                icon="Promotion"
                @click="methods.publishFlow(null, '1')"
                :btnLoading="btnLoading"
              >
                发布
              </el-button>
            </el-tooltip>
            <el-tooltip content="撤销" placement="bottom">
              <el-button
                class="header-option-button"
                icon="RefreshLeft"
                @click="methods.undoFlow()"
              >
                撤销
              </el-button>
            </el-tooltip>
            <el-tooltip content="重做" placement="bottom">
              <el-button
                class="header-option-button"
                icon="RefreshRight"
                @click="methods.redoFlow()"
              >
                重做
              </el-button>
            </el-tooltip>
            <el-tooltip content="清空" placement="bottom">
              <span>
                <el-popconfirm
                  title="是否确认重新绘制?"
                  width="200"
                  @confirm="methods.clearAll"
                >
                  <template #reference>
                    <el-button class="header-option-button" icon="Delete">清空</el-button>
                  </template>
                </el-popconfirm>
              </span>
            </el-tooltip>
            <!--<el-tooltip :content="data.gridConfig.showGridText" placement="bottom">
                            <el-button
                                    @click="methods.toggleShowGrid"
                                    class="header-option-button"
                                    :icon="data.gridConfig.showGridIcon"
                            >{{ data.gridConfig.showGridText }}
                            </el-button>
                        </el-tooltip>-->
            <el-tooltip content="画布设置" placement="bottom">
              <el-button
                class="header-option-button"
                icon="SetUp"
                @click="methods.setting"
              >
                画布设置
              </el-button>
            </el-tooltip>
            <!-- <el-tooltip content="导入导出" placement="bottom">
							<el-button
								class="header-option-button"
								icon="UploadFilled"
								@click="methods.openViewJson"
							>
								导入导出
							</el-button>
						</el-tooltip> -->
            <el-tooltip content="查看快捷键或文档" placement="bottom">
              <span>
                <el-popconfirm
                  title="快速入门选项"
                  placement="bottom"
                  confirm-button-text="快捷入门"
                  cancel-button-text="使用文档"
                  icon="QuestionFilled"
                  width="200"
                  @confirm="methods.shortcutHelper"
                  @cancel="methods.usingDoc"
                >
                  <template #reference>
                    <el-button
                      style="font-size: 12px"
                      class="header-option-button"
                      icon="QuestionFilled"
                      type="primary"
                      text
                    >
                      快速入门
                    </el-button>
                  </template>
                </el-popconfirm>
              </span>
            </el-tooltip>
          </div>
        </el-header>
        <el-main class="flow-content">
          <flow-area
            ref="flowArea"
            :drag-info="data.dragInfo"
            :curr-select="data.currSelect"
            @remove-ele-tools="methods.removeEleTools"
            @show-attr-config="methods.showAttrConfig"
            @init-json-flow="initJsonFlow"
          />
        </el-main>
      </el-container>
      <flow-attr
        ref="flowAttr"
        :flow-data="data.flowData"
        :curr-flow-form="props.currFlowForm"
        :curr-select="data.currSelect"
        @show-attr-config="methods.showAttrConfig"
        @init-flow="methods.initFlow"
      />
    </el-container>
    <!-- 画布设置 -->
    <setting-modal ref="settingModal" @flow-separated="methods.flowSeparated" />
    <!-- 快捷入门 -->
    <shortcut-modal ref="shortcutModal" :shortcut="data.shortcut" />
    <!-- 导入导出 -->
    <json-modal ref="jsonModal" @load-flow="methods.loadFlow" />
  </div>
</template>

<script setup lang="ts" name="JsonFlow">
import * as menuConfig from "@/flow/designer/config/menu-config"
import * as nodeConfig from "@/flow/designer/config/node-config"
import { flowConfig } from "@/flow/designer/config/flow-config"
import { utils } from "@/flow/designer/utils/common"
import { deepClone } from "@/utils/index"
import * as defFlow from "@/flow/designer/api/jsonflow"
import { setPropsDataValue, setPropsNull } from "@/flow/support/common"
import JsonflowDesign from "@jackrolling/jsonflow3"
import { initDefFlowFromAttrs, notifyLeft, validateRunFlow } from "@/flow"
import { useMessage, useMessageBox } from "@/hooks/message"
import { validateNull } from "@/utils/validate"
import {
  confirmCancelAndClose,
  handleUpgradeVersion,
  hideVueContextmenuName,
} from "@/flow/utils"
import { listDefFlowByFlowKey } from "@/flow/designer/api/jsonflow"

const { proxy } = getCurrentInstance()
const $route = useRoute()
// 引入组件
const FlowArea = defineAsyncComponent(
  () => import("@/flow/designer/components/flow-area.vue")
)
const FlowAttr = defineAsyncComponent(
  () => import("@/flow/designer/components/flow-attr.vue")
)
const SettingModal = defineAsyncComponent(
  () => import("@/flow/designer/views/setting.vue")
)
const ShortcutModal = defineAsyncComponent(
  () => import("@/flow/designer/views/shortcut.vue")
)
const JsonModal = defineAsyncComponent(
  () => import("@/flow/designer/views/json-view.vue")
)
const NodeMenu = defineAsyncComponent(
  () => import("@/flow/designer/components/node-menu.vue")
)

const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null,
  },
})
const loading = ref(false)
const btnLoading = ref(false)
const data = reactive({
  menuList: {
    commonNodes: menuConfig.commonNodes,
    highNodes: menuConfig.highNodes,
    laneNodes: menuConfig.laneNodes,
  },
  flowData: deepClone(flowConfig.flowData),
  globalConfig: flowConfig.globalConfig,
  gridConfig: flowConfig.gridConfig,
  dragInfo: {
    type: "",
    belongTo: "",
  },
  shortcut: flowConfig.shortcut,
  globalValue: {},
  currSelect: {},
})

const methods = {
  // drag
  setDragInfo(info) {
    data.dragInfo = info
  },
  setElementTools(graph, paper, model) {
    methods.setCurrSelect(model)
    window._eleTools.setElementTools(graph, paper, model)
  },
  removeEleTools() {
    window._jfOperate.removeEleTools()
    methods.clearCurrSelect()
    hideVueContextmenuName()
  },
  // 关闭提示
  listenPage() {
    window.onbeforeunload = function (e) {
      e = e || window.event
      if (e) {
        e.returnValue = "关闭提示"
      }
      return "关闭提示"
    }
  },
  // 初始化流程
  initFlow(currFlowId?: any, flowInstId?: any, callback?: Function, isNew?: boolean) {
    let flowType = "0"
    // 判断编辑
    if (!currFlowId) {
      flowType = <string>$route.query.flowType
      currFlowId = $route.query.currFlowId
    } else if (flowInstId) {
      currFlowId = flowInstId
      flowType = "1"
    }
    if (currFlowId) methods.clearAll()
    console.log("currFlowId", currFlowId)
    if (currFlowId) {
      loading.value = true
      defFlow
        .getNodesByIdType(currFlowId, flowType, "1")
        .then((resp) => {
          let defFlowData = resp.object
          if (defFlowData.attrs) {
            if (!validateNull(defFlowData.nodeList)) {
              methods.loadFlow(defFlowData, callback, isNew)
            } else {
              let flowData = deepClone(flowConfig.flowData)
              setPropsDataValue(
                flowData.attrs,
                defFlowData.attrs,
                "id",
                "flowName",
                "flowKey",
                "groupName",
                "status",
                "sort",
                "remark",
                "version"
              )
              methods.loadFlow(flowData, callback, isNew)
            }
          }
          methods.currFlowFormFlowDesigning(false, false)
        })
        .catch((e) => {
          methods.currFlowFormFlowDesigning(false, false)
          proxy.$modal.msgError("获取流程信息失败")
          console.log(e)
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      data.flowData.attrs.id = utils.getId()
      if (!props.currFlowForm) window._jfOperate.reAutoInitNodes()
    }
  },
  async initNewFlow(callback, isNew) {
    data.flowData.graph = window._jfGraph.toJSON()
    let resp = await listDefFlowByFlowKey(data.flowData.attrs.flowKey)
    if (validateNull(resp.object)) {
      return
    }
    loading.value = false
    data.flowData.attrs.version = resp.object[0].version
    methods.loadFlow(data.flowData, callback, isNew)
  },
  flowSeparated(isJobSeparated) {
    data.flowData.graph = window._jfGraph.toJSON()
    data.flowData.attrs.isJobSeparated = isJobSeparated
    // 为了节点自适应布局
    data.flowData.attrs.autoLayout = data.globalConfig.defaultAutoLayoutName
    methods.jfGraphToFlowData(data.flowData, null)
    methods.loadFlow(data.flowData, null, false)
  },
  // 加载流程
  loadFlow(loadData, callback, isNew) {
    window._jfOperate.reInitFlowSetting(data.flowData, loadData, data)
    window._shapes = nodeConfig.initNodeShapes()

    window._jfOperate.handleGraphCells(loadData, "id")
    if (isNew === true) {
      window._jfOperate.handleNewFromTemp(loadData)
    }
    data.flowData.attrs = loadData.attrs

    if (!validateNull(loadData.graph) && !validateNull(loadData.graph.cells)) {
      window._jfGraph.fromJSON(loadData.graph)
    }
    if (callback) {
      callback()
    }
  },
  // 检测
  checkFlow() {
    let nodes = window._jfGraph.getElements()
    if (nodes.length <= 0) {
      proxy.$modal.msgWarning("流程无任何节点")
      return false
    }
    let links = window._jfGraph.getLinks()
    if (links.length <= 0) {
      proxy.$modal.msgWarning("流程无任何连线")
      return false
    }
    return true
  },
  async upgradeVersion() {
    try {
      let isNew = data.flowData.attrs.isNew
      if (isNew) {
        notifyLeft("当前流程版本已最新, 请确认是否已保存")
        return
      }
      await useMessageBox().confirm("是否确定升级版本?")
    } catch {
      return
    }
    methods.initNewFlow(() => {
      let confirmObj = {
        text: "发布流程",
        callback: () => {
          methods.publishFlow(null, "1")
        },
      }
      let cancelObj = {
        text: "暂存流程",
        callback: () => {
          methods.publishFlow(null, "-1")
        },
      }
      confirmCancelAndClose(
        confirmObj,
        cancelObj,
        "流程设计升级版本成功! 是否立即暂存或发布?"
      )
    }, true)
  },
  // 发布流程(addNodePublish == true 不弹出有增删节点升级直接调用)
  async publishFlow(callback, status, addNodePublish?) {
    let flowObj = Object.assign({}, data.flowData)
    if (!methods.checkFlow()) {
      return
    }

    if (!flowObj.attrs.flowKey || !flowObj.attrs.flowName || !flowObj.attrs.groupName) {
      proxy.$modal.msgWarning(
        "流程属性【流程名称】或【流程KEY】或【分组名称】为空，请点击左上角《流程属性》填写"
      )
      methods.showAttrConfig(true, "1")
      return
    }
    function doPublishFlow(isNew?) {
      // 升版本后取最新
      if (isNew) {
        flowObj = Object.assign({}, data.flowData)
      }
      methods.doPublishFlow(flowObj, callback, status)
    }
    // 直接新增节点发布
    if (addNodePublish) {
      await handleUpgradeVersion(props)
      methods.initNewFlow(() => {
        doPublishFlow(true)
      }, true)
    } else {
      if (!validateRunFlow(data)) {
        let exists = await defFlow.listRunFlowsByDefFlowId(
          flowObj.attrs.id,
          flowObj.attrs.version
        )
        console.log(
          "exists",
          exists,
          !validateNull(exists.object),
          exists.object.isIndependent !== "1"
        )
        if (!validateNull(exists.object) && exists.object.isIndependent !== "1") {
          let text = status === "-1" ? "暂存" : "发布"
          let confirmObj = {
            text: "无增删节点继续" + text,
            callback: () => {
              doPublishFlow()
            },
          }
          let cancelObj = {
            text: "有增删节点升级" + text,
            callback: async () => {
              await handleUpgradeVersion(props)
              methods.initNewFlow(() => {
                doPublishFlow(true)
              }, true)
            },
          }

          // 模版固定流程直接调用有新增节点发布
          confirmCancelAndClose(
            confirmObj,
            cancelObj,
            "当前流程版本已存在流程实例! 若本次修改存在增删节点，请先确认是否已升级版本"
          )
        } else {
          doPublishFlow()
        }
      } else {
        doPublishFlow()
      }
    }
  },
  doPublishFlow(flowObj, callback, status) {
    // 转换格式
    methods.jfGraphToFlowData(flowObj, status)
    if (methods.validateDistFlowNode(flowObj)) {
      return
    }

    let addObj = defFlow.addObjByDefFlowId
    let flowType = <string>$route.query.flowType
    if (validateRunFlow(props) || flowType === "1") {
      addObj = defFlow.addObjByFlowInstId
    }
    btnLoading.value = true
    addObj(flowObj)
      .then((resp) => {
        methods.currFlowFormFlowDesigning(false, true)
        if (status === "-1") {
          notifyLeft("流程设计暂存成功")
        } else {
          notifyLeft("流程设计发布成功")
        }
        if (callback) {
          callback(resp)
        }
      })
      .catch((err) => {
        methods.currFlowFormFlowDesigning(false, false)
        proxy.$modal.msgError(err || "流程设计发布失败")
      })
      .finally(() => {
        btnLoading.value = false
      })
  },
  currFlowFormFlowDesigning(flowDesigning, flowDesign) {
    if (props.currFlowForm) {
      props.currFlowForm.flowDesigning = flowDesigning
      props.currFlowForm.flowDesign = flowDesign
    }
  },
  jfGraphToFlowData(flowObj, status) {
    flowObj.graph = window._jfGraph.toJSON()
    if (status) {
      flowObj.attrs.status = status
    }
    initDefFlowFromAttrs(props, flowObj.attrs)
    window._jfOperate.initFlowSetting(flowObj, data)
    window._jfOperate.jfGraphToFlowData(flowObj)
  },
  validateDistFlowNode(flowObj) {
    let notExist = false
    flowObj.nodeList.forEach((each) => {
      if (each.defJob && each.defJob.distFlowNodeId) {
        let some = flowObj.nodeList.some((s) => s.attrs.id === each.defJob.distFlowNodeId)
        if (!some) {
          proxy.$modal.msgError(
            "当前任务【" +
              each.defJob.jobName +
              "】配置的【待分配参与者节点】不存在, 请重新配置"
          )
          notExist = true
        }
      }
    })
    return notExist
  },
  // 连线切换
  changeConnector(name) {
    window._jfOperate.changeConnector(name)
  },
  // 路由切换
  changeRouter(name) {
    window._jfOperate.changeRouter(name)
  },
  // 自动布局
  layout(name) {
    window._jfOperate.layout(name)
  },
  // 显示隐藏网格
  toggleShowGrid() {
    window._jfOperate.toggleShowGrid()
  },
  // 导入导出
  openViewJson() {
    let flowData = Object.assign({}, data.flowData)
    methods.jfGraphToFlowData(flowData, null)
    proxy.$refs.jsonModal.open(flowData)
  },
  showAttrConfig(bool, type) {
    if (type === "1") methods.removeEleTools()
    proxy.$refs.flowAttr.open(bool)
  },
  showFlowMode() {
    let isSimpleMode = data.globalConfig.isSimpleMode === "1" ? "0" : "1"
    data.globalConfig.isSimpleMode = isSimpleMode
    flowConfig.globalConfig.isSimpleMode = isSimpleMode
    data.flowData.attrs.isSimpleMode = isSimpleMode
    window._jfOperate.reAutoInitNodes()
  },
  undoFlow() {
    window._jfOperate.undo()
  },
  redoFlow() {
    window._jfOperate.redo()
  },
  clearAll() {
    methods.removeEleTools()
    data.flowData.nodeList = []
    data.flowData.linkList = []
    data.flowData.graph = null
    setPropsNull(
      data.flowData.attrs,
      "remark",
      "flowName",
      "flowKey",
      "groupName",
      "version"
    )
    window._jfGraph.clear()
    data.flowData.attrs.status = "-1"
  },
  setCurrSelect(curr) {
    data.currSelect = curr
  },
  resetConfig() {
    data.globalConfig.isSimpleMode = "1"
    flowConfig.globalConfig.isSimpleMode = "1"
    data.flowData.attrs.isSimpleMode = "1"
    data.globalConfig.isJobSeparated = "1"
    data.gridConfig.gridSize = 1
  },
  clearCurrSelect() {
    data.currSelect = {}
  },
  // 画布设置
  setting() {
    proxy.$refs.settingModal.open()
  },
  // 快捷入门
  shortcutHelper() {
    proxy.$refs.shortcutModal.open()
  },
  // 使用文档
  usingDoc() {
    window.open("http://jsonflow.top:9000/")
  },
  $refs() {
    return proxy.$refs
  },
}

const exposeObj = {
  gridConfig: data.gridConfig,
  globalValue: data.globalValue,
  setCurrSelect: methods.setCurrSelect,
  clearCurrSelect: methods.clearCurrSelect,
  showAttrConfig: methods.showAttrConfig,
  setElementTools: methods.setElementTools,
  removeEleTools: methods.removeEleTools,
  notifyLeft: notifyLeft,
  $refs: methods.$refs,
}

function initJsonFlow() {
  methods.resetConfig()
  // 实例化
  window._shapes = nodeConfig.initNodeShapes()
  JsonflowDesign.initFlow.initJsonFlow(exposeObj)
  // 初始化流程图
  methods.initFlow()
}

onBeforeMount(() => {
  window._flowConfig = flowConfig
  window._eleTools = JsonflowDesign.eleTools.initEleTools(exposeObj)
})
onMounted(() => {
  nextTick(() => {
    // 关闭提示
    methods.listenPage()
  })
})

// 暴露变量
defineExpose({
  initFlow: methods.initFlow,
  initNewFlow: methods.initNewFlow,
  publishFlow: methods.publishFlow,
  flowData: data.flowData,
})
</script>

<style lang="scss">
@use "@/flow/designer/assets/style/flow-designer.scss" as *;
@use "@/flow/components/style/flow-drawer.scss" as *;
</style>
