<template>
  <el-dialog
    title="文件详情"
    v-model="dialogVisible"
    width="1000px"
    :before-close="handleClose"
    top="5vh"
  >
    <el-form
      :model="queryParams"
      ref="queryRef"
      inline
      label-width="75px"
      class="grey-form-box"
    >
      <el-form-item label="文件名称" prop="flowName">
        <el-input
          v-model.trim="queryParams.flowName"
          placeholder="请输入文件名称"
          clearable
          maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="签署方" prop="signUserName">
        <el-input
          v-model.trim="queryParams.signUserName"
          placeholder="请输入名称"
          clearable
          maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item class="min-w-100px!">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <CustomTabs
      class="text-16px p-t-20px"
      v-model="currentTab"
      :options="tabsOptions"
      @tab-click="changeTab"
    />

    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :has-toolbar="false"
      :data="tableData"
      :loading="loading"
      :total="total"
      @reload="getList"
      :opt-width="40"
      noPadding
    >
      <vxe-column type="seq" width="70" fixed="left" />
      <vxe-column field="flowName" title="文件名称" min-width="180" show-overflow />
      <vxe-column field="flowUsers" title="签署方" min-width="150" show-overflow />
      <vxe-column
        field="signStartTime"
        title="发起时间"
        min-width="120"
        v-if="+currentTab === 1 || +currentTab === -1"
      />
      <vxe-column
        field="errorInfo"
        title="失败原因"
        min-width="100"
        v-if="currentTab === -1"
        show-overflow
      />
    </CustomTable>
  </el-dialog>
</template>

<script setup>
import { getFileBatchHisDetailAPI } from "@/api/ess/fileManagement/fileBatchLunch"

const { proxy } = getCurrentInstance();


const dialogVisible = ref(false)
const currentTab = ref(1)

const tabsOptions = [
  {
    label: "已发起",
    value: 1,
  },
  {
    label: "待发起",
    value: 0,
  },
  {
    label: "发起失败",
    value: -1,
  },
]

const changeTab = (tab) => {
  currentTab.value = tab
  //切换tab时，重置page和limit
 handleQuery()
}

const queryParams = reactive({
  flowBatchPublishId: '',
  page: 1,
  limit: 10,
  flowName: '',
  signUserName:''
})

const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 获取列表
async function getList() {
  loading.value = true
  const res = await getFileBatchHisDetailAPI({...queryParams,publishStatus:currentTab.value})
  tableData.value = res.object.records.map(i => {
    // 判断userName是否为空，为空就不要join /
    i.flowUsers = i.flowUsers.map(j => j.userName)
    //userName为空的删除
    i.flowUsers = i.flowUsers.filter(j => j)
    return i
  })
  total.value = res.object.total
  loading.value = false
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

//暴露expose方法
function open(row) {
  queryParams.flowBatchPublishId = row.id
  currentTab.value = 1
  dialogVisible.value = true
  getList()
}


// 关闭弹窗
function handleClose() {
  dialogVisible.value = false
}


defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.grey-form-box {
  @apply pl-20px pt-10px bg-#f7f8f9 rounded-4px;
  :deep(.el-form-item) {
    margin-bottom: 10px;
  }
}
</style>

