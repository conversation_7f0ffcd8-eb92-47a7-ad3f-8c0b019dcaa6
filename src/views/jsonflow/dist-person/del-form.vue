<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="110px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.delRoleId')" prop="delRoleId">
						<el-select
							v-model="form.delRoleId"
							:placeholder="t('distPerson.inputDelRoleIdTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in dicData.users"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.recRoleId')" prop="roleId">
						<el-select
							v-model="form.roleId"
							:placeholder="t('distPerson.inputRoleIdTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in dicData.users"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.delStartTime')" prop="delStartTime">
						<el-date-picker
							v-model="form.delStartTime"
							type="datetime"
							:placeholder="t('distPerson.inputDelStartTimeTip')"
							:value-format="dateTimeStr"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.delEndTime')" prop="delEndTime">
						<el-date-picker
							v-model="form.delEndTime"
							type="datetime"
							:placeholder="t('distPerson.inputDelEndTimeTip')"
							:value-format="dateTimeStr"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('distPerson.delStatus')" prop="delStatus">
						<el-radio-group v-model="form.delStatus">
							<el-radio v-for="(item, index) in DIC_PROP.DELEGATE_STATUS" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="DelPersonDialog">

import { useMessage } from "@/hooks/message"
import { getObj, delegate } from "@/api/jsonflow/dist-person"
import { useI18n } from "vue-i18n"
import { onLoadDicUrl } from "@/flow/components/convert-name/convert"
const emit = defineEmits(["refresh"])

const { t } = useI18n()

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "users" })
onMounted(() => {
	onLoad(dicData)
})

// 提交表单数据
const form = reactive({
	delRoleId: "",
	roleId: "",
	delStartTime: null,
	delEndTime: null,
	delStatus: "0",
})

// 定义校验规则
const dataRules = ref({
	delRoleId: [{ required: true, message: "委托人不能为空", trigger: "blur" }],
	roleId: [{ required: true, message: "接收人不能为空", trigger: "blur" }],
	delStartTime: [{ required: true, message: "委托开始时间不能为空", trigger: "blur" }],
	delEndTime: [{ required: true, message: "委托结束时间不能为空", trigger: "blur" }],
	delStatus: [{ required: true, message: "委托状态不能为空", trigger: "blur" }],
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取DistPerson信息
	if (id) {
		form.id = id
		getDistPersonData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true

		await delegate(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getDistPersonData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
