<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :background="background"
      :layout="layout"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :total="total"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { scrollTo } from "@/utils/scroll-to"

const props = defineProps({
  total: {
    required: true,
    type: Number,
  },
  page: {
    type: Number,
    default: 1,
  },
  limit: {
    type: Number,
    default: 20,
  },
  pageSizes: {
    type: Array,
    default() {
      return [8, 10, 20, 30, 50]
    },
  },
  // 移动端页码按钮的数量端默认值5
  pagerCount: {
    type: Number,
    default: document.body.clientWidth < 992 ? 5 : 7,
  },
  layout: {
    type: String,
    default: "total, sizes, prev, pager, next, jumper",
  },
  background: {
    type: Boolean,
    default: true,
  },
  autoScroll: {
    type: Boolean,
    default: true,
  },
  hidden: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits()
const currentPage = computed({
  get() {
    return props.page
  },
  set(val) {
    emit("update:page", val)
  },
})
const pageSize = computed({
  get() {
    return props.limit
  },
  set(val) {
    emit("update:limit", val)
  },
})
function handleSizeChange(val) {
  if (currentPage.value * val > props.total) {
    currentPage.value = 1
  }
  emit("pagination", { page: currentPage.value, limit: val })
  if (props.autoScroll) {
    scrollTo(0, 800)
  }
}
function handleCurrentChange(val) {
  emit("pagination", { page: val, limit: pageSize.value })
  if (props.autoScroll) {
    scrollTo(0, 800)
  }
}
</script>

<style scoped lang="scss">
$input-height: 30px;
.pagination-container {
  padding: 15px;
  display: flex;
  justify-content: flex-end;
  width: 100%;
  .el-pagination {
    color: #677281;
    :deep(*) {
      .el-select__wrapper,
      .el-input__wrapper {
        height: $input-height;
        line-height: $input-height;
        min-height: $input-height;
      }
    }
  }

  :deep(.el-pagination) {
    .el-select {
      min-width: 100px;
      width: auto;
    }
    .el-pagination__editor.el-input {
      width: 45px;
      min-width: 45px;
      .el-input__wrapper {
        padding: 1px 5px;
      }
    }
    &.el-pagination--small {
      .el-select {
        min-width: 80px;
      }
      .el-pager li {
        height: 26px;
        line-height: 26px;
      }
      .el-input__wrapper {
        height: 26px;
        line-height: 26px;
        width: 38px;
      }
    }
    &.is-background {
      .el-pager li {
        height: $input-height;
        line-height: $input-height;
        background-color: #fff;
        border: 1px solid #e2e4e7;
        border-radius: 4px;
        &.is-active {
          border-color: transparent;
          background-color: var(--el-color-primary);
        }
      }
    }
  }
}
.pagination-container.hidden {
  display: none;
}
</style>
