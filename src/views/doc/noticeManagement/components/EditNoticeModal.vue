<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="850px"
      :before-close="handleClose"
      top="8vh"
  >
    <el-form
        ref="formRef"
        v-loading="loading"
        :model="form"
        label-width="100px"
        :rules="rules"
    >
      <el-form-item label="公告标题" prop="noticeTitle">
        <el-input v-model="form.noticeTitle" placeholder="请输入公告标题" clearable/>
      </el-form-item>

      <el-form-item label="公告类型" prop="noticeType">
        <dict-select
            v-model="form.noticeType"
            :options="sys_notice_type"
            value-type="string"
        />
      </el-form-item>

      <el-form-item label="内容" prop="noticeContent">
        <editor v-model="form.noticeContent" :min-height="192"/>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose"> 取消</el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { getNotice, addNotice, updateNotice } from "@/api/ess/doc/noticeManagementApi.ts"

import Editor from "@/components/Editor/index.vue"
import { rule } from "@/utils/validate.js";

const { proxy } = getCurrentInstance()

const title = ref("新增")
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)

const props = defineProps({
  sys_notice_type: Array,
})

const form = ref({
  noticeTitle: null,
  noticeType: null,
})
const rules = ref({
  noticeTitle: [
    { required: true, message: "请输入公告标题", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  noticeType: [{ required: true, message: "请选择公告类型", trigger: "change" }],
  noticeContent: [{ required: true, message: "请输入内容", trigger: "change" }],
})
const emit = defineEmits(["change", "refresh"])

const open = (item) => {
  dialogVisible.value = true
  title.value = item?.id ? "编辑" : "新增"
  nextTick(() => {
    if (item?.id) {
      form.value = { ...form, ...item }
      getNoticeInfo(item.id)
    }
  })
}

function getNoticeInfo(id) {
  getNotice(id)
      .then((res) => {
        form.value = { ...form, ...res.object }
      })
      .finally(() => {
        loading.value = false
      })
}

// 取消
const handleClose = () => {
  form.value.id = null
  proxy.resetForm("formRef")
  dialogVisible.value = false
}

// 确定
const submitForm = () => {
  proxy.$refs["formRef"].validate(async (valid) => {
    if (!valid) {
      return
    }

    loading.value = true

    // 防止循环序列化报错
    const submitData = {
      id: form.value?.id || null,
      noticeTitle: form.value.noticeTitle,
      noticeType: form.value.noticeType,
      noticeContent: form.value.noticeContent,
    }
    const actionUrl = form.value.id ? updateNotice : addNotice
    try {
      await actionUrl(submitData)
      handleSuccess()
    } catch (error) {
      console.error("提交表单时出错:", error)
    } finally {
      loading.value = false
    }
  })
}

const handleSuccess = () => {
  proxy.$modal.msgSuccess(form.value.id ? "修改成功" : "新增成功")
  form.value.id ? emit("change") : emit("refresh")
  handleClose()
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
