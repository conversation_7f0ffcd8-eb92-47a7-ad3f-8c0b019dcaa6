<template>
  <el-dialog
    v-model="dialogVisible"
    title="草稿箱"
    width="800px"
    :before-close="handleClose"
    top="5vh"
  >
    <CustomTable
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      :data="tableData"
      custom
      :hasToolbar="false"
      :loading="loading"
      :total="total"
      @reload="getList"
      ref="customTableRef"
    >
      <vxe-column type="seq" width="70" fixed="left" />
      <vxe-column title="工单编号" field="code" show-overflow />
      <vxe-column title="流程名称" field="formName" show-overflow />
      <vxe-column title="创建时间" field="createTime" show-overflow />
      <vxe-column title="操作" fixed="right" width="100">
        <template #default="{ row }">
          <TableColOptBtn
            :key="Math.random()"
            :buttons="[
              {
                text: '继续编辑',
                click: () => handleEdit(row)
              }
            ]"
          />
        </template>
      </vxe-column>
    </CustomTable>
  </el-dialog>
</template>

<script setup>
import { fetchList } from '@/api/order/run-application'

const dialogVisible = ref(false)
const loading = ref(true)
const total = ref(0)
const tableData = ref([])
const form = ref({})

const queryParams = reactive({
  current: 1,
  size: 10,
  status: '-1' // 草稿箱
})

const emit = defineEmits(['edit'])
const open = (row) => {
  form.value = { ...row }
  queryParams.defFlowId = row.defFlowId
  dialogVisible.value = true
  getList()
}

async function getList() {
  loading.value = true
  const res = await fetchList(queryParams)
  tableData.value = res.object.records
  total.value = res.object.total
  loading.value = false
}

function handleClose() {
  dialogVisible.value = false
  queryParams.page = 1
  tableData.value = []
  total.value = 0
}

function handleEdit(row) {
  emit('edit', row)
}

defineExpose({
  open,
  getList
})
</script>
<style scoped lang="scss"></style>
