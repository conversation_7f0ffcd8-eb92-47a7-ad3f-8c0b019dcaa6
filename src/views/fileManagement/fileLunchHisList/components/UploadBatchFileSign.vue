<template>
  <div class="w-full upload-progress-wrapper" style="position: relative">
    <el-upload
      v-loading="isUploading"
      element-loading-text="正在上传并校验结果"
      ref="uploadRef"
      :action="uploadUrl"
      :file-list="fileList"
      :limit="1"
      name="dataFile"
      accept=".xlsx,.xls"
      :data="{ essTemplateId,flowBatchPublishId:flowBatchPublishIds }"
      :on-exceed="handleExceed"
      :show-file-list="false"
      :headers="uploadHeaders"
      :on-change="handleFileChange"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :before-upload="handleBeforeUpload"
      class="upload-drag-box-container"
      drag
    >
      <template #trigger>
        <div class="upload-drag-box" v-if="!flowBatchPublishId">
          <img src="@/assets/images/file/drag-icon.png" alt="" />
          <div class="upload-drag-text">
            <p>
              将文件拖拽至此区域，或
              <el-link type="primary" underline="always" class="m-t-[-2px] m-l-4px">
                点击上传文件
              </el-link>
            </p>
            <p class="tip">支持格式{{ acceptedFileTypes }}</p>
          </div>
        </div>
        <UploadFileInfo v-else-if="fileRaw && flowBatchPublishId" :file="fileRaw" />
      </template>
    </el-upload>
    <div class="is-upload-success" v-if="flowBatchPublishId && !isUploading">
      <div class="is-upload-success__title">本次上传结果:</div>
      <div class="ml-10px mt-5px">
        <div v-html="coloredUploadResult"></div>
      </div>
      <el-button
        :disabled="uploadResult.errorCount == 0"
        type="primary"
        class="min-h-32px! w-134px ml-10px mt-12px"
        @click="downloadError"
      >
        <svg-icon name="local-download-line" :size="16" class="mr-2px"/>
        下载不通过原因
      </el-button>
    </div>
  </div>
</template>

<script name="UploadBatchFileSign" setup>
import { throttle } from "lodash-es"
import { essPrefix } from "@/config/constant"
import { getToken } from "@/utils/auth"
import { genFileId } from "element-plus"
const UploadFileInfo = defineAsyncComponent(() =>
  import("@/components/FileUpload/UploadFileInfo.vue")
)

const emit = defineEmits(["exportPublishId"])

const { proxy } = getCurrentInstance()

const props = defineProps({
  essTemplateId: {
    type: String,
  },
  flowBatchPublishIds:{
    type:String
  }
})

// ------------------文件上传相关start------------------
const fileRaw = ref()
// 文件上传相关配置
const acceptedFileTypes = ".xls,.xlsx"
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${getToken()}`,
}))
const uploadUrl =
  import.meta.env.VITE_APP_BASE_API + essPrefix + "/flowInfoBatch/importError "
const fileList = ref([])
const uploadRef = ref()
const flowBatchPublishId = ref(null)
const isUploading = ref(false) // 上传loading

/**
 * 处理文件超出限制
 */
const handleExceed = (files) => {
  uploadRef.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  uploadRef.value.handleStart(file)
  uploadRef.value.submit()
}

/**
 * 上传前校验文件类型和大小
 */
const handleBeforeUpload = (file) => {
  if (!validateFile(file)) {
    return false // 阻止上传
  }
  isUploading.value = true
  fileRaw.value = file
  return true
}

/**
 * 处理文件选择变化
 */
const handleFileChange = (file) => {
  fileRaw.value = file
}

/**
 * 验证文件类型和大小
 */
const validateFile = (file) => {
  const allowedTypes = acceptedFileTypes
    .split(",")
    .map((type) => type.replace(".", ""))
    .filter(Boolean)

  const fileType = file.name.split(".").pop().toLowerCase()

  if (!allowedTypes.includes(fileType)) {
    proxy.$modal.msgError("不支持的文件格式，请上传指定类型文件")
    return false
  }
  return true
}

const responseUpload = ref('')

// 添加带颜色的上传结果计算属性
const coloredUploadResult = computed(() => {
  if (!responseUpload.value) return ''
  // 匹配数字+条，并为校验通过的添加绿色样式
  let result = responseUpload.value.replace(/(\d+条)(?=校验通过)/g, '<span class="color-primary">$1</span>')
  // 匹配数字+条，并为校验不通过的添加红色样式
  result = result.replace(/(\d+条)(?=校验不通过)/g, '<span class="text-danger">$1</span>')
  return result
})
/**
 * 处理文件上传成功
 */
const handleUploadSuccess = (res) => {
  if (+res.code !== 200) {
    handleUploadError(res)
    return
  }
  responseUpload.value = res.object.msg
  emit('exportPublishId', res)
  flowBatchPublishId.value = props.flowBatchPublishIds
  queryValidateResult(res)
}

/**
 * 处理文件上传失败
 */
const handleUploadError = (error) => {
  isUploading.value = false
  if(!error.object) return proxy.$modal.alertWarning(error.message)
  proxy.$modal.alertWarning(error.object)
}


// 上传校验结果
const uploadResult = ref({}) // 上传结果
const queryValidateResult = (res) => {
  uploadResult.value.errorCount = res.object.errorCount
  uploadResult.value.passCount = res.object.normalCount
  isUploading.value = false
  
}
// 上传结果总数
const uploadResultTotal = computed(() => {
  return Number(uploadResult.value.passCount) + Number(uploadResult.value.errorCount)
})

const downloadError = throttle(function () {
  proxy.download(
    `${essPrefix}/flowInfoBatch/exportFailReason?flowBatchPublishId=${props.flowBatchPublishIds}`,
    {},
    `不通过原因.xlsx`
  )
}, 800)
</script>

<style scoped lang="scss">
.upload-drag-box-container {
  margin-bottom: 5px;
  width: 100%;
  border-radius: 6px;
  :deep(.el-upload) {
    width: 100%;
    height: 100%;
    justify-content: flex-start;
    .el-upload-dragger {
      border: 1px dashed var(--el-color-primary-light-6);
      padding: 30px 20px;
    }
  }
}
.upload-drag-box {
  display: flex;
  align-items: center;
  height: 100%;
  img {
    width: 60px;
  }
  .upload-drag-text {
    font-size: 14px;
    text-align: left;
    p {
      line-height: 20px;
      margin: 0;
      display: flex;
      align-items: center;
    }
    .tip {
      color: #9b9b9b;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  .el-link {
    line-height: 18px;
  }
}

.is-upload-success {
  background-color: var(--el-color-primary-light-9);
  border-radius: 6px;
  padding: 12px 15px 15px;
  margin-top: 10px;
  &__title {
    @apply dot;
  }
}
</style>
