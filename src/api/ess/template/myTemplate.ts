import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

import { FlowInfoPageDto } from './type'

// 查询我的模板列表 带分页
export function getMyTemplateListPage(data: object) {
  return request({
    url: essPrefix + "/my/template/listPage",
    method: "post",
    data,
    headers: {
      repeatSubmit: false,
    },
  })
}

// 创建模版
export function getCreateMyTemplateUrl(data: object) {
  return request({
    url: essPrefix + "/my/template/create",
    method: "post",
    data,
  })
}
// 获取模板签署方类型信息
export function getTemplateRecipientsAPI(essTemplateId: string) {
  return request({
    url: essPrefix + "/my/template/templateRecipients/" + essTemplateId,
    method: "get",
  })
}
// 保存草稿-模板
export function saveDraftTemplateAPI(data: any) {
  return request({
    url: essPrefix + "/my/template/draft",
    method: "post",
    data,
  })
}
// 使用嵌入页发起签署流程-模板
export function initiateTemplateAPI(flowId: any) {
  return request({
    url: essPrefix + "/my/template/initiate/" + flowId,
    method: "get",
  })
}
// 草稿箱列表
export function getTemplateDraftListPageAPI(data: FlowInfoPageDto) {
  return request({
    url: essPrefix + "/my/template/listDraftPage",
    method: "post",
    data
  })
}
// 草稿箱详情
export function getTemplateDraftInfoAPI(id: string) {
  return request({
    url: essPrefix + "/my/template/draft/" + id,
    method: "get",
  })
}
