export default {
	flowrule: {
		index: "#",
		importFlowRuleTip: "导入条件/人员规则",
		id: "",
		flowKey: "业务KEY",
		defFlowId: "流程名称",
		flowNodeId: "节点名称",
		flowNodeRelId: "连线名称",
		roleId: "参与者",
		jobType: "参与者类型",
		type: "数据类型",
		valType: "数据值类型",
		groupId: "同组条件ID",
		groupsType: "条件组关系",
		groupType: "组内条件关系",
		varKeyVal: "表单字段",
		operator: "运算符",
		varVal: "校验值",
		paramFrom: "参数来源",
		paramValType: "参数值类型",
		targetProp: "目标属性",
		paramType: "参数类型",
		createUser: "创建人",
		createTime: "创建时间",
		updateUser: "更新人",
		updateTime: "更新时间",
		inputIdTip: "请输入",
		inputFlowKeyTip: "请输入业务KEY",
		inputDefFlowIdTip: "请选择流程名称",
		inputFlowNodeIdTip: "请先选择流程名称",
		inputFlowNodeRelIdTip: "请先选择连线名称",
		inputRoleIdTip: "请选择参与者",
		inputJobTypeTip: "请输入参与者类型",
		inputTypeTip: "请选择数据类型",
		inputValTypeTip: "请选择模式",
		inputGroupIdTip: "请输入同组条件ID",
		inputGroupsTypeTip: "请选择条件组关系",
		inputGroupTypeTip: "请选择组内条件关系",
		inputVarKeyValTip: "请输入表单字段",
		inputOperatorTip: "请选择运算符",
		inputVarValTip: "请输入校验值",
		inputParamFromTip: "请选择参数来源",
		inputParamValTypeTip: "请选择参数值类型",
		inputTargetPropTip: "请输入目标属性",
		inputParamTypeTip: "请选择参数类型",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputUpdateUserTip: "请输入更新人",
		inputUpdateTimeTip: "请输入更新时间",
	},
}
