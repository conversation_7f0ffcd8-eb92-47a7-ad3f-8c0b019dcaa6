<template>
    <div style="width: 100%">
        <el-input v-model="data.modelValue" @blur="changeModelValue" :disabled="props.disabled" clearable>
        </el-input>
    </div>
</template>

<script setup lang="ts" name="UserRolePickerIndex">
    import {useMessage} from "@/hooks/message";

    const props = defineProps({
        modelValue: {
            type: String,
            default: null,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    });

    const emits = defineEmits(['update:modelValue']);

    const $message = useMessage();
    const data = reactive({
        modelValue: null
    })

    const changeModelValue = () => {
        if (data.modelValue) {
            emits('update:modelValue', data.modelValue);
        } else {
            emits('update:modelValue', null);
        }
    };

    onMounted(() => {
        data.modelValue = props.modelValue
    })
</script>
