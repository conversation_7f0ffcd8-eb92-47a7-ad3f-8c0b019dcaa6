export default {
	runReject: {
		index: "#",
		importrunRejectTip: "导入驳回任务记录",
		id: " id",
		flowKey: "流程名称",
		fromFlowNodeId: "来源节点名称",
		toFlowNodeId: "到达节点名称",
		handleUserId: "指定被驳回人",
		status: "状态",
		createUser: "创建人",
		createTime: "创建时间",
		defFlowId: "流程名称",
		flowInstId: "流程实例ID",
		fromRunNodeId: "来源节点名称",
		fromRunJobId: "来源任务名称",
		toRunNodeId: "到达节点名称",
		toRunJobId: "达到任务名称",
		remark: "驳回意见",

		inputIdTip: "请输入ID",
		inputFlowKeyTip: "请输入流程名称",
		inputFromFlowNodeIdTip: "请输入来源节点名称",
		inputToFlowNodeIdTip: "请输入到达节点名称",
		inputHandleUserIdTip: "请输入指定被驳回人",
		inputStatusTip: "请输入节点状态",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputDefFlowIdTip: "请输入流程定义ID",
		inputFlowInstIdTip: "请输入流程实例ID",
		inputFromRunNodeIdTip: "请输入来源节点名称",
		inputFromRunJobIdTip: "请输入来源任务名称",
		inputToRunNodeIdTip: "请输入到达节点名称",
		inputToRunJobIdTip: "请输入达到任务名称",
		inputRemarkTip: "请输入驳回意见",

	},
}
