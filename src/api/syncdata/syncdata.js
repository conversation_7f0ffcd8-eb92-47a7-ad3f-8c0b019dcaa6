import request from "@/utils/request.js"
import { eduUser } from "@/config/constant.js"

/**
 * 部门用户数据同步
 * @param data
 * @returns {*}
 */
export function syncDepartmentInfos(data) {
	return request({
		url: eduUser + "/zsDataSyncApi/syncDepartmentInfos",
		method: "post",
		data: data,
	})
}

// 用户数据同步
export function syncUserInfos(data) {
	return request({
		url: eduUser + "/zsDataSyncApi/syncUserInfos",
		method: "post",
		data: data,
	})
}
// 角色数据同步
export function syncRoleInfos(data) {
	return request({
		url: eduUser + "/zsDataSyncApi/syncRoleInfos",
		method: "post",
		data: data,
	})
}
