<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <form-render
        ref="formCreateRef"
        :curr-flow-form="props.currFlowForm"
        :initFormPermPrint="initFormPermPrint"
      />
    </div>
    <footer v-if="data.submitBtn" class="el-dialog__footer">
      <span class="dialog-footer">
        <template v-if="props.currFlowForm.status !== DIC_PROP.ORDER_STATUS[0].value">
          <el-button type="primary" :disabled="loading" @click="submitForm">
            操作
          </el-button>
          <el-button type="primary" :disabled="loading" @click="handleTempStore">
            暂存
          </el-button>
        </template>
        <template v-else>
          <el-button type="primary" :disabled="loading" @click="submitForm">
            修改
          </el-button>
        </template>
      </span>
    </footer>
  </div>
</template>

<script setup lang="ts" name="RunApplicationInitiate">
import * as runApplication from '@/api/order/run-application'
import { paramsFilter } from '@/flow'
import { useI18n } from 'vue-i18n'
import { validateNull } from '@/utils/validate'
import { deepClone } from '@/utils/index'
import * as common from '@/flow/support/common'
import { doInitiateForm, doTempStore, initFormMethods, initJobDataByApp } from '../index'
import { handleFormStartPerm } from '@/flow/utils/form-perm'
import { currFormIsView } from '@/api/order/order-key-vue'

const FormRender = defineAsyncComponent(() => import('@/flow/components/form-create/render.vue'))
const formCreateRef = ref(null)

const { t } = useI18n()
const $emit = defineEmits(['handleInitiateOrder', 'success'])
const loading = ref(false)
const props = defineProps({
  currFlowForm: {
    type: Object,
    default: {}
  }
})

const data = reactive({
  submitBtn: true
})

const $route = useRoute()
function initJobData() {
  initJobDataByApp($route, handleGetObj, null)
}

function handleGetObj(id) {
  runApplication.getObj(id).then((resp) => {
    let form = resp.object ? resp.object : {}
    Object.assign(props.currFlowForm, form)
  })
}

async function submitForm() {
  await doInitiateForm(loading, props, data, $route, formCreateRef, $emit, saveInitData, t)
}

const methods = initFormMethods(formCreateRef, data)

function handleTempStore() {
  doTempStore(loading, data, $route, formCreateRef, $emit, saveInitData)
}
async function initFormPermPrint(formInfo) {
  // 处理表单权限
  let res = await handleFormStartPerm(
    null,
    null,
    formInfo,
    props.currFlowForm.defFlowId,
    null,
    props.currFlowForm.type
  )
  await currFormIsView(methods, res.elTab, true, res.callback, res.widgetList)
  return res.elTab
}

function saveInitData(form) {
  form = paramsFilter(form)
  props.currFlowForm.formData = validateNull(form) ? undefined : form
  let formJson = deepClone(props.currFlowForm)
  let clone = { operType: props.currFlowForm.operType, form: formJson }
  common.handleCloneSubmit(clone)
  formJson.formData = JSON.stringify(formJson.formData)
  return formJson
}

async function getFormData() {
  return await doInitData(formCreateRef, saveInitData)
}

// 暴露变量
defineExpose({
  getFormData
})

// 监听双向绑定
watch(
  () => props.currFlowForm.id,
  () => {
    initJobData()
  }
)

onMounted(() => {
  initJobData()
})
</script>

<style lang="scss" scoped>
.el-dialog__footer {
  text-align: center;
  margin-top: 10px;

  .dialog-footer {
    text-align: center;
  }
}
</style>
