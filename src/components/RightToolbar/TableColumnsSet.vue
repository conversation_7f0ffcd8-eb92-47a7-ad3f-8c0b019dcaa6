<template>
	<div class="column-container">
		<el-checkbox-group v-model="selectedColumns">
			<div v-for="column in columns" :key="column.prop" class="column-item">
				<el-icon><Rank /></el-icon>
				<el-checkbox :label="column.prop">
					{{ column.label }}
				</el-checkbox>
				<el-icon><ArrowLeft /></el-icon>
				<el-icon><ArrowRight /></el-icon>
			</div>
		</el-checkbox-group>
	</div>
</template>

<script setup>

const props = defineProps({
	columns: Array,
})

const selectedColumns = ref(props.columns.map(col => col.prop))
</script>

<style scoped lang="scss">

.column-container{

}

</style>
