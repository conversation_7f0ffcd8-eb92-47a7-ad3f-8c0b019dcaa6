<script setup lang="ts">
defineOptions({ name: "TableHeaderOperation" })

interface Props {
	itemAlign?: UI.Align;
	disabledDelete?: boolean;
	loading?: boolean;
}

defineProps<Props>()

interface Emits {
	(e: "add"): void;
	(e: "delete"): void;
	(e: "refresh"): void;
}

const emit = defineEmits<Emits>()

const columns = defineModel<UI.TableColumnCheck[]>("columns", {
	default: () => [],
})

function add() {
	emit("add")
}

function batchDelete() {
	emit("delete")
}

function refresh() {
	emit("refresh")
}
</script>

<template>
	<!-- <NSpace :align="itemAlign" wrap justify="end" class="lt-sm:w-200px"> -->
	<ElSpace
		direction="horizontal"
		wrap
		justify="end"
		class="lt-sm:w-200px"
	>
		<slot name="prefix" />
		<slot name="default">
			<ElButton plain type="primary" @click="add">
				<template #icon>
					<icon-ic-round-plus class="text-icon" />
				</template>
				新增
			</ElButton>
			<ElPopconfirm title="是否确认删除" @confirm="batchDelete">
				<template #reference>
					<ElButton type="danger" plain :disabled="disabledDelete">
						<template #icon>
							<icon-ic-round-delete class="text-icon" />
						</template>
						批量删除
					</ElButton>
				</template>
			</ElPopconfirm>
		</slot>
		<ElButton @click="refresh">
			<template #icon>
				<icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
			</template>
			刷新
		</ElButton>
		<TableColumnSetting v-model:columns="columns" />
		<slot name="suffix" />
	</ElSpace>
</template>

<style scoped></style>
