<template>
	<el-dialog
		v-model="dialogVisible"
		:title="title"
		width="700px"
		append-to-body
	>
		<el-form
			ref="deptRef"
			:model="form"
			:rules="rules"
			label-width="100px"
			inline
		>
			<el-form-item v-if="form.parentId && +form.parentId !== 0" label="上级部门" prop="parentId">
				<el-tree-select
					v-model="form.parentId"
					:data="deptOptions"
					:props="{ value: 'deptId', label: 'deptName', children: 'children' }"
					value-key="deptId"
					placeholder="选择上级部门"
					check-strictly
					style="width: 535px"
				/>
			</el-form-item>
			<el-form-item label="部门名称" prop="deptName">
				<el-input
					v-model="form.deptName"
					placeholder="请输入部门名称"
					maxlength="50"
					clearable
				/>
			</el-form-item>
			<el-form-item label="显示排序" prop="orderNum">
				<el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
			</el-form-item>
			<el-form-item label="负责人" prop="leader">
				<el-input
					v-model="form.leader"
					placeholder="请输入负责人"
					maxlength="20"
					clearable
				/>
			</el-form-item>
			<el-form-item label="联系电话" prop="phone">
				<el-input
					v-model="form.phone"
					placeholder="请输入联系电话"
					maxlength="11"
					clearable
				/>
			</el-form-item>
			<el-form-item label="邮箱" prop="email">
				<el-input
					v-model="form.email"
					placeholder="请输入邮箱"
					maxlength="50"
					clearable
				/>
			</el-form-item>
			<el-form-item label="部门状态">
				<el-radio-group v-model="form.status">
					<el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">
						{{ dict.label }}
					</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="handleClose">
				取消
			</el-button>
			<el-button type="primary" @click="submitForm">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import {
	listDept,
	getDept,
	delDept,
	addDept,
	updateDept,
	listDeptExcludeChild,
} from "@/api/system/dept"
const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const title = ref("")
const deptOptions = ref([])
const props = defineProps(["sys_normal_disable"])
const data = reactive({
	form: {},
	rules: {
		parentId: [{ required: true, message: "上级部门不能为空", trigger: "blur" }],
		deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
		orderNum: [{ required: true, message: "显示排序不能为空", trigger: "blur" }],
		email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
		phone: [
			{
				pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
				message: "请输入正确的手机号码",
				trigger: "blur",
			},
		],
	},
})

const { form, rules } = toRefs(data)
function reset() {
	form.value = {
		deptId: undefined,
		parentId: undefined,
		deptName: undefined,
		orderNum: 0,
		leader: undefined,
		phone: undefined,
		email: undefined,
		status: "0",
	}
	proxy.resetForm("deptRef")
}
const handleClose = () => {
	reset()
	dialogVisible.value = false
}
const open = (row, type = "添加部门") => {
	title.value = type
	dialogVisible.value = true
	reset()
	let getDeptTreeUrl = type == "修改部门" ? listDeptExcludeChild(row.deptId) : listDept()
	getDeptTreeUrl.then(res => {
		deptOptions.value = proxy.handleTree(res.data, "deptId")
	})
	if (type == "修改部门") {
		getDept(row.deptId).then(res => {
			form.value = res.data
		})
	}
	else {
		if (row != undefined) {
			form.value.parentId = row.deptId
		}
	}
}

function submitForm() {
	proxy.$refs["deptRef"].validate(valid => {
		if (valid) {
			let actionUrl = form.value.deptId ? updateDept : addDept
			actionUrl(form.value).then(response => {
				proxy.$modal.msgSuccess(form.value.deptId ? "修改成功" : "新增成功")
				emit("change")
				handleClose()
			})
		}
	})
}

defineExpose({
	open,
})
</script>

<style scoped lang="scss"></style>
