<template>
  <div class="app-container">
    <div class="components-table">
      <el-form
        ref="queryRef"
        :model="queryParams"
        label-width="85px"
        class="white-form-box"
        inline
      >
        <el-form-item label="所属部门" prop="deptId">
          <dept-picker v-model="queryParams.deptId" />
        </el-form-item>
        <el-form-item label="姓名" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入申请人"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属业务线" prop="busLineId">
          <BusinessSelect v-model="queryParams.busLineId" />
        </el-form-item>
        <el-form-item label="发起时间" prop="timeRange">
          <el-date-picker
            v-model="queryParams.timeRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="common-input-width"
            clearable
            :default-time="dateDefaultTime"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <CustomTable
        ref="customTableRef"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :has-toolbar="false"
        :data="tableData"
        :loading="loading"
        :total="total"
        :opt-width="180"
        @reload="getList"
      >
        <template #actions>
          <el-button
            type="primary"
            v-auths="['ess:personData:export']"
            @click="handleExport"
          >
            导出
          </el-button>
        </template>
        <vxe-column type="seq" width="60" />
        <vxe-column title="姓名" field="userName" min-width="140" show-overflow />
        <vxe-column title="所属部门" field="deptName" min-width="140" show-overflow />
        <vxe-column title="业务线" field="busLineName" min-width="140" show-overflow />
        <vxe-column title="已完成签署" field="finishSign" min-width="140" show-overflow>
          <template #header>
            <div class="table-opt-set">
              已完成签署
              <div class="flex flex-col">
                <el-icon size="8" class="pt-2px" @click="toSort('finishSign')">
                  <ArrowUpBold
                    :color="queryParams?.orderByFinishSignDesc ? '#909090' : '#24a87e'"
                  />
                </el-icon>
                <el-icon size="8" class="pb-2px" @click="toSort('finishSign')">
                  <ArrowDownBold
                    :color="queryParams?.orderByFinishSignDesc ? '#24a87e' : '#909090'"
                  />
                </el-icon>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column title="签署中" field="signing" min-width="140" show-overflow>
          <template #header>
            <div class="table-opt-set">
              签署中
              <div class="flex flex-col">
                <el-icon size="8" class="pt-2px" @click="toSort('signing')">
                  <ArrowUpBold
                    :color="queryParams?.orderBySigningDesc ? '#909090' : '#24a87e'"
                  />
                </el-icon>
                <el-icon size="8" class="pb-2px" @click="toSort('signing')">
                  <ArrowDownBold
                    :color="queryParams?.orderBySigningDesc ? '#24a87e' : '#909090'"
                  />
                </el-icon>
              </div>
            </div>
          </template>
        </vxe-column>
        >
      </CustomTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="Pdoc">
import { getPersonDataListAPI } from "@/api/ess/dataStatistics/fileStatusAndPerson.ts"
import { essPrefix } from "@/config/constant"
import BusinessSelect from "@/views/components/BusinessSelect.vue"

const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(false)
const total = ref(0)

const queryParams = reactive({
  page: 1,
  limit: 10,
  userName: "",
  busLineId: "",
  timeRange: [],
  deptId: "",
  orderByFinishSignDesc: true,
  orderBySigningDesc: true,
})

async function getList() {
  try {
    loading.value = true
    queryParams.startTime = queryParams.timeRange?.[0]
    queryParams.endTime = queryParams.timeRange?.[1]
    let res = await getPersonDataListAPI(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    essPrefix + "/personData/export",
    {
      ...queryParams,
    },
    `个人文件统计_${new Date().getTime()}.xlsx`
  )
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 排序分类操作 */
function toSort(type) {
  switch (type) {
    case "finishSign":
      queryParams.orderByFinishSignDesc = !queryParams.orderByFinishSignDesc
      break
    case "signing":
      queryParams.orderBySigningDesc = !queryParams.orderBySigningDesc
      break
  }
  queryParams.page = 1
  getList()
}

getList()
</script>

<style scoped lang="scss"></style>
