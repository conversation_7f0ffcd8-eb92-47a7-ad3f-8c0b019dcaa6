<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="90px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item :label="$t('wsNotice.flowInstId')" prop="flowInstId">
						<el-input v-model="form.flowInstId" :placeholder="t('wsNotice.inputFlowInstIdTip')" disabled />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('wsNotice.flowKey')" prop="flowKey">
						<el-select
							v-model="form.flowKey"
							:placeholder="t('wsNotice.inputFlowKeyTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowKey"
								:key="index"
								:label="item.flowName"
								:value="item.flowKey"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('wsNotice.userId')" prop="userId">
						<el-select
							v-model="form.userId"
							:placeholder="t('wsNotice.inputUserIdTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in dicData.userId"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('wsNotice.type')" prop="type">
						<el-radio-group v-model="form.type">
							<el-radio v-for="(item, index) in DIC_PROP.MSG_TYPE" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('wsNotice.jobType')" prop="jobType">
						<el-select
							v-model="form.jobType"
							:placeholder="t('wsNotice.inputJobTypeTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.JOB_MSG_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('wsNotice.status')" prop="status">
						<el-select
							v-model="form.status"
							:placeholder="t('wsNotice.inputStatusTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.NOTICE_STATUS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('wsNotice.data')" prop="data">
						<el-input v-model="form.data" type="textarea" :placeholder="t('wsNotice.inputDataTip')" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="WsNoticeDialog">

import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/jsonflow/ws-notice"
import { useI18n } from "vue-i18n"
import { rule } from "@/utils/validate"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
const emit = defineEmits(["refresh"])

const { t } = useI18n()

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "userId" })
const onCascade = onCascadeChange(cascadeDic, { key: "flowInstId", cascades: ["flowKey"] })
onMounted(() => {
	onLoad(dicData)
})
// 提交表单数据
const form = reactive({
	flowKey: "",
	userId: "",
	type: "",
	jobType: "",
	status: "",
	data: "",
})

// 定义校验规则
const dataRules = ref({
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取WsNotice信息
	if (id) {
		form.id = id
		getWsNoticeData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true
		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getWsNoticeData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
		onCascade(form)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
