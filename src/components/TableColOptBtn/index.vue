<template>
  <div class="button-group">
    <el-space :size="10">
      <el-button
        v-for="(item, index) in visibleButtons"
        :key="index"
        link
        :type="item.type ? item.type : 'primary'"
        :icon="item.icon"
        :loading="!!item.loading"
        @click="item.click"
      >
        {{ item.text }}
      </el-button>
      <el-popover
        v-if="showMore"
        placement="right-start"
        :width="140"
        trigger="hover"
        popper-class="table-operation-popover"
      >
        <template #reference>
          <el-button type="primary" link icon="MoreFilled" class="more-icon" />
        </template>
        <div class="operation-btn-content">
          <template v-for="(item, index) in hiddenButtons" :key="index">
            <el-button plain type="info" @click="item.click">
              {{ item.text }}
            </el-button>
          </template>
        </div>
      </el-popover>
    </el-space>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance()
const props = defineProps({
  buttons: {
    type: Array,
    default: () => [],
  },
})

const isShowButtons = computed(() => {
  return props.buttons?.filter((item) => {
    let hasPermi = item.permission ? proxy.$auth.hasPermiOr(item.permission) : true
    return !item.hidden && hasPermi
  })
})
const visibleButtons = computed(() => {
  if (isShowButtons.value.length <= 3) {
    return isShowButtons.value
  } else {
    return isShowButtons.value.slice(0, 2)
  }
})
const hiddenButtons = computed(() => {
  if (isShowButtons.value.length <= 3) {
    return []
  } else {
    return isShowButtons.value.slice(2)
  }
})
const showMore = computed(() => {
  return isShowButtons.value.length > 3
})
</script>

<style scoped lang="scss">
.operation-btn {
  padding: 2px 4px;
}
.operation-btn-content {
  display: flex;
  flex-direction: column;
  gap: 11px;
  :deep(.el-button) {
    display: block;
    width: 100%;
    height: 32px !important;
    min-height: 32px !important;
    border-radius: 6px;
    padding: 0 5px;
  }
  :deep(.el-button + .el-button) {
    margin-left: 0;
  }
  :deep(.el-button--info.is-plain) {
    background: #fff;
    color: #38383a;
    border-color: #d1d1d1;
    &:hover {
      color: #fff;
      background: var(--el-color-primary);
      border-color: var(--el-color-primary);
    }
  }
}
</style>

<style lang="scss">
.table-operation-popover {
  padding: 12px 15px !important;
  min-width: 135px !important;
}
</style>
