<template>
  <div class="flow-simple-design">
    <div class="design-wrapper">
      <div
        class="design-node-item"
        :class="{ 'sort-link': disabled }"
        v-for="(item, index) in nodes"
        :key="item.nodeId"
      >
        <div class="node-name" v-if="index === 0 || index === nodes.length - 1">
          <el-icon v-if="index === nodes.length - 1"><Select /></el-icon>
          <span>{{ item.nodeName }}</span>
        </div>
        <div
          class="node-item"
          :class="{ 'cursor-pointer': !disabled }"
          v-else
          @click="editAuditUser(item, index)"
        >
          <div class="node-item__title">
            <span>{{ item.nodeName || "请输入节点名称" }}</span>
            <img src="@/assets/images/icon/white-user.png" alt="" />
          </div>
          <div class="flex-x-between p-y-13px p-x-15px">
            <div
              class="content-users"
              :class="{ 'color-#AAAAAA': !item.userOrRole.length }"
              v-tooltip
            >
              {{ !item.userOrRole.length ? "请选择" : userOrRoleNames(item) }}
            </div>
            <el-icon class="font-600 color-#707070" v-if="!disabled"
              ><ArrowRight
            /></el-icon>
          </div>
          <el-icon class="remove-btn" v-if="!disabled" @click="remove(index)"
            ><SemiSelect
          /></el-icon>
        </div>
        <div class="link-box" v-if="index !== nodes.length - 1">
          <div class="arrow-line"></div>
          <el-icon><CaretBottom /></el-icon>
          <img
            @click="addNode(index)"
            class="add-btn"
            src="@/assets/images/icon/orange-add.png"
            alt=""
            v-if="!disabled"
          />
        </div>
      </div>
    </div>

    <EditAuditUserModal ref="editAuditUserRef" @confirm="setAuditUser" />
  </div>
</template>

<script setup>
const EditAuditUserModal = defineAsyncComponent(() => import("./EditAuditUserModal.vue"))
defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
})

const nodes = defineModel("nodes")
const defaultNode = {
  userOrRole: [],
  approveMethod: "",
  nodeName: "",
}

const editAuditUserRef = ref(null)
const updateIndex = ref(null)

// 新增节点
function addNode(index) {
  nodes.value.splice(index + 1, 0, { ...defaultNode })
}
// 删除节点
function remove(index) {
  nodes.value.splice(index, 1)
}

// 选择节点人员
function editAuditUser(item, index) {
  if (props.disabled) return
  updateIndex.value = index
  editAuditUserRef.value.open(item)
}

// 获取getUserOrRoleNames
function userOrRoleNames(item) {
  return item.userOrRole.map((i) => i.userOrRoleName).join("、")
}

function setAuditUser(item) {
  nodes.value[updateIndex.value] = { ...item }
}
</script>

<style scoped lang="scss">
.flow-simple-design {
  background-image: url(@/assets/images/flow-bg.png);
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  padding: 40px 0;
  overflow-y: auto;
  position: relative;
}

.design-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;

  .design-node-item {
    position: relative;
    &:first-child,
    &:last-child {
      color: var(--el-color-primary);
      background: var(--el-color-primary-light-8);
      width: 100px;
      height: 40px;
      line-height: 40px;
      border-radius: 50px;
      .node-name {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    &:last-child {
      background: var(--el-color-primary);
      color: #fff;
      .el-icon {
        margin-right: 5px;
        font-size: 10px;
      }
    }
    &.sort-link {
      margin-bottom: 60px;
      &:not(:last-child) {
        margin-bottom: 56px;
      }
      .link-box .arrow-line {
        height: 50px;
      }
    }
  }
  .design-node-item:not(:last-child) {
    margin-bottom: 100px;
  }
  .design-node-item:not(:first-child):not(:last-child) {
    .node-item {
      background: #ffffff;
      border-radius: 4px;
      width: 190px;
      position: relative;
      box-shadow: 0px 3px 6px 1px rgba(72, 152, 255, 0.09);
      .remove-btn {
        @apply absolute  flex-center h-18px w-18px rounded-50% font-600 text-12px color-white bg-#f94e4f;
        right: -10px;
        top: -10px;
        cursor: pointer;
        display: none;
      }
      &:hover {
        box-shadow: 0px 3px 6px 1px rgba(72, 152, 255, 0.51);
        .remove-btn {
          display: flex;
        }
      }

      .node-item__title {
        background: linear-gradient(90deg, #2986ff 0%, #7bb4ff 100%);
        box-shadow: 0px 3px 6px 1px rgba(72, 152, 255, 0.51);
        color: #fff;
        @apply flex-x-between h-40px p-x-15px;
        border-radius: 4px 4px 0 0;
        img {
          width: 15px;
        }
      }
    }
  }

  .link-box {
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .arrow-line {
      background: #cecece;
      width: 2px;
      height: 90px;
    }
    .el-icon {
      color: #cecece;
      margin-top: -10px;
      font-size: 20px;
    }
    .add-btn {
      width: 28px;
      height: 28px;
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      transform: translateY(-62%);
      margin: auto;
      cursor: pointer;
      transition: all 0.3s ease;
      &:hover {
        transform: translateY(-62%) scale(1.1);
      }
    }
  }
}
</style>
