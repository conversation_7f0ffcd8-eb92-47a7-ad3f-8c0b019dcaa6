<template>
  <el-dialog v-model="visible" :title="title" width="60%" draggable>
    <el-form
      ref="dataFormRef"
      v-loading="loading"
      :model="form"
      :rules="dataRules"
      label-width="150px"
      :disabled="operType === 'view'"
    >
      <el-row :gutter="24">
        <el-col :span="12" class="mb-1">
          <el-form-item label="流程实例ID" prop="flowInstId">
            <el-input v-model="form.flowInstId" placeholder="请输入流程实例ID" disabled />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="流程定义ID" prop="defFlowId">
            <el-select
              v-model="form.defFlowId"
              placeholder="请选择流程定义ID"
              clearable
              filterable
              disabled
            >
              <el-option
                v-for="(item, index) in cascadeDic.defFlowId"
                :key="index"
                :label="item.flowName"
                :value="item.defFlowId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="运行节点ID" prop="runNodeId">
            <el-select
              v-model="form.runNodeId"
              placeholder="请选择运行节点ID"
              clearable
              filterable
              disabled
            >
              <el-option
                v-for="(item, index) in cascadeDic.runNodeId"
                :key="index"
                :label="item.nodeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="任务名称" prop="jobName">
            <el-input v-model="form.jobName" placeholder="请输入任务名称" />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="任务类型" prop="jobType">
            <el-select
              v-model="form.jobType"
              placeholder="请选择任务类型"
              clearable
              filterable
              @change="handleRoleType"
            >
              <el-option
                v-for="(item, index) in DIC_PROP.JOB_USER_NONE_TYPE"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="角色ID" prop="roleId">
            <el-select
              v-model="form.roleId"
              placeholder="请选择角色ID"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in dicData.users"
                v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[0].value"
                :key="index"
                :label="item.name"
                :value="item.userId"
              />
              <el-option
                v-for="(item, index) in dicData.roles"
                v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[1].value"
                :key="index"
                :label="item.roleName"
                :value="item.roleId"
              />
              <el-option
                v-for="(item, index) in dicData.depts"
                v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value"
                :key="index"
                :label="item.name"
                :value="item.deptId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="用户ID" prop="userId">
            <el-select
              v-model="form.userId"
              placeholder="请选择用户ID"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in dicData.users"
                :key="index"
                :label="item.name"
                :value="item.userId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="form.startTime"
              disabled
              type="datetime"
              placeholder="请选择开始时间"
              :value-format="dateTimeStr"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              disabled
              type="datetime"
              placeholder="请选择结束时间"
              :value-format="dateTimeStr"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="用户密钥" prop="userKey">
            <el-select
              v-model="form.userKey"
              placeholder="请选择用户密钥"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in dicData.userKey"
                :key="index"
                :label="item.userKey"
                :value="item.userKey"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="目标流程节点ID" prop="distFlowNodeId">
            <el-select
              v-model="form.distFlowNodeId"
              placeholder="请选择目标流程节点ID"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in cascadeDic.runNodeId"
                :key="index"
                :label="item.nodeName"
                :value="item.flowNodeId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="超时时间" prop="timeout">
            <el-input-number
              v-model="form.timeout"
              :min="1"
              :max="1000"
              placeholder="请输入超时时间"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="form.sort"
              :min="1"
              :max="1000"
              placeholder="请输入排序"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="form.status"
              disabled
              placeholder="请选择状态"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in DIC_PROP.NODE_STATUS"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="是否暂停" prop="suspension">
            <el-radio-group v-model="form.suspension">
              <el-radio
                v-for="(item, index) in DIC_PROP.YES_OR_NO"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="暂停原因" prop="suspensionReason">
            <el-input v-model="form.suspensionReason" placeholder="请输入暂停原因" />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="是否跳过驳回" prop="isSkipRejected">
            <el-radio-group v-model="form.isSkipRejected">
              <el-radio
                v-for="(item, index) in DIC_PROP.YES_OR_NO"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="是否立即运行" prop="isNowRun">
            <el-radio-group v-model="form.isNowRun" disabled>
              <el-radio
                v-for="(item, index) in DIC_PROP.YES_OR_NO"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template v-if="operType !== 'view'" #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="RunJobDialog">
import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/jsonflow/run-job"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
import { handleChangeJobType } from "@/flow"

const { proxy } = getCurrentInstance()

const emit = defineEmits(["refresh"])

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")

// 提交表单数据
const form = reactive({
  defFlowId: "",
  flowNodeId: "",
  nodeJobId: "",
  jobType: "",
  roleId: "",
  userId: "",
  startTime: "",
  endTime: "",
  userKey: "",
  distFlowNodeId: "",
  timeout: 0,
  status: "",
  suspension: "",
  suspensionReason: "",
  isSkipRejected: "0",
  createUser: "",
  flowKey: "",
  flowInstId: "",
  runNodeId: "",
  isNowRun: "0",
  sort: 1,
})

// 定义校验规则
const dataRules = ref({
  defFlowId: [{ required: true, message: "流程定义ID不能为空", trigger: "blur" }],
  flowNodeId: [{ required: true, message: "运行节点ID不能为空", trigger: "blur" }],
  nodeJobId: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
  jobType: [{ required: true, message: "任务类型不能为空", trigger: "blur" }],
  timeout: [{ required: true, message: "超时时间不能为空", trigger: "blur" }],
  isSkipRejected: [{ required: true, message: "是否跳过驳回不能为空", trigger: "blur" }],
})

// 定义查询字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl(
  { key: "userKey" },
  { key: "users" },
  { key: "roles" },
  { key: "posts" },
  { key: "depts" }
)
const onCascade = onCascadeChange(
  cascadeDic,
  { key: "flowInstId", cascades: ["defFlowId"] },
  { key: "flowInstId", cascades: ["runNodeId"] }
)
onMounted(() => {
  onLoad(dicData)
})

function cascadeChange(key: string, cascades: string[]) {
  onCascade(form, { key: key, cascades: cascades })
}

function handleRoleType() {
  handleChangeJobType(dicData, form)
}

// 打开弹窗
const openDialog = (type: string, id: string) => {
  visible.value = true
  operType.value = type
  form.id = ""

  const titleMap = {
    add: "新增",
    edit: "编辑",
    view: "查看",
  }
  title.value = titleMap[type]

  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields()
  })

  // 获取RunJob信息
  if (id) {
    form.id = id
    getRunJobData(id)
  }
}

// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => {})
  if (!valid) {
    return false
  }

  try {
    loading.value = true
    form.id ? await putObj(form) : await addObj(form)
    proxy.$modal.msgSuccess(form.id ? "修改成功" : "新增成功")
    visible.value = false
    emit("refresh")
  } catch (err: any) {
    proxy.$modal.msgError(err.msg)
  } finally {
    loading.value = false
  }
}

// 初始化表单数据
const getRunJobData = (id: string) => {
  // 获取数据
  loading.value = true
  getObj(id)
    .then((res: any) => {
      Object.assign(form, res.object)
      onCascade(form)
    })
    .finally(() => {
      loading.value = false
    })
}

// 暴露变量
defineExpose({
  openDialog,
})
</script>
