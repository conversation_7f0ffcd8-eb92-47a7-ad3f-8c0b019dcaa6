<template>
  <div class="table-container" :class="{ 'is--maximize': isFullScreen }">
    <div v-if="$slots.actions || hasToolbar" class="table-handle-box">
      <div class="table-handle-box__left">
        <slot name="actions" />
      </div>
      <div v-if="$slots.toolbar || hasToolbar" class="table-handle-box__right">
        <slot name="toolbar">
          <right-toolbar
            v-model:show-search="showSearch"
            v-model:full-screen="isFullScreen"
            show-full-screen
            :show-refresh="false"
            :custom="custom"
            @custom-change="customChange"
            @query-table="getList"
          />
        </slot>
      </div>
    </div>
    <div
      class="white-body-box"
      :class="{ 'no-actions': !$slots.actions && !hasToolbar, 'no-padding': noPadding }"
    >
      <vxe-table
        ref="tableRef"
        :data="data"
        min-height="60"
        :custom-config="{
          immediate: true,
        }"
        :row-config="rowConfig"
        :id="rowIdKey"
        v-bind="$attrs"
        :loading="loading"
        :class="{ 'table-inline-custom': $slots.opts }"
      >
        <slot />
        <vxe-column
          v-if="$slots.opts"
          title="操作"
          field="opts"
          fixed="right"
          :min-width="optWidth"
        >
          <template #header>
            <div class="table-opt-set">
              操作
              <el-tooltip content="显隐列" placement="top" effect="dark">
                <el-icon @click="customChange">
                  <Edit />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="{ row }">
            <slot name="opts" :row="row" />
          </template>
        </vxe-column>
        <template #empty>
          <CustomEmpty />
        </template>
      </vxe-table>
    </div>

    <pagination
      v-show="total > 0"
      v-model:page="page"
      v-model:limit="limit"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
const props = defineProps({
  data: Array, // 表格数据源
  loading: Boolean,
  total: {
    type: Number,
    default: 0,
  },
  // 操作列宽度
  optWidth: {
    type: Number,
    default: 200,
  },
  // 是否展示工具栏
  hasToolbar: {
    type: Boolean,
    default: true,
  },
  // 是否无边框
  noPadding: {
    type: Boolean,
    default: false,
  },
  rowIdKey: {
    type: String,
    default: "id",
  },
})

const showSearch = defineModel("showSearch")
const custom = defineModel("custom")
const page = defineModel("page")
const limit = defineModel("limit")
const isFullScreen = ref(false)

const tableRef = ref()

const rowConfig = ref({
  isHover: true,
})

const emit = defineEmits(["reload"])
function getList() {
  emit("reload")
}

function customChange() {
  if (tableRef.value) {
    tableRef.value.openCustom()
  }
}

function getCheckboxRecords() {
  const $table = tableRef.value
  if ($table) {
    const records = $table.getCheckboxRecords()
    return records
  }
}

defineExpose({
  customChange,
  getCheckboxRecords,
  tableRef,
})
</script>

<style scoped lang="scss">
.table-container {
  --vxe-ui-font-color: #141a26;
  --vxe-ui-table-header-background-color: #f0f1f3;
  --vxe-ui-table-row-striped-background-color: #f7f8f9;
  --vxe-ui-table-border-color: transparent;
  --vxe-ui-table-header-font-weight: 400;
  --vxe-ui-font-primary-color: var(--el-color-primary);
  --vxe-ui-font-primary-lighten-color: var(--el-color-primary-light-2);
  --vxe-ui-table-header-font-color: #141a26;
  --vxe-ui-loading-color: var(--el-color-primary);
  --vxe-ui-font-primary-darken-color: var(--el-color-primary);
  --vxe-ui-font-primary-disabled-color: var(--el-color-primary-light-5);
  --vxe-ui-font-lighten-color: #7c7c7c;
  --vxe-ui-table-cell-padding-default: 12px 10px;
  --vxe-ui-table-row-hover-striped-background-color: var(--el-color-primary-light-9);
  --vxe-ui-table-row-hover-background-color: var(--el-color-primary-light-9);
  :deep(.vxe-toolbar) {
    background: transparent;
  }
  :deep(.vxe-loading.is--visible) {
    padding: 100px 0;
  }

  &.is--maximize {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 2002;
    padding: 10px;
  }

  :deep(.vxe-table-custom-wrapper) {
    min-height: 250px;
  }

  :deep(tbody) {
    .vxe-cell--label span {
      &:empty::before {
        content: "-";
        color: #909090;
      }
    }
    .col--seq {
      .vxe-cell {
        padding-left: 15px;
      }
    }
  }
  :deep(.table-opt-set) {
    display: flex;
    align-items: center;
    .el-icon {
      cursor: pointer;
      color: var(--el-color-primary);
      margin-left: 5px;
      font-size: 16px;
      font-weight: bold;
    }
  }
}

.no-actions {
  margin-top: 0px;
}

.no-padding {
  padding: 0px;
}

.table-inline-custom {
  :deep(.vxe-table-custom-wrapper.placement--top-right) {
    top: 50px;
  }
}
</style>
