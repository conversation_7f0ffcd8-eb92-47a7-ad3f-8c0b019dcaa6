import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/jsonflow/run-reject/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/jsonflow/run-reject",
		method: "post",
		data: obj,
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/jsonflow/run-reject/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/jsonflow/run-reject/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/jsonflow/run-reject/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/jsonflow/run-reject",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/jsonflow/run-reject",
		method: "put",
		data: obj,
	})
}
