<template>
  <div class="sign-flow-table">
    <EssFilesTable
      ref="essFilesTableRef"
      v-model:queryParams="queryParams"
      :tableData="tableData"
      :loading="loading"
      :total="total"
      @query="handleQuery"
    >
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '详情',
              click: () => handleViewDetails(row),
              permission: ['todo:doingFile:details'],
            },
            {
              text: '撤销',
              click: () => handleRevocation(row),
              hidden: mine === 1,
              permission: ['todo:doingFile:cancel'],
            },
            {
              text: '催办',
              click: () => handlePress(row),
              hidden: mine === 1,
              permission: ['todo:doingFile:urging'],
            },
          ]"
        />
      </template>
    </EssFilesTable>
  </div>
</template>

<script setup>
import { myInitiationPage, otherInitiationPage } from "@/api/ess/todo/sign"

const EssFilesTable = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileList/components/EssFilesTable.vue")
)

import { useEssFilesTable } from "@/views/fileManagement/fileList/components/useEssFilesTable"

const props = defineProps({
  mine: {
    type: Number,
    default: 0, // 0 我发起的 1他人发起
  },
})
const essFilesTableRef = ref(null)

const { resetQuery, handleViewDetails, handleRevocation, handlePress } = useEssFilesTable(
  essFilesTableRef
)

const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(true)

const total = ref(0)
const queryParams = ref({
  page: 1,
  limit: 10,
})

/** 查询列表 */
async function getList() {
  await nextTick()
  try {
    loading.value = true
    queryParams.mine = props.mine
    const actionUrl = props.mine === 0 ? myInitiationPage : otherInitiationPage
    const res = await actionUrl(queryParams.value)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery(data) {
  Object.assign(queryParams.value, data)
  getList()
}

getList()

defineExpose({
  resetQuery,
})
</script>

<style lang="scss" scoped>
.sign-flow-table {
  :deep(.app-container) {
    padding: 0;
  }
}
</style>
