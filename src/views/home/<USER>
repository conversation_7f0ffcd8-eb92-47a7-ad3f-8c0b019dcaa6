<template>
  <div v-if="items.length || loading">
    <CommonCard :title="title">
      <template #suffix>
        <el-link v-show="showMore" @click="handleShowMoreNavPage"> 更多 </el-link>
      </template>
      <div ref="navBox" class="nav-box">
        <IconNav v-for="(item, index) in items" :key="index" :info="item" />
      </div>
      <IconSkeleton v-if="loading" />
    </CommonCard>

    <NavMoreListView v-model="showMoreList" :title="title" :list="items" />
  </div>
</template>

<script setup>
import CommonCard from "@/views/components/CommonCard.vue"
import IconNav from "./components/IconNav.vue"
import IconSkeleton from "./components/IconSkeleton.vue"
import NavMoreListView from "@/views/home/<USER>/NavMoreListView.vue"
import { getHomeModuleList } from "@/api/ess/home/<USER>"

const props = defineProps({
  title: {
    type: String,
    default: "常用流程",
  },
  moduleType: {
    type: Number,
    default: 0,
  },
})

const loading = ref(true)
const showMoreList = ref(false)
const navBox = ref(null)
const items = ref([])

const showMore = ref(true)
const checkOverflow = () => {
  if (!navBox.value) {
    return
  }
  const items = navBox.value.children
  let totalWidth = 0

  for (let i = 0; i < items.length; i++) {
    totalWidth += items[i].offsetWidth // 使用实际宽度
    if (totalWidth > navBox.value.clientWidth) {
      showMore.value = true
      return // 提前退出循环
    }
  }
  showMore.value = false // 如果没有溢出，在循环结束后设置
}

const getList = () => {
  getHomeModuleList({ moduleType: props.moduleType, appType: 0 })
    .then((res) => {
      items.value = res.object
    })
    .finally(() => {
      loading.value = false
    })
}

const handleResize = () => {
  checkOverflow()
}
const handleShowMoreNavPage = () => {
  showMoreList.value = true
}
onMounted(() => {
  checkOverflow()
  window.addEventListener("resize", handleResize)
})
onUnmounted(() => {
  window.removeEventListener("resize", handleResize)
})
getList()

onActivated(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.nav-box {
  display: flex;
  overflow: hidden;
  position: relative;
  &::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    width: 40px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), #fff);
    pointer-events: none;
  }
}
</style>
