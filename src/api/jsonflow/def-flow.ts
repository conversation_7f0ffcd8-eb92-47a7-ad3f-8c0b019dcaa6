import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-jsonflow/def-flow/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/def-flow",
		method: "post",
		data: obj,
    headers: {
      hiddenError: true,
    },
	})
}

export function getObj(id?: string) {
	return request({
		url: "/cloud-jsonflow/def-flow/" + id,
		method: "get",
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/cloud-jsonflow/def-flow/temp-store",
		method: "post",
		data: obj,
	})
}

export function delObj(id: any) {
	return request({
		url: "/cloud-jsonflow/def-flow/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-jsonflow/def-flow",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/def-flow",
		method: "put",
		data: obj,
	})
}

/**
 * 选择流程定义ID集合
 */
export function getDefFlowIds() {
	return request({
		url: "/cloud-jsonflow/def-flow/list",
		method: "get",
	})
}

/**
 * 根据流程名称获取信息
 *
 * @return AxiosPromise
 */
export function getByFlowName(flowName: string) {
	return request({
		url: "/cloud-jsonflow/def-flow/flow-name/" + flowName,
		method: "get",
	})
}

/**
 * 根据配置信息获取流程节点-简化版
 */
export function getSimpleFlowNodesByConfigKey( configKey: string ) {
	return request({
		url: "/cloud-jsonflow/runFlowExt/queryFlowNodeByCurrentUser?configKey=" + configKey,
		method: "get",
	})
}

/**
 * 获取流程节点-简化版
 */
export function getSimpleFlowNodes(defFlowId: string) {
	return request({
		url: "/cloud-jsonflow/runFlowExt/querySimpleFlowNodes?defFlowId=" + defFlowId,
		method: "get",
	})
}

/**
 * 保存简化版流程
 * @param flowType 流程类型：1=文件发起；2=模板发起
 */
export function generateFlowByUser(data: any, flowType: number) {
	return request({
		url: "/cloud-jsonflow/runFlowExt/generateFlowByUser?flowType=" + flowType,
		method: "post",
    data
	})
}

