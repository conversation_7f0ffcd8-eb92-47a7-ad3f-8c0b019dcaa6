<template>
  <div class="layout-padding">
    <el-container>
      <el-header style="background: white">
        <flow-design-header
          :curr-flow-form="props.currFlowForm"
          @handle-design-flow="methods.handleDesignFlow"
          @sync-curr-flow-form="methods.syncCurrFlowForm"
          @publish="methods.publishFlow"
        />
      </el-header>
      <div style="min-width: 980px">
        <form-design
          v-show="methods.showFormDesign()"
          ref="formDesign"
          :curr-flow-form="props.currFlowForm"
        />
        <form-def-perm
          v-show="methods.showFormDefPerm()"
          ref="formDefPerm"
          :curr-flow-form="props.currFlowForm"
        />
      </div>
      <div id="initClientHeight">
        <form-setting
          v-show="props.currFlowForm.active === 'formSetting'"
          ref="formSetting"
          :curr-flow-form="props.currFlowForm"
        />
        <flow-design
          v-show="props.currFlowForm.active === 'flowDesign'"
          ref="flowDesign"
          :curr-flow-form="props.currFlowForm"
        />
      </div>
    </el-container>
  </div>
</template>

<script setup name="FlowFormDesign">
import { useMessageBox } from "@/hooks/message"
import { notifyLeft, validateRunFlowId } from "@/flow"
import {
  confirmCancelAndClose,
  formWidgetDesignHeight,
  handleUpgradeVersion,
} from "@/flow/utils"
import { DIC_PROP } from "../../support/dict-prop"

const { proxy } = getCurrentInstance()
// 引入组件
const FlowDesignHeader = defineAsyncComponent(() => import("./header.vue"))
const FormDesign = defineAsyncComponent(() => import("../form-create/designer.vue"))
const FlowDesign = defineAsyncComponent(() =>
  import("@/views/jsonflow/flow-design/index.vue")
)
const FormSetting = defineAsyncComponent(() => import("./setting.vue"))
const FormDefPerm = defineAsyncComponent(() =>
  import("@/views/jsonflow/form-option/form-def-perm.vue")
)

const $emit = defineEmits(["handleDesignFlow"])
const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null,
  },
})

const methods = {
  handleDesignFlow(bool) {
    $emit("handleDesignFlow", bool)
  },
  initClientHeight() {
    nextTick(() => {
      let browserHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight
      formWidgetDesignHeight(browserHeight)
    })
  },
  showFormDesign() {
    return (
      props.currFlowForm.active === "formDesign" &&
      props.currFlowForm.type !== DIC_PROP.FORM_TYPE[1].value
    )
  },
  showFormDefPerm() {
    return (
      props.currFlowForm.active === "formDesign" &&
      props.currFlowForm.type === DIC_PROP.FORM_TYPE[1].value
    )
  },
  initCurrFlowInfo(active) {
    nextTick(() => {
      window._jfOperate.reAutoInitNodes()
      methods.syncCurrFlowInfo(active)
    })
  },
  syncCurrFlowInfo(active, isInit) {
    console.log(props, 2)
    let attrs = methods.refsFlowDataAttrs()
    if (active !== "flowDesign" || isInit) {
      attrs.flowKey = props.currFlowForm.flowKey
      attrs.groupName = props.currFlowForm.groupName
      attrs.flowName = props.currFlowForm.formName
    } else if (attrs.flowKey) {
      props.currFlowForm.defFlowId = attrs.id
      props.currFlowForm.flowKey = attrs.flowKey
      props.currFlowForm.groupName = attrs.groupName
      props.currFlowForm.formName = attrs.flowName
    }

    validateRunFlowId(props, attrs)
  },
  syncCurrFormInfo(callback) {
    if (props.currFlowForm.type !== DIC_PROP.FORM_TYPE[1].value) {
      proxy.$refs.formDefPerm.syncCurrFormInfo(true)
      // 保存表单信息
      proxy.$refs.formDesign.handleSubmit(() => {
        if (callback) callback()
      })
    } else {
      proxy.$refs.formDefPerm.syncCurrFormInfo(false)
      props.currFlowForm.formInfo = null
    }
  },
  refsFlowDataAttrs() {
    // 每次取最新
    return proxy.$refs.flowDesign?.flowData.attrs
  },
  async syncCurrFlowForm(menu, active, callback) {
    await nextTick()
    let preSave = await proxy.$refs.formSetting.validatePreSave()
    if (callback) callback(preSave)
    if (!preSave) return
    console.log("syncCurrFlowForm", active)
    if (active === "formDesign") methods.syncCurrFormInfo()
    let defFlowId = props.currFlowForm.defFlowId
    let flowInstId = props.currFlowForm.flowInstId
    if (menu === "flowDesign") {
      let attrs = methods.refsFlowDataAttrs()
      if (defFlowId && !attrs.flowKey) {
        // 切换到才初始化
        proxy.$refs.flowDesign.initFlow(defFlowId, flowInstId, () => {
          methods.syncCurrFlowInfo(menu, true)
        })
      } else methods.initCurrFlowInfo(active)
    } else if (active === "flowDesign") {
      methods.syncCurrFlowInfo(active)
    }
  },
  async publishFlow(menu, callback, status, version) {
    if (version === true) {
      try {
        if (props.currFlowForm.isNew) {
          notifyLeft("当前设计版本已最新, 请确认是否已保存")
          return
        }
        await useMessageBox().confirm("是否确定升级版本?")
      } catch {
        return
      }
      await handleUpgradeVersion(props)
      // 升级流程信息
      proxy.$refs.flowDesign.initNewFlow(() => {
        methods.syncCurrFlowInfo("flowDesign")
        let confirmObj = {
          text: "发布流程",
          callback: () => {
            methods.publishFlow(menu, callback, "1", null)
          },
        }
        let cancelObj = {
          text: "暂存流程",
          callback: () => {
            methods.publishFlow(menu, callback, "-1", null)
          },
        }
        confirmCancelAndClose(
          confirmObj,
          cancelObj,
          "流程设计升级版本成功! 是否立即暂存或发布?"
        )
      }, true)
      return
    }
    methods.syncCurrFormInfo()
    // 校验表单设计
    let otherSave = await proxy.$refs.formSetting.validateOtherSave()
    if (!otherSave) return
    // 保存流程信息
    props.currFlowForm.flowDesigning = true
    proxy.$refs.flowDesign.publishFlow(() => {
      methods.syncCurrFlowInfo("flowDesign")
      methods.submitFormSetting(callback, status)
      if (callback) callback()
    }, status)
  },
  async submitFormSetting(callback, status) {
    // 保存字段信息
    if (props.currFlowForm.type === DIC_PROP.FORM_TYPE[1].value) {
      await proxy.$refs.formDefPerm.handleSubmit(() => {
        if (callback) callback()
      })
    }

    await proxy.$refs.formSetting.handleSubmit(() => {
      if (callback) callback()
    }, status)
  },
}

// 监听双向绑定
watch(
  () => props.currFlowForm.id,
  (val) => {
    methods.initClientHeight()
  }
)

onMounted(() => {
  methods.initClientHeight()
  window.addEventListener("resize", () => {
    methods.initClientHeight()
  })
})

onUnmounted(() => {
  window.removeEventListener("resize", () => {
    methods.initClientHeight()
  })
})
</script>

<style lang="scss" scoped>
#initClientHeight {
  overflow: hidden auto;
}
</style>

<style lang="scss">
._fd-preview-dialog {
  .el-dialog__title {
    display: none;
  }
  ._fd-preview-copy {
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }
}
</style>
