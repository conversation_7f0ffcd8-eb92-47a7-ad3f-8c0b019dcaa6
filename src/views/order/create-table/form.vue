<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="80%"
		append-to-body
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="100px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item label="表名称" prop="tableName">
						<el-input v-model="form.tableName" placeholder="请输入表名称" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="表注释" prop="comments">
						<el-input v-model="form.comments" placeholder="请输入表注释" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="数据库类型" prop="databaseType">
						<el-select v-model="form.databaseType" placeholder="请选择数据库类型">
							<el-option
								v-for="(item, index) in DIC_PROP.DATABASE_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="主键策略" prop="pkPolicy">
						<el-select v-model="form.pkPolicy" placeholder="请选择主键策略" clearable>
							<el-option label="ID_WORKER(分布式自增)" value="ID_WORKER" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb-1">
					<el-form-item label="字段信息" prop="columns">
						<el-table
							:data="form.columns"
							border
							style="width: 100%"
							max-height="500"
							class="form-table"
						>
							<el-table-column label="序号" width="50">
								<template #header>
									<el-button
										icon="Plus"
										type="primary"
										circle
										@click="onAddItem"
									/>
								</template>
								<template #default="scope">
									<el-button
										icon="Minus"
										type="danger"
										circle
										@click="handleDelete(scope.$index, scope.row)"
									/>
								</template>
							</el-table-column>
							<el-table-column prop="name" label="字段名" show-overflow-tooltip>
								<template #default="scope">
									<el-input v-model="scope.row.name" placeholder="请输入字段名" />
								</template>
							</el-table-column>
							<el-table-column prop="comment" label="字段注释" show-overflow-tooltip>
								<template #default="scope">
									<el-input v-model="scope.row.comment" placeholder="请输入字段注释" />
								</template>
							</el-table-column>
							<el-table-column prop="typeName" label="字段类型" show-overflow-tooltip>
								<template #default="scope">
									<el-select
										v-model="scope.row.typeName"
										placeholder="请选择字段类型"
										clearable
										filterable
									>
										<el-option
											v-for="(item, index) in DIC_PROP.COLUMN_TYPE"
											:key="index"
											:label="item.label"
											:value="item.value"
										/>
									</el-select>
								</template>
							</el-table-column>
							<el-table-column prop="precision" label="精度">
								<template #default="scope">
									<el-input-number
										v-model="scope.row.precision"
										:min="0"
										:max="10000"

										placeholder="请输入精度"
									/>
								</template>
							</el-table-column>
							<el-table-column prop="scale" label="小数位数">
								<template #default="scope">
									<el-input-number
										v-model="scope.row.scale"
										:min="0"
										:max="10000"

										placeholder="请输入小数位数"
									/>
								</template>
							</el-table-column>
							<el-table-column prop="defaultValue" label="默认值" show-overflow-tooltip>
								<template #default="scope">
									<el-input v-model="scope.row.defaultValue" placeholder="请输入默认值" />
								</template>
							</el-table-column>
							<el-table-column prop="primaryKey" label="是否主键" show-overflow-tooltip>
								<template #default="scope">
									<el-radio-group v-model="scope.row.primaryKey">
										<el-radio v-for="(item, index) in DIC_PROP.YES_OR_NO_BOOL" :key="index" :value="item.value">
											{{ item.label }}
										</el-radio>
									</el-radio-group>
								</template>
							</el-table-column>
							<el-table-column prop="nullable" label="是否可为空" show-overflow-tooltip>
								<template #default="scope">
									<el-radio-group v-model="scope.row.nullable">
										<el-radio v-for="(item, index) in DIC_PROP.YES_OR_NO_BOOL" :key="index" :value="item.value">
											{{ item.label }}
										</el-radio>
									</el-radio-group>
								</template>
							</el-table-column>
						</el-table>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup name="CreateTableDialog">
import { useMessage, useMessageBox } from '@/hooks/message'
import { getObj, addObj, putObj } from '@/api/order/create-table'
import { rule, validateNull } from '@/utils/validate'

const { proxy } = getCurrentInstance()
const emit = defineEmits(['refreshDone', 'refresh'])

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref('')
// 定义字典

// 提交表单数据
const form = reactive({
	tableName: '',
	comments: '',
	databaseType: '',
	pkPolicy: '',
	columns: [],
})

/**
     * 校验数据源名
     * @param {校验数据源名} rule
     * @param {*} value
     * @param {*} callback
     */
let validateDsName = (rule, value, callback) => {
	let re = /(?=.*[a-z]|[A-Z])(?=.*_)/
	if (value && (!(re).test(value)))
		callback(new Error('数据源名称不合法, 组名_数据源名形式'))

	else
		callback()
}

// 定义校验规则
const dataRules = ref({
	tableName: [
		{ required: true, message: '表名称不能为空', trigger: 'blur' },
		{ max: 32, message: '长度在 32 个字符', trigger: 'blur' },
		{ validator: validateDsName, trigger: 'blur' },
	],
	comments: [{ required: true, message: '表注释不能为空', trigger: 'blur' }],
	databaseType: [{ required: true, message: '数据库类型不能为空', trigger: 'blur' }],
	pkPolicy: [{ required: true, message: '主键策略不能为空', trigger: 'blur' }],
	columns: [{ required: true, message: '字段信息不能为空', trigger: 'blur' }],
})

// 打开弹窗
const openDialog = (type, id) => {
	visible.value = true
	operType.value = type
	form.id = ''

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
		if (!id) onAddItem()
	})

	// 获取CreateTable信息
	if (id) {
		form.id = id
		getCreateTableData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) return false

	try {
		if (form.id)
			await useMessageBox().confirm('注意：目前修改会重新建表' + form.tableName)
	}
	catch {
		return
	}

	try {
		loading.value = true
		form.columnInfo = JSON.stringify(form.columns)
		let columns = {}
		form.columns.forEach(each => {
			if (validateNull(each.defaultValue)) each.defaultValue = null
			columns[each.name] = each
		})
		form.columnsInfo = JSON.stringify(columns)
		await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '编辑成功' : '新增成功')
		visible.value = false
		emit('refreshDone', form.tableName)
		emit('refresh')
	}
	catch (err) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getCreateTableData = id => {
	// 获取数据
	loading.value = true
	getObj(id).then(res => {
		let columnInfo = res.object.columnInfo
		res.object.columns = validateNull(columnInfo) ? [] : JSON.parse(columnInfo)
		Object.assign(form, res.object)
	})
		.finally(() => {
			loading.value = false
		})
}

const onAddItem = () => {
	let find = form.columns.find(f => f.name === 'id')
	if (find) {
		let obj = { name: '', comment: '', typeName: '', precision: 0, scale: 0, defaultValue: null, primaryKey: false, nullable: true }
		form.columns.push(obj)
		return
	}
	let id = { name: 'id', comment: '主键', typeName: 'bigint', precision: 20, scale: 0, defaultValue: null, primaryKey: true, nullable: false }
	let create_user = { name: 'create_user', comment: '创建人', typeName: 'bigint', precision: 20, scale: 0, defaultValue: null, primaryKey: false, nullable: false }
	let create_time = { name: 'create_time', comment: '创建时间', typeName: 'date', precision: 0, scale: 0, defaultValue: null, primaryKey: false, nullable: true }
	form.columns.push(id)
	form.columns.push(create_user)
	form.columns.push(create_time)
}

const handleDelete = (index, row) => {
	if (row.name === 'id' || row.name === 'create_user' || row.name === 'create_time') {
		proxy.$modal.msgWarning('不能删除【主键】、【创建人】、【创建时间】')
		return
	}
	form.columns.splice(index, 1)
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>

<style lang="scss" scoped>
.form-table{
  .el-radio{
    margin-right: 10px;
    :deep(.el-radio__label){
      padding-left: 4px;
    }
  }
  .el-input-number{
    width: 110px;
    :deep(.el-input-number__increase, .el-input-number__decrease){
      width:25px ;
    }
  }
}

</style>
