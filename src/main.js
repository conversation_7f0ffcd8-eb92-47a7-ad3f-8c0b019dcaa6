import { createApp } from "vue"

import Cookies from "js-cookie"
import ElementPlus from "element-plus"
// import 'element-plus/dist/index.css'
import locale from "element-plus/es/locale/lang/zh-cn"

import App from "./App"
import store from "./store"
import router from "./router"
import directive from "./directive" // directive

// 注册指令
import plugins from "./plugins" // plugins

import "vxe-pc-ui/lib/style.css"
import "vxe-table/lib/style.css"

// svg图标
import "virtual:svg-icons-register"

import "virtual:uno.css"
import "./permission" // permission control

import "@/assets/styles/element-theme.scss"
import "@/assets/styles/index.scss"

import registerGlobalComponents from "@/components/index"
import { registerGlobalMethod } from "@/utils/registerUtils/registerMethod"
import { registerElement } from "@/utils/registerUtils/elementConfig"
import { registerFlow } from "@/utils/registerUtils/flowConfig"
import { lazyVxeUI, lazyVxeTable } from "@/utils/registerUtils/vxeTable"

import { initAntiDebug } from '@/utils/antiDebug';

// Initialize anti-debug protection
initAntiDebug();

const app = createApp(App)

app.use(router)
app.use(store)
app.use(plugins)
registerGlobalComponents(app) // 全局挂载组件
registerGlobalMethod(app) // 全局挂载方法
registerFlow(app) // 全局挂载流程
directive(app)

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
	locale: locale,
	// 支持 large、default、small
	size: Cookies.get("size") || "default",
})

lazyVxeUI(app)
lazyVxeTable(app)

app.mount("#app")

registerElement(app)
