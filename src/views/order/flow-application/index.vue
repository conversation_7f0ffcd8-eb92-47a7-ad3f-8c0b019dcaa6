<template>
  <div class="layout-padding">
    <div style="background: #f4f4f5; margin-bottom: 10px;" class="el_result_layout">
      <el-row :gutter="24">
        <el-col :span="8">
          <el-space class="el-space_container" @click="handleClickHref('TodoJobHash')">
            <el-result style="width: 100px; padding: 0;">
              <template #icon>
                <el-icon :size="50" style="color: #409eff;">
                  <BellFilled />
                </el-icon>
              </template>
            </el-result>
            <el-card shadow="never" class="el-card__title__" style="width: 220px; border: none;">
              <template #header>
                待我审批
              </template>
              <div style="height: 23px; font-size: 30px; font-weight: bold;">
                {{ useFlowJob().jobLen }}
              </div>
            </el-card>
          </el-space>
        </el-col>
        <el-col :span="4" style="padding: 0;">
          <el-result
            class="el-result__style__"
            title="我的申请"
            @click="handleClickHref('RunApplicationHash')"
          >
            <template #icon>
              <el-icon :size="40" style="color: #409eff;">
                <Promotion />
              </el-icon>
            </template>
          </el-result>
        </el-col>
        <el-col :span="4" style="padding: 0;">
          <el-result
            class="el-result__style__"
            title="抄送我的"
            @click="handleClickHref('TodoJobHash', '?belongType=1')"
          >
            <template #icon>
              <el-icon :size="40" style="color: #409eff;">
                <Avatar />
              </el-icon>
            </template>
          </el-result>
        </el-col>
        <el-col :span="4" style="padding: 0;">
          <el-result
            class="el-result__style__"
            title="待认领的"
            @click="handleClickHref('SignJobHash')"
          >
            <template #icon>
              <el-icon :size="40" style="color: #409eff;">
                <Opportunity />
              </el-icon>
            </template>
          </el-result>
        </el-col>
        <el-col :span="4" style="padding: 0 10px 0 0;">
          <el-result
            class="el-result__style__"
            title="我审批的"
            @click="handleClickHref('HiJobHash')"
          >
            <template #icon>
              <el-icon :size="40" style="color: #409eff;">
                <Stamp />
              </el-icon>
            </template>
          </el-result>
        </el-col>
      </el-row>
    </div>

    <div class="layout-padding-auto layout-padding-view" style="overflow-y: auto;">
      <template v-for="(tabs, index) in data.tabsData" :key="index">
        <el-collapse v-model="data.collapse">
          <el-collapse-item :name="index">
            <template #title>
              <el-icon :size="24" style="margin-right: 8px; color: #409eff;">
                <CaretRight v-if="!data.collapse.includes(index)" />
                <CaretBottom v-else />
              </el-icon>
              <div style="font-size: 14px;">
                {{ tabs.groupName }}
              </div>
            </template>

            <div class="avue-view__avue-card">
              <el-row :span="24" :gutter="20">
                <el-col
                  v-for="(item, index) in data.tableData.filter(
                    (f) => f.groupName === tabs.groupName
                  )"
                  :key="index"
                  :span="6"
                >
                  <div class="avue-card__item">
                    <div class="avue-card__body">
                      <div class="avue-card__avatar2">
                        <i
                          :class="item.icon"
                          alt=""
                          style="font-size: 38px !important; color: #409eff;"
                        />
                      </div>
                      <div class="avue-card__detail">
                        <div class="avue-card__title">{{ item.formName }} V{{ item.version }}</div>
                        <div class="avue-card__info">
                          {{ item.remark }}
                        </div>
                      </div>
                    </div>
                    <div class="avue-card__menu">
                      <span
                        v-if="item.status === '1'"
                        v-auth="'order_flowapplication_edit'"
                        @click.stop="handleInitiateOrder(item, index)"
                      >
                        发起申请
                      </span>
                      <span
                        v-auth="'order_flowapplication_edit'"
                        @click.stop="openPreview(item.defFlowId)"
                      >
                        查看流程图
                      </span>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-collapse-item>
        </el-collapse>
      </template>

      <!-- 发起申请 -->
      <json-flow-predict ref="predict" :proxy="proxy" @handleInitiateOrder="handleInitiateOrder">
        <template v-slot="slotProps" v-if="data.showInitiateOrder">
          <flow-initiate
            ref="form"
            v-show="slotProps.currActive === 'form'"
            :curr-flow-form="data.currFlowForm"
            @handleInitiateOrder="handleInitiateOrder"
          ></flow-initiate>
        </template>
        <template v-slot="slotProps" v-if="data.showHandleForm">
          <custom-form
            ref="form"
            v-show="slotProps.currActive === 'form'"
            :curr-job="data.currFlowForm"
            @onHandleForm="handleInitiateOrder"
          ></custom-form>
        </template>
      </json-flow-predict>

      <custom-form
        v-if="data.showHandleForm"
        :curr-job="data.currFlowForm"
        @on-handle-form="handleInitiateOrder"
      />

      <!-- 查看流程图 -->
      <el-drawer
        v-model="data.showFlowPic"
        class="flow-overflow-drawer"
        direction="rtl"
        append-to-body
        size="90%"
      >
        <flow-photo v-if="data.showFlowPic" :curr-job="data.currFlowForm" />
      </el-drawer>
    </div>
  </div>
</template>

<script setup lang="ts" name="systemFlowApplication">
import * as flowApplication from '@/api/order/flow-application'
import { deepClone } from '@/utils/index'
import { useFlowJob } from '@/flow/stores/flowJob'
import { windowLocationHrefParam } from '@/flow/support/extend'
import { handleCustomForm, vueKey } from '@/api/order/order-key-vue'

// 引入组件
const FlowPhoto = defineAsyncComponent(() => import('@/views/jsonflow/flow-design/view.vue'))
const FlowInitiate = defineAsyncComponent(() => import('./initiate.vue'))
const CustomForm = defineAsyncComponent(() => import('@/flow/components/custom-form/handle.vue'))
const JsonFlowPredict = defineAsyncComponent(() =>
  import('@/views/jsonflow/flow-design/predict.vue')
)

const { proxy } = getCurrentInstance()

const data = reactive({
  tableData: [],
  tabsData: [],
  showInitiateOrder: false,
  showFlowPic: false,
  currFlowForm: {},
  showHandleForm: false,
  collapse: [0]
})

// 列表查询
function getList() {
  flowApplication.listByPerms({}).then((response) => {
    data.tableData = response.data
    data.tabsData = data.tableData.filter(
      (value, i, arr) => arr.findIndex((x) => x.groupName === value.groupName) === i
    )
  })
}

function handleInitiateOrder(row, index) {
  if (row === false) {
    openPredict({}, false)
    data.showInitiateOrder = false
    data.showHandleForm = false
    return
  }
  data.currFlowForm = deepClone(row)
  // 判断是否自定义首页
  if (row.path !== vueKey.RunApplicationForm) {
    handleCustomForm(data, row)
    data.currFlowForm.operType = 'add'
    data.showHandleForm = true
  } else {
    data.showInitiateOrder = true
  }
  openPredict(row, true)
}

function openPredict(row, bool) {
  proxy.$refs.predict.open(row, bool)
}

function handleClickHref(hash, param = '') {
  windowLocationHrefParam(hash, param)
}

function openPreview(defFlowId) {
  data.currFlowForm = { defFlowId: defFlowId }
  data.showFlowPic = true
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss">
@use '../../../flow/components/style/flow-drawer.scss' as *;
</style>

<style lang="scss" scoped>
.avue-view__avue-card {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;

  .avue-card__item:hover {
    border-color: var(--el-color-primary);
  }

  .avue-card__item {
    margin-bottom: 12px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background-color: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    cursor: pointer;

    .avue-card__body {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;

      .avue-card__avatar2 {
        width: 48px;
        height: 48px;
        border-radius: 48px;
        overflow: hidden;
        margin-right: 12px;
        margin-left: 12px;
      }

      .avue-card__detail {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;

        .avue-card__title {
          color: rgba(0, 0, 0, 0.85);
          margin-top: 6px;
          margin-bottom: 6px;
          font-size: 16px;
        }

        .avue-card__info {
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          height: 64px;
        }
      }
    }

    .avue-card__menu {
      border-radius: 8px;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-pack: distribute;
      justify-content: space-around;
      height: 50px;
      background: #f7f9fa;
      text-align: center;
      line-height: 50px;
    }
  }
}

.el_result_layout {
  cursor: pointer;

  .el-space_container {
    width: 100%;
    background: white;
    border-radius: 8px;
  }

  .el-card__title__:hover {
    background: #f4f4f5;
  }
  .el-card__title__ {
    // 居中不能适应屏幕大小
    // display: grid;
    // place-items: center;
    margin: 0;
    font-size: 20px;
  }

  .el-result__style__:hover {
    background: #f4f4f5;
  }
  .el-result__style__ {
    border-radius: 8px;
    background: white;
    padding: 10px;
  }
}
</style>
