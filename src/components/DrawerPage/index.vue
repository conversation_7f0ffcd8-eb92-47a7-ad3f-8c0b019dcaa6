<template>
	<div v-if="show" class="drawer-page-wrapper">
		<div class="drawer-page__header">
			<div class="drawer-page__header-title">
				<span class="back-icon" @click="handleBack">
					<el-icon><ArrowLeftBold /></el-icon>
				</span>
				<span class="title">{{ title }}</span>
			</div>
			<div class="drawer-page__header-actions">
				<slot name="actions" />
			</div>
		</div>
		<div class="drawer-page__body" :class="{ 'grey-bg': !hasBg }">
			<slot />
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	title: {
		type: String,
		default: "",
	},
	hasBg: {
		type: Boolean,
		default: true,
	},
})

const show = defineModel(false)
const $emit = defineEmits(["back"])

const handleBack = () => {
	show.value = false
	$emit("back")
}

</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.drawer-page-wrapper {
  background: $base-main-page-background;
  @apply absolute top-0 left-0 right-0 bottom-0 z-99 w-100% h-100% p-15px;
}

.drawer-page__body {
  width: 100%;
  height: 95%;
  overflow-y: auto;
  padding: 15px;
  border-radius: 10px;
  background: #fff;
  min-height: calc(#{$base-main-page-height} - 60px);
  &.grey-bg{
    background: $base-main-page-background;
    padding: 0;
  }
}

.drawer-page__header {
  @apply flex-x-between mb-12px;
}

.drawer-page__header-title {
  @apply flex-y-center;
  .back-icon {
    @apply mr-10px cursor-pointer rounded-full h-20px w-20px flex-center color-white font-size-12px;
    background: #d1d1d1;
    transition: all 0.3s;
    &:hover {
      transform: scale(1.1);
    }
  }
  .title {
    @apply font-bold font-size-18px;
  }
}

</style>
