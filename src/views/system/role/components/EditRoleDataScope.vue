<template>
  <!-- 分配角色数据权限对话框 -->
  <el-dialog v-model="dialogVisible" title="分配数据权限" width="720px" append-to-body>
    <el-form ref="roleRef" :model="form" label-width="100px">
      <el-form-item label="角色名称" props="roleName">
        <el-input v-model="form.roleName" disabled />
      </el-form-item>
      <el-form-item label="权限字符" props="roleKey">
        <el-input v-model="form.roleKey" disabled />
      </el-form-item>
      <div class="divider-title"><span>统计数据权限</span></div>
      <el-form-item label="权限数据范围" props="dataScope">
        <el-select
          v-model="form.dataScope"
          @change="dataScopeSelectChange"
          :disabled="!isEdit"
        >
          <el-option
            v-for="item in dataScopeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="印章使用详情" props="sealUseDetailsScope">
        <el-select v-model="form.sealUseDetailsScope" :disabled="!isEdit">
          <el-option
            v-for="item in dataScopeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="印章使用统计" props="sealDataScope">
        <el-select v-model="form.sealDataScope" :disabled="!isEdit">
          <el-option
            v-for="item in dataScopeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件情况统计" props="fileDataScope">
        <el-select v-model="form.fileDataScope" :disabled="!isEdit">
          <el-option
            v-for="item in dataScopeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="个人文件统计" props="personDataScope">
        <el-select v-model="form.personDataScope" :disabled="!isEdit">
          <el-option
            v-for="item in dataScopeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="角色类型" props="roleType">
        <dict-select
          valueType="string"
          v-model="form.roleType"
          :options="business_ascription"
          type="radio"
        />
      </el-form-item>
      <el-form-item label="所属业务线" props="businessLineIds">
        <BusinessSelect
          :ascriptionType="form.roleType == 1 ? 'single' : 'all'"
          :ascription="form.roleType"
          v-model="form.businessLineIds"
          type="checkbox"
        />
      </el-form-item>
      <!-- 自定义数据权限 -->
      <el-form-item v-show="form.dataScope == 2" label="数据权限">
        <el-checkbox
          v-model="deptExpand"
          @change="handleCheckedTreeExpand($event, 'dept')"
        >
          展开/折叠
        </el-checkbox>
        <el-checkbox
          v-model="deptNodeAll"
          @change="handleCheckedTreeNodeAll($event, 'dept')"
        >
          全选/全不选
        </el-checkbox>
        <el-checkbox
          v-model="form.deptCheckStrictly"
          @change="handleCheckedTreeConnect($event, 'dept')"
        >
          父子联动
        </el-checkbox>
        <el-tree
          ref="deptRef"
          class="tree-border"
          :data="deptOptions"
          show-checkbox
          default-expand-all
          node-key="id"
          :check-strictly="!form.deptCheckStrictly"
          empty-text="加载中，请稍候"
          :props="{ label: 'label', children: 'children' }"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancel"> 取消 </el-button>
      <el-button type="primary" :loading="loading" @click="submitDataScope">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  dataScope,
  getRole,
  deptTreeSelectForRoleId,
  updateRoleInfoData,
} from "@/api/system/role"
import BusinessSelect from "@/views/components/BusinessSelect.vue"

const { proxy } = getCurrentInstance()
const emit = defineEmits(["change"])

const props = defineProps(["business_ascription"])

const dialogVisible = ref(false)
const form = ref({})
const deptOptions = ref([])
const deptRef = ref(null)
const deptExpand = ref(true)
const deptNodeAll = ref(false)
const loading = ref(false)
/** 数据范围选项 */
const dataScopeOptions = ref([
  { value: "1", label: "全部数据权限" },
  // { value: '2', label: '自定数据权限' },
  { value: "3", label: "本部门数据权限" },
  // { value: '4', label: '本部门及以下数据权限' },
  { value: "5", label: "仅本人数据权限" },
])

const isEdit = ref(import.meta.env.VITE_SYS_EDIT === "true") // 是否可编辑

const open = (row) => {
  reset()
  form.value = { ...row, businessLineIds: row.businessLineIds || [] }
  dialogVisible.value = true
}

/** 根据角色ID查询部门树结构 */
function getDeptTree(roleId) {
  nextTick(() => {
    deptTreeSelectForRoleId(roleId).then((res) => {
      deptOptions.value = res.depts
      nextTick(() => {
        if (deptRef.value) {
          deptRef.value.setCheckedKeys(res.checkedKeys)
        }
      })
    })
  })
}
/** 树权限（全选/全不选） */
function handleCheckedTreeNodeAll(value, type) {
  deptRef.value.setCheckedNodes(value ? deptOptions.value : [])
}

/** 提交按钮（数据权限） */
function submitDataScope() {
  if (form.value.roleId != undefined) {
    loading.value = true
    // form.value.deptIds = getDeptAllCheckedKeys()
    updateRoleInfoData(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess("修改成功")
        dialogVisible.value = false
        emit("change")
      })
      .finally(() => (loading.value = false))
  }
}

/** 所有部门节点数据 */
function getDeptAllCheckedKeys() {
  // 目前被选中的部门节点
  let checkedKeys = deptRef.value.getCheckedKeys()
  // 半选中的部门节点
  let halfCheckedKeys = deptRef.value.getHalfCheckedKeys()
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
  return checkedKeys
}

/** 取消按钮（数据权限） */
function cancel() {
  dialogVisible.value = false
  reset()
}

/** 树权限（父子联动） */
function handleCheckedTreeConnect(value, type) {
  form.value.deptCheckStrictly = value ? true : false
}

/** 重置新增的表单以及其他数据  */
function reset() {
  deptExpand.value = true
  deptNodeAll.value = false
  form.value = {
    roleName: undefined,
    roleKey: undefined,
    deptIds: [],
    menuCheckStrictly: true,
    deptCheckStrictly: true,
  }
  proxy.resetForm("roleRef")
}

/** 选择角色权限范围触发 */
function dataScopeSelectChange(value) {
  if (value !== "2") {
    deptRef.value.setCheckedKeys([])
  }
}

/** 树权限（展开/折叠） */
function handleCheckedTreeExpand(value, type) {
  let treeList = deptOptions.value
  for (let i = 0; i < treeList.length; i++) {
    deptRef.value.store.nodesMap[treeList[i].id].expanded = value
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.divider-title {
  @apply title-line-b  before:(bg-[var(--el-color-primary-light-6)]) ml-5px m-y-15px text-16px;

  span {
    @apply z-10 relative;
  }
}
</style>
