<!--
 * @Author: ljn
 * @Date: 2024-12-03 10:33:31
 * @LastEditors: ljn
 * @LastEditTime: 2025-02-27 16:04:26
 * @Description: el-table 操作列 显示三个点
-->
<template>
	<span class="operation-btn">
		<el-popover
			placement="left-start"
			:width="140"
			trigger="hover"
			popper-class="table-operation-popover"
		>
			<template #reference>
				<el-button
					type="primary"
					link
					icon="MoreFilled"
					class="more-icon"
				/>
			</template>
			<div class="operation-btn-content">
				<slot />
			</div>
		</el-popover>
	</span>
</template>

<script setup name="TableColumnOperation"></script>

<style scoped lang="scss">
.operation-btn {
  padding: 2px 4px;
}
.operation-btn-content {
  display: flex;
  flex-direction: column;
  gap: 11px;
  :deep(.el-button) {
    display: block;
    width: 100%;
    height: 32px !important;
    min-height: 32px !important;
    border-radius: 6px;
    padding: 0 5px;
  }
  :deep(.el-button + .el-button) {
    margin-left: 0;
  }
  :deep(.el-button--info.is-plain) {
    background: #fff;
    color: #38383a;
    border-color: #d1d1d1;
    &:hover {
      color: #fff;
      background: var(--el-color-primary);
      border-color: var(--el-color-primary);
    }
  }
}
</style>

<style lang="scss">
.table-operation-popover {
  padding: 12px 15px !important;
  min-width: 135px !important;
}
</style>
