<template>
  <DrawerPage v-model="show" :title="title" class="auth-list-container" :has-bg="false">
    <el-form
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      label-width="110px"
      class="white-form-box"
    >
      <el-form-item label="授权时间" prop="authTimeRange">
        <el-date-picker
          v-model="queryParams.authTimeRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="common-input-width"
          clearable
          :default-time="dateDefaultTime"
        />
      </el-form-item>
      <el-form-item label="持有人" prop="holdUserName">
        <el-input
          v-model="queryParams.holdUserName"
          placeholder="请输入创建人姓名"
          clearable
          maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="持有人归属部门" prop="holdDeptId">
        <dept-picker v-model="queryParams.holdDeptId" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :data="tableData"
      :has-toolbar="false"
      :loading="loading"
      :total="total"
      @reload="getList"
    >
      <template #actions>
        <el-button
          v-auths="['ess:authList:addApply']"
          type="primary"
          @click="handleAuthApply"
        >
          授权申请
        </el-button>
        <el-button v-auths="['ess:authList:add']" type="success" @click="handleAddAuth">
          添加授权
        </el-button>
      </template>
      <vxe-column type="checkbox" width="40" fixed="left" />
      <vxe-column type="seq" width="70" />
      <vxe-column title="持有人" field="holdUserName" min-width="100" show-overflow />
      <vxe-column
        title="持有人归属部门"
        field="holdDeptName"
        min-width="100"
        show-overflow
      />
      <vxe-column title="授权人" field="createUser" min-width="100" show-overflow />
      <vxe-column title="授权时间" field="createTime" min-width="140" show-overflow />
      <vxe-column title="授权结束时间" field="limitTime" min-width="100" show-overflow />
      <vxe-column title="操作" field="opt" width="200">
        <template #default="{ row }">
          <TableColOptBtn
            :key="Math.random()"
            :buttons="[
              {
                text: '变更授权申请',
                click: () => handleChangeAuthApply(row),
                permission: ['ess:authList:changeApply'],
              },
              {
                text: '取消授权',
                click: () => handleCancelAuth(row),
                permission: ['ess:authList:cancel'],
              },
            ]"
          />
        </template>
      </vxe-column>
    </CustomTable>

    <AddAuthUserModal
      ref="selectUserTreeRef"
      :sealInfo="sealInfo"
      @change="handleQuery"
    />

    <FlowInitiateModal ref="flowInitiateModalRef" @opened="setSealId" />
  </DrawerPage>
</template>

<script setup>
import { fetchByConfig } from "@/api/order/flow-application"
import { getSealAuthListPage, removeAuthUserSeal } from "@/api/ess/seal/sealAuth"
const AddAuthUserModal = defineAsyncComponent(() => import("./AddAuthUserModal.vue"))
const FlowInitiateModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowInitiateModal.vue")
)

const { proxy } = getCurrentInstance()

const { deptOptions } = defineProps(["deptOptions"])
const show = ref(false)
const loading = ref(true)
const queryRef = ref(null)
const title = ref("")
const total = ref(0)
const tableData = ref([]) // 授权列表
const sealInfo = ref([]) // 印章详情

const queryParams = reactive({
  holdDeptId: null,
  holdUserName: "",
  authTimeRange: [],
  page: 1,
  limit: 10,
})

// 已选列表
const customTableRef = ref(null)
const selections = computed(() => {
  if (customTableRef.value) {
    return customTableRef.value.getCheckboxRecords()
  }
  return []
})

function open(row) {
  show.value = true
  title.value = "印章授权列表：" + row.sealName
  sealInfo.value = { ...row }
  queryParams.sealId = row.id
  resetQuery()
}

function getList() {
  getSealAuthListPage(queryParams)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

const selectUserTreeRef = ref(null)
//  添加授权
function handleAddAuth() {
  selectUserTreeRef.value.open(queryParams.sealId)
}

//  取消授权确认---兼容了批量的（后面去掉了批量）
function handleCancelAuth(row) {
  if (!selections.value.length && !row.id) {
    proxy.$modal.msgError("请选择要取消授权的用户")
    return
  }
  let tip = row.id ? row.holdUserName : `${selections.value.length}名用户`
  proxy.$modal
    .confirm(`即将取消${tip}的授权, 是否继续？`)
    .then(function () {
      let reqData = {
        sealId: queryParams.sealId,
        sealAuthUser: row.id ? [row] : selections.value,
      }
      removeAuthUserSeal(reqData)
        .then(() => {
          proxy.$modal.msgSuccess("取消成功")
          loading.value = true
          setTimeout(() => {
            handleQuery()
          }, 3000)
        })
        .catch(() => {})
    })
    .catch(() => {})
}
const updateRow = ref(null)
// 授权申请
async function handleAuthApply() {
  updateRow.value = null
  const res = await fetchByConfig("SEAL_AUTH_LIST")
  proxy.$refs.flowInitiateModalRef.open(res.object)
}
// 变更授权申请
async function handleChangeAuthApply(row) {
  updateRow.value = { ...row }
  const res = await fetchByConfig("SEAL_AUTH_CHANGE_FLOW_ID")
  proxy.$refs.flowInitiateModalRef.open(res.object, false)
}
function setSealId(initiateRef) {
  initiateRef?.design?.fApi?.setValue("sealId", queryParams.sealId)
  if (updateRow.value) {
    // 印章授权记录ID
    initiateRef?.design?.fApi?.setValue("sealAuthId", updateRow.value.id)
  }
}

defineExpose({
  open,
})
</script>
