<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      class="white-form-box"
    >
      <el-form-item label="菜单名称" prop="menuName">
        <el-input
          v-model="queryParams.menuName"
          placeholder="请输入菜单名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="菜单状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <div class="table-handle-box">
      <div>
        <el-button
          v-auths="['system:menu:add']"
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-if="isEdit"
        >
          新增
        </el-button>
        <el-button type="primary" plain class="is-deep" @click="toggleExpandAll">
          展开/折叠
        </el-button>
      </div>
      <right-toolbar v-model:show-search="showSearch" @query-table="getList" />
    </div>

    <div class="white-body-box">
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="menuList"
        row-key="menuId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        stripe
      >
        <el-table-column
          prop="menuName"
          label="菜单名称"
          show-overflow-tooltip
          min-width="140"
        />
        <el-table-column prop="icon" label="图标" width="100">
          <template #default="scope">
            <svg-icon :name="scope.row.icon" />
          </template>
        </el-table-column>
        <el-table-column prop="orderNum" label="排序" width="60" />
        <el-table-column
          prop="perms"
          label="权限标识"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          prop="component"
          label="组件路径"
          :show-overflow-tooltip="true"
        />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              v-auths="['system:menu:edit', 'system:menu:editStyle']"
              type="primary"
              link
              @click="handleUpdate(scope.row)"
            >
              修改
            </el-button>
            <el-button
              v-auths="['system:menu:add']"
              type="primary"
              link
              @click="handleAdd(scope.row)"
              v-if="isEdit"
            >
              新增
            </el-button>
            <el-button
              v-auths="['system:menu:remove']"
              type="danger"
              link
              @click="handleDelete(scope.row)"
              v-if="isEdit"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加或修改菜单对话框 -->
    <EditMenuModal ref="editMenuModal" @change="getList" />
  </div>
</template>

<script setup name="Menu">
import { delMenu, listMenu } from "@/api/system/menu"
import SvgIcon from "@/components/SvgIcon"
import EditMenuModal from "./components/EditMenuModal.vue"

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

const editMenuModal = ref(null)
const menuList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const isExpandAll = ref(false)
const refreshTable = ref(true)
const isEdit = ref(import.meta.env.VITE_SYS_EDIT === "true") // 是否可编辑

const data = reactive({
  queryParams: {
    menuName: undefined,
    visible: undefined,
  },
})
const { queryParams } = toRefs(data)

/** 查询菜单列表 */
function getList() {
  loading.value = true
  listMenu(queryParams.value).then((response) => {
    menuList.value = proxy.handleTree(response.data, "menuId")
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd(row) {
  editMenuModal.value.open(row)
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  editMenuModal.value.open(row, "修改菜单")
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.menuName + '"的数据项?')
    .then(function () {
      return delMenu(row.menuId)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

getList()
</script>
