import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/jsonflow/run-job/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/jsonflow/run-job",
		method: "post",
		data: obj,
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/jsonflow/run-job/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/jsonflow/run-job/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/jsonflow/run-job/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/jsonflow/run-job",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/jsonflow/run-job",
		method: "put",
		data: obj,
	})
}

/**
 * 任务交接：查询交接人是自己未完成的任务
 */
export function fetchNodeHandover(query: any) {
	return request({
		url: "/jsonflow/run-job/page/job-handover",
		method: "get",
		params: query,
	})
}

// 减签任务
export function signOff(obj: any) {
	return request({
		url: "/jsonflow/run-job/sign-off",
		method: "put",
		data: obj,
	})
}

// 催办任务
export function remind(obj: any) {
	return request({
		url: "/jsonflow/run-job/remind",
		method: "put",
		data: obj,
	})
}
