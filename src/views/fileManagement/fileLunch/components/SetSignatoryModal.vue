<template>
  <el-dialog
    v-model="dialogVisible"
    width="1500px"
    title="签署方"
    :before-close="handleClose"
    top="2vh"
  >
    <div class="signatory-container">
      <div class="form-body">
        <div
          class="form-body-item"
          v-for="(item, index) in recipientList"
          :key="item.recipientType"
        >
          <div class="recipient-title">{{ item.label }}</div>
          <el-table
            :data="item.list"
            :empty-text="item.recipientType === 2 ? '请先添加签字人' : '请先添加经办人'"
          >
            <el-table-column
              label="单位名称"
              prop="organizationName"
              v-if="item.recipientType === 1"
            />
            <el-table-column
              :label="item.recipientType === 2 ? '签字人' : '经办人'"
              prop="approveName"
            >
              <template #default="{ row, $index }">
                {{ row.approveName }}{{ row.workNumber ? `(${row.workNumber})` : "" }}
              </template>
            </el-table-column>
            <el-table-column label="手机号" prop="approveMobile" />
            <el-table-column
              label="属性"
              prop="signAttributes"
              v-if="item.recipientType === 0"
            />
            <el-table-column label="操作" prop="opt" width="90">
              <template #default="{ row, $index }">
                <el-button
                  text
                  icon="Delete"
                  type="danger"
                  @click="deleteUserItem($index, index)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-wrapper">
            <el-button
              v-if="item.recipientType != 2"
              :color="item.color"
              plain
              class="has-line"
              icon="Plus"
              @click="addSignatory(item, 0)"
            >
              添加{{ item.recipientType == 1 ? "经办人" : "用印人" }}
            </el-button>
            <el-button
              v-if="item.recipientType != 1"
              :color="item.color"
              plain
              class="has-line"
              icon="Plus"
              @click="addSignatory(item, 1)"
            >
              添加签字人
            </el-button>
          </div>
        </div>
      </div>
      <div class="iframe-container w-57%" v-loading="loading">
        <div class="title">
          <span>文件预览《{{ removeFileExtension() }}》</span>
        </div>
        <FilePreview :fileRaw="fileRaw" :previewUrl="fileInfo.previewUrl"></FilePreview>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>

    <!-- 选择全部 -->
    <UserRolePicker
      ref="userPickerRef"
      title="选择校内签字人（注：外国国籍成员选择功能受限，暂不开放）"
      :businessLineType="ascription"
      izChineseGj="1"
      :selectTypes="['user']"
      @onSelectItems="selectUser"
    />

    <!-- 印章管理员 -->
    <SelectSealUserModal
      multiple
      v-model="recipientList[0].list"
      ref="selectSealUserRef"
      @select="changeSelectItems"
    />
    <!-- 选择企业 -->
    <EnterpriseUserModal
      ref="enterpriseUserRef"
      v-model="recipientList[1].list"
      multiple
      hiddenAdd
      @select="changeSelectItems"
    />
    <!-- 选择联系人 -->
    <ExtUserModal
      ref="extUserModalRef"
      v-model="recipientList[2].list"
      multiple
      hiddenAdd
      @select="changeSelectItems"
    />
  </el-dialog>
</template>

<script setup>
import { replacePhone, getFileName } from "@/utils/index"

const UserRolePicker = defineAsyncComponent(() =>
  import("@/flow/components/user-role/picker2.vue")
)
const EnterpriseUserModal = defineAsyncComponent(() =>
  import("@/views/template/useTemplate/components/EnterpriseUserModal.vue")
)
const ExtUserModal = defineAsyncComponent(() =>
  import("@/views/template/useTemplate/components/ExtUserModal.vue")
)

const SelectSealUserModal = defineAsyncComponent(() =>
  import("@/views/template/useTemplate/components/SelectSealUserModal/index.vue")
)
const FilePreview = defineAsyncComponent(() => import("./FilePreview.vue"))

const { proxy } = getCurrentInstance()
// const { recipient_type } = proxy.useDict("recipient_type")

const props = defineProps({
  fileInfo: {
    type: Object,
    default: () => ({}),
  },
  fileRaw: {
    type: Object,
    default: () => ({}),
  },
  // 业务线类型
  ascription: {
    default: null,
  },
})
const dialogVisible = ref(false)
const loading = ref(false)
const userPickerRef = ref(null)
const recipientList = ref([
  {
    label: "代表校内签署",
    recipientType: 0,
    color: "#24A87E",
    list: [],
  },
  {
    label: "代表校外企业签署",
    recipientType: 1,
    color: "#4898FF",
    list: [],
  },
  {
    label: "代表个人签署",
    recipientType: 2,
    color: "#FB9547",
    list: [],
  },
])

const { seal_belong_subject } = proxy.useDict("seal_belong_subject")
const organizationId = ref(null)

watchEffect(() => {
  if (seal_belong_subject.value?.length && !organizationId.value) {
    organizationId.value = seal_belong_subject.value[0].value
  }
})

// 移除文件后缀
const removeFileExtension = () => {
  if (props.fileRaw?.name || props.fileInfo?.filePaths?.[0]) {
    const filename = props.fileRaw?.name || getFileName(props.fileInfo?.filePaths?.[0])
    if (!filename) return ""
    return filename.split(".").slice(0, -1).join(".")
  }
  return ""
}
const handleClose = () => {
  dialogVisible.value = false
}

const open = (data) => {
  dialogVisible.value = true
  if (data.length) {
    setDraftData(data)
  }
}

const setDraftData = (signData) => {
  // 先按照 recipientType 分组
  const groupedData = signData.reduce((map, item) => {
    if (!map.has(item.recipientType)) {
      map.set(item.recipientType, [])
    }
    map.get(item.recipientType).push(item)
    return map
  }, new Map())

  // 批量处理每个类型的数据
  recipientList.value.forEach((recipient) => {
    const itemsToAdd = groupedData.get(recipient.recipientType) || []

    // 完全替换原有数据（根据需求可选择追加模式）
    recipient.list = itemsToAdd.map((item) => ({
      ...item,
      approveName: item.approveName || item.userName,
    }))
  })
}
const emit = defineEmits(["confirm"])
const handleSubmit = () => {
  // 合并所有类型的 list 为一个数组
  const allSignatories = recipientList.value.flatMap((recipient) => recipient.list)
  emit("confirm", allSignatories)
  handleClose()
}

const updateRow = ref(null)

/**
 * 处理用户选择变更
 * @param {Array<{name: string, phonenumber: string, userId: string}>} selectedItems 选中的用户列表
 */
const changeSelectItems = (selectedItems) => {
  if (!updateRow.value) return

  const { recipientType, signAttributes, approveType } = updateRow.value
  const currentList =
    recipientList.value.find((item) => item.recipientType === recipientType)?.list || []

  // 处理空选择情况：清空对应 signAttributes 类型的数据
  if (!selectedItems?.length) {
    if (recipientType === 0) {
      recipientList.value[0].list = currentList
        .map((user) => ({
          ...user,
          signAttributes: user.signAttributes?.filter(
            (attr) => !signAttributes.includes(attr)
          ),
        }))
        .filter((user) => user.signAttributes?.length > 0)
    }
    return
  }
  // 处理新增用户数据
  const newUsers = selectedItems.map((user) => ({
    ...user,
    recipientType,
    signAttributes,
    approveType,
    approveName: user.approveName || user.name || user.operatorName || user.userName,
    approveIdCardType: user.approveIdCardType || user.certificateType,
    approveIdCardNumber: user.approveIdCardNumber || user.certificateNo,
    approveMobile: user.approveMobile || user.phonenumber || user.phoneNumber,
    organizationName: user.organizationName || user.enterpriseName,
    insideFlag: user.userId ? 0 : 1, // 签署方标识，0-校内 1-校外
  }))

  // 校内签署方特殊处理
  if (recipientType === 0) {
    // 使用 Map 存储新用户，提高查找效率
    const newUsersMap = new Map(newUsers.map((user) => [user.userId, user]))

    // 更新现有用户列表
    const updatedList = currentList.map((user) => {
      if (user.userId && newUsersMap.has(user.userId)) {
        newUsersMap.delete(user.userId)
        return {
          ...user,
          signAttributes: Array.from(
            new Set([...user.signAttributes, ...signAttributes])
          ),
        }
      }
      return user
    })

    // 添加新用户
    recipientList.value[0].list = [...updatedList, ...newUsersMap.values()]

    // 处理未选中用户的 signAttributes
    const selectedUserIds = new Set(newUsers.map((u) => u.userId))
    recipientList.value[0].list = recipientList.value[0].list
      .map((user) => {
        if (
          user.userId &&
          !selectedUserIds.has(user.userId) &&
          user.signAttributes?.some((attr) => signAttributes.includes(attr))
        ) {
          return {
            ...user,
            signAttributes: user.signAttributes.filter(
              (attr) => !signAttributes.includes(attr)
            ),
          }
        }
        return user
      })
      .filter((user) => user.signAttributes?.length > 0)
  } else {
    // 非校内签署方直接追加新用户
    recipientList.value[recipientType].list = [...currentList, ...newUsers]
  }
}

// 添加签字人/用印人
const addSignatory = (row, type) => {
  updateRow.value = { ...row }
  if (row.recipientType === 0) {
    if (type === 0) {
      proxy.$refs.selectSealUserRef.open()
      updateRow.value.signAttributes = ["用印"]
    } else {
      const signList = recipientList.value[0].list.filter(
        (item) => item.signAttributes && item.signAttributes.includes("签字")
      )
      userPickerRef.value.onOpen(signList)
      updateRow.value.signAttributes = ["签字"]
    }
    updateRow.value.approveType = 0
  } else if (row.recipientType === 1) {
    // 企业联系人
    updateRow.value.approveType = 0
    proxy.$refs.enterpriseUserRef.open()
  } else {
    // 校外联系人
    updateRow.value.approveType = 1
    proxy.$refs.extUserModalRef.open()
  }
}

// 选择个人签署
const selectUser = (e) => {
  const orgName = proxy.selectDictLabel(seal_belong_subject.value, organizationId.value)
  e = e.map((i) => {
    return {
      ...i,
      organizationId: organizationId.value,
      organizationName: orgName,
    }
  })
  changeSelectItems(e)
}

const deleteUserItem = (index, parentIndex) => {
  recipientList.value[parentIndex].list.splice(index, 1)
}

// 手机号脱敏
const formatterPhoneNumber = (row) => {
  return replacePhone(row.approveMobile)
}
defineExpose({
  open,
})
</script>

<style scoped lang="scss">
@use "sass:list";
.signatory-container {
  display: flex;
  justify-content: space-between;
  height: 80vh;
  .form-body {
    width: 43%;
    height: 100%;
    overflow-y: auto;
    padding-left: 10px;
    padding-right: 10px;
    margin-right: 10px;
    box-sizing: border-box;
  }
}

.form-body-item {
  margin-bottom: 30px;
  position: relative;
  padding-top: 30px;
  .recipient-title {
    display: inline-block;
    color: #fff;
    height: 40px;
    line-height: 30px;
    width: 150px;
    text-align: center;
    background-size: 100% 100%;
    font-size: 14px;
    position: absolute;
    top: 0;
    left: -12px;
    z-index: 99;
  }
  $colors: #f2f8f7, #f2f6fc, #fbf6f3;
  $btnColors: #24a87e, #2e89ff, #ff7b17;
  @for $i from 1 through 3 {
    &:nth-child(#{$i}) {
      .recipient-title {
        background-image: url(@/assets/images/type-title-#{$i}.png);
      }
      :deep(.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell) {
        background-color: list.nth($colors, $i);
      }
      .opt-btn {
        color: list.nth($btnColors, $i);
        padding-left: 0;
        :deep(span) {
          margin-left: 2px;
        }
        &:hover {
          background: transparent;
        }
      }
    }
  }
  :deep(.el-table) {
    thead th.el-table__cell {
      color: #6b6b6b;
    }
    tr {
      background-color: #fbfbfc;
    }
    .el-table__cell {
      padding: 8px 0;
    }
    tbody .el-table__row:not(:last-child) {
      td.el-table__cell {
        border-bottom: 1px solid #f4f4f5;
      }
    }
    &.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
      background-color: #fbfbfc;
    }
    .el-button {
      padding-left: 0;
    }
  }
  .add-wrapper {
    margin-top: 10px;
    .el-button {
      @apply fw-500 p-x-6px bg-#fff min-h-30px h-30px rounded-4px;
      &:hover {
        background: var(--el-button-text-color);
      }
      :deep(span) {
        margin-left: 2px;
      }
    }
  }
}

.iframe-container {
  flex-shrink: 0;
  flex: auto;
  .title {
    @apply title-line-b before:(bg-[var(--el-color-primary-light-6)]) mb-10px;
    span {
      @apply z-10 relative;
    }
  }
}

.belong-radio-box {
  margin-bottom: 10px;
  :deep(.el-radio) {
    background: var(--el-color-primary-light-9);
    border: none;
    height: 40px;
    min-width: 200px;
    padding-left: 15px;
    border-radius: 6px;
    .el-radio__inner {
      background: none;
      border-color: #b2b2b2;
    }
    &.is-checked {
      background: var(--el-color-primary);
      .el-radio__label {
        color: #fff;
      }
      .el-radio__inner {
        border-color: #fff;
        &::before {
          border-color: var(--el-color-primary);
        }
        &::after {
          background: #fff !important;
          width: 7px;
          height: 7px;
        }
      }
    }
  }
}
</style>
