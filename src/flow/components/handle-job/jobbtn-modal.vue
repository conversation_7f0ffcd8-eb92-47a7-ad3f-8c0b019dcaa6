<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="600px"
      :before-close="handleClose"
      top="8vh"
  >

    <el-form
        ref="formRef"
        :model="form"
    >
      <el-form-item  prop="comment">
        <el-input v-model="form.comment" @input="handleChange" :autosize="{ minRows: 6, maxRows: 10 }" type="textarea" :maxlength="500" :placeholder="'请输入'+title" clearable/>
      </el-form-item>
      <el-form-item label="快捷填入" label-position="top">
        <div class="reason-list">
          <div v-for="(item,index) in reasonList" :key="index" class="reason-item cursor-default" @click="handleReply(item)">
              {{item}}
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm"> 确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { DIC_PROP } from "../../support/dict-prop"
import { getConfigByConfigKeyAPI } from  "@/api/jsonflow/do-job"
const { proxy } = getCurrentInstance()

const title = ref("新增")
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)
const Audit = ref()
const jobButton = ref()
const reasonList = ref([])

const props = defineProps({
  modelValue: {
    type: String
  },
})

const form = ref({
  comment:null
})

const emit = defineEmits(["confirm"])

const open = (jobBtn,isAudit) => {
  getReplyShortPhrase(jobBtn)
  dialogVisible.value = true
  setTitle(jobBtn)
  Audit.value = isAudit
  jobButton.value = jobBtn

}

// 标题
const setTitle = (jobBtn)=>{
 if(jobBtn === DIC_PROP.JOB_BTNS[0].value) title.value = "审批意见"
  else if (jobBtn === DIC_PROP.JOB_BTNS[10].value ) title.value = "驳回原因"
  else if (jobBtn === DIC_PROP.JOB_BTNS[1].value || jobBtn === DIC_PROP.JOB_BTNS[2].value) title.value = "加签原因"
  else if (jobBtn === DIC_PROP.JOB_BTNS[8].value) title.value = "退回原因"
  else if (jobBtn === DIC_PROP.JOB_BTNS[15].value) title.value = "拒绝原因"
}

// 获取回复短语
const getReplyShortPhrase = async (jobBtn) => {
  let configKey = "flowCommentAgree"
  if(jobBtn === DIC_PROP.JOB_BTNS[0].value) configKey = "flowCommentAgree"
  else if (jobBtn === DIC_PROP.JOB_BTNS[10].value || jobBtn === DIC_PROP.JOB_BTNS[8].value ||jobBtn === DIC_PROP.JOB_BTNS[15].value ) configKey = "flowCommentReject"
  try {
    const res = await getConfigByConfigKeyAPI(configKey)
    reasonList.value = res.object.split(',').map(item => item)
  } catch (error) {
    console.error("获取回复短语时出错:", error)
  }
}

//点击短语
const handleReply = (item) => {
  if (form.value.comment) {
    form.value.comment = form.value.comment + '，' + item
  } else {
    form.value.comment = item
  }
  handleChange()
}

// 输入改变
const handleChange = () => {
  emit('update:modelValue', form.value.comment)
}

// 取消
const handleClose = () => {
  proxy.resetForm("formRef")
  dialogVisible.value = false
}

// 确定
const handleConfirm = () => {
  emit("confirm", jobButton.value, Audit.value)
  handleClose()
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.reason-list{
  display: flex;
  flex-wrap: wrap
}
.reason-item{
  padding:0px 8px;
  background: #EAEBED;
  margin-right: 8px;
  border-radius: 4px;
  font-size: 14px;
  &:hover{
    background: #dadbdd;
  }
}
</style>
