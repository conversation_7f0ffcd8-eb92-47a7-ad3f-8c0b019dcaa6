import axios from 'axios'
import { ElNotification, ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import { getToken } from '@/utils/auth'
import { tansParams, blobValidate, checkEnglishRatio } from '@/utils/common'
import { paramsFilter } from '@/flow'
import { saveAs } from 'file-saver'
import errorCode from '@/utils/errorCode'
import modal from '@/plugins/modal'
import cache from '@/plugins/cache'
import useUserStore from '@/store/modules/user'
import qs from 'qs'
import { wrapEncryption, encryptRequestParams, decrypt } from './apiCrypto';

let downloadLoadingInstance
// 是否显示重新登录
export let isRelogin = { show: false }

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 允许后台设置认证信息
axios.defaults.withCredentials = true
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时时间
  timeout: 1000000,
  paramsSerializer: {
    serialize: (params) => {
      return qs.stringify(paramsFilter(params), { arrayFormat: 'repeat' })
    }
  }
})

// request拦截器
service.interceptors.request.use(
  (config) => {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
    if (getToken() && !isToken) {
      config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }

    if (useUserStore().id) {
      config.headers['Tenant-Id'] = 1
    }

    // 如果是 GET ，加密 config.param 的每一个参数，并URLencode
    if (config.method === 'get' && config.params) {
      config.params = encryptRequestParams(config.params)
    }

    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      const requestObj = {
        url: config.url,
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime()
      }
      const requestSize = JSON.stringify(requestObj)?.length // 请求数据大小
      const limitSize = 5 * 1024 * 1024 // 限制存放数据5M
      if (requestSize >= limitSize) {
        console.warn(`[${config.url}]: ` + '请求数据大小超出允许的5M限制，无法进行防重复提交验证。')
        return config
      }
      const sessionObj = cache.session.getJSON('sessionObj')
      if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
        cache.session.setJSON('sessionObj', requestObj)
      } else {
        const s_url = sessionObj.url // 请求地址
        const s_data = sessionObj.data // 请求数据
        const s_time = sessionObj.time // 请求时间
        const interval = 500 // 间隔时间(ms)，小于此时间视为重复提交
        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          // const message = '数据正在处理，请勿重复提交'
          const message = '请勿重复操作'
          console.warn(`[${s_url}]: ` + message)
          return Promise.reject(new Error(message))
        } else {
          cache.session.setJSON('sessionObj', requestObj)
        }
      }
    }
    return config
  },
  (error) => {
    Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    const hiddenError = res.config.headers?.hiddenError
    // 未设置状态码则默认成功状态
    const code = +res.data.code || 200
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || res.data.message || errorCode['default']
    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res
    }

    if (code === 401) {
      console.log('未登录或登录超时。请重新登录', isRelogin.show)
      if (!isRelogin.show) {
        isRelogin.show = true
        ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            isRelogin.show = false
            useUserStore().logOut()
          })
          .catch(() => {
            isRelogin.show = false
          })
      }
      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
    } else if (code === 500) {
      let resultErr = checkEnglishRatio(res.data?.object) ? msg : (res.data?.object || res.data?.message)
      !hiddenError && modal.msgError(resultErr)
      return Promise.reject(resultErr)
    } else if (code === 601) {
      modal.msgWarning(msg)
      return Promise.reject(new Error(msg))
    } else if (code !== 200) {
      ElNotification.error({ title: msg })
      return Promise.reject('error')
    } else {
      return Promise.resolve(res.data)
    }
  },
  (error) => {
    let { message } = error
    if (message == 'Network Error') {
      message = '后端接口连接异常'
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时'
    } else if (message.includes('Request failed with status code')) {
      message = '系统接口' + message.substr(message.length - 3) + '异常'
    }

    if (message !== '请勿重复操作') {
      modal.msgError(message)
    }

    return Promise.reject(error)
  }
)



// 通用下载方法
export function download (url, params, filename, config = {}) {
  downloadLoadingInstance = ElLoading.service({
    text: '正在下载数据，请稍候',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  // 合并 headers，全部转小写 key
  const headers = {
    ...(config.headers || {})
  };
  // 统一小写处理
  const contentType = (headers['content-type'] || headers['Content-Type'] || '').toLowerCase();

  let data = params;
  let transformRequest = undefined;

  if (contentType === 'application/json') {
    data = JSON.stringify(params); // 必须转字符串
    transformRequest = undefined;
  } else if (contentType === 'application/x-www-form-urlencoded') {
    transformRequest = [
      (params) => tansParams(params)
    ];
  }
  return service[config.method || 'post'](url, data, {
    ...config,
    headers,
    transformRequest,
    responseType: 'blob'
  })
    .then(async (res) => {
      let { data } = res;
      const isBlob = blobValidate(data);
      if (isBlob) {
        const blob = new Blob([data]);
        saveAs(blob, filename);
      } else {
        const resText = await data.text();
        const rspObj = JSON.parse(resText);
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default'];
        modal.msgError(errMsg);
      }
      downloadLoadingInstance.close();
    })
    .catch((r) => {
      modal.msgError('下载文件出现错误，请联系管理员！');
      downloadLoadingInstance.close();
    });
}

export default service
