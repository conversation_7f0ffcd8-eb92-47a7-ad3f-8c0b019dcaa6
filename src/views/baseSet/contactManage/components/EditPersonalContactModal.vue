<template>
  <el-dialog
      v-model="dialogVisible"
      width="500px"
      :title="title"
      append-to-body
      :before-close="handleClose"
  >
    <el-form
        :model="form"
        ref="formRef"
        :rules="rules"
        label-width="110px"
        :inline="false"
    >
      <el-form-item label="姓名" prop="userName">
        <el-input
            v-model="form.userName"
            clearable
            :maxlength="50"
            placeholder="请输入姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
            v-model="form.phoneNumber"
            clearable
            :maxlength="11"
            placeholder="请输入手机号"
        ></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="success" v-if="!props.hiddenAdd" :loading="loading" @click="handleSave('add')">
        保存并新建
      </el-button>
      <el-button type="primary" @click="handleSave" :loading="loading"> 保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { addExtUserAPI, updateExtUserAPI } from "@/api/ess/baseSet/contacts"
import { rule } from "@/utils/validate"

const { proxy } = getCurrentInstance()

const { certificate_type } = proxy.useDict("certificate_type")

defineOptions({
  inheritAttrs: false,
})

const props = defineProps({
  hiddenAdd: {
    type: Boolean,
    default: false
  }
})

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)
const title = ref("")
const form = ref({
  phoneNumber: "",
  userName: "",
})
let _form = toRaw({
  ...form.value,
})
const rules = reactive({
  userName: [
    { required: true, message: "请输入姓名", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
    { validator: rule.inputName, trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  phoneNumber: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { validator: rule.mobilePhone, trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
})

const emit = defineEmits(["change", "refresh"])

function open(row) {
  dialogVisible.value = true
  if (row) {
    form.value = { ...row }
    delete form.value.createTime
  }
  title.value = row?.id ? "修改联系人" : "新增联系人"
}

/** 提交按钮操作 */
async function handleSave(type = "save") {
  try {
    // 表单验证
    const isValid = await formRef.value.validate()
    if (!isValid) return

    loading.value = true

    const apiMethod = form.value.id ? updateExtUserAPI : addExtUserAPI

    const res = await apiMethod(form.value)
    if (+res.code !== 200) return
    handleSuccess(type)
  } catch (error) {

  } finally {
    loading.value = false
  }
}

/** 成功处理逻辑 */
function handleSuccess(type) {
  const isEdit = !!form.value.id
  proxy.$modal.msgSuccess(isEdit ? "修改成功" : "新增成功")
  // 保存并新建

  if (type === "add") {
    proxy.resetForm("formRef")
    emit("change")
    title.value = "新增联系人"
  } else {
    handleClose()
    emit(isEdit ? "change" : "refresh")
  }
}

function handleClose() {
  proxy.resetForm("formRef")
  form.value = { ..._form }
  dialogVisible.value = false
}

defineExpose({
  open,
})
</script>
