import router from './router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { isHttp, isPathMatch } from '@/utils/validate'
import { getToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'
import { flowConfig } from '@/flow/designer/config/flow-config'
import { replaceRouterRoute } from '@/flow/support/extend'
import modal from '@/plugins/modal'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/account', '/realName','/outSide', '/inner']

const isWhiteList = ({path,meta}) => {
	return whiteList.some(pattern => isPathMatch(pattern, path)) || !!meta?.isAuth
}

router.beforeEach((to, from, next) => {
	NProgress.start()
	// 设置标题
	to.meta.title && useSettingsStore().setTitle(to.meta.title)
	if (isWhiteList(to)) {
		next()
	}
	else if (to.fullPath.indexOf(flowConfig.mobileConfig.mobilePrefix) !== -1) {
		flowRouter(to, from, next)
	}
	else {
		if (import.meta.env.VITE_CAS_SSO) {
			handleCasLogin(to, from, next)
		}
		else {
			handleNormalLogin(to, from, next)
		}
	}
})

async function flowRouter(to, from, next) {
	try {
		await replaceRouterRoute(to, router)
		next()
		NProgress.done()
	}
	catch (error) {}
}

// 单点登录
async function handleCasLogin(to, from, next) {
	const module = from.query.module || import.meta.env.VITE_APP_MODULE
	try {
		if (!getToken()) {
			await useUserStore().casLogin({ module })
		}
		await getUserInfo(to, from, next)
	}
	catch (err) {
		modal.msgError(err)
		useUserStore().logOut()
	}
	finally {
		NProgress.done()
	}
}
// 普通登录
function handleNormalLogin(to, from, next) {
	if (getToken()) {
		if (to.path === '/login') {
			next({ path: '/' })
		}else {
			getUserInfo(to, from, next)
		}
	}
	else {
		next({ path: `/login?redirect=${to.fullPath}` })
	}
	NProgress.done()
}

async function getUserInfo(to, from, next) {
	if (useUserStore().roles.length === 0) {
		try {
			await useUserStore().getInfo()
			if (!window.__POWERED_BY_QIANKUN__) {
				await usePermissionStore().generateRoutes()
				// usePermissionStore()
				//             .generateRoutes()
				//             .then((accessRoutes) => {
				//               // 根据roles权限生成可访问的路由表
				//               accessRoutes.forEach((route) => {
				//                 if (!isHttp(route.path)) {
				//                   router.addRoute(route) // 动态添加可访问路由表
				//                 }
				//               })
				//               next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
				//             })
			}
			else {
				// TODO:微前端 暂时注释掉 后续微前端适配
				// await store.dispatch('GenerateRoutesToSetData', actions.actions.menuRes)
			}
			next({ ...to, replace: true })
		}
		catch (err) {
			console.log('err', err)
			useUserStore().logOut()
			next('/login') // 假设'/login'是登录路由
		}
	}
	else {
		next()
	}
}

router.afterEach(() => {
	NProgress.done()
})
