<template>
  <router-view />
</template>

<script setup>
import useSettingsStore from "@/store/modules/settings"
import { handleThemeStyle } from "@/utils/theme"
import { logoSettingList } from "@/api/system/logoSetting"

// 设置初始化，防止刷新时恢复默认
onBeforeMount(() => {})
onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
  })
})

// 获取系统配置
const getThemeSetting = async () => {
  const res = await logoSettingList(`/${import.meta.env.VITE_PUBLIC_PATH}/`)
  if (res?.object?.length) useSettingsStore().setTheme(res.object[0])
}

getThemeSetting()
</script>
