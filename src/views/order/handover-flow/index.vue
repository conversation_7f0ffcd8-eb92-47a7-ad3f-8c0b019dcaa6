<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item :label="$t('handoverFlow.code')" prop="code">
						<el-input
							v-model="state.queryForm.code"
							:placeholder="t('handoverFlow.inputCodeTip')"
							style="max-width: 180px"
						/>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.handoverReason')" prop="handoverReason">
						<el-select
							v-model="state.queryForm.handoverReason"
							:placeholder="t('handoverFlow.inputHandoverReasonTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_REASON"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.receiveDept')" prop="receiveDept">
						<el-select
							v-model="state.queryForm.receiveDept"
							:placeholder="t('handoverFlow.inputReceiveDeptTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.receiveDept"
								:key="index"
								:label="item.name"
								:value="item.deptId"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.receiveUser')" prop="receiveUser">
						<el-select
							v-model="state.queryForm.receiveUser"
							:placeholder="t('handoverFlow.inputReceiveUserTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.receiveUser"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.type')" prop="type">
						<el-select
							v-model="state.queryForm.type"
							:placeholder="t('handoverFlow.inputTypeTip')"
							clearable
							filterable
							style="max-width: 180px"
							@change="typeChange"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_TYPE"
								:key="index"
								:disabled="DIC_PROP.HANDOVER_TYPE[1].value === item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							{{ $t('common.queryBtn') }}
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							{{ $t('common.resetBtn') }}
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<el-button
						v-auth="'order_handoverflow_add'"
						icon="Promotion"
						type="primary"
						class="ml10"
						:loading="state.loading"
						@click="handleInitiate"
					>
						{{ $t('jfI18n.initHandover') }}
					</el-button>
					<right-toolbar
						v-model:show-search="showSearch"
						:export="'order_handoverflow_export'"
						class="ml10"
						style="float: right;margin-right: 20px"
						@export-excel="exportExcel"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" :label="t('handoverFlow.index')" width="40" />
				<el-table-column prop="code" :label="t('handoverFlow.code')" show-overflow-tooltip />
				<el-table-column prop="flowKey" :label="t('handoverFlow.flowKey')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.flowInstId"
							:value="scope.row.flowKey"
							:value-key="'flowKey'"
							:show-key="'flowName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="runJobId" :label="t('handoverNodeRecord.runJobId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.runJobId"
							:value="scope.row.runJobId"
							:value-key="'id'"
							:show-key="'jobName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="type" :label="t('handoverFlow.type')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.HANDOVER_TYPE" :value="scope.row.type ? scope.row.type : state.queryForm.type" />
					</template>
				</el-table-column>
				<el-table-column prop="status" :label="t('handoverFlow.status')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.HANDOVER_STATUS" :value="scope.row.status" />
					</template>
				</el-table-column>
				<el-table-column prop="createUser" :label="t('handoverFlow.createUser')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.createUser"
							:value="scope.row.createUser"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" :label="t('handoverFlow.createTime')" show-overflow-tooltip />
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />

		<el-dialog
			v-if="data.showNodeHandover"
			v-model="data.showNodeHandover"
			append-to-body
			top="20px"
			width="80%"
			:title="data.handoverTitle"
		>
			<handover-node-record
				ref="node"
				:selections="data.selections"
				:handover-form="state.queryForm"
				@on-handover-flow="onHandoverFlow"
			/>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemHandoverFlow">
import { BasicTableProps, useTable } from "@/hooks/table"
import { useMessage, useMessageBox } from "@/hooks/message"
import { useI18n } from "vue-i18n"
import { onLoadDicUrl, onLoaded } from "@/flow/components/convert-name/convert"
import { validateNull } from "@/utils/validate"
import { DIC_PROP } from "@/flow/support/dict-prop"
import * as handoverFlow from "@/api/order/handover-flow"
import * as runJob from "@/api/jsonflow/run-job"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))
const HandoverNodeRecord = defineAsyncComponent(() => import("@/views/order/handover-node-record/initiate.vue"))
const { t } = useI18n()
// 定义查询字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "receiveDept" }, { key: "receiveUser" })
onMounted(() => {
	onLoad(dicData)
})
// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		type: DIC_PROP.HANDOVER_TYPE[0].value,
		status: DIC_PROP.HANDOVER_STATUS[1].value,
	},
	pageList: runJob.fetchNodeHandover,
	onLoaded: onLoaded({ key: "createUser" }, { key: "flowInstId" }, { key: "runJobId" }),
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	// 清空多选
	selectObjs.value = []
	data.selections = []
	getDataList()
}

// 导出excel
const exportExcel = () => {
	downBlobFile("/cloud-order/handover-flow/export", state.queryForm, "handover-flow.xlsx")
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	data.selections = objs
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t("common.delConfirmText"))
	}
	catch {
		return
	}
	try {
		await handoverFlow.delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess(t("common.delSuccessText"))
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
}

const data = reactive({
	selections: [],
	showNodeHandover: false,
	handoverTitle: null,
})

function fetchListFun(fetchList, params) {
	if (!params.status || !params.type) {
		proxy.$modal.info("请选择必填交接条件")
		return
	}
	state.pageList = fetchList
	getDataList()
}

function typeChange() {
	let type = state.queryForm.type
	let handoverType = DIC_PROP.HANDOVER_TYPE
	if (type === handoverType[0].value) {
		// 任务交接
		fetchListFun(runJob.fetchNodeHandover, state.queryForm)
	}
	else {
		// 公共交接 TODO 需自行扩展自身业务，参考任务交接
		// fetchListFun(company.fetchCommonHandover, state.queryForm)
	}
	handleSelectionChange([])
}

function handleInitiate() {
	const type = state.queryForm.type
	if (validateInfo(type)) {
		return
	}
	data.handoverTitle = DIC_PROP.HANDOVER_TYPE.filter(f => f.value === type)[0].label
	// 转岗（可选择交接公司）和离职（全部交接）
	// 任务交接交接数据一定不能为空
	if (validateNull(data.selections)) {
		proxy.$modal.msg("请选择交接项")
		return
	}
	data.showNodeHandover = true
}

function validateInfo(type) {
	const handoverReason = state.queryForm.handoverReason
	if (!handoverReason) {
		proxy.$modal.msg("请选择交接原因")
		return true
	}
	if (!type) {
		proxy.$modal.msg("请选择交接类型")
		return true
	}
	if (!state.queryForm.status) {
		proxy.$modal.msg("请选择交接状态")
		return true
	}
	if (!state.queryForm.receiveDept) {
		proxy.$modal.msg("请选择接收部门")
		return true
	}
	if (!state.queryForm.receiveUser) {
		proxy.$modal.msg("请选择接收人")
		return true
	}
	return false
}

function onHandoverFlow(type) {
	getDataList()
	data.showNodeHandover = false
}

onMounted(() => {
	state.loading = false
})
</script>
