<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="1200px"
    :close-on-click-modal="false"
    draggable
    top="5vh"
  >
    <el-form
      ref="dataFormRef"
      v-loading="loading"
      :model="form"
      :rules="dataRules"
      label-width="130px"
      :disabled="operType === 'view'"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="流程KEY" prop="flowKey">
            <el-input v-model="form.flowKey" placeholder="请输入流程KEY" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="表单名称" prop="formName">
            <el-input v-model="form.formName" placeholder="请输入表单名称" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="图标" prop="icon">
            <ImageSelector height="40" type="flow" v-model="form.icon" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="分组名称" prop="groupName">
            <div class="flex">
              <el-select
                v-model="form.groupName"
                placeholder="请选择所属分类"
                clearable
                filterable
                allow-create
                default-first-option
              >
                <el-option
                  v-for="(item, index) in dicData.groupName"
                  :key="index"
                  :label="item.groupName"
                  :value="item.groupName"
                />
              </el-select>
              <el-button
                v-if="operType !== 'view'"
                type="primary"
                @click="handleAddGroupName"
                class="ml-10px mt-5px p-x-8px!"
                icon="plus"
                plain
                size="small"
              >
              </el-button>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="设置可见范围" prop="range">
            <el-radio-group v-model="range">
              <el-radio :value="1">成员选择</el-radio>
              <el-radio :value="2">范围全选</el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="mb-20px">
            <UserTreeTransfer
              ref="rangeRef"
              :cols="3"
              :rangeType="range"
              :select-types="['user', 'dept', 'role', 'tag']"
              v-model:ranges="form.rangeRefs"
              v-model:users="form.userRefs"
              v-if="visible"
            />
          </div>
        </el-col>

        <el-col :span="12">
          <el-form-item label="表单类型" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择表单类型"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in DIC_PROP.FORM_TYPE"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col v-if="form.type === DIC_PROP.FORM_TYPE[1].value" :span="12">
          <el-form-item label="PC端路径" prop="path">
            <el-input v-model="form.path" placeholder="请输入PC端路径" clearable />
          </el-form-item>
        </el-col>
        <!-- 
        <el-col :span="12">
          <el-form-item label="表名" prop="tableName">
            <el-tooltip
              content="若自行调用接口保存表单数据，则此处需为空"
              placement="top"
            >
              <el-select
                v-model="form.tableName"
                placeholder="请选择表名"
                clearable
                filterable
              >
                <el-option
                  v-for="(item, index) in dicData.tableName"
                  :key="index"
                  :label="item.tableName"
                  :value="item.tableName"
                />
              </el-select>
            </el-tooltip>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="权限" prop="permission">
            <el-select
              v-model="form.permission"
              placeholder="请选择权限"
              clearable
              filterable
              multiple
            >
              <el-option
                v-for="(item, index) in dicData.permission"
                :key="index"
                :label="item.roleName"
                :value="item.roleId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="关联子表名称" prop="subTableName">
            <el-tooltip
              content="当存在多个关联子表时，多个子表名称名顺序与《关联子表属性》顺序一一对应"
              placement="top"
            >
              <el-select
                v-model="form.subTableName"
                placeholder="请选择关联子表名称"
                clearable
                filterable
                multiple
                @change="changeSubTableName"
              >
                <el-option
                  v-for="(item, index) in dicData.tableName"
                  :key="index"
                  :label="item.tableName"
                  :value="item.tableName"
                />
              </el-select>
            </el-tooltip>
          </el-form-item>
        </el-col>

        <el-col v-if="!validateNull(form.subTableName)" :span="12">
          <el-tooltip
            content="当关联子表名称存在时，可配置子表中关联【主表主键】的列名，如main_id"
            placement="top"
          >
            <el-form-item label="关联主表列名" prop="subMainField">
              <el-input v-model="form.subMainField" placeholder="请输入关联主表列名" />
            </el-form-item>
          </el-tooltip>
        </el-col>

        <el-col v-if="!validateNull(form.subTableName)" :span="12">
          <el-tooltip
            content="当关联子表名称存在时，可配置主表中关联【子表集合数据】的属性名，如subFormList。多个子表属性名以逗号分割，与《关联子表名称》顺序一一对应"
            placement="top"
          >
            <el-form-item label="关联子表属性" prop="mainSubProp">
              <el-input v-model="form.mainSubProp" placeholder="请输入关联子表属性" />
            </el-form-item>
          </el-tooltip>
        </el-col> -->

        <el-col :span="24">
          <el-form-item label="表单备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="form.sort"
              :min="1"
              :max="1000"
              placeholder="请输入排序值"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="默认展示" prop="isActive">
            <el-radio-group v-model="form.isActive">
              <el-radio
                v-for="(item, index) in DIC_PROP.YES_OR_NO"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="24">
          <el-form-item label="提交时自动审批" prop="isAutoAudit">
            <el-radio-group v-model="form.isAutoAudit">
              <el-radio
                v-for="(item, index) in DIC_PROP.YES_OR_NO"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col> -->

        <el-col :span="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="(item, index) in DIC_PROP.TEMP_STATUS"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template v-if="operType !== 'view'" #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="FlowApplicationDialog">
import { useMessageBox } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/order/flow-application"
import { validateNull } from "@/utils/validate"
import { onLoadDicUrl, onUpdateDicData } from "@/flow/components/convert-name/convert"
import { setPropsNull } from "../../support/common"
import { DIC_PROP } from "../../support/dict-prop"
import { validateFormTypeSave } from "@/api/order/order-key-vue"
const emit = defineEmits(["refresh"])

const { proxy } = getCurrentInstance()
// 引入组件
const ImageSelector = defineAsyncComponent(
  () => import("@/components/ImageSelector/index.vue")
)
const UserTreeTransfer = defineAsyncComponent(
  () => import("@/views/components/UserTreeTransfer/index.vue")
)
// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")

const range = ref(1) // 默认成员选择

// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl(
  { key: "groupName" },
  { key: "tableName" },
  { key: "permission" }
)
const onUpdate = onUpdateDicData({ key: "groupName" })
onMounted(() => {
  onLoad(dicData)
})
// 提交表单数据
const form = reactive({
  flowKey: "",
  icon: "",
  formName: "",
  groupName: "",
  tableName: "",
  permission: "",
  remark: "",
  status: "-1",
  isActive: "1",
  isAutoAudit: "0",
  sort: 1,
  rangeRefs: [],
  userRefs: [],
})

// 定义校验规则
const dataRules = ref({
  flowKey: [{ required: true, message: "流程KEY不能为空", trigger: "blur" }],
  icon: [{ required: true, message: "表单图标不能为空", trigger: "blur" }],
  formName: [{ required: true, message: "表单名称不能为空", trigger: "blur" }],
  groupName: [{ required: true, message: "分组名称不能为空", trigger: "blur" }],
  isActive: [{ required: true, message: "默认展示不能为空", trigger: "blur" }],
  isAutoAudit: [{ required: true, message: "提交时自动审批不能为空", trigger: "blur" }],
  sort: [{ required: true, message: "排序值不能为空", trigger: "blur" }],
  type: [{ required: true, message: "表单类型不能为空", trigger: "blur" }],
  status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
  visible.value = true
  operType.value = type
  setPropsNull(form, "id", "flowKey", "defFlowId")

  const typeMap = {
    add: "新增",
    edit: "编辑",
    view: "查看",
  }
  title.value = typeMap[type]
  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields()
    // 获取FlowApplication信息
    if (id) {
      form.id = id
      getFlowApplicationData(id)
    }
  })
}

function handleAddGroupName() {
  useMessageBox()
    .prompt("请输入新的分组名称")
    .then(({ value }) => {
      form.groupName = value
      onUpdate(dicData, form)
    })
}

function changeSubTableName() {
  form.subMainField = null
  form.mainSubProp = null
}

// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => {})
  if (!valid) {
    return false
  }

  if (form.type === DIC_PROP.FORM_TYPE[1].value) {
    if (!form.path) {
      proxy.$modal.msgWarning("当前表单类型为系统表单，【PC端路径】不能为空")
      return false
    }
  }
  if (!validateNull(form.subTableName)) {
    if (!form.subMainField || !form.mainSubProp) {
      proxy.$modal.msgWarning("请填写 关联主表列名 或者 关联子表属性")
      return false
    }
  }
  let isDesign = form.type !== DIC_PROP.FORM_TYPE[1].value
  if (isDesign && !form.formInfo) {
    proxy.$modal.msgWarning("当前表单类型为设计表单，请先完善第二项设计的表单内容")
    return false
  }
  validateFormTypeSave(form)
  try {
    loading.value = true
    form.id ? await putObj(form) : await addObj(form)
    proxy.$modal.msgSuccess(form.id ? "编辑成功" : "新增成功")
    visible.value = false
    emit("refresh")
  } finally {
    loading.value = false
  }
}

// 初始化表单数据
const getFlowApplicationData = (id: string) => {
  // 获取数据
  loading.value = true
  getObj(id)
    .then((res: any) => {
      Object.assign(form, res.object)
    })
    .finally(() => {
      loading.value = false
    })
}

// 暴露变量
defineExpose({
  openDialog,
})
</script>
