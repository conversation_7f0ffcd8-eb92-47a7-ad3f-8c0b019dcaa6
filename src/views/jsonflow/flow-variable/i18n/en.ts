export default {
	flowVariable: {
		index: "#",
		importflowVariableTip: "import FlowVariable",
		id: "id",
		flowInstId: "flowInstId",
		flowKey: "flowKey",
		varKey: "varKey",
		varVal: "varVal",
		createUser: "createUser",
		createTime: "createTime",
		code: "code",
		updateUser: "updateUser",
		updateTime: "updateTime",
		tenantId: "tenantId",
		inputIdTip: "input id",
		inputFlowInstIdTip: "input flowInstId",
		inputFlowKeyTip: "input flowKey",
		inputVarKeyTip: "input varKey",
		inputVarValTip: "input varVal",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputCodeTip: "input code",
		inputUpdateUserTip: "input updateUser",
		inputUpdateTimeTip: "input updateTime",
		inputTenantIdTip: "input tenantId",
	},
}
