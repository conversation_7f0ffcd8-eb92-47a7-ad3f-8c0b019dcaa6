<template>
	<div class="table-container">
		<div class="white-body-box">
			<el-table
				v-loading="loading"
				class="table-empty"
				:data="data"
				v-bind="$attrs"
			>
				<el-table-column
					v-if="hasSelection"
					type="selection"
					width="55"
					align="center"
					:reserve-selection="reserveSelection"
					fixed="left"
				/>
				<el-table-column v-if="hasSerial" label="序号" type="index" />
				<template v-for="col in columns" :key="col.prop">
					<slot v-if="col.slot" />
					<el-table-column
						v-else
						:prop="col.prop"
						:label="col.label"
						:width="col.width"
						:align="col.align || align"
						:min-width="col.minWidth"
						:fixed="col.fixed"
						:sortable="col.sortable"
						:show-overflow-tooltip="col.overflowTip"
					/>
				</template>
			</el-table>
		</div>
		<pagination
			v-show="pageTotal > 0"
			v-model:page="page"
			v-model:limit="limit"
			:total="pageTotal"
			@pagination="getList"
		/>
	</div>
</template>

<script setup>
const props = defineProps({
	loading: {
		type: Boolean,
		default: false,
	},
	// 数据刷新后是否保留选项
	reserveSelection: {
		type: Boolean,
		default: false,
	},
	// 表格数据
	data: {
		type: Array,
		default: () => [],
	},
	columns: {
		type: Array,
		default: () => [],
	},
	// 是否有选择
	hasSelection: {
		type: Boolean,
		default: true,
	},
	// 是否有序号列
	hasSelection: {
		type: Boolean,
		default: true,
	},
	align: {
		type: String,
		default: "left",
	},
	// 页码总数
	pageTotal: {
		type: Number,
		default: 0,
	},
})

const emit = defineEmits(["pagination"])

const page = defineModel("page")
const limit = defineModel("limit")
const getList = e => {
	emit("pagination", e)
}
</script>

<style scoped lang="scss"></style>
