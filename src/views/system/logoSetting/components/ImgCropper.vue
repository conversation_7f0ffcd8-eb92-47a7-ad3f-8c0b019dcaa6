<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    append-to-body
    @opened="modalOpened"
    @close="closeDialog"
  >
    <el-row>
      <el-col :xs="24" :md="12" :style="{ height: '350px' }">
        <vue-cropper
          v-if="visible"
          ref="cropper"
          :img="options.img"
          :info="true"
          output-type="png"
          :auto-crop="options.autoCrop"
          :auto-crop-width="options.autoCropWidth"
          :auto-crop-height="options.autoCropHeight"
          :fixed-box="options.fixedBox"
          @real-time="realTime"
        />
      </el-col>
      <el-col :xs="24" :md="12" :style="{ height: '350px' }">
        <div
          class="avatar-upload-preview"
          :style="{
            width: options.autoCropWidth,
            height: options.autoCropHeight,
          }"
        >
          <img :src="options.previews.url" :style="options.previews.img" />
        </div>
      </el-col>
    </el-row>
    <br />
    <div class="opt-footer-btn">
      <div class="btn-upload">
        <el-upload
          action="uploadAndAccess"
          :http-request="requestUpload"
          :show-file-list="false"
          :before-upload="beforeUpload"
        >
          <el-button type="primary">
            选择图片
            <el-icon class="el-icon--right">
              <Upload />
            </el-icon>
          </el-button>
        </el-upload>
        <el-button icon="Plus" @click="changeScale(1)" />
        <el-button icon="Minus" @click="changeScale(-1)" />
        <el-button icon="RefreshLeft" @click="rotateLeft()" />
        <el-button icon="RefreshRight" @click="rotateRight()" />
      </div>

      <el-button type="primary" @click="uploadImg()"> 提 交 </el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { uploadAndAccess } from "@/api/file/file" // 上传并访问
import { VueCropper } from "vue-cropper"
import "vue-cropper/dist/index.css"

const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const visible = ref(false)
const title = ref("裁剪图片")
// 图片裁剪数据
const options = reactive({
  img: null, // 裁剪图片的地址
  autoCrop: true, // 是否默认生成截图框
  autoCropWidth: 280, // 默认生成截图框宽度
  autoCropHeight: 60, // 默认生成截图框高度
  fixedBox: true, // 固定截图框大小 不允许改变
  outputType: "png", // 默认生成截图为PNG格式
  filename: "avatar", // 文件名称
  previews: {}, // 预览数据
})

/** 编辑头像 */
function editCropper(img) {
  dialogVisible.value = true
  options.img = img
}

/** 打开弹出层结束时的回调 */
function modalOpened() {
  visible.value = true
}

/** 覆盖默认上传行为 */
function requestUpload() {}

/** 向左旋转 */
function rotateLeft() {
  proxy.$refs.cropper.rotateLeft()
}

/** 向右旋转 */
function rotateRight() {
  proxy.$refs.cropper.rotateRight()
}

/** 图片缩放 */
function changeScale(num) {
  num = num || 1
  proxy.$refs.cropper.changeScale(num)
}

/** 上传预处理 */
function beforeUpload(file) {
  if (file.type.indexOf("image/") == -1) {
    proxy.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。")
  } else {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      options.img = reader.result
      options.filename = file.name
    }
  }
}
const emit = defineEmits(["change"])
/** 上传图片 */
function uploadImg() {
  proxy.$refs.cropper.getCropBlob((data) => {
    let formData = new FormData()
    formData.append("file", data)
    uploadAndAccess(formData).then((res) => {
      dialogVisible.value = false
      options.img = res.object.imageUrl
      emit("change", res.object.imageUrl)
      proxy.$modal.msgSuccess("修改成功")
      visible.value = false
    })
  })
}

/** 实时预览 */
function realTime(data) {
  options.previews = data
}

/** 关闭窗口 */
function closeDialog() {
  options.visible = false
}
defineExpose({
  editCropper,
})
</script>

<style scoped lang="scss">
.el-col {
  position: relative;
}

.avatar-upload-preview {
  height: 60px;
  width: 280px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.opt-footer-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .btn-upload {
    display: flex;
    align-items: center;
    .el-upload {
      margin-right: 10px;
      .el-button {
        margin-right: 10px;
      }
    }
  }
}
</style>
