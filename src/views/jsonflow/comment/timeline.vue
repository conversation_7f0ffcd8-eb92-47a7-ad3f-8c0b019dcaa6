<template>
  <div>
    <div class="text-warning m-b-15px text-14px ml-30px">
      注：一个任务若被审批多次，则会显示出多条审批记录（审批留痕）
    </div>
    <div id="printComment">
      <el-timeline class="audit-timeline">
        <el-timeline-item v-for="(item, index) in tableData" :key="index" size="large">
          <template #dot>
            <div class="dot-circle">
              <img class="avatar" src="@/assets/images/icon/grey-avatar.png" />
              <img
                class="status"
                v-if="
                  ['2', '9'].includes(item.status) ||
                  item.handleTypeName?.includes('拒绝')
                "
                src="@/assets/images/icon/danger.png"
              />
              <img
                class="status"
                v-else-if="['1', '3'].includes(item.status)"
                src="@/assets/images/icon/success.png"
              />

              <img
                class="status"
                v-else-if="['0', '-1'].includes(item.status)"
                src="@/assets/images/icon/wait.png"
              />
            </div>
          </template>
          <div class="timeline-item pt-3px">
            <div class="flex justify-between" :class="{ 'm-t-7px': !item.handleComment }">
              <div>
                <convert-role-name :value="item" class="inline font-bold mr-10px" />
                <span
                  class="color-[#6B6B6B]"
                  :class="{ 'text-danger': item.handleTypeName?.includes('拒绝') }"
                >
                  {{ item.handleTypeName || item.jobName }}
                </span>
              </div>
              <span class="color-[#6B6B6B]">{{ item.endTime }}</span>
            </div>
            <div class="timeline-item-content" v-if="item.handleComment">
              <div class="desc">
                <dict-tag
                  v-if="item.belongType !== '0'"
                  :options="DIC_PROP.BELONG_TYPE"
                  :value="item.belongType"
                />
                <dict-tag
                  v-else
                  :options="DIC_PROP.SIGNATURE_TYPE"
                  :value="item.signatureType"
                />
                <div v-if="item.status === '0'">
                  操作：<span class="text-warning">审核中</span>
                </div>
                <div v-if="item.handleComment">审批意见: {{ item.handleComment }}</div>
                <template v-if="item.signName">
                  <img width="200px" class="mt-10px" :src="item.signName" />
                </template>
              </div>
              <!-- <div class="handle-btn">
                <el-link
                  v-if="(item.status === '0' || item.status === '9') && item.roleId"
                  @click="remind(item, '0')"
                  type="success"
                  underline="never"
                  w-70px
                >
                  <svg-icon name="local-bell" class="mr-5px"></svg-icon>
                  催办
                </el-link>
                <el-link
                  v-if="item.subFlowStatus === '0'"
                  type="primary"
                  icon="Bell"
                  @click="remind(item, '1')"
                  underline="never"
                  w-70px
                >
                  催办子流程
                </el-link>
                <el-link
                  v-if="item.status === '0' && validateSignOff(item)"
                  type="danger"
                  @click="signOff(item)"
                  underline="never"
                  w-70px
                >
                  <el-icon class="mr-5px"><Delete /></el-icon>
                  减签
                </el-link>
              </div> -->
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    <!-- <footer class="el-dialog__footer" style="text-align: center">
      <span class="dialog-footer">
        <el-button type="primary" @click="printForm">打印</el-button>
      </span>
    </footer> -->
  </div>
</template>

<script setup lang="ts" name="CommentTimeline">
// import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchComment } from "@/api/jsonflow/comment"
import { useMessage, useMessageBox } from "@/hooks/message"
import { onLoaded } from "@/flow/components/convert-name/convert"
import * as runJob from "@/api/jsonflow/run-job"
import { validateNull } from "@/utils/validate"
import { printHtml } from "@/flow"
import { debounce } from "lodash-es"

// 引入组件
const { proxy } = getCurrentInstance()

const { flow_node_status } = proxy.useDict(["flow_node_status"])

const props = defineProps({
  currJob: {
    type: Object,
    default: null,
  },
})

// 搜索变量
const loading = ref(true)
const total = ref(0)

const data = reactive({
  width: "-",
})

const tableData = ref([])
const queryParams = reactive({
  page: 1,
  size: 50,
  flowInstId: props.currJob.flowInstId,
  descs: "create_time",
})
// const state: BasicTableProps = reactive<BasicTableProps>({
//   queryForm: {
//     flowInstId: props.currJob.flowInstId,
//   },
//   pageList: fetchComment,
//   pagination: {
//     size: 1000,
//   },
//   onLoaded: onLoaded(
//     { key: "userId" },
//     { key: "flowInstId" },
//     { key: "runNodeId" },
//     { key: "runJobId" }
//   ),
//   descs: ["create_time"],
// })

function signOff(row) {
  runJob.signOff(row).then(() => {
    getList()
    proxy.$modal.msgSuccess("减签成功")
  })
}

const remind = debounce((row, type) => {
  row.remindType = type
  runJob.remind(row).then(() => {
    proxy.$modal.msgSuccess("催办成功")
  })
}, 300)

function validateSignOff(row) {
  // TODO 此处需要全量数据，否则判断有问题。目前pageSize 1000
  // 当前节点下只有一个任务不能做减签操作
  let existNodeJobs = tableData.value.filter(
    (f) =>
      f.runNodeId === row.runNodeId &&
      f.id !== row.id &&
      ["0", "1", "2", "9"].some((s) => s === f.status)
  )
  if (validateNull(existNodeJobs)) {
    return false
  }
  // 当前流程只有一个任务不能做减签操作
  let existFlowJobs = tableData.value.filter(
    (f) =>
      f.flowInstId === row.flowInstId &&
      f.id !== row.id &&
      ["0", "2", "9"].some((s) => s === f.status)
  )
  return !validateNull(existFlowJobs)
}

function printForm() {
  data.width = "55"
  // let flowName = state.dicData.flowInstId.find(
  //   (f) => f.flowKey === tableData.value[0].flowKey
  // ).flowName
  // printHtml("printComment", flowName, data)
}

// //  table hook
// const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle } = useTable(
//   state
// )

async function getList() {
  const res = await fetchComment(queryParams)
  tableData.value = res.object.records
  total.value = res.object.total
  loading.value = false
}
// 监听双向绑定
watch(
  () => props.currJob.id,
  (val) => {
    getList()
  }
)

getList()
</script>

<style scoped lang="scss">
.audit-timeline {
  width: 95%;
  min-width: 400px;
  max-width: 700px;
  :deep(.el-timeline-item) {
    padding-bottom: 30px;
    .el-timeline-item__tail {
      border-left: 1.5px dashed #d8d8d8;
    }
    .el-timeline-item__dot {
      left: -12px;
    }
    .el-timeline-item__wrapper {
      padding-left: 35px;
    }
  }
  .timeline-item {
    .timeline-item-content {
      position: relative;
      .desc {
        background: #f7f8f9;
        border-radius: 4px;
        margin-right: 10px;
        padding: 10px;
        font-size: 14px;
        margin-top: 10px;
        min-height: 60px;
        width: 100%;
      }
      .handle-btn {
        position: absolute;
        left: 102%;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
      }
    }
  }
  .dot-circle {
    position: relative;
    width: 34px;
    height: 34px;
    .avatar {
      height: 100%;
      width: 100%;
    }
    .status {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 14px;
      height: 14px;
    }
  }
}
</style>
