import request from '@/utils/request'
import { essPrefix } from '@/config/constant'

interface PageParams {
  page: number
  limit: number
  [key: string]: any
}

// 我的待办-待我签署列表 带分页
export function mySigningPage(data: PageParams) {
  return request({
    url: essPrefix + '/todo/mySigningPage',
    method: 'post',
    data
  })
}

// 进行中-签署-我的发起列表 带分页
export function myInitiationPage(data: PageParams) {
  return request({
    url: essPrefix + '/todo/myInitiationPage',
    method: 'post',
    data
  })
}

// 进行中-签署-他人发起列表 带分页
export function otherInitiationPage(data: PageParams) {
  return request({
    url: essPrefix + '/todo/otherInitiationPage',
    method: 'post',
    data
  })
}

// 即将超时-签署-我发起的列表 带分页
export function myApproachingPage(data: PageParams) {
  return request({
    url: essPrefix + '/todo/myApproachingPage',
    method: 'post',
    data
  })
}

// 即将超时-签署-他人发起的列表 带分页
export function otherApproachingPage(data: PageParams) {
  return request({
    url: essPrefix + '/todo/otherApproachingPage',
    method: 'post',
    data
  })
}

// 已完结-签署-我发起列表 带分页
export function myCompletePage(data: PageParams) {
  return request({
    url: essPrefix + '/todo/myCompletePage',
    method: 'post',
    data
  })
} 
// 已完结-签署-他人发起列表 带分页
export function otherCompletePage(data: PageParams) {
  return request({
    url: essPrefix + '/todo/otherCompletePage',
    method: 'post',
    data
  })
} 
