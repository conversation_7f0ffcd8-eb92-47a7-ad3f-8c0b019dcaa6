@font-face {
  font-family: "iconfont"; /* Project id 4959822 */
  src: url('iconfont.woff2?t=1751514016017') format('woff2'),
       url('iconfont.woff?t=1751514016017') format('woff'),
       url('iconfont.ttf?t=1751514016017') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-file:before {
  content: "\e62c";
}

.icon-gif:before {
  content: "\e605";
}

.icon-pptx:before {
  content: "\e60a";
}

.icon-svg:before {
  content: "\e60b";
}

.icon-txt:before {
  content: "\e60c";
}

.icon-doc:before {
  content: "\e601";
}

.icon-docx:before {
  content: "\e602";
}

.icon-html:before {
  content: "\e603";
}

.icon-jpg:before {
  content: "\e604";
}

.icon-pdf:before {
  content: "\e606";
}

.icon-png:before {
  content: "\e607";
}

.icon-xls:before {
  content: "\e608";
}

.icon-xlsx:before {
  content: "\e609";
}
