import request from "@/utils/request"
import { eduUser } from "@/config/constant"

// 任务完成审批
export function complete(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/complete",
		method: "post",
		data: obj,
	})
}

// 退回首节点
export function backFirstJob(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/back-first",
		method: "post",
		data: obj,
	})
}

// 退回上一步
export function backPreJob(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/back-pre",
		method: "post",
		data: obj,
	})
}

// 任意跳转
export function anyJump(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/any-jump",
		method: "post",
		data: obj,
	})
}

// 任意驳回
export function reject(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/reject",
		method: "post",
		data: obj,
	})
}

// 加签
export function signature(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/signature",
		method: "post",
		data: obj,
	})
}

// 任务挂起/激活
export function flowSuspension(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/suspension",
		method: "put",
		data: obj,
	})
}
// 任务挂起/激活
export function suspension(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/suspension",
		method: "put",
		data: obj,
	})
}

// 任务签收/反签收
export function signForJob(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/sign-for-job",
		method: "put",
		data: obj,
	})
}

// 处理转办逻辑
export function turnRunJob(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/turn",
		method: "put",
		data: obj,
	})
}

// 指定人员
export function appointUser(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/appoint",
		method: "put",
		data: obj,
	})
}

// 获取待办任务
export function fetchTodoPage(query: any) {
	return request({
		url: "/cloud-jsonflow/run-job/todo/page",
		method: "get",
		params: query,
	})
}

// 获取待办任务数量
export function fetchTodoSize(query: any) {
	return request({
		url: "/cloud-jsonflow/run-job/todo/size",
		method: "get",
		params: query,
	})
}

// 审批前获取节点配置信息
export function getTodoDetail(query: any) {
	return request({
		url: "/cloud-jsonflow/run-job/todo/detail",
		method: "get",
		params: query,
	})
}

// 是否已阅
export function isRead(obj: any) {
	return request({
		url: "/cloud-jsonflow/run-job/is-read",
		method: "put",
		data: obj,
	})
}

// 获取快捷回复短语
export function getConfigByConfigKeyAPI(configKey) {
	return request({
		url: `${eduUser}/system/config/getConfigValueByConfigKey/${configKey}`,
		method: "get"
	})
}
