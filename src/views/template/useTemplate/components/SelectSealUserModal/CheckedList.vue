<template>
  <div class="checked-list">
    <div class="checked-item-content" v-if="list.length">
      <div class="user-item" v-for="(item, index) in list">
        <UserNameItem :item="item" class="name" showType></UserNameItem>
        <el-icon class="remove-icon" @click="remove(item, index)">
          <RemoveFilled />
        </el-icon>
      </div>
    </div>

    <template v-else>
      <CustomEmpty text="暂未选择人员" />
    </template>
  </div>
</template>

<script setup>
const UserNameItem = defineAsyncComponent(() => import("./UserNameItem.vue"))

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(["remove"])
function remove(i, index, item) {
  emit("remove", i, index, item)
}
</script>

<style scoped lang="scss">
.checked-list {
  height: calc(100% - 20px);
  overflow-y: auto;
}

.checked-item-content {
  display: flex;
  flex-wrap: wrap;
  background: #fff;
  padding: 15px 0 15px 15px;
  margin-top: 10px;
  border-radius: 6px;
  .user-item {
    border: 1px solid #e2e2e2;
    border-radius: 5px;
    position: relative;
    width: 140px;
    text-align: center;
    margin-bottom: 15px;
    margin-right: 15px;
    padding: 3px 5px;
    .name {
      width: 100%;
      display: block;
    }
    .remove-icon {
      font-size: 16px;
      color: #f94e4f;
      cursor: pointer;
      position: absolute;
      right: -8px;
      top: -8px;
    }
  }
}
</style>
