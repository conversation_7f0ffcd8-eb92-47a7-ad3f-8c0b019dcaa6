<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="140px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item :label="$t('runNode.flowInstId')" prop="flowInstId">
						<el-input v-model="form.flowInstId" :placeholder="t('runNode.inputFlowInstIdTip')" disabled />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.defFlowId')" prop="defFlowId">
						<el-select
							v-model="form.defFlowId"
							:placeholder="t('runNode.inputDefFlowIdTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.defFlowId"
								:key="index"
								:label="item.flowName"
								:value="item.defFlowId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.nodeName')" prop="nodeName">
						<el-input v-model="form.nodeName" :placeholder="t('runNode.inputNodeNameTip')" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.startTime')" prop="startTime">
						<el-date-picker
							v-model="form.startTime"
							type="datetime"
							:placeholder="t('runNode.inputStartTimeTip')"
							:value-format="dateTimeStr"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.endTime')" prop="endTime">
						<el-date-picker
							v-model="form.endTime"
							type="datetime"
							:placeholder="t('runNode.inputEndTimeTip')"
							:value-format="dateTimeStr"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.nodeType')" prop="nodeType">
						<el-select
							v-model="form.nodeType"
							:placeholder="t('runNode.inputNodeTypeTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.NODE_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.nodeApproveMethod')" prop="nodeApproveMethod">
						<el-select v-model="form.nodeApproveMethod" :placeholder="t('runNode.inputNodeApproveMethodTip')">
							<el-option
								v-for="(item, index) in DIC_PROP.NODE_APPROVE_METHOD"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.rejectType')" prop="rejectType">
						<el-radio-group v-model="form.rejectType">
							<el-radio v-for="(item, index) in DIC_PROP.REJECT_TYPE" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.timeout')" prop="timeout">
						<el-input-number
							v-model="form.timeout"
							:min="1"
							:max="1000"
							:placeholder="t('runNode.inputTimeoutTip')"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.status')" prop="status">
						<el-select
							v-model="form.status"
							disabled
							:placeholder="t('runNode.inputStatusTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.NODE_STATUS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.isAutoNext')" prop="isAutoNext">
						<el-radio-group v-model="form.isAutoNext">
							<el-radio v-for="(item, index) in DIC_PROP.YES_OR_NO" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.isContinue')" prop="isContinue">
						<el-radio-group v-model="form.isContinue">
							<el-radio v-for="(item, index) in DIC_PROP.YES_OR_NO" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.suspension')" prop="suspension">
						<el-radio-group v-model="form.suspension">
							<el-radio v-for="(item, index) in DIC_PROP.YES_OR_NO" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runNode.sort')" prop="sort">
						<el-input-number
							v-model="form.sort"
							:min="1"
							:max="1000"
							:placeholder="t('runNode.inputSortTip')"
						/>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="RunNodeDialog">

import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/jsonflow/run-node"
import { useI18n } from "vue-i18n"
import { rule } from "@/utils/validate"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"

const emit = defineEmits(["refresh"])
const { t } = useI18n()
// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const cascadeDic = reactive({})
const onCascade = onCascadeChange(cascadeDic, { key: "flowInstId", cascades: ["defFlowId"] })

function cascadeChange(key, cascades) {
	onCascade(form, { key: key, cascades: cascades })
}
// 提交表单数据
const form = reactive({
	sort: 0,
	defFlowId: "",
	flowNodeId: "",
	startTime: "",
	endTime: "",
	nodeType: "",
	nodeApproveMethod: "",
	rejectType: "",
	timeout: 0,
	status: "",
	isAutoNext: "",
	isContinue: "",
	suspension: "",
})

// 定义校验规则
const dataRules = ref({
	defFlowId: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
	flowNodeId: [{ required: true, message: "节点名称不能为空", trigger: "blur" }],
	nodeType: [{ required: true, message: "节点类型不能为空", trigger: "blur" }],
	nodeApproveMethod: [{ required: true, message: "多节点审批方式不能为空", trigger: "blur" }],
	rejectType: [{ required: true, message: "被驳回后再次提交时不能为空", trigger: "blur" }],
	status: [{ required: true, message: "节点状态不能为空", trigger: "blur" }],
	isAutoNext: [{ required: true, message: "是否自动流转下一节点不能为空", trigger: "blur" }],
	isContinue: [{ required: true, message: "是否继续下一节点不能为空", trigger: "blur" }],
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取RunNode信息
	if (id) {
		form.id = id
		getRunNodeData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true
		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getRunNodeData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
		onCascade(form)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
