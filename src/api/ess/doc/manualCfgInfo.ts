import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询操作手册配置列表 带分页
export function getManualCfgListPage(data: Object) {
	return request({
		url: essPrefix + "/manualCfgInfo/listPage",
		method: "post",
		data,
	})
}
// 新增 操作手册配置
export function addManualCfgInfo(data: Object) {
	return request({
		url: essPrefix + "/manualCfgInfo/add",
		method: "post",
		data,
	})
}

// 操作手册配置详情
export function detailManualCfgInfo(id: string) {
	return request({
		url: essPrefix + `/manualCfgInfo/${id}`,
		method: "get"
	})
}
// 修改 操作手册配置
export function editManualCfgInfo(data: Object) {
	return request({
		url: essPrefix + "/manualCfgInfo/edit",
		method: "post",
		data,
	})
}

// 删除
export function delManualCfgInfo(ids: Array) {
	return request({
		url: essPrefix + "/manualCfgInfo/remove",
		method: "post",
		data: ids
	})
}
// 获取下载地址
export function downLoadManualCfgInfoUrl(id: string) {
	return request({
		url: essPrefix + `/manualCfgInfo/download/${id}`,
		method: "GET"
	})
}

// 查询用户导航栏操作手册配置列表 带分页
export function getNavManualCfgListPage(data: Object) {
	return request({
		url: essPrefix + "/manualCfgInfo/listPageFilter",
		method: "post",
		data,
	})
}
