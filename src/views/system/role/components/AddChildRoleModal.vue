<template>
	<el-dialog
		v-model="dialogVisible"
		:title="title"
		:before-close="handleClose"
		width="400px"
		append-to-body
	>
		<el-form
			ref="formRef"
			:model="form"
			label-width="100px"
			:rules="rules"
		>
			<el-form-item label="上级角色" prop="parentId">
				<el-input v-model="parentRoleInfo.roleName" disabled />
			</el-form-item>

			<el-form-item label="角色名称" prop="roleName">
				<el-input
					v-model="form.roleName"
					placeholder="请输入角色名称"
					clearable
					:maxlength="20"
					show-word-limit
				/>
			</el-form-item>
			<!-- <el-form-item prop="roleKey">
        <template #label>
          <span>
            <el-tooltip
              content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)"
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            权限字符
          </span>
        </template>
        <el-input v-model="form.roleKey" placeholder="请输入权限字符" />
      </el-form-item> -->
			<el-form-item label="状态" prop="status">
				<el-radio-group v-model="form.status">
					<el-radio
						v-for="dict in sys_normal_disable"
						:key="dict.value"
						:value="dict.value"
					>
						{{ dict.label }}
					</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="业务线" prop="businessLineIds">
				<BusinessSelect v-model="form.businessLineIds" ascriptionType="single" :ascription="parentRoleInfo.ascription" type="checkbox" />
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="handleClose">
				取消
			</el-button>
			<el-button type="primary" :loading="loading" @click="submitForm">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script setup name="AddChildRoleModal">
import BusinessSelect from "@/views/components/BusinessSelect.vue"
import { addZsRoleInfo, editZsRoleInfo } from "@/api/system/role"

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

const title = ref("添加子角色")
const loading = ref(false)
const dialogVisible = ref(false)

const parentRoleInfo = ref({}) // 上级角色信息
const formRef = ref(null)
const form = ref({
	parentId: null,
	roleName: null,
	businessLineIds: [],
	status: "0",
})

const rules = ref({
	parentId: [{ required: true, message: "请选择上级角色", trigger: "blur" }],
	roleName: [{ required: true, message: "角色名称不能为空", trigger: "blur" }],
	status: [{ required: true, message: "请选择状态", trigger: "change" }],
	businessLineIds: [{ required: true, message: "请选择业务线", trigger: "change" }],
})

const open = (parentInfo, row) => {
	dialogVisible.value = true
	parentRoleInfo.value = parentInfo
	form.value.parentId = parentInfo.roleId

	title.value = row?.roleId ? "修改子角色" : "新增子角色"
	if (row?.roleId) { form.value = { ...row } }
}

const emit = defineEmits(["change"])

const handleClose = () => {
	dialogVisible.value = false
	formRef.value.resetFields()
	parentRoleInfo.value = {}
}

const submitForm = () => {
	formRef.value.validate(valid => {
		if (valid) {
			loading.value = true
			let actionUrl = form.value.roleId ? editZsRoleInfo : addZsRoleInfo
			actionUrl(form.value)
				.then(() => {
					proxy.$modal.msgSuccess(form.value.roleId ? "修改成功" : "新增成功")
					emit("change")
					handleClose()
				})
				.finally(() => {
					loading.value = false
				})
		}
	})
}

defineExpose({
	open,
})
</script>
