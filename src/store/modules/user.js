import { login, logout, getInfo, casLoginAPI } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { encrypt } from '@/utils/jsencrypt'
import router from '@/router'
import useTagsViewStore from '@/store/modules/tagsView'

const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    userId: '',
    userName: '',
    avatar: '',
    loginDeptInfo: {},
    zsUserInfo: {},
    roles: [],
    permissions: [],
    userInfo: {},
    password: '',
  }),
  actions: {
    updateToken (token) {
      setToken(token)
      this.token = token
    },
    // 登录
    login (info) {
      const { username, password, code, uuid } = info
      return new Promise((resolve, reject) => {
        login(username.trim(), password, code, uuid)
          .then(res => {
            const { access_token, zsUserInfo } = res.object
            this.password = encrypt(password)
            this.userName = username
            this.userInfo.nickName = zsUserInfo.nickName
            this.zsUserInfo = zsUserInfo
            this.verifyRealAuth(access_token)
            resolve(res.object)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    verifyRealAuth (access_token) {
      const { deptList, izRealAuth, izChineseGj } = this.zsUserInfo
      if (+izRealAuth === 0 && +izChineseGj === 1) {
        // 未实名并且中国国籍
        router.push({ path: "/realName" })
      } else if (deptList && deptList.length > 1) { // 双肩挑
        router.push({ path: "/account" })
      } else {
        router.push({ path: "/" })
        this.updateToken(access_token)
      }
    },
    // 单点登录
    casLogin (params) {
      return new Promise((resolve, reject) => {
        casLoginAPI(params)
          .then(res => {
            if (res?.object?.redirect_uri) {
              window.location.href = res.object.redirect_uri
              resolve()
            } else {
              const token = res.token || res?.data?.access_token || res?.object?.access_token || ''
              setToken(token)
              this.token = token
              resolve()
            }
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    // 获取用户信息
    getInfo () {
      return new Promise((resolve, reject) => {
        getInfo()
          .then(res => {
            const { user, loginDeptId, loginDeptName } = res
            this.roles = res.roles && res.roles.length > 0 ? res.roles : []
            this.permissions = res.permissions
            this.userId = user.userId
            this.loginDeptInfo = { loginDeptId, loginDeptName }
            this.userName = user.userName
            this.avatar = user.avatar
            this.userInfo = user
            this.password = null // 清空密码
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    // 退出系统
    logOut () {
      return new Promise((resolve, reject) => {
        const casEnable = import.meta.env.VITE_CAS_SSO
        const params = casEnable ? { module: import.meta.env.VITE_APP_MODULE } : ''
        logout(params)
          .then((res) => {
            this.$reset()
            removeToken()
            useTagsViewStore().delAllViews()
            if (casEnable) {
              setTimeout(() => {
                window.location.href = res.object
              }, 800)
            }
            else {
              router.push('/login')
            }
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },
  },
  persist: {
    key: 'piniaStore', // 存储名称
    storage: sessionStorage, // 存储方式
    pick: ['userName', 'password','zsUserInfo'], // 指定存储的数据
  },
})

export default useUserStore
