<template>
	<div class="dynamic-iframe">
		<el-scrollbar class="main">
			<iframe :src="src" class="iframe" />
		</el-scrollbar>
	</div>
</template>

<script lang="ts" name="DynamicIframe" setup>
import { Session } from "@/utils/storage"
const { proxy } = getCurrentInstance()
const route = useRoute()
const src = ref("")

const emits = defineEmits(["handleJob"])

const props = defineProps({
	currJob: {
		type: Object,
		default: null,
	},
	currElTab: {
		type: Object,
		default: null,
	},
})

const init = () => {
	const token = Session.getToken()
	const tenantId = Session.getTenant()
	let flowInstId = props.currJob.flowInstId
	let runJobId = props.currJob.id
	src.value = props.currElTab.path + `?token=${token}&tenantId=${tenantId}&flowInstId=${flowInstId}&runJobId=${runJobId}`
}

function handleJob(jobBtn) {
	emits("handleJob", jobBtn)
}

watch([route], () => {
	init()
})

onMounted(() => {
	init()
})

</script>

<style lang="scss" scoped>
.iframe {
	width: 100%;
	height: 80vh;
	border: 0;
	overflow: hidden;
	box-sizing: border-box;
}
</style>
