import { onMounted, onBeforeUnmount } from "vue"
import { getCurrentInstance } from "vue"

export function useIframeCommunication(iframeRef, getUrl, path) {
	const { proxy } = getCurrentInstance()

	function handleMessage(event) {
		const { messageType, action, embedParams } = event.data
		if (messageType === "NOT_LOGIN") {
			proxy.$modal.alert("页面停留时间过长，请重新输入").then(() => {
				getUrl()
			})
		}
		// sendMsgToIframe(event)
	}

	function sendMsgToIframe(evt) {
		if (evt.data.messageType === "PAGE_LOADED") {
			// PAGE_LOADED页面加载完成事件
			// 向子页面（电子签页面）发送消息UI_MESSAGE_CONFIG，开启对应事件的开关
			iframeRef.value.contentWindow.postMessage(
				{
					messageType: "UI_MESSAGE_CONFIG",
					config: {
						CREATE_TEMPLATE_SUCCESS_BACK: "on", // 创建模板页面成功返回事件
					},
				},
				evt.origin,
			) // 需指定域名 或 设置为 '*'
		}
		if (evt.data.messageType === "CREATE_TEMPLATE_SUCCESS_BACK") {
			// 用户点击了返回按钮
			let obj = {
				path: path,
				query: { r: 1 },
			}
			proxy.$tab.closeOpenPage(obj)
		}
	}

	onMounted(() => {
		window.addEventListener("message", handleMessage)
	})

	onBeforeUnmount(() => {
		window.removeEventListener("message", handleMessage)
	})
}
