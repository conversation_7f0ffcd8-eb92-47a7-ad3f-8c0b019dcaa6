/*
 * @Author: ljn
 * @Date: 2025-02-11 17:27:55
 * @LastEditors: ljn
 * @LastEditTime: 2025-02-11 18:04:04
 * @Description: 本地vite加载文件 加快热更新速度
 */

import fs from "fs"
import path from "path"

// 查找文件
function getFiles(e) {
	const arr = []
	const dirents = fs.readdirSync(e, { withFileTypes: true })
	for (const dirent of dirents) {
		if (dirent.isDirectory()) {
			arr.push(...getFiles(path.join(e, dirent.name)))
		}
		else {
			arr.push(path.join(e, dirent.name))
		}
	}
	return arr
}

// 插入加载文件脚本
export const setPreLoadFile = (options = { pathList: [], preFix: "" }) => {
	if (options.pathList && options.pathList.length) {
		let res = []
		options.pathList.forEach(path => {
			res = res.concat(getFiles(path))
		})
		let linkStr = `
        <script>
        setTimeout(() => {
            function preLoadSource(url){
                var xhr = new XMLHttpRequest();
                xhr.open('GET', url);
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        // console.log('预加载成功');
                    } else {
                        console.error('预加载失败');
                    }
                };
                xhr.send();
            }\n
        `
		res.forEach(item => {
			linkStr += `preLoadSource('${options.preFix + item.substring(1)}')\n`
		})
		linkStr += "})\n</script>"
		return {
			name: "preload-file",
			transformIndexHtml(dom) {
				return dom.replace("</body>", `${linkStr}</body>`)
			},
		}
	}
}
