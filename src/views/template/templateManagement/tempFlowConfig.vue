<template>
  <div class="app-container">
    <div class="container-handle-btn">
      <div class="title-line flex-y-center">
        <span>配置固定流程</span>
        <span class="font-400 color-#333 text-14px w-300px inline-block" v-tooltip>
          （模版名称：{{ templateInfo.templateName }}）
        </span>
      </div>
      <el-button w-100px type="primary" @click="handleSave" :loading="loading">
        提交
      </el-button>
    </div>

    <CreateFlowSimple
      :historyAPI="queryOperateTop10"
      :configKey="flowConfigKey.FILE_FLOW_STABLE"
      :defFlowId="templateInfo.workId"
      ref="createFlowSimpleRef"
      :hisExtData="{ flowType: 2 }"
      v-loading="loading"
    />
  </div>
</template>

<script setup name="TempFlowConfig">
import { queryOperateTop10 } from "@/api/ess/template/template"
import { generateFlowByUser } from "@/api/jsonflow/def-flow"
import { flowConfigKey } from "@/config/constant"
import { getTemplateInfo, updateTemplateFlow } from "@/api/ess/template/template"
import mittBus from "@/utils/mitt"

defineOptions({
  inheritAttrs: false,
})

const CreateFlowSimple = defineAsyncComponent(() =>
  import("@/views/flow/createFlowSimple/index.vue")
)

const { proxy } = getCurrentInstance()
const route = useRoute()
const templateInfo = ref({}) // 模版信息
const loading = ref(false)
const templateId = route.params.id
const backTo = route.query.to

// 初始化流程
const init = async () => {
  const res = await getTemplateInfo(templateId)
  templateInfo.value = res.object
  // 初始化流程配置
  console.log(res.object)
  await nextTick()
  proxy.$refs.createFlowSimpleRef?.initFlow()
}

// 保存流程
async function handleSave() {
  try {
    const nodes = proxy.$refs.createFlowSimpleRef?.getNodes() || []
    loading.value = true
    const flowRes = await generateFlowByUser(nodes, 2)
    await updateTemplateFlow({
      templateId,
      defFlowId: flowRes.object.defFlowId,
      flowType: 2,
      batchDefFlowId: flowRes.object.batchDefFlowId
    })
    proxy.$modal.confirm("提交成功", "即将返回模版列表", "success", false).then(() => {
      if (backTo !== "fileBatchLunch") {
        proxy.$tab.closeOpenPage({ path: "/template/" + backTo })
      } else {
        proxy.$tab.closeOpenPage({ path: "/file/fileBatchLunch" })
      }
      mittBus.emit("refreshTemplateList")
    })
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  init()
})
</script>

<style scoped lang="scss">
.container-handle-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.flow-his-list {
  width: 300px;
  flex-shrink: 0;
  margin-left: 15px;
}
</style>
