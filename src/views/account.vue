<template>
  <div class="account-container">
    <div class="account-info">
      <img src="@/assets/images/account/user-icon.png" alt="" />
      <div class="info-desc">
        <div class="account-name">HI, {{ nickName }}</div>
        <div>系统检测到您的账号归属多个部门，请先切换对应的部门</div>
      </div>
    </div>

    <div class="account-switch">
      <div class="switch-dept">
        <div
          v-for="item in loginDeptList"
          :key="item.deptId"
          class="dept-item"
          :class="{ active: item.deptId == deptId }"
          @click="handleDeptChange(item)"
        >
          <div class="dept-item__left">
            <div class="dept-name">{{ item.deptName }}</div>
            <div class="role-list">
              <span v-for="role in item.detailsList" :key="role.roleId">
                {{ role.roleName }}
              </span>
            </div>
          </div>
          <div class="checked-icon">
            <el-icon v-show="item.deptId == deptId">
              <CircleCheckFilled />
            </el-icon>
          </div>
        </div>
      </div>
      <!-- <div class="switch-role">
					<div v-for="item in roleList" :key="item.roleId" class="role-item">
						{{ item.roleName }}
					</div>
				</div> -->
    </div>

    <div class="account-btn">
      <el-button type="primary" :loading="btnLoading" @click="handleLogin">
        确认登录
      </el-button>
      <br />
      <el-button type="info" plain @click="cancel"> 取消 </el-button>
    </div>
  </div>
</template>

<script setup name="Account">
import { logout, zsLogin } from "@/api/login"
import { decrypt } from "@/utils/jsencrypt"
import useUserStore from "@/store/modules/user"
import Cookies from "js-cookie"
import { selectZsRoleDeptInfos } from "@/api/system/user.js"

const { zsUserInfo } = useUserStore()
const route = useRoute()
const router = useRouter()

const loginDeptList = ref(zsUserInfo.deptList)

const btnLoading = ref(false)
const deptId = ref(loginDeptList.value?.[0].deptId)
const redirect = ref(undefined)
const userName = ref(useUserStore().userName)
const nickName = ref(zsUserInfo.nickName)

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect
  },
  { immediate: true }
)

onMounted(async () => {
  const username = Cookies.get("ty_auth_key")
  const encodedNickName = Cookies.get("nickName")

  if (!loginDeptList.value?.length && username != null) {
    userName.value = username
    let res = await selectZsRoleDeptInfos(username)
    loginDeptList.value = +res?.code == 200 ? res.object : []
    deptId.value = loginDeptList.value?.[0].deptId || null
    nickName.value = decodeURIComponent(encodedNickName)
  }
})

// 操作成功
const handleSuccess = () => {
  const query = route.query
  router.replace({ path: redirect.value || "/", query })
}

const handleDeptChange = (item) => {
  deptId.value = item.deptId
}

const cancel = () => {
  return useUserStore().logOut()
}

// 登录
const handleLogin = async () => {
  btnLoading.value = true
  zsLogin({
    username: userName.value,
    loginDeptId: deptId.value,
    password: decrypt(useUserStore().password),
  })
    .then((res) => {
      if (res.object.access_token) {
        useUserStore().updateToken(res.object.access_token)
        handleSuccess()
      }
    })
    .finally(() => {
      btnLoading.value = false
    })
}

// getList()
</script>

<style scoped lang="scss">
.account-container {
  background: url("@/assets/images/account/bg.png") no-repeat center;
  background-size: cover;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 80px;
}

.account-info {
  display: flex;
  align-items: center;
  background: url("@/assets/images/account/dialog-bg.png") no-repeat center;
  background-size: 100% 100%;
  padding: 60px 60px 60px 70px;
  img {
    flex-shrink: 0;
    width: 80px;
    object-fit: contain;
    margin-right: 20px;
  }
  .account-name {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 7px;
    span {
      font-size: 16px;
      color: #6b6b6b;
      vertical-align: middle;
      font-weight: normal;
    }
  }
}

.account-switch {
  margin-top: 30px;
  box-sizing: border-box;
  width: 700px;
  .switch-dept {
    margin-bottom: 15px;
    .dept-item {
      background: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 12px;
      border: 2px solid #d2f1e8;
      margin-bottom: 15px;
      padding: 20px 28px;
      cursor: pointer;
      .checked-icon {
        flex-shrink: 0;
      }
      .dept-name {
        font-size: 20px;
        font-weight: bold;
      }
      .role-list {
        span {
          color: #24a87e;
          background: #e0f3ed;
          border-radius: 4px;
          padding: 4px 10px;
          margin-top: 20px;
          margin-right: 10px;
          display: inline-block;
          font-size: 14px;
        }
      }
      .checked-icon {
        border: 1.5px solid #d2f1e8;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .el-icon {
          color: #fb9547;
          font-size: 22px;
        }
      }
      &.active,
      &:hover {
        background: url("@/assets/images/account/selected.png") no-repeat center;
        border-color: #fb9547;
        .role-list {
          span {
            background: #fb9547;
            color: #fff;
          }
        }
        .checked-icon {
          border-color: #fb9547;
        }
      }
    }
  }
}

.account-btn {
  text-align: center;
  margin-top: 40px;
  .el-button {
    width: 250px;
    height: 56px;
    font-size: 18px;
    border-radius: 8px;
  }
  .el-button--info {
    background: transparent;
    color: #6b6b6b;
    margin-top: 15px;
    border: 1px solid #d1d1d1;
  }
}
</style>
