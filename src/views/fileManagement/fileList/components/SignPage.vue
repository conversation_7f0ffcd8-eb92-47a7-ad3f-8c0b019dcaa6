<template>
  <DrawerPage v-model="show" title="签署" @back="handleBack">
    <div v-loading="loading" class="preview">
      <iframe
        v-if="iframeUrl"
        :src="iframeUrl"
        frameborder="0"
        width="100%"
        height="100%"
      />
    </div>
  </DrawerPage>
</template>

<script setup>
import { getFileSignUrl } from "@/api/ess/fileManagement/file"

defineOptions({
  inheritAttrs: false,
})
const { proxy } = getCurrentInstance()
const show = defineModel(false)

const loading = ref(true)
const iframeUrl = ref(null)
const fileInfo = ref({})
const essFlowIds = ref([])

const emit = defineEmits(["back"])
function handleBack() {
  emit("back")
}

function open(data, isBatch) {
  if (isBatch) {
    essFlowIds.value = data.map((item) => item.essFlowId)
    getUrl()
  } else {
    fileInfo.value = data
    essFlowIds.value = [data.essFlowId]
    getUrl()
  }
}

// 获取签署地址
function getUrl() {
  if (!essFlowIds.value.length) return
  getFileSignUrl(essFlowIds.value)
    .then((res) => {
      iframeUrl.value = res.object.url
    })
    .finally(() => {
      loading.value = false
    })
}

onActivated(() => {
  getUrl()
})

onDeactivated(() => {
  iframeUrl.value = null
})

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.preview {
  height: 100%;
  width: 100%;
}
</style>
