<template>
  <div class="app-container">
    <el-card class="box-card" shadow="none">
      <el-form
        ref="form"
        :model="form"
        label-width="120px"
        :inline="false"
        :rules="rules"
      >
        <el-form-item label="系统名称" prop="name" style="width: 400px">
          <el-input
            v-model="form.name"
            clearable
            type="text"
            placeholder="请输入名称"
            maxlength="10"
          />
        </el-form-item>
        <el-form-item label="主颜色" prop="themeColor">
          <el-color-picker v-model="form.themeColor" :predefine="predefineColors" />
        </el-form-item>
        <!-- 上传组件 -->
        <el-form-item label="logo" prop="homeLogoUrl">
          <div
            class="user-info-head"
            title="点击上传logo"
            @click="editCropper('homeLogoUrl')"
          >
            <img v-if="form.homeLogoUrl" :src="form.homeLogoUrl" class="img-lg logo-bg" />
            <span v-else class="img-lg" />
          </div>
          <div class="u-m-l-10 text-info">建议尺寸280*60</div>
        </el-form-item>
        <el-form-item label="登录页logo" prop="loginLogoUrl">
          <div
            class="user-info-head"
            title="点击上传logo"
            @click="editCropper('loginLogoUrl')"
          >
            <img v-if="form.loginLogoUrl" :src="form.loginLogoUrl" class="img-lg" />
            <span v-else class="img-lg" />
          </div>
          <div class="u-m-l-10 text-info">建议尺寸280*60</div>
        </el-form-item>
        <!-- <el-form-item label="导航样式" prop="navStyle">
          <el-radio v-model="form.navStyle" :label="1">侧边导航</el-radio>
          <el-radio v-model="form.navStyle" :label="0">顶部导航</el-radio>
        </el-form-item> -->
        <el-form-item label="侧边导航背景色" prop="sidebarNavBackgroundColor">
          <el-color-picker
            v-model="form.sidebarNavBackgroundColor"
            :predefine="menuBgColors"
          />
        </el-form-item>
      </el-form>
      <div style="margin-left: 200px; margin-top: 40px">
        <el-button
          style="width: 100px"
          type="primary"
          :loading="loading"
          @click="saveForm"
        >
          保 存
        </el-button>
      </div>
    </el-card>

    <ImgCropper ref="imgCropper" @change="handleLogoSuccess" />
  </div>
</template>

<script>
import { addLogo, logoSettingList, updateLogo } from "@/api/system/logoSetting"
import variables from "@/assets/styles/variables.module.scss"
import settings from "@/settings"
import ImgCropper from "./components/ImgCropper.vue"
import useSettingsStore from "@/store/modules/settings"

export default {
  components: { ImgCropper },
  data() {
    return {
      publicPath: import.meta.env.VITE_PUBLIC_PATH,
      loading: false,
      // 表单参数
      form: {
        name: settings.title,
        themeColor: variables.mainColor,
        sidebarNavBackgroundColor: variables.menuBackground,
        homeLogoUrl: null,
        loginLogoUrl: null,
        navStyle: settings.topNav ? 0 : 1, // 默认侧边导航
      },
      // 表单校验
      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
      },
      predefineColors: [
        "#006fff",
        "#24A87E",
        "#1890ff",
        "#304156",
        "#212121",
        "#11a983",
        "#13c2c2",
        "#6959CD",
        "#f5222d",
      ],
      menuBgColors: ["#1b3045", "#e8f3f0", "#e8f3ff"],
      logoType: "homeLogoUrl",
    }
  },
  created() {
    this.logoList()
  },
  methods: {
    /** 查询换肤管理列表 */
    logoList() {
      logoSettingList(`/${this.publicPath}/`)
        .then((res) => {
          if (res?.object.length > 0) {
            let { themeColor, sidebarNavBackgroundColor } = res.object[0]
            this.form = {
              ...res.object[0],
              sidebarNavBackgroundColor:
                sidebarNavBackgroundColor || variables.menuBackground,
              themeColor: themeColor || variables.mainColor,
            }
          }
        })
        .catch(() => {})
    },

    editCropper(key) {
      this.$refs.imgCropper.editCropper(this.form[key])
      this.logoType = key
    },

    handleLogoSuccess(res) {
      this.form[this.logoType] = res
    },

    // 保存按钮
    saveForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const reqUrl = !this.form.id ? addLogo : updateLogo
          this.loading = true
          reqUrl(this.form, `/${this.publicPath}/`)
            .then((res) => {
              this.$modal.msgSuccess("保存成功")
              this.logoList()
              useSettingsStore().setTheme(this.form)
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.box-card {
  width: 100%;
  margin: 0 auto;
  margin-top: 20px;
  min-width: 500px;
  padding: 40px;
  border: 0;
}
.user-info-head {
  position: relative;
  display: inline-block;
  text-align: center;
  border: 1px solid #eee;
  width: 280px;
  height: 60px;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  &:hover::after {
    content: "+";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    color: #eee;
    background: rgba(0, 0, 0, 0.2);
    font-size: 24px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    cursor: pointer;
    line-height: 60px;
  }
}
.img-lg {
  width: 140px;
  height: 30px;
  cursor: pointer;
}

.logo-bg {
  background: var(--el-color-primary);
}
</style>
