<template>
  <div class="app-container">
    <div class="flex-x-between">
      <CustomTabs v-model="currentTab" :options="authTabs" @tab-click="handleTabClick" />
      <el-radio-group
        v-model="radioValue"
        class="radio-box"
        v-if="authRadioOptions.length"
      >
        <el-radio-button
          v-for="item in authRadioOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-radio-group>
    </div>

    <template v-if="$auth.hasPermiOr(['todo:doingMine:flow', 'todo:doingOther:flow'])">
      <FlowTable ref="flowTableRef" v-show="radioValue === 0" :mine="currentTab" />
    </template>

    <template v-if="$auth.hasPermiOr(['todo:doingMine:file', 'todo:doingOther:file'])">
      <DoingFileTable
        ref="doingFileTableRef"
        v-show="radioValue === 1"
        :mine="currentTab"
      />
    </template>
  </div>
</template>

<script setup name="Doing">
import { defineAsyncComponent, ref } from "vue"
import { useAgendaTabs } from "../useAgendaTabs"

const FlowTable = defineAsyncComponent(() => import("./components/FlowTable.vue"))
const DoingFileTable = defineAsyncComponent(() =>
  import("./components/DoingFileTable.vue")
)

const flowTableRef = ref(null)
const doingFileTableRef = ref(null)

const tabs = [
  {
    label: "我发起的",
    value: 0,
    permissions: ["todo:doingMine:list"],
    children: [
      {
        label: "流程",
        value: 0,
        permissions: ["todo:doingMine:flow"],
      },
      {
        label: "签署",
        value: 1,
        permissions: ["todo:doingMine:file"],
      },
    ],
  },
  {
    label: "他人发起",
    value: 1,
    permissions: ["todo:doingOther:list"],
    children: [
      {
        label: "流程",
        value: 0,
        permissions: ["todo:doingOther:flow"],
      },
      {
        label: "签署",
        value: 1,
        permissions: ["todo:doingOther:file"],
      },
    ],
  },
]

const {
  currentTab,
  radioValue,
  authTabs,
  authRadioOptions,
  handleTabClick,
} = useAgendaTabs(tabs, [flowTableRef, doingFileTableRef])
</script>

<style scoped lang="scss">
.radio-box {
  margin-bottom: 15px;

  :deep(.el-radio-button__inner) {
    background-color: var(--el-color-primary-light-8);
    border-color: var(--el-color-primary-light-8);
  }

  :deep(.el-radio-button) {
    --el-border-radius-base: 2px;
  }
}
</style>
