<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="100px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.initiatorId')" prop="initiatorId">
						<el-select
							v-model="form.initiatorId"
							:placeholder="t('runFlow.inputInitiatorIdTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in dicData.initiatorId"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.flowName')" prop="flowName">
						<el-input v-model="form.flowName" :placeholder="t('runFlow.inputFlowNameTip')" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.groupName')" prop="groupName">
						<el-input v-model="form.groupName" :placeholder="t('runFlow.inputGroupNameTip')" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.startTime')" prop="startTime">
						<el-date-picker
							v-model="form.startTime"
							type="datetime"
							:placeholder="t('runFlow.inputStartTimeTip')"
							:value-format="dateTimeStr"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.endTime')" prop="endTime">
						<el-date-picker
							v-model="form.endTime"
							type="datetime"
							:placeholder="t('runFlow.inputEndTimeTip')"
							:value-format="dateTimeStr"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.useTime')" prop="invalidReason">
						<el-input v-model="form.useTime" :placeholder="t('runFlow.inputUseTimeTip')" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.invalidReason')" prop="invalidReason">
						<el-input v-model="form.invalidReason" :placeholder="t('runFlow.inputInvalidReasonTip')" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.status')" prop="status">
						<el-select
							v-model="form.status"
							:placeholder="t('runFlow.inputStatusTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.FLOW_STATUS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.remark')" prop="remark">
						<el-input v-model="form.remark" type="textarea" :placeholder="t('runFlow.inputRemarkTip')" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.version')" prop="version">
						<el-input-number
							v-model="form.version"
							:min="1"
							:max="1000"
							:placeholder="t('runFlow.inputVersionTip')"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runFlow.isIndependent')" prop="isIndependent">
						<el-radio-group v-model="form.isIndependent">
							<el-radio v-for="(item, index) in DIC_PROP.YES_OR_NO" :key="index" :value="item.value">
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="RunFlowDialog">

import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/jsonflow/run-flow"
import { useI18n } from "vue-i18n"
import { rule } from "@/utils/validate"
import { onLoadDicUrl } from "@/flow/components/convert-name/convert"
const emit = defineEmits(["refresh"])

const { t } = useI18n()

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "initiatorId" })
onMounted(() => {
	onLoad(dicData)
})
// 提交表单数据
const form = reactive({
	initiatorId: "",
	flowName: "",
	groupName: "",
	startTime: "",
	endTime: "",
	invalidReason: "",
	status: "",
	version: 0,
	createUser: "",
	createTime: "",
})

// 定义校验规则
const dataRules = ref({
	initiatorId: [{ required: true, message: "流程申请人不能为空", trigger: "blur" }],
	flowName: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
	groupName: [{ required: true, message: "分组名称不能为空", trigger: "blur" }],
	status: [{ required: true, message: "流程状态不能为空", trigger: "blur" }],
	version: [{ required: true, message: "版本不能为空", trigger: "blur" }],
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取RunFlow信息
	if (id) {
		form.id = id
		getRunFlowData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true
		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getRunFlowData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
