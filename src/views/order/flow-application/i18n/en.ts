export default {
	flowApplication: {
		index: "#",
		importflowApplicationTip: "import FlowApplication",
		id: "id",
		flowKey: "flowKey",
		icon: "icon",
		formName: "formName",
		groupName: "groupName",
		tableName: "tableName",
		permission: "permission",
		remark: "remark",
		status: "status",
		version: "version",
		createUser: "createUser",
		createTime: "createTime",
		defFlowId: "defFlowId",
		formInfo: "formInfo",
		type: "type",
		subTableName: "subTableName",
		subMainField: "subMainField",
		mainSubProp: "mainSubProp",
		sort: "sort",
		updateUser: "updateUser",
		updateTime: "updateTime",
		delFlag: "delFlag",

		inputIdTip: "input id",
		inputFlowKeyTip: "input flowKey",
		inputIconTip: "input icon",
		inputFormNameTip: "input formName",
		inputGroupNameTip: "input groupName",
		inputTableNameTip: "input tableName",
		inputPermissionTip: "input permission",
		inputRemarkTip: "input remark",
		inputStatusTip: "input status",
		inputVersionTip: "input version",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputDefFlowIdTip: "input defFlowId",
		inputFormInfoTip: "input formInfo",
		inputTypeTip: "input type",
		inputSubTableNameTip: "input subTableName",
		inputSubMainFieldTip: "input subMainField",
		inputMainSubPropTip: "input mainSubProp",
		inputSortTip: "input sort",
		inputUpdateUserTip: "input updateUser",
		inputUpdateTimeTip: "input updateTime",
		inputDelFlagTip: "input delFlag",

	},
}
