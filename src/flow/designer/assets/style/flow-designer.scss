.flow-container {
  height: 100%;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .el-main {
    border-radius: 15px;
  }

}

.select-area {
  position: relative;
  z-index: 1001;
  box-shadow: 0 3px 0 #ddd;
  width: 54px;
  background: #fff;
  padding-top: 6px;

  .el-row-tab {
    text-align: center;
    margin-bottom: 10px;
    font-size: 12px;
    height: 26px;
    line-height: 24px;
    margin-top: 7px;
    margin-left: 17px;
  }

  /*左侧菜单栏*/
  .el-row {
    padding-bottom: 10px;
  }
}

.header-option {
  background: #fff;
  height: 46px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 3px 5px #ddd;
  position: relative;
  z-index: 1000;

  &__tools {
    align-items: center;
    display: flex;
    width: 570px;

    .el-select__wrapper {
      border-radius: 16px;
    }
  }

  .el-button {
    height: 30px;
    border-radius: 16px;
    font-size: 12px;
  }

  .el-button + .el-button {
    margin-left: 0;
  }

  .el-input__wrapper {
    height: 30px;
    border-radius: 16px;
  }

  .el-input__inner {
    width: 70px;
  }
}

.header-view-option {
  background: #fff;
  height: 46px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1000;

  &__tools {

  }
}

.header-option-button {
  border: 0;
  padding: 8px 8px;
}

.flow-content {
  background: #fafafa;
  height: 100%;
  border: 1px dashed rgba(170, 170, 170, 0.7);
  padding: 0;
}

