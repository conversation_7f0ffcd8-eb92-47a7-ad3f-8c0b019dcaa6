<template>
  <div class="components-table">
    <el-form
      ref="queryRef"
      :model="queryParams"
      label-width="70px"
      class="white-form-box"
      inline
    >
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入姓名"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="queryParams.createTimeRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="common-input-width"
          clearable
          :default-time="dateDefaultTime"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" :loading="loading"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery" :loading="loading">重置</el-button>
      </el-form-item>
    </el-form>
    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :has-toolbar="false"
      :data="tableData"
      :loading="loading"
      :total="total"
      :opt-width="100"
      @reload="getList"
    >
      <template #actions>
        <el-button type="primary" icon="plus" @click="handleAdd"> 新增 </el-button>
        <el-button type="primary" plain @click="handleImport"> 导入 </el-button>
        <el-button
          type="danger"
          plain
          @click="handleDelete"
          :disabled="!selections.length"
        >
          删除
        </el-button>
      </template>
      <vxe-column type="checkbox" width="60" fixed="left" />
      <vxe-column type="seq" width="60" />
      <vxe-column title="姓名" field="userName" show-overflow />
      <vxe-column title="手机号" field="phoneNumber" show-overflow>
        <template #default="{ row }">
          {{ replacePhone(row.phoneNumber) }}
        </template>
      </vxe-column>
      <vxe-column title="添加时间" field="createTime" show-overflow />
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '编辑',
              click: () => handleEdit(row),
            },
            {
              text: '删除',
              type: 'danger',
              click: () => handleDelete(row),
            },
          ]"
        />
      </template>
    </CustomTable>

    <!-- 编辑、新增  -->
    <EditPersonalContactModal
      ref="editPersonalRef"
      @refresh="handleQuery"
      @change="getList"
    />
    <!-- 导入弹窗  -->
    <ImportFile
      ref="importFileRef"
      :tempUrl="essPrefix + '/extUser/importTemplate'"
      :uploadUrl="essPrefix + '/extUser/importMember'"
      title="个人联系人导入"
      @change="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { getExtUserListAPI, deleteExtUserAPI } from "@/api/ess/baseSet/contacts"
import EditPersonalContactModal from "./EditPersonalContactModal.vue"
import { replacePhone } from "@/utils/index"
import { essPrefix } from "@/config/constant"

const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const editPersonalRef = ref()
const importFileRef = ref(null)

const queryParams = reactive({
  page: 1,
  limit: 10,
  userName: "",
})

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

async function getList() {
  try {
    loading.value = true
    let res = await getExtUserListAPI(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 新增按钮操作 */
function handleAdd() {
  editPersonalRef.value.open()
}

/** 编辑按钮操作 */
function handleEdit(row: object) {
  editPersonalRef.value.open(row)
}

/** 删除按钮操作 */
function handleDelete(row: any) {
  const ids = row?.id ? row.id : selections.value.map((item) => item.id)
  const names = row?.id ? row.userName : selections.value.map((item) => item.userName)
  proxy.$modal
    .confirm("是否确认个人联系人？", `当前已选联系人（${names}）`)
    .then(function () {
      return deleteExtUserAPI(ids)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功！")
    })
    .catch(() => {})
}

/** 导入按钮操作 */
function handleImport() {
  importFileRef.value.handleImport()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

getList()
</script>

<style scoped lang="scss"></style>
