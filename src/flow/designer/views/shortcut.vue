<template>
	<el-dialog
		v-model="data.modalVisible"
		title="快捷入门"
		width="60%"
		append-to-body
		top="5vh"
	>
		<el-table
			row-key="code"
			:data="data.dataSource"
		>
			<el-table-column
				v-for="item in data.columns"
				:key="item.dataIndex"
				:label="item.title"
				:prop="item.key"
				style="width: 100%"
			/>
		</el-table>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="methods.cancel">取 消</el-button>
				<el-button type="primary" @click="methods.saveSetting">确 定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="FlowShortcut">

const props = defineProps({
	shortcut: {
		type: Object,
		default: null,
	},
})

const data = reactive({
	modalVisible: false,
	columns: [
		{
			title: "功能",
			align: "center",
			key: "shortcutName",
			dataIndex: "shortcutName",
			width: "50%",
		},
		{
			title: "快捷入门",
			align: "center",
			key: "codeName",
			dataIndex: "codeName",
			width: "50%",
		},
	],
	dataSource: [],
})

onMounted(() => {
	let obj = Object.assign({}, props.shortcut)
	for (let k in obj) {
		data.dataSource.push(obj[k])
	}
})

const methods = {
	open() {
		data.modalVisible = true
	},
	close() {
		data.modalVisible = false
	},
	saveSetting() {
		methods.close()
	},
	cancel() {
		methods.close()
	},
}

// 暴露变量
defineExpose({
	open: methods.open,
})
</script>

<style lang="scss" scoped>
</style>
