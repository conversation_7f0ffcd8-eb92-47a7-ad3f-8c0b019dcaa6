import request from "@/utils/request"
import { eduMinio } from "@/config/constant"

// 下载附件
export function downloadUrl(data: object) {
  return request({
    url: eduMinio + "/minio/download",
    method: "post",
    data: data,
  })
}
// 删除附件
export function deleteMinio(data: object) {
  return request({
    url: eduMinio + "/minio/delete",
    method: "delete",
    data: data,
  })
}

// 上传并返回访问的url
export function uploadAndAccess(data: object) {
  return request({
    url: eduMinio + "/minio/getFileUrl",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
}
