<template>
	<section class="app-main">
		<router-view v-slot="{ Component, route }">
			<transition name="fade-transform" mode="out-in">
				<keep-alive :include="tagsViewStore.cachedViews">
					<component :is="Component" v-if="!route.meta.link" :key="route.path" />
				</keep-alive>
			</transition>
		</router-view>
		<iframe-toggle />
	</section>
</template>

<script setup>
import iframeToggle from "./IframeToggle/index"
import useTagsViewStore from "@/store/modules/tagsView"

const route = useRoute()
const tagsViewStore = useTagsViewStore()

onMounted(() => {
	addIframe()
})

watch(route, () => {
  addIframe()
})
// watchEffect(() => {
//   addIframe()
// })
function addIframe() {
	if (route.meta.link) {
		useTagsViewStore().addIframeView(route)
	}
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;

.app-main {
  /* 50= navbar  50  */
  width: 100%;
  position: relative;
  overflow: hidden;
  top: 46px;
}

.fixed-header + .app-main {
  padding-top: 46px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 46 */
    // min-height: calc(100vh - #{$base-nav-height} - 46px);
    min-height: $base-main-page-height;
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>
