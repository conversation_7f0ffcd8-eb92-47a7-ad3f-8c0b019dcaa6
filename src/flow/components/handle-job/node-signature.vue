<template>
  <div>
    <el-form
      ref="dataFormRef"
      v-loading="loading"
      :model="form"
      :rules="dataRules"
      label-width="150px"
      :disabled="operType === 'view'"
    >
      <span style="margin-left: 30px;">
        什么是加减节点？加节点指在流程图里增加其他节点及【参与者】参与审批，加节点分为前加节点、后加节点、加并节点
        <br />
        <span style="margin-left: 30px;">
          1、前加节点即在当前审批人节点之前增加一个节点及参与者参与审批，该参与者节点审批完成之后会回到当前审批人节点
        </span>
        <br />
        <span style="margin-left: 30px;">
          2、后加节点即在当前审批人节点之后增加一个节点及参与者参与审批，当前审批人节点审批之后流转到该参与者节点
        </span>
        <br />
        <span style="margin-left: 30px;">
          3、加并节点即与当前审批人节点在同一个节点层级，若当前审批人节点为会签节点，则需所有节点通过。
        </span>
        若当前审批人节点为或签节点，则一个节点通过即可。若当前审批人节点为依次审批，则按顺序依次通过即可
        <br />
        <span style="margin-left: 30px;">4、减节点即减少节点及参与者</span>
      </span>
      <el-row :gutter="24">
        <el-divider>加节点设置</el-divider>

        <el-col :span="12" class="mb-1">
          <el-form-item label="基于当前节点加节点" prop="runNodeId">
            <el-select
              v-model="form.runNodeId"
              placeholder="默认当前节点"
              clearable
              filterable
              disabled
            >
              <el-option
                v-for="(item, index) in cascadeDic.runNodeId"
                :key="index"
                :label="item.nodeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item
            label="节点名称"
            prop="nodeName"
            :rules="{ required: true, message: '节点名称不能为空', trigger: 'blur' }"
          >
            <el-input v-model="form.nodeJobSigned.nodeName" placeholder="请输入节点名称" />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb-1">
          <el-form-item label="加节点参与者类型" prop="jobType">
            <el-radio-group v-model="form.jobType" @change="handleRoleType">
              <el-radio
                v-for="(item, index) in DIC_PROP.JOB_USER_TYPE"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col
          v-if="
            props.currJob.runNodeVO.nodeApproveMethod === DIC_PROP.NODE_APPROVE_METHOD[2].value &&
            props.currJob.nodeJobSigned.nodeSignedType === DIC_PROP.NODE_SIGNATURE_TYPE[2].value
          "
          :span="24"
          class="mb-1"
        >
          <el-form-item label="依次审批节点顺序" prop="signedType">
            <el-radio-group v-model="form.signedType">
              <el-radio
                v-for="(item, index) in DIC_PROP.NODE_SEQUENTIAL_TYPE"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[0].value" :span="12" class="mb-1">
          <el-form-item label="指定参与者" prop="roleId">
            <UserSelect ref="userSelectRef" v-model="form.roleId" />
          </el-form-item>
        </el-col>

        <el-col v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[1].value" :span="12" class="mb-1">
          <el-form-item label="指定参与者" prop="roleId">
            <el-select v-model="form.roleId" placeholder="请选择参与者" clearable filterable>
              <el-option
                v-for="(item, index) in dicData.roles"
                :key="index"
                :label="item.roleName"
                :value="item.roleId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- <el-col v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value" :span="12" class="mb-1">
					<el-form-item label="指定参与者" prop="roleId">
						<el-select
							v-model="form.roleId"
							placeholder="请选择参与者"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in dicData.posts"
								:key="index"
								:label="item.postName"
								:value="item.postId"
							/>
						</el-select>
					</el-form-item>
				</el-col> -->

        <el-col v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value" :span="12" class="mb-1">
          <el-form-item label="指定参与者" prop="roleId">
            <DeptPicker ref="deptPickerRef" v-model="form.roleId" placeholder="请选择指定参与者" />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20">
          <el-form-item label="节点类型" prop="nodeType">
            <el-select
              v-model="form.nodeJobSigned.nodeType"
              placeholder="请输入节点类型"
              filterable
            >
              <el-option
                v-for="(item, index) in DIC_PROP.NODE_TYPE.slice(1, 3)"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <footer class="el-dialog__footer">
      <el-button type="primary" :loading="loading" @click="handleUpdate">
        确定
      </el-button>
    </footer>
  </div>
</template>

<script setup lang="ts" name="JobSignature">
import { onCascadeChange, onLoadDicUrl } from '../convert-name/convert'
import { getCurrentInstance } from 'vue'
import { handleChangeJobType } from '../../index'

const $emit = defineEmits(['onNodeSignature'])
const { proxy } = getCurrentInstance()

// 定义变量内容
const dataFormRef = ref()
const loading = ref(false)
const operType = ref(false)

// 定义字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: 'roles' })
const onCascade = onCascadeChange(cascadeDic, {
  key: 'flowInstId',
  cascades: ['runNodeId']
})
onMounted(() => {
  onLoad(dicData)
})

// 提交表单数据
const form = reactive({
  runNodeId: null,
  jobType: '',
  roleId: null,
  nodeJobSigned: {}
})

// 定义校验规则
const dataRules = ref({
  jobType: [{ required: true, message: '参与者类型不能为空', trigger: 'blur' }],
  roleId: [{ required: true, message: '参与者不能为空', trigger: 'blur' }]
  // nodeName: [{ required: true, message: '节点名称不能为空', trigger: 'blur' }]
})

const props = defineProps({
  currJob: {
    type: Object as PropType<any>,
    default: () => ({})
  }
})

function initJobData() {
  Object.assign(form, props.currJob)
  onCascade(form)
}

const userSelectRef = ref(null)
const deptPickerRef = ref(null)
function handleRoleType() {
  let dictData = {
    users: userSelectRef?.value?.users || [],
    depts: deptPickerRef?.value?.deptOptions || [],
    posts: dicData.posts
  }
  handleChangeJobType(dictData, form)
}

async function handleUpdate() {
  // TOD 上面两种方式都校验不了
  if (!form.nodeJobSigned.nodeName) {
    proxy.$modal.msgError('节点名称不能为空')
    return
  }

  const valid = await dataFormRef.value.validate().catch(() => {})
  if (!valid) return false

  form.nextUserRole = { jobType: form.jobType, roleId: form.roleId }

  props.currJob.nextUserRole = form.nextUserRole
  props.currJob.nodeJobSigned = form.nodeJobSigned

  loading.value = true
  try {
    $emit('onNodeSignature', props.currJob)
    setTimeout(() => {
      // 异步异常
      loading.value = false
    }, 3000)
  } catch (e) {
    loading.value = false
  }
}

// 监听双向绑定
watch(
  () => props.currJob.id,
  (val) => {
    initJobData()
  }
)

onMounted(() => {
  initJobData()
})
</script>

<style lang="scss" scoped></style>
