<template>
  <div class="has-iframe-page">
    <div v-show="!showPreviewPage" class="app-container">
      <div class="app-container__title">
        批量发起
        <el-tooltip effect="dark" placement="right-start">
          <svg-icon name="local-question" class="color-#6B6B6B cursor-pointer" />
          <template #content>
            <div class="p-5px leading-25px">
              存在以下情况时，不支持批量发起：<br />
              1. 存在发起方需要上传图片、附件上传的模板不支持批量发起;
              <br />2. 合同的签署方超过5方。
            </div>
          </template>
        </el-tooltip>
      </div>

      <el-form :model="queryParams" ref="queryRef" inline class="white-form-box" label-width="90px">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model.trim="queryParams.templateName" placeholder="请输入模板名称" clearable maxlength="100"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="所属业务线" prop="businessLineId">
          <BusinessSelect v-model="queryParams.businessLineId" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTimeRange">
          <el-date-picker v-model="queryParams.createTimeRange" value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm" type="datetimerange" range-separator="-" start-placeholder="开始时间"
            end-placeholder="结束时间" class="common-input-width" clearable :default-time="dateDefaultTime" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <CustomTable ref="customTableRef" v-model:show-search="showSearch" v-model:page="queryParams.page"
        v-model:limit="queryParams.limit" :data="tableData" :has-toolbar="false" :loading="loading" :total="total"
        @reload="getList" :opt-width="140">
        <template #actions>
          <div style="display: flex;justify-content: spac;">
            <el-button v-auths="['file:fileList:batchLunch:his']" type="primary" @click="getHisLunchList">
              历史发起
            </el-button>
          </div>
        </template>

        <template #toolbar>
          <el-button plain type="primary" class="is-trans is-white" v-auths="['file:fileList:batchLunch:draft']" @click="toDrafts">
            <svg-icon name="local-edit-fill" class="u-m-r-5" />
            草稿箱({{ draftTotal }})
          </el-button>
        </template>

        <vxe-column type="seq" width="70" fixed="left" />
        <vxe-column field="templateName" title="模板名称" fixed="left" min-width="200" />
        <vxe-column field="businessLineName" title="所属业务线" min-width="120" />
        <vxe-column field="templateType" title="模版类型" width="90">
          <template #default="{ row }">
            <dict-tag :options="template_type" :value="row.templateType" />
          </template>
        </vxe-column>
        <vxe-column field="essTemplateId" title="模板id" min-width="180" />
        <vxe-column field="createUser" title="创建人" min-width="100" />
        <vxe-column field="flowFlag" title="使用模板流程设置" width="160">
          <template #default="{ row }">
            <div class="flex-y-center">
              {{ selectDictLabel(template_flow_flag, row.flowFlag) }}
              <el-link type="success" v-if="+row.flowFlag === 1" underline="never" class="ml-5px"
                @click="toFlowSetting(row)" v-auths="['file:fileList:batchLunch:flowSetting']">
                (流程配置)
              </el-link>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="createTime" title="创建时间" width="170" />
        <template #opts="{ row }">
          <TableColOptBtn :key="Math.random()" :buttons="[
            {
              permission: ['file:fileList:batchLunch:add'],
              text: '批量发起',
              click: () => handleBatchLunch(row),
            },
            {
              permission: ['file:fileList:batchLunch:preview'],
              text: '预览',
              click: () => handlePreview(row),
            },
          ]" />
        </template>
      </CustomTable>
    </div>

    <!-- 预览 -->
    <PreviewTemplatePage ref="previewRef" v-model="showPreviewPage" />

    <!-- 草稿箱 -->
    <DraftListModal ref="draftListRef" @updateTotal="updateDraftTotal" />
  </div>
</template>

<script setup name="FileBatchLunchList">
import { getTmpBatchPageListAPI } from "@/api/ess/fileManagement/fileBatchLunch"

const DraftListModal = defineAsyncComponent(() =>
  import("./components/DraftListModal.vue")
)

const BusinessSelect = defineAsyncComponent(() =>
  import("@/views/components/BusinessSelect.vue")
)
const PreviewTemplatePage = defineAsyncComponent(() =>
  import("@/views/template/templateManagement/components/PreviewTemplatePage.vue")
)
const router = useRouter()
const { proxy } = getCurrentInstance()
const { template_type, template_flow_flag } = proxy.useDict(
  "template_type",
  "template_flow_flag"
)

const queryParams = reactive({
  templateName: null,
  templateStatus: null,
  businessLineId: null,
  createTimeRange: [],
  limit: 10,
  page: 1,
})

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const previewRef = ref(null) // 预览page
const showPreviewPage = ref(false) // 预览页面

/** 查询列表 */
async function getList() {
  loading.value = true
  try {
    queryParams.queryStartTime = queryParams.createTimeRange?.[0]
    queryParams.queryEndTime = queryParams.createTimeRange?.[1]
    const res = await getTmpBatchPageListAPI(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}
// 批量发起
function handleBatchLunch(row) {
  router.push(`/file/fileBatchLunch/${row.id}`)
}
/** 历史发起 */
function getHisLunchList() {
  router.push(`/file/fileLunchHisList`)
}

// 流程
function toFlowSetting(row) {
  router.push(`/template/tempFlowConfig/${row.id}?to=fileBatchLunch`)
}

/** 预览按钮操作 */
function handlePreview(row) {
  showPreviewPage.value = true
  previewRef.value.open(row)
}


const draftTotal = ref(0)

/** 草稿箱 */
function toDrafts() {
  proxy.$refs.draftListRef.open()
}


// 更新草稿箱数量
function updateDraftTotal(total) {
  draftTotal.value = total
}

getList()
</script>

<style scoped lang="scss">
.app-container__title {
  @apply title-line text-20px mb-10px;
}
</style>
