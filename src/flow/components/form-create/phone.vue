<template>
	<div style="width: 100%;">
		<el-input
			v-model="data.modelValue"
			:disabled="props.disabled"
			clearable
			@blur="changeModelValue"
		/>
	</div>
</template>

<script setup lang="ts" name="UserRolePickerIndex">
import { rule } from "@/utils/validate"
const { proxy } = getCurrentInstance()

const props = defineProps({
	modelValue: {
		type: String,
		default: null,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
})

const emits = defineEmits(["update:modelValue"])

const data = reactive({
	modelValue: null,
})

const changeModelValue = () => {
	if (data.modelValue) {
		rule.validatePhone(null, data.modelValue, err => {
			if (err) {
				data.modelValue = null
				proxy.$modal.msgSuccess(err.message)
			}
			emits("update:modelValue", data.modelValue)
		})
	}
	else {
		emits("update:modelValue", null)
	}
}

onMounted(() => {
	data.modelValue = props.modelValue
})
</script>
