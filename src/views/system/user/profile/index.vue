<template>
	<div class="app-container profile-container">
		<el-card class="box-card" shadow="none">
			<template #header>
				<span>身份信息</span>
			</template>
			<UserBaseInfo v-if="state.user && state.user.userId" :info="state" />
		</el-card>
		<el-card class="box-card" shadow="none">
			<template #header>
				<span>当前账号角色：{{ activeRoles ? activeRoles : "" }}</span>
			</template>
			<AccountRole />
		</el-card>
		<!-- <el-card class="box-card" shadow="none">
      <template #header>
        <span>基本资料</span>
      </template>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本资料" name="userinfo">
          <EditUserInfoForm :user="state.user" />
        </el-tab-pane>
        <el-tab-pane label="修改密码" name="resetPwd">
          <EditResetPwdForm />
        </el-tab-pane>
      </el-tabs>
    </el-card> -->
	</div>
</template>

<script setup name="Profile">
// import EditResetPwdForm from './components/EditResetPwdForm'
// import EditUserInfoForm from './components/EditUserInfoForm'

import AccountRole from "./components/AccountRole"
import UserBaseInfo from "./components/UserBaseInfo.vue"
import { getUserProfile } from "@/api/system/user"

const activeTab = ref("userinfo")
const activeRoles = ref()
const state = reactive({
	user: {},
	roleGroup: {},
  loginRoleGroup: {},
	postGroup: {},
})

function getUser() {
	getUserProfile().then(response => {
		state.user = response.data
		state.roleGroup = response.roleGroup
		state.loginRoleGroup = response.loginRoleGroup
		state.postGroup = response.postGroup
		activeRoles.value = response.data.dept.deptName
	})
}

getUser()
</script>

<style lang="scss" scoped>
$lineColor: var(--el-color-primary-light-6);
.profile-container {
  .box-card {
    @apply border-0 mb-20px rounded-12px;
    :deep(.el-card__header) {
      margin: 20px 0 0 20px;
      @apply color-#0c1433 border-0 p-0 title-line-b  before:(bg-[var(--el-color-primary-light-6)]);
      span {
        @apply z-10 relative;
      }
    }
  }
}
</style>
