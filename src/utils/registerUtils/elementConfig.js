export function registerElement(app) {
	const ElementComponents = app._context.components
	// 全局设置禁止dialog点击遮罩层关闭
	ElementComponents.ElDialog["props"].closeOnClickModal.default = false
	// 全局设置dialog draggable
	ElementComponents.ElDialog.props.draggable = {
		default: true,
		type: ElementComponents.ElDialog.props.draggable,
	}
	ElementComponents.ElDialog.props.overflow = {
		default: true,
		type: ElementComponents.ElDialog.props.overflow,
	}
	ElementComponents.ElTable.props.stripe = { type: <PERSON><PERSON><PERSON>, default: true }
}
