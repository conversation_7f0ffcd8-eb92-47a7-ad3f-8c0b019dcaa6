.flow-header-drawer {
  /*抽屉内容靠上*/
  .el-drawer__header {
    margin-bottom: 0;
  }
}

.tinymce-print-drawer {
  /*抽屉内容靠上*/
  .el-drawer__header {
    margin-bottom: 0;
  }
}

.flow-overflow-drawer {
  /*抽屉内容靠上*/
  .el-drawer__header {
    margin-bottom: 0;
  }
  /*隐藏滚动条*/
  .el-drawer__body {
    overflow: hidden;
    padding: 10px 0;
  }
}

.flow-dialog-drawer {
  /*隐藏滚动条*/
  .el-dialog__body {
    overflow: hidden;
  }
}

.flow-attr-drawer {
  /*抽屉内容靠上*/
  .el-drawer__header {
    margin-bottom: 0;
    margin-left: 15px;
  }
  .el-tabs__header .is-top{
    //display: none;
  }
}

