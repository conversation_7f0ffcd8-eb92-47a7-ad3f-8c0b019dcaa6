<template>
  <div class="select-list">
    <RecycleScroller
      v-if="processedList.length"
      v-slot="{ item }"
      class="scroller"
      :items="processedList"
      :item-size="45"
      key-field="id"
      :grid-items="gridItems"
      :item-secondary-size="itemSecondarySize"
    >
      <div class="select-list__item" :class="{ 'has-bg': itemBg }">
        <span v-tooltip:middle>
          {{
            item.joinNames ||
            item.name ||
            item.nickName ||
            item.userName ||
            item.userOrRoleName
          }}
          {{ item.workNumber ? `(${item.workNumber})` : "" }}
        </span>
        <el-icon v-if="showRemove" @click="removeItem(item)">
          <RemoveFilled />
        </el-icon>
      </div>
    </RecycleScroller>
    <el-empty v-if="!list.length" :description="emptyText" />
  </div>
</template>

<script setup name="TagUserList">
import { RecycleScroller } from "vue-virtual-scroller"
import "vue-virtual-scroller/dist/vue-virtual-scroller.css"
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  showRemove: {
    type: Boolean,
    default: true,
  },
  emptyText: {
    type: String,
    default: "暂未选择",
  },
  itemSecondarySize: {
    type: Number,
    default: 122,
  },
  gridItems: {
    type: Number,
    default: 3,
  },
  // item是否带背景
  itemBg: {
    type: Boolean,
    default: false,
  },
})

const processedList = computed(() => {
  return props.list.map((item) => ({
    ...item,
    name: item.name || item.rangeName || item.userName,
    id: item.id || item.userId || item.rangeId || item.tagId, // 如果 id 不存在，使用 userId
  }))
})

const emit = defineEmits(["remove"])
const removeItem = (item) => {
  emit("remove", item)
}
</script>

<style scoped lang="scss">
.select-list {
  height: 350px;
  @apply pl-20px overflow-y-auto pb-10px;
  .scroller {
    height: 100%;
  }
  &__item {
    @apply relative w-90% text-14px m-y-8px;
    span {
      @apply w100% inline-block text-center rounded-4px bg-white box-border h-26px leading-26px px-5px;
      border: 1px solid #e2e2e2;
    }
    &.has-bg {
      span {
        border-color: #eaeef0;
        background-color: #eaeef0;
      }
    }
    .el-icon {
      @apply absolute right-[-7px] top-[-7px] cursor-pointer color-#f94e4f text-16px;
    }
  }
}
</style>
