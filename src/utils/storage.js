import Cookies from "js-cookie"
const TokenKey = "Admin-Token"

/**
 * window.localStorage 浏览器永久缓存
 * @method set 设置永久缓存
 * @method get 获取永久缓存
 * @method remove 移除永久缓存
 * @method clear 移除全部永久缓存
 */
export const Local = {
	setKey(key) {
		return `${__NEXT_NAME__}:${key}`
	},
	set(key, val) {
		window.localStorage.setItem(Local.setKey(key), JSON.stringify(val))
	},
	get(key) {
		let json = window.localStorage.getItem(Local.setKey(key))
		return JSON.parse(json)
	},
	remove(key) {
		window.localStorage.removeItem(Local.setKey(key))
	},
	clear() {
		window.localStorage.clear()
	},
}

/**
 * window.sessionStorage 浏览器临时缓存
 * @method set 设置临时缓存
 * @method get 获取临时缓存
 * @method remove 移除临时缓存
 * @method clear 移除全部临时缓存
 */
export const Session = {
	set(key, val) {
		if ([TokenKey, "refresh_token", "tenantId"].includes(key)) {
			Cookies.set(key, val)
		}
		window.sessionStorage.setItem(key, JSON.stringify(val))
	},
	get(key) {
		if ([TokenKey, "refresh_token", "tenantId"].includes(key)) {
			const token = Cookies.get(key)
			if (token) { return token }
		}
		const json = window.sessionStorage.getItem(key)
		return JSON.parse(json)
	},
	remove(key) {
		if ([TokenKey, "refresh_token", "tenantId"].includes(key)) {
			Cookies.remove(key)
		}
		window.sessionStorage.removeItem(key)
	},
	clear() {
		[TokenKey, "refresh_token", "tenantId"].forEach(key => Cookies.remove(key))
		window.sessionStorage.clear()
	},
	getToken() {
		return this.get(TokenKey)
	},
	getTenant() {
		return Local.get("tenantId") || 1
	},
}
