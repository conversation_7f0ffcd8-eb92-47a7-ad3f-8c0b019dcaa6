/**
 * 用于获取表单组件路径
 * <AUTHOR>
 */
import { validateNull } from "@/utils/validate"
import { DIC_PROP } from "@/flow/support/dict-prop"
import { PROP_CONST } from "@/flow/support/prop-const"
import { deepClone } from "@/utils/index";


// 定制化工单Key
export const orderKeyMap = {
	HandoverFlow: "HandoverFlow",
	AskLeave: "AskLeave",
}

// 跳转页面路径
export const locationHash = {
	TodoJobHash: "#/jsonflow/run-job/do-job",
	RunApplicationHash: "#/order/run-application/index",
	SignJobHash: "#/jsonflow/run-job/sign-job",
	HiJobHash: "#/jsonflow/run-job/hi-job",
}

// Vue页面路径
export const vueKey = {
	HandoverNodeRecordFlow: "/order/handover-node-record/flow",
	RunApplicationForm: "/order/run-application/flow",
	CommentFlow: "/jsonflow/comment/flow",
 CommentFlowId: '1' , 
	FlowDesignView: "/jsonflow/flow-design/view",
  FlowDesignViewId: '2' ,
	CommentTimeline: "/jsonflow/comment/timeline",
}

export const vueKeySys = {
  sysPaths: [vueKey.CommentFlow, vueKey.CommentTimeline, vueKey.FlowDesignView],
  sysPathIds: [vueKey.CommentFlowId, vueKey.FlowDesignViewId]
}

export function handleCustomForm(data, row) {
    // TODO form为默认发起页面
    let rowClone = deepClone(row);
    rowClone.path = rowClone.path.replace('flow', 'form')
    data.currFlowForm.currElTab = deepClone(rowClone)
}


/**
 * 用于控制页面的显隐
 * 显隐条件->工单path
 */
export function handleElTab(currJob) {
	let order = currJob.order, conditions: any = []
	// 业务任务页面无order信息
	if (validateNull(order)) { return conditions }
	// 判断工作交接切换不同页面
	if (currJob.flowKey === orderKeyMap.HandoverFlow) {
		let handoverNode = order.type === "-1"
		conditions.push({ isShow: handoverNode ? "1" : "0", path: vueKey.HandoverNodeRecordFlow })
	}
	return conditions
}

export function compatibleAppHeight(isApp) {
	if (!isApp) { return }
	// 兼容移动端 自适应页面高度
	let app = document.getElementById("app")
	if (app) { app.style.overflow = "auto" }
}

export function validateFormTypeSave(form) {
    // 设计表单默认表
    let isDesign = form.type !== DIC_PROP.FORM_TYPE[1].value;
    if (isDesign && !form.tableName) form.tableName = PROP_CONST.COMMON.tableName

    // 设计表单path
    if (isDesign) {
        form.path = vueKey.RunApplicationForm
    } else {
        form.formInfo = null
    }
}

export function currElTabIsExist(currJob) {
	let currElTab
	// 兼容移动端
	if (!currJob.currElTab) {
		currElTab = currJob.elTabs.find(f => f.path === vueKey.RunApplicationForm)
	}
	else {
		currElTab = currJob.elTabs.find(f => f.id === currJob.currElTab.active)
	}
	// 查看则只读
	if (currJob.hiJob) {
		currElTab.isAutoAudit = "0"
		currElTab.isFormEdit = "0"
	}
	return currElTab
}

// 查看还是修改
export async function currFormIsView(_this, find, existRoleId, callback?, widgetList?) {
    if (find?.isFormEdit === '0' || !existRoleId) {
        if (callback) await callback(_this)
        if (_this && _this.disableForm) _this.disableForm(true)
        if (_this && _this.disableSubmit) _this.disableSubmit()
        return
    }
    // 全部只读部分可编辑
    if (find?.defFormEdit === '0') {
        if (_this && _this.disableForm) _this.disableForm(null, widgetList)
        if (_this && _this.enableSubmit) _this.enableSubmit()
        if (callback) await callback(_this)
    } else {
        if (callback) await callback(_this)
    }
}

// 查看还是修改
export async function currElTabIsView(_this, currJob, submitForm?, callback?, widgetList?) {
    // 从elTabs中获取
    let find = currElTabIsExist(currJob)
    if (find?.isFormEdit !== '0' || find?.isAutoAudit === '1') {
        if (submitForm) currJob.resolveSaves?.push(submitForm)
    }
    await currFormIsView(_this, find, currJob.roleId, callback, widgetList)
    // 重置保存按钮状态
    currElTabIsSave(currJob, false)
}


// 处理修改时保存状态
export function currElTabIsSave(currJob, bool, emits?) {
    // 从elTabs中获取
    let find = currElTabIsExist(currJob)
    if (find?.isFormEdit !== '0') find.isSave = bool
    // 提交时自动审批
    if (emits && find.isAutoAudit === '1') {
        emits("handleJob", DIC_PROP.JOB_BTNS[0].value);
    }
}


