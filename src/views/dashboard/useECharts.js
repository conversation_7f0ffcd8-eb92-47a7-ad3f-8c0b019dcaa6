import { ref, onMounted, onUnmounted } from "vue"
import { useResizeObserver } from "@vueuse/core"
import { debounce } from "lodash-es"
import * as echarts from "echarts"

export default function useECharts(chartContainer, options) {
	const chartInstance = ref(null)

	const resizeChart = debounce(() => {
		chartInstance.value.resize()
	}, 100)

	onMounted(() => {
		// 初始化 ECharts 实例
		chartInstance.value = echarts.init(chartContainer.value)
		// 设置 ECharts 配置项
		chartInstance.value.setOption(options.value)
		console.log("初始化图表")

		// 监听窗口大小变化，自动调整图表大小
		window.addEventListener("resize", () => resizeChart)
	})

	onUnmounted(() => {
		// 销毁 ECharts 实例
		chartInstance.value.dispose()
		chartInstance.value = null
		// 移除窗口大小变化监听器
		window.removeEventListener("resize", () => resizeChart)
	})

	// 返回 ECharts 实例，以便在外部进行操作
	return chartInstance
}
