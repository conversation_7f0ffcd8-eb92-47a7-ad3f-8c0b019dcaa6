<template>
	<div class="auth-list">
		<CustomTable
			ref="customTableRef"
			v-model:page="queryParams.page"
			v-model:limit="queryParams.limit"
			:data="tableData"
			:has-toolbar="false"
			:loading="loading"
			:total="total"
			@reload="getList"
		>
			<vxe-column type="seq" width="70" />
			<vxe-column
				title="操作时间"
				field="createTime"
				min-width="100"
				show-overflow
			/>
			<vxe-column
				title="操作类型"
				field="opType"
				min-width="100"
				show-overflow
			>
				<template #default="{ row }">
					<dict-tag :options="seal_op_type" :value="row.opType" />
				</template>
			</vxe-column>
			<vxe-column
				title="操作人名称"
				field="userName"
				min-width="100"
				show-overflow
			/>
			<vxe-column
				title="操作内容"
				field="opContent"
				min-width="160"
				show-overflow
			/>
		</CustomTable>
	</div>
</template>

<script setup>
import { getSealAuthLog } from "@/api/ess/seal/sealAuth"

const { proxy } = getCurrentInstance()
const { seal_op_type } = proxy.useDict("seal_op_type")

const route = useRoute()
const sealId = route.params.id
const loading = ref(true)
const total = ref(0)
const tableData = ref([])

const queryParams = reactive({
	sealId,
	page: 1,
	limit: 10,
})

async function getList() {
	try {
		loading.value = true
		let res = await getSealAuthLog(queryParams)
		tableData.value = res.object.records
		total.value = res.object.total
	}
	finally {
		loading.value = false
	}
}

getList()
</script>

<style scoped lang="scss">
.auth-list{
  :deep(.white-body-box) {
    padding: 0;
  }
}
</style>
