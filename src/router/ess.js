import Layout from '@/layout'

export const staticRoutesEss = [
  {
    path: '/account',
    component: () => import('@/views/account'),
    hidden: true,
    meta: { title: '账号切换' },
  },
  {
    path: '/templateIframe',
    component: () => import('@/views/template/templateManagement/templateIframe.vue'),
    hidden: true,
    meta: { title: '模版预览' },
  },
  {
    path: '/fileIframe/details',
    component: () => import('@/views/fileManagement/fileIframe/details.vue'),
    hidden: true,
    meta: { title: '文件预览' },
  },
  {
    path: '/fileIframe/sign',
    component: () => import('@/views/fileManagement/fileIframe/sign.vue'),
    hidden: true,
    meta: { title: '文件签署' },
  },
  {
    path: '/fileIframe/preview',
    component: () => import('@/views/fileManagement/fileIframe/preview.vue'),
    hidden: true,
    meta: { title: '文件预览' },
  },
  {
    path: '/realName',
    hidden: true,
    component: () => import('@/views/system/user/realName'),
    name: 'RealName',
    meta: { title: '实名认证' },
  },
  {
    path: '/outSide',
    name: 'PreviewOutside',
    component: () => import('@/views/doc/devManual/previewOutside'),
    hidden: true,
    meta: { title: '电子印章平台开发手册' }
  },
  {
    path: '/inner',
    name: 'PreviewInner',
    component: () => import('@/views/doc/devManual/previewInner'),
    hidden: true,
    meta: { title: '电子印章平台开发手册' }
  },
]


export const dynamicRoutesEss = [
  {
    path: '/template',
    component: Layout,
    permissions: ['template:list:edit'],
    hidden: true,
    children: [
      {
        path: 'editTemplate/:essTemplateId',
        component: defineAsyncComponent(() =>
          import('@/views/template/templateManagement/editTemplate').then(r => r.default || r),
        ),
        name: 'EditTemplate',
        meta: {
          title: '编辑模版',
          link: true,
        }
      },
    ]
  },
  {
    path: '/template',
    component: Layout,
    hidden: true,
    permissions: ['template:list:flowSetting', 'template:privateList:flowSetting', 'template:myTemplate:flowSetting'],
    children: [
      {
        path: 'tempFlowConfig/:id',
        component: () => import('@/views/template/templateManagement/tempFlowConfig'),
        name: 'TempFlowConfig',
        meta: {
          title: '流程配置',
        }
      },
    ]
  },
  {
    path: '/template',
    component: Layout,
    hidden: true,
    permissions: ['template:list:add'],
    children: [
      {
        path: 'createTemplate',
        component: defineAsyncComponent(() =>
          import('@/views/template/templateManagement/createTemplate').then(r => r.default || r),
        ),
        name: 'CreateTemplate',
        meta: {
          title: '创建模版',
          link: true,
        }
      },
    ]
  },
  {
    path: '/template',
    component: Layout,
    hidden: true,
    permissions: ['template:myTemplate:create'],
    children: [
      {
        path: 'createMyTemplate',
        component: defineAsyncComponent(() =>
          import('@/views/template/myTemplate/createMyTemplate').then(r => r.default || r),
        ),
        name: 'CreateMyTemplate',
        meta: {
          title: '创建我的模版',
          link: true,
        }
      },
    ]
  },
  {
    path: '/template',
    component: Layout,
    hidden: true,
    permissions: ['template:myTemplate:useTemplate'],
    children: [
      {
        path: 'useTemplate/:id',
        component: defineAsyncComponent(() =>
          import('@/views/template/useTemplate/index').then(r => r.default || r),
        ),
        name: 'UseTemplate',
        meta: {
          title: '使用模版',
          link: true,
        }
      },
    ]
  },
  {
    path: '/file',
    component: Layout,
    hidden: true,
    permissions: ['file:fileList:lunch', 'file:fileList:lunchAudit', 'file:initiatedFiles:lunch', 'file:initiatedFiles:lunchAudit'],
    children: [
      {
        path: 'fileLunch/:draftId/:flowFlag',
        component: () => import('@/views/fileManagement/fileLunch/index'),
        props: true,
        // component: defineAsyncComponent(() =>
        //   import('@/views/fileManagement/fileLunch/index').then(r => r.default || r),
        // ),
        name: 'FileLunch',
        meta: {
          title: '文件发起',
          // link: true,
        }
      },
    ]
  },
  {
    path: '/file',
    component: Layout,
    hidden: true,
    permissions: ['file:fileList:batchLunch'],
    children: [
      {
        path: 'fileBatchLunchList',
        component: () => import('@/views/fileManagement/fileBatchLunchList/index'),
        name: 'FileBatchLunchList',
        meta: {
          title: '文件批量发起',
        }
      },
    ]
  },
  {
    path: '/file',
    component: Layout,
    hidden: true,
    permissions: ['file:fileList:batchLunch'],
    children: [
      {
        path: 'fileBatchLunch/:id',
        component: () => import('@/views/fileManagement/fileBatchLunch/index'),
        props: true,
        name: 'FileBatchLunch',
        meta: {
          title: '文件批量发起',
        }
      },
    ]
  },
  {
    path: '/file',
    component: Layout,
    hidden: true,
    permissions: ['file:fileBatchLunch:his'],
    children: [
      {
        path: 'fileLunchHisList',
        component: () => import('@/views/fileManagement/fileLunchHisList/index'),
        name: 'FileLunchHisList',
        meta: {
          title: '历史发起',
        }
      },
    ]
  },
  {
    path: '/ess',
    component: Layout,
    hidden: true,
    permissions: ['ess:seal:sealDetails', 'ess:mySeal:sealDetails'],
    children: [
      {
        path: 'sealDetails/:id',
        component: () => import('@/views/sealManagement/sealDetails/index'),
        props: true,
        name: 'SealDetails',
        meta: {
          title: '印章详情',
        }
      },
    ]
  },
  {
    path: '/ess',
    component: Layout,
    hidden: true,
    permissions: ['ess:list:disableAudit', 'ess:list:disable'],
    children: [
      {
        path: 'updateSealStatusFlow',
        component: () => import('@/views/sealManagement/sealList/updateSealStatusFlow'),
        props: true,
        name: 'UpdateSealStatusFlow',
        meta: {
          title: '启用/停用印章流程',
        }
      },
    ]
  },
  {
    path: '/navUser',
    component: Layout,
    hidden: true,
    permissions: ['doc:optManual:list'],
    children: [
      {
        path: 'optManual/previewDoc',
        component: () => import('@/views/doc/optManual/previewDoc'),
        name: 'PreviewDoc',
        meta: {
          title: '操作手册查看',
        }
      },
    ]
  }
]
