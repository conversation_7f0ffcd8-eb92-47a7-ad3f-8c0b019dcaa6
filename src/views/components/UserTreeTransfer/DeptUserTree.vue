<template>
  <div class="dept-user-tree">
    <el-input
      v-model="filterText"
      placeholder="请输入关键字"
      clearable
      maxlength="100"
      suffix-icon="Search"
      @input="onQueryChanged"
      class="filter-input"
    >
    </el-input>
    <div v-loading="loading">
      <el-tree-v2
        class="user-tree"
        v-bind="treeCommonProps"
        :data="treeData"
        :default-checked-keys="defaultCheckedKeys"
        :filter-method="filterTreeMethod"
      >
        <!-- <el-tree-v2
        class="user-tree"
        v-bind="treeCommonProps"
        :data="filterData()"
        :default-checked-keys="defaultCheckedKeys"
      > -->
        <template #default="{ node, data }">
          <span class="user-tree-label">{{ node.label }}</span>
          <span v-if="data.workNumber">({{ data.workNumber }})</span>
        </template>
      </el-tree-v2>
    </div>
  </div>
</template>

<script setup>
import { useDeptStore } from "@/store/modules/dept"
import { selectZsDeptTreeInfo, fetchDeptTreeAPI } from "@/api/system/dept"
defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 业务线类型（传入就会筛选）
  businessLineType: {
    default: null,
  },
  // 是否只允许选择单个
  isOnlyOne: {
    type: Boolean,
    default: false,
  },
  // 是否隐藏部门选择框
  hiddenDeptCheckbox: {
    type: Boolean,
    default: false,
  },
  // 是否存在部门id 如果存在---需要过滤部门
  deptId: {
    default: null,
  },
  // 仅部门选择
  onlyDept: {
    type: Boolean,
    default: false,
  },
  // 所有数据
  allData: {
    type: Array,
    default: () => [],
  },
})

const treeProps = {
  value: "id",
  label: "name",
  children: "children",
  isLeaf: "isLeaf",
  class: customNodeClass,
}

const { proxy } = getCurrentInstance()

const treeRef = ref(null)
const filterText = ref(null)
const treeData = ref([]) // 树节点数据
const loading = ref(true)

const defaultCheckedKeys = ref([])
const selectList = ref([])

// 提取树组件公共配置
const treeCommonProps = {
  "node-key": "id",
  ref: treeRef,
  showCheckbox: true,
  props: treeProps,
  height: 350,
  "check-strictly": props.onlyDept,
  "item-size": 30,
  "empty-text": "暂无数据",
  onCheckChange: handleTreeCheckChange,
}

const emits = defineEmits(["update:modelValue", "change"])

// 赋值已选数据
const updateCheckedKeys = () => {
  nextTick(() => {
    selectList.value = [...props.modelValue].map((i) => {
      let id = i.id || i.userId || i.rangeId
      return {
        ...i,
        id: id?.startsWith("userId-") || props.onlyDept ? id : `userId-${id}`,
      }
    })
    defaultCheckedKeys.value = selectList.value.map((item) => item.id)
    treeRef.value.setCheckedKeys(defaultCheckedKeys.value)
  })
}

watch(
  () => props.businessLineType,
  (val) => {
    filterText.value = null
    getTreeFilter()
  }
)

watch(() => props.modelValue, updateCheckedKeys, { immediate: true })

// 新增：根据deptId过滤树节点，只返回目标节点及其子节点
function filterTreeByDeptId(tree, deptId) {
  for (const node of tree) {
    if (node.id == deptId) return [node]
    if (node.children) {
      const found = filterTreeByDeptId(node.children, deptId)
      if (found.length) return found
    }
  }
  return []
}

async function getTreeFilter() {
  loading.value = true
  try {
    // 1. 获取原始树数据
    const rawData = await fetchTreeData()
    // 2. 转换为树结构
    let tree = transformData(rawData)
    // 3. 按部门过滤（如有需要）
    if (props.deptId) {
      tree = filterTreeByDeptId(tree, props.deptId)
      expandFirstNode(tree)
    }
    // 4. 设置树数据
    setTreeData(tree)
  } finally {
    loading.value = false
  }
}

/**
 * 获取原始树数据
 */
async function fetchTreeData() {
  const action = props.onlyDept ? fetchDeptTreeAPI : useDeptStore().fetchZsDeptTreeInfo
  const params = { businessLineType: props.businessLineType }
  const res = await action(params)
  return props.onlyDept ? res.object : res
}

/**
 * 设置树数据到组件
 */
function setTreeData(data) {
  treeData.value = data
  if (treeRef.value) {
    treeRef.value.setData(data)
  }
}

/**
 * 展开第一个节点
 */
function expandFirstNode(tree) {
  if (tree.length && treeRef.value) {
    nextTick(() => {
      treeRef.value.setExpandedKeys([tree[0].id])
    })
  }
}

// 是否禁用父级可点击
function customNodeClass({ type }, node) {
  return type !== "user" && props.hiddenDeptCheckbox ? "is-dept" : ""
}

// const filterData = () => {
//   if (!filterText.value) return treeData.value // 如果过滤条件为空，返回完整数据

//   // 递归过滤树形数据
//   const filterNode = (node) => {
//     // 如果当前节点匹配，保留
//     if (matchKeyword(filterText.value, node)) return true

//     // 如果存在子节点，递归过滤子节点
//     if (node.children) {
//       node.children = node.children.filter(filterNode)
//       return node.children.length > 0 // 如果子节点有匹配项，保留当前节点
//     }

//     return false // 不匹配的节点移除
//   }

//   // 使用 cloneDeep 避免污染原始数据
//   return cloneDeep(treeData.value).filter(filterNode)
// }

const flatten = (arr) => {
  return arr.reduce((result, item) => {
    return result.concat(item, Array.isArray(item.children) ? flatten(item.children) : [])
  }, [])
}

// 节点点击
function handleTreeCheckChange(data, checked) {
  if (data.type !== "user" && props.hasDeptCheckbox && !props.onlyDept) return // 不是人员节点 && 隐藏部门复选框 && 不是仅部门模式
  if (
    props.isOnlyOne &&
    (selectList.value.length === 1 ||
      props.allData.some((type) => type.checkedItems.length))
  ) {
    proxy.$modal.msgWarning("当前只允许选择一个选项")
    treeRef.value.setChecked(data.id, false)
    return
  }
  const selectNodes = treeRef.value.getCheckedNodes(false)

  // 根据模式过滤选中的节点
  selectList.value = props.onlyDept
    ? selectNodes // 仅部门模式：过滤叶子节点
    : selectNodes.filter((i) => i.type === "user") // 非仅部门模式：过滤用户节点

  selectList.value = selectList.value.filter((i) => matchKeyword(filterText.value, i))

  emits("change", selectList.value, props.onlyDept ? "dept" : "user")
  updateModelValue()
}

// 转换users为children节点
function transformData(items) {
  return items.map((item) => {
    let personNodes = []
    if (!props.onlyDept) {
      // 如果不是部门模式，则需要转换users为children节点
      personNodes = (item.users || []).map((p) => ({
        ...p,
        id: `userId-${p.userId}`,
        zsGrade: item.zsGrade,
        zsUserType: item.zsUserType,
        name: p.name || p.nickName,
        parentId: item.deptId,
        isLeaf: true,
        type: "user",
        jobType: 0, // 用户标识
      }))
    }
    // 递归处理children并合并person节点
    return {
      ...item,
      type: "dept",
      jobType: 3, // 部门标识
      id: item.id || item.userId || item.deptId, // 统一格式
      joinId: item.id,
      rangeId: item.zsUserType ? item.parentId : item.id, // 如果是教师类型，使用parentId
      rangeName: item.joinNames || item.name,
      rangeType: 1, // 范围类型-1部门/标签需要
      name: item.name || item.nickName || item.deptName, // 统一格式
      isLeaf: item.hasOwnProperty("workNumber") || !item.children,
      children: [...(item.children ? transformData(item.children) : []), ...personNodes],
    }
  })
}

// 过滤树
function filterTreeMethod(value, data) {
  return matchKeyword(value, data)
}

// 重置查询条件
function resetQuery() {
  filterText.value = null
  treeRef.value?.setExpandedKeys([])
}

// 统一过滤逻辑
function matchKeyword(keyword, data) {
  const searchData = [data.name, data.phoneNumber, data.workNumber]
  if (!keyword) return true
  return searchData.some((item) => item?.includes(keyword.toLowerCase()))
}

async function onQueryChanged(query) {
  await nextTick()
  treeRef.value.filter(query)
  if (!query) {
    treeRef.value?.setExpandedKeys([])
  }
}

// 更新
function updateModelValue() {
  emits("update:modelValue", selectList.value)
}

getTreeFilter()

defineExpose({
  resetQuery,
})
</script>

<style scoped lang="scss">
.filter-input {
  margin-bottom: 10px;
  :deep(.el-input__wrapper) {
    padding-left: 80px;
  }
}

.dept-user-tree {
  :deep(.el-tree-node.is-dept) {
    .el-checkbox {
      display: none;
    }
  }
}
</style>
