# UserRolePicker 组件优化总结

## 优化内容

### 1. 代码逻辑优化

#### 1.1 单选模式逻辑改进
**原代码问题：**
- 单选模式下的逻辑不够清晰，没有考虑取消选中的情况
- 缺少对 `checked` 参数的有效利用

**优化后：**
```javascript
// 单选模式处理
if (props.isOnlyOne) {
  if (selectList.value.length >= 1 && checked) {
    // 如果已有选中项且当前是选中操作，先清空再选中当前项
    treeRef.value?.setCheckedKeys([])
    treeRef.value?.setChecked(data.id, true)
    selectList.value = [data]
  } else if (!checked) {
    // 取消选中
    selectList.value = []
  } else {
    // 首次选中
    selectList.value = [data]
  }
  return
}
```

#### 1.2 多选模式安全性改进
**原代码问题：**
- 没有对 `getCheckedNodes` 返回值进行空值检查

**优化后：**
```javascript
const selectNodes = treeRef.value?.getCheckedNodes(false) || []
```

### 2. 代码复用性优化

#### 2.1 统一ID获取逻辑
**原代码问题：**
- ID获取逻辑重复出现在多个地方

**优化后：**
```javascript
// 统一ID获取逻辑
function getUnifiedId(item) {
  return item.userOrRoleId || item.userId || item.roleId || item.id
}
```

#### 2.2 统一用户国籍过滤逻辑
**原代码问题：**
- 国籍过滤逻辑在多个 transform 函数中重复

**优化后：**
```javascript
// 统一的用户国籍过滤逻辑
function filterUsersByNationality(users) {
  return props.izChineseGj == null
    ? users
    : users.filter((u) => u.izChineseGj === props.izChineseGj)
}
```

### 3. 配置化优化

#### 3.1 数据类型映射配置化
**原代码问题：**
- 硬编码的数据映射对象，不易维护

**优化后：**
```javascript
// 数据映射配置
const DATA_TYPE_MAP = {
  0: () => deptData.value,
  1: () => rolesData.value,
  2: () => roleUserData.value,
  3: () => tagData.value,
}
```

#### 3.2 API请求映射配置化
**原代码问题：**
- API请求映射内联在函数中

**优化后：**
```javascript
// API请求映射配置
const API_ACTION_MAP = {
  user: getList,
  role: getRoleList,
  tagUser: getTagUserList,
  roleUser: getRoleUserList,
}
```

### 4. 错误处理优化

#### 4.1 移除不必要的 try-catch
**原代码问题：**
- `clearAllChecked` 函数使用空的 catch 块

**优化后：**
- 移除了不必要的 try-catch，因为操作本身是安全的

#### 4.2 改进错误处理
**原代码问题：**
- 初始化函数缺少错误处理

**优化后：**
```javascript
try {
  // 业务逻辑
  await Promise.all(promises)
} catch (error) {
  console.error('初始化数据失败:', error)
} finally {
  loading.value = false
}
```

### 5. 代码安全性优化

#### 5.1 数组索引检查
**原代码问题：**
- `removeItem` 函数没有检查索引是否有效

**优化后：**
```javascript
function removeItem(item) {
  const index = selectList.value.findIndex((i) => i.id === item.id)
  if (index > -1) {
    selectList.value.splice(index, 1)
    treeRef.value?.setChecked(item.id, false)
    updateModelValue()
  }
}
```

#### 5.2 移除未使用参数
**原代码问题：**
- `customNodeClass` 函数有未使用的 `node` 参数

**优化后：**
```javascript
function customNodeClass({ type }) {
  return !type && !props.showCheckedBox ? "hidden-checked" : ""
}
```

## 优化效果

1. **代码可读性提升**：逻辑更清晰，注释更明确
2. **代码复用性提升**：提取公共函数，减少重复代码
3. **维护性提升**：配置化处理，便于后续修改
4. **安全性提升**：增加边界检查，避免潜在错误
5. **性能优化**：减少不必要的操作和计算

## 建议

1. 考虑将一些通用的工具函数提取到独立的 utils 文件中
2. 可以考虑使用 TypeScript 来提供更好的类型安全
3. 建议添加单元测试来确保优化后的代码功能正确性
