<template>
  <div v-loading="loading">
    <el-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-width="160px"
      :inline="false"
      class="w-800px mt-10px"
      :disabled="!!currJob"
    >
      <template v-if="+templateInfo.flowFlag !== 0">
        <el-form-item label="标题:" prop="flowTitle">
          <el-input
            v-model="form.flowTitle"
            :maxlength="20"
            show-word-limit
            clearable
            placeholder="请输入标题"
          ></el-input>
        </el-form-item>
      </template>
      <el-form-item label="申请原由:" prop="applyReason">
        <el-input
          type="textarea"
          v-model="form.applyReason"
          :maxlength="1000"
          show-word-limit
          clearable
          placeholder="请输入申请原由"
          :rows="4"
        ></el-input>
      </el-form-item>
      <el-form-item label="文件:" prop="templateName">
        {{ form.templateName || templateInfo.templateName }}
        <el-form v-if="currJob" class="ml-10px">
          <el-button type="primary" plain @click="handlePreview"> 预览 </el-button>
        </el-form>
      </el-form-item>
      <el-form-item label="盖章后电子文件名称:" prop="flowName">
        <el-input
          v-model="form.flowName"
          clearable
          show-word-limit
          placeholder="请输入盖章后电子文件名称"
        ></el-input>
      </el-form-item>

      <el-form-item label="签署方:" prop="approveDtoList" v-if="!currJob">
        <el-button type="primary" @click="setSignatory">模版预览并设置签署方</el-button>
      </el-form-item>
      <template v-if="form.approveDtoList.length">
        <el-form-item label="签署顺序:" prop="order">
          <el-radio disabled v-model="order" label="无序" :value="1"></el-radio>
          <el-radio disabled v-model="order" label="有序" :value="0"></el-radio>
        </el-form-item>

        <el-form-item label="" prop="approveDtoList">
          <SignatoryTable :order="order" v-model="form.approveDtoList" />
        </el-form-item>
        <el-form-item
          label="签署截止时间:"
          prop="deadline"
          v-if="+templateInfo.flowFlag !== 0"
        >
          <span>审批通过后</span>
          <el-input-number v-model="form.deadline" :min="1" class="ml-5px" />
          <span class="ml-5px">天</span>
        </el-form-item>
        <el-form-item
          label="签署截止时间:"
          prop="deadlineTime"
          v-if="+templateInfo.flowFlag === 0"
        >
          <el-date-picker
            v-model="form.deadlineTime"
            placeholder="请选择签署截止时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            type="datetime"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="申请人信息:" prop="user" v-if="!currJob">
          <UserInfoDescCard :applyUser="form.loginUser" />
        </el-form-item>
      </template>
    </el-form>

    <!-- 模版预览并设置签署方 -->
    <SetSignatoryModal
      ref="setSignatoryRef"
      :templateInfo="templateInfo"
      @confirm="getSetSignatory"
      v-if="!currJob"
    />
  </div>
</template>

<script setup name="FillApplicationInfo">
import { rule } from "@/utils/validate"
import { generateUUID } from "@/utils/index"
import { getTmpSignOrderAPI } from "@/api/ess/template/template"
import { queryFormDataByOrderId } from "@/api/order/run-application"
const SetSignatoryModal = defineAsyncComponent(() => import("../SetSignatoryModal.vue"))
const SignatoryTable = defineAsyncComponent(() => import("../SignatoryTable.vue"))
const UserInfoDescCard = defineAsyncComponent(() => import("../UserInfoDescCard.vue"))
const props = defineProps({
  templateInfo: {
    type: Object,
    default: () => ({}),
  },
  draftForm: {
    type: Object,
    default: () => ({}),
  },
  currJob: {
    type: Object,
    default: null,
  },
})
const { proxy } = getCurrentInstance()
const { templateInfo } = toRefs(props)
const loading = ref(false)
const formRef = ref(null)
const form = ref({
  flowTitle: "",
  applyReason: "",
  flowName: "",
  deadline: null,
  approveDtoList: [],
  flowDisplayType: 1,
  resourceType: 1,
})

// 流程回显
async function initForm() {
  if (props.currJob?.orderId) {
    loading.value = true
    const res = await queryFormDataByOrderId(props.currJob.orderId)
    const formData = res.object ? res.object : {}
    Object.assign(form.value, formData)
    loading.value = false
    getTmpSignOrder()
  } else {
    form.value.flowName = templateInfo.value.templateName
    form.value.resourceId = templateInfo.value?.id
    templateInfo.value?.id && getTmpSignOrder()
  }
}

watch(
  () => props.draftForm,
  (val) => {
    if (!val.id) return
    const flowUserRefs = (val.flowUserRefs || []).map((item) => {
      return {
        ...item,
        approveName: item.userName,
      }
    })
    form.value = {
      ...val,
      deadline: val.signTmpLimitDay ? +val.signTmpLimitDay : null,
      deadlineTime: val.signLimitTime,
      approveDtoList: flowUserRefs,
    }
  },
  {
    immediate: true,
  }
)

const rules = ref({
  flowTitle: [
    { required: true, message: "请输入标题", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" },
  ],
  applyReason: [{ required: true, message: "请输入申请原由", trigger: "blur" }],
  flowName: [
    { required: true, message: "请输入文件名称", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" },
  ],
  deadline: [{ required: true, message: "请输入截止时间", trigger: "change" }],
  deadlineTime: [{ required: true, message: "请输入截止时间", trigger: "change" }],
  approveDtoList: [{ required: true, message: "请选择签署方", trigger: "change" }],
})
const order = ref(0)
// 获取签署顺序
async function getTmpSignOrder() {
  const templateId = props.templateInfo.id || form.value.templateId
  const res = await getTmpSignOrderAPI(templateId)
  order.value = res.object
}

// -------- 设置签署方 --------

const setSignatoryRef = ref(null)
// 设置签署方
function setSignatory() {
  setSignatoryRef.value.open(form.value.approveDtoList)
}

// 确认签署方
function getSetSignatory(data) {
  const arr = data.map((item, index) => {
    return {
      ...item,
      id: generateUUID(),
      signOrder: index + 1, // 签署顺序
    }
  })
  form.value.approveDtoList = arr
  formRef.value.clearValidate("approveDtoList")
}

function validForm() {
  return new Promise((resolve, reject) => {
    formRef.value.validate(async (valid) => {
      if (valid) {
        resolve(form.value)
      } else {
        proxy.$modal.msgWarning("请完善表单信息！")
        reject(valid)
      }
    })
  })
}

// 文件预览
function handlePreview() {
  window.open(`/templateIframe?essTemplateId=${form.value.essTemplateId}`)
}
// -------- 设置签署方 --------

onMounted(() => {
  initForm()
})

defineExpose({
  validForm,
})
</script>

<style scoped lang="scss"></style>
