<template>
  <div class="sign-flow-table">
    <EssFilesTable
      ref="essFilesTableRef"
      v-model:queryParams="queryParams"
      :tableData="tableData"
      :loading="loading"
      :total="total"
      :opt-width="80"
      @query="handleQuery"
    >
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '详情',
              click: () => handleViewDetails(row),
            },
          ]"
        />
      </template>
    </EssFilesTable>
  </div>
</template>

<script setup>
import { myCompletePage, otherCompletePage } from "@/api/ess/todo/sign"
import { useEssFilesTable } from "@/views/fileManagement/fileList/components/useEssFilesTable"
const { proxy } = getCurrentInstance()

const essFilesTableRef = ref(null)
const { resetQuery, handleViewDetails } = useEssFilesTable(essFilesTableRef)

const EssFilesTable = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileList/components/EssFilesTable.vue")
)

const props = defineProps({
  mine: {
    type: Number,
    default: 0, // 0 我发起的 1他人发起
  },
})

const tableData = ref([])
const loading = ref(true)

const total = ref(0)
const queryParams = ref({
  page: 1,
  limit: 10,
})

/** 查询列表 */
async function getList() {
  await nextTick()
  try {
    loading.value = true
    queryParams.mine = props.mine
    const actionUrl = props.mine === 0 ? myCompletePage : otherCompletePage
    const res = await actionUrl(queryParams.value)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery(data) {
  Object.assign(queryParams.value, data)
  getList()
}

getList()

defineExpose({
  resetQuery,
})
</script>

<style lang="scss" scoped>
.sign-flow-table {
  :deep(.app-container) {
    padding: 0;
  }
}
</style>
