import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
import path from "path"

export default function createSvgIcon(isBuild) {
	return createSvgIconsPlugin({
		iconDirs: [
			path.resolve(process.cwd(), "src/assets/icons/svg"),
			path.resolve(process.cwd(), "src/assets/icons/menu-svg"),
			path.resolve(process.cwd(), "src/assets/icons/color-svg"),
		],
		enforce: "pre",
		symbolId: "local-[dir]-[name]",
		svgoOptions: isBuild,
	})
}
