export default {
	flowClazz: {
		index: "#",
		importflowClazzTip: "导入节点事件设置",
		id: "主键ID",
		sort: "排序值",
		defFlowId: "流程名称",
		flowNodeId: "节点名称",
		clazz: "监听类",
		httpUrl: "Http请求地址",
		httpMethod: "Http请求类型",
		methods: "方法名称",
		valType: "条件模式",
		varKeyVal: "表单字段",
		operator: "运算符",
		varVal: "校验值",
		remark: "备注",
		createUser: "创建人",
		createTime: "创建时间",
		flowKey: "业务KEY",
		type: "事件类型",
		delFlag: "删除标识",

		inputIdTip: "请输入主键ID",
		inputSortTip: "请输入排序值",
		inputDefFlowIdTip: "请输入流程名称",
		inputFlowNodeIdTip: "请输入节点名称",
		inputClazzTip: "Bean名称：FlowInterceptor实现类",
		inputHttpUrlTip: "可输入全路径或相对路径",
		inputHttpMethodTip: "请选择Http请求类型",
		inputMethodsTip: "多个方法名称，英文逗号分隔，顺序从左到右",
		inputValTypeTip: "请输入条件模式",
		inputVarKeyValTip: "请输入表单字段",
		inputOperatorTip: "请输入条件运算符",
		inputVarValeTip: "请输入条件校验值",
		inputRemarkTip: "请输入备注",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputFlowKeyTip: "请输入业务KEY",
		inputTypeTip: "请输入事件类型",
		inputDelFlagTip: "请输入删除标识",

	},
}
