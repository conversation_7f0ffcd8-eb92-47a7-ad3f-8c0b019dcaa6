.child-ul-wrapper {
  padding-left: 1px !important;
}

.jf-wrap-paper {
  width: 100%;
  height: 100%;
  //position: relative;
  position: absolute;
  overflow: auto;
}

#jfDragWrapPaper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.jsonflow-navigator {
  position: absolute !important;
  bottom: 12px;
  right: 8px;
  border: #ccc solid 1px;
  overflow: hidden;

  .minimap-view {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 100%;
    height: 100%;
    border: 2px dotted #999;
    cursor: move;
  }
}

.jf-wrap-paper-active {
  background-color: #e4e4e4;
  cursor: crosshair;
}

.container-scale {
  position: absolute;
  top: 20px;

  > span {
    display: inline-block;
    width: 40px;
    text-align: center;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  /*缩放按钮*/
  .el-button--small.is-circle {
    padding: 5px 5px;
  }
}

/*缩放按钮*/
.btn-zoom {
  left: 20px;
}

/*状态提示*/
.status-tip {
  right: 20px;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-weight: 500;

  /*状态颜色块*/
  .flow-tip-btn {
    border: none;
    width: 26px;
    height: 17px;
    margin-right: 5px;
    margin-left: 25px;
  }

  /*已执行*/
  .executed {
    background-image: linear-gradient(to right, #00AA74, #64D6B1);
  }

  /*未执行*/
  .unexecuted {
    background-image: linear-gradient(to right, #2986FF, #7BB4FF);
  }

  /*执行中*/
  .executing {
    background-image: linear-gradient(to right, #FD6406, #FFA55C);
  }
}

.horizontal-line-x {
  position: absolute;
  height: 1px;
  background: var(--el-color-primary);
  display: none;
}

.vertical-line-y {
  position: absolute;
  width: 1px;
  background: var(--el-color-primary);
  display: none;
}
