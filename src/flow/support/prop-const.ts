/**
 * 字段属性常量
 * <AUTHOR>
 */
const varKeyVal = {
  sys: '#sys.',
  flow: '#flow.',
  order: '#order.',
  form: '#form.',
  user: '#user.',
  var: '#var.',
  dist: '#dist.'
}

export let PROP_CONST = {
  COMMON: {
    flowInstId: 'flowInstId',
    code: 'code',
    userUserPrefix: 'USER_',
    userRolePrefix: 'ROLE_',
    userPostPrefix: 'POST_',
    userDeptPrefix: 'DEPT_',
    tableName: 'order_run_application'
  },
  FORM_DESIGN: {
    // 注意相反的！
    subForm: 'group',
    group: 'subForm',
    fcRow: 'fcRow',
    tableForm: 'tableForm',
    fcTable: 'fcTable',
    elTabs: 'elTabs',
    elCollapse: 'elCollapse',
    elCard: 'elCard'
  },
  TEXT_DESC: {
    condSpELExplain:
      '当选择SpEL模式时, SpEL表达式必须符合SpEL格式, #anyKey表示表单的字段 ( 默认#form.前缀 ), 例如0<#days && #days>=3 ( 加前缀#var.anyKey表示从流程条件中取值，#user.anyKey表示从当前用户中取值 )',
    condUserExplain:
      '1、SpEL上下文表达式 ( 常用于发起时可确定的参与者值 ), #anyKey表示表单的字段 ( 默认#form.前缀 ), 例如#userId ( 加前缀#var.anyKey表示从流程条件中取值，#user.anyKey表示从当前用户中取值 )',
    condMethodExplain1:
      '采用函数表达式 ( 以下两种方式均支持自定义任意扩展 ), 返回值为字符串 1 ( 满足 ) 或 0 ( 不满足 ), 满足您复杂条件的场景 :',
    condMethodExplain2:
      '1、SpEL上下文表达式, #anyKey表示表单的字段 ( 默认#form.前缀 ), 例如#isGoEnd ( 加前缀#var.anyKey表示从流程条件中取值，#user.anyKey表示从当前用户中取值 )',
    condMethodExplain3:
      '2、SpringBean函数表达式, 如某个Bean对象的beanName为bean，取值#bean.anyMethod(), 带参数格式#bean.anyMethod(String#admin,SysUser#{"username": "admin"},SysRole#NULL)',
    condMethodExplain4:
      '备注：函数表达式 ( 参数支持SpEL上下文表达式且需带前缀如Long#form.userId ), 参数格式为#bean.anyMethod(参数类型#参数值), 多个参数逗号分割。类型为复杂对象时参数值为Json格式',
    condMethodExplain5:
      '字段可包含参与者类型、参与者ID、参与者任务名称、参与者任务排序( 只有ID值时，参与者类型默认人员，参与者ID是人员ID )',
    condMethodExplain6: '返回值要求请参考路由指定动作接口的入参'
  },
  HANDOVER_FLOW: {
    userKey: {
      create_user: 'create_user',
      receive_user: 'receive_user',
      curr_dept_manager: 'curr_dept_manager'
    }
  },
  SYS_FIELDS: [
    {
      prefix: varKeyVal.order,
      prop: 'flowInstId',
      label: '流程实例ID',
      valueKey: 'id',
      showKey: 'flowName'
    },
    { prefix: varKeyVal.order, prop: 'flowKey', label: '流程业务KEY' },
    {
      prefix: varKeyVal.order,
      prop: 'createUser',
      label: '发起人',
      valueKey: 'userId',
      showKey: 'name'
    },
    { prefix: varKeyVal.order, prop: 'code', label: '工单编号' },
    { prefix: varKeyVal.order, prop: 'createTime', label: '发起时间' },
    { prefix: varKeyVal.order, prop: 'finishTime', label: '完成时间' },
    { prefix: varKeyVal.order, prop: '_define_', label: '可自定义更多' }
  ],
  VAR_KEY_VAL: {
    route: '#route.',
    routeName: '路由规则',
    person: '#person.',
    personName: '审批规则',
    link: '#link.',
    // 取值来源
    order: varKeyVal.order,
    form: varKeyVal.form,
    user: varKeyVal.user,
    var: varKeyVal.var,
    dist: varKeyVal.dist
  },
  FLOW_METHOD: {
    whoseLeader: {
      realNameUser: '发起人本人',
      userId: varKeyVal.order.replace('#', '') + 'createUser'
    }
  }
}
