$main-text-color: #0C1433;
@use './variables' as theme;
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    //主色
    'primary': ('base': theme.$main-color),
    'success': (
      //成功色
      'base': #2e89ff
    ),
    'warning': (
      //警告色
      'base': #fb9547
    ),
    'danger': (
      //危险色
      'base': #f94e4f
    ),
    'error': (
      //错误色
      'base': #f94e4f
    )
  ),
  $text-color: (
    'regular': $main-text-color
  ),
  $fill-color: (
    'light': #f7f8f9
  ),
  $table: (
    'header-text-color': $main-text-color
  ),
  $checkbox: (
    'input-height': 16px,
    'input-width': 16px,
    'font-weight': 400,
  ),
  $radio: (
    'font-weight': 400,
    'text-color': $main-text-color
  ),
);

@use 'element-plus/theme-chalk/src/index.scss' as *; //全部导入打开

:root {
  --el-input-height: 35px;
  --main-theme: theme.$main-color;
}
