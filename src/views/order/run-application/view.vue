<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <form-render
        v-if="!validateNull(form.currFlowForm)"
        ref="formCreateRef"
        :curr-flow-form="form.currFlowForm"
        :render-type="'-1'"
        :init-form-perm-print="initFormPermPrint"
      />
    </div>
    <footer class="el-dialog__footer">
      <span class="dialog-footer">
        <el-button v-if="form.currFlowForm.printInfo" type="primary" @click="printForm">
          打印
        </el-button>
      </span>
    </footer>

    <!-- 打印表单 -->
    <el-dialog
      v-model="form.showTinymceView"
      top="20px"
      width="700px"
      :title="form.tinymceTitle"
      append-to-body
      @close="closePrint"
    >
      <tinymce-view v-if="form.showTinymceView" :curr-flow-form="form.currFlowForm" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="RunApplicationView">
import * as runApplication from "@/api/order/run-application";
import { initJobDataByApp } from "../index";
import { deepClone } from "@/utils/index";
import { handleFormPrint } from "@/flow/utils/form-perm";
const FormRender = defineAsyncComponent(
  () => import("@/flow/components/form-create/render.vue")
);
const formCreateRef = ref(null);
// 引入组件
const TinymceView = defineAsyncComponent(
  () => import("@/flow/components/tinymce/TinymceView.vue")
);

const props = defineProps({
  currFlowForm: {
    type: Object,
    default: {},
  },
});

const form = reactive({
  // 兼容app端监听printInfo
  currFlowForm: {
    type: Object,
    default: {},
  },
  showTinymceView: false,
  tinymceTitle: null,
});

const $route = useRoute();
function initJobData() {
  initJobDataByApp($route, handleGetObj, () => {
    form.currFlowForm = props.currFlowForm;
  });
}

function handleGetObj(id) {
  runApplication.getObj(id).then((resp) => {
    let formData = resp.object ? resp.object : {};
    Object.assign(form.currFlowForm, formData);
  });
}

async function initFormPermPrint() {
  await handleFormPrint(
    form.currFlowForm,
    form.currFlowForm.type,
    form.currFlowForm.formId,
    "1"
  );
}

function printForm() {
  form.currFlowForm.formData = formCreateRef.value.design.formData;
  form.currFlowForm.modelRefList = formCreateRef.value.design.fApi.children;
  form.currFlowForm.rule = formCreateRef.value.design.rule;
  form.tinymceTitle = form.currFlowForm.formName;
  form.showTinymceView = true;
}

function closePrint() {
  delete form.currFlowForm.modelRefList;
  delete form.currFlowForm.rule;
}

// 监听双向绑定
watch(
  () => props.currFlowForm.id,
  () => {
    initJobData();
  }
);

onMounted(() => {
  initJobData();
});
</script>

<style lang="scss" scoped>
.el-dialog__footer {
  text-align: center;
  margin-top: 10px;

  .dialog-footer {
    text-align: center;
  }
}
</style>
