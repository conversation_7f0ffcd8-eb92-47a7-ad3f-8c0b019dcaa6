import { CommonNodeType, HighNodeType, LaneNodeType } from "./type"
import {
	endAttr, gateAttr,
	jobAttr,
	laneAttr,
	linkAttr,
	nodeAttr, startAttr,
	virtualAttr,
} from "./attr-config"
import JsonflowDesign from "@jackrolling/jsonflow3"
import { deepClone } from "@/utils/index"

export function initNodeShapes() {
	window._defShapes = JsonflowDesign.shapes.initDefShapes()
	let initShapes = JsonflowDesign.shapes.initShapes(true)

	let cdata = window._defShapes.cdata

	initShapes.start.attr(cdata, {
		type: CommonNodeType.START,
		attrs: deepClone(startAttr),
		defJob: deepClone(jobAttr),
	})

	initShapes.serial.attr(cdata, {
		type: CommonNodeType.SERIAL,
		attrs: deepClone(nodeAttr),
		defJob: deepClone(jobAttr),
	})

	initShapes.parallel.attr(cdata, {
		type: CommonNodeType.PARALLEL,
		attrs: deepClone(nodeAttr),
		defJob: deepClone(jobAttr),
	})

	initShapes.serialGate.attr(cdata, {
		type: CommonNodeType.SERIAL,
		attrs: deepClone(gateAttr),
		defJob: deepClone(jobAttr),
	})

	initShapes.parallelGate.attr(cdata, {
		type: CommonNodeType.PARALLEL,
		attrs: deepClone(gateAttr),
		defJob: deepClone(jobAttr),
	})

	initShapes.end.attr(cdata, {
		type: CommonNodeType.END,
		attrs: deepClone(endAttr),
		defJob: deepClone(jobAttr),
	})

	const commonNodes = [
		initShapes.start,
		initShapes.serial,
		initShapes.parallel,
		initShapes.end,
	]

	initShapes.virtual.attr(cdata, {
		type: HighNodeType.VIRTUAL,
		attrs: deepClone(virtualAttr),
		defJob: deepClone(jobAttr),
	})

	initShapes.job.attr(cdata, {
		type: HighNodeType.JOB,
		defJob: deepClone(jobAttr),
	})

	const highNodes = [
		initShapes.serialGate,
		initShapes.parallelGate,
		initShapes.virtual,
		initShapes.job,
		/* ,
    { cdata: {
        type: HighNodeType.CHILD_FLOW,
        attrs: deepClone(highAttr)
      }
    } */
	]

	initShapes.xLane.attr(cdata, {
		type: LaneNodeType.X_LANE,
		attrs: deepClone(laneAttr),
	})

	initShapes.yLane.attr(cdata, {
		type: LaneNodeType.Y_LANE,
		attrs: deepClone(laneAttr),
	})

	const laneNodes = [
		initShapes.xLane,
		initShapes.yLane,
	]

	const defLink = initShapes.link.attr(cdata, {
		type: CommonNodeType.LINK,
		attrs: deepClone(linkAttr),
	})

	return { commonNodes, highNodes, laneNodes, defLink }
}

window._nodeConfig = { initNodeShapes }
