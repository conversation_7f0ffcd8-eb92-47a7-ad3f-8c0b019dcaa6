{"name": "electronic-seal-web", "version": "1.0.0", "description": "电子印章平台", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "dev:devtools": "cross-env VITE_ENABLE_DEVTOOLS=true vite", "build:prod": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:docker": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --outDir ./docker/electronic-seal-web/", "build:stage": "vite build --mode staging", "build:zs": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --mode zs", "build:prod-cas": "vite build --mode production-cas", "lint": "eslint --cache .", "lint:fix": "eslint --cache --fix .", "preview": "vite preview"}, "dependencies": {"@axolo/json-editor-vue": "^0.3.2", "@element-plus/icons-vue": "2.3.1", "@form-create/element-ui": "3.2.20", "@jackrolling/jsonflow3": "2.2.9", "@microsoft/fetch-event-source": "^2.0.1", "@tinymce/tinymce-vue": "^4.0.5", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "13.3.0", "axios": "1.9.0", "codemirror": "5.65.5", "crypto-js": "^3.1.9-1", "disable-devtool": "^0.3.8", "echarts": "5.6.0", "element-plus": "2.10.2", "file-saver": "2.0.5", "form-create-designer": "3.2.11-oem", "fuse.js": "6.6.2", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "json-editor-vue3": "^1.1.1", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "nprogress": "0.2.0", "pinia": "3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "print-js": "^1.6.0", "qs": "^6.13.1", "quill-blot-formatter": "^1.0.5", "quill-image-drop-module": "^1.0.3", "sass-loader": "^16.0.3", "sm-crypto": "^0.3.12", "sortablejs": "^1.15.6", "splitpanes": "4.0.4", "tinymce": "^5.10.2", "unplugin-element-plus": "^0.8.0", "v-calendar": "3.1.2", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-qiankun": "^1.0.15", "vue": "3.5.16", "vue-clipboard3": "^2.0.0", "vue-cropper": "1.1.1", "vue-draggable-plus": "^0.6.0", "vue-i18n": "9.2.2", "vue-json-viewer": "^3.0.4", "vue-router": "4.5.1", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-infinite-list": "^0.2.5", "vue3-tree-org": "^4.2.2", "vuedraggable": "^4.1.0", "vxe-pc-ui": "4.6.38", "vxe-table": "4.13.50"}, "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@unocss/eslint-plugin": "^66.1.2", "@vitejs/plugin-vue": "5.2.4", "cross-env": "^7.0.3", "eslint": "^9.23.0", "lint-staged": "^15.5.0", "rollup-plugin-visualizer": "^5.14.0", "sass-embedded": "1.89.1", "simple-git-hooks": "^2.12.1", "terser": "^5.37.0", "typescript": "5.7.3", "unocss": "66.1.0-beta.6", "unplugin-auto-import": "0.18.6", "unplugin-vue-components": "^0.27.5", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "6.3.5", "vite-plugin-compression": "0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "^7.6.7"}, "overrides": {"quill": "2.0.2"}, "simple-git-hooks": {"pre-commit": "npx prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "lint-staged": {"*": "eslint --fix"}}