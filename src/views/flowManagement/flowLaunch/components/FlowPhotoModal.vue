<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="1000px"
    :before-close="handleClose"
    top="5vh"
  >
    <flow-photo v-if="dialogVisible" :curr-job="currFlowForm" />
  </el-dialog>
</template>

<script setup>
const FlowPhoto = defineAsyncComponent(() =>
  import("@/views/jsonflow/flow-design/view.vue")
)

const props = defineProps({
  title: {
    type: String,
    default: "查看流程图",
  },
})

const dialogVisible = ref(false)
const currFlowForm = ref({})
const open = ({ defFlowId }) => {
  currFlowForm.value = { defFlowId: defFlowId }
  dialogVisible.value = true
}

const handleClose = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
})
</script>
<style scoped lang="scss"></style>
