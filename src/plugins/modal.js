import { ElMessage, ElMessageBox, ElNotification, ElLoading } from "element-plus"

let loadingInstance

export default {
  // 消息提示
  msg (content) {
    ElMessage({
      type: "info",
      message: content,
      showClose: true,
      plain: true,
    })
  },
  // 错误消息
  msgError (content) {
    ElMessage({
      type: "error",
      message: content,
      showClose: true,
      plain: true,
    })
  },
  // 成功消息
  msgSuccess (content) {
    ElMessage({
      type: "success",
      message: content,
      showClose: true,
      plain: true,
    })
  },
  // 警告消息
  msgWarning (content) {
    ElMessage({
      type: "warning",
      message: content,
      showClose: true,
      plain: true,
    })
  },
  // 弹出提示---居中---自动关闭
  alertCenter (content) {
    ElMessageBox.alert('', content, {
      type: 'success',
      showClose: false,
      showConfirmButton: false,
      customClass: "el-alert-box-custom",
      center: true,
    })
    setTimeout(() => {
      ElMessageBox.close()
    }, 1000)
  },
  // 弹出提示
  alert (content) {
    ElMessageBox.alert(content, "系统提示")
  },
  // 错误提示
  alertError (content) {
    ElMessageBox.alert(content, "系统提示", { type: "error" })
  },
  // 成功提示
  alertSuccess (content) {
    ElMessageBox.alert(content, "系统提示", { type: "success" })
  },
  // 警告提示
  alertWarning (content) {
    ElMessageBox.alert(content, "", { type: "warning", dangerouslyUseHTMLString: true, customClass: "el-message-box-danger" })
  },
  // 通知提示
  notify (content) {
    ElNotification.info(content)
  },
  // 错误通知
  notifyError (content) {
    ElNotification.error(content)
  },
  // 成功通知
  notifySuccess (content) {
    ElNotification.success(content)
  },
  // 警告通知
  notifyWarning (content) {
    ElNotification.warning(content)
  },
  // 确认窗体-删除等类型用 图标为红色的
  confirm (title, content, type = "warning", showCancelButton = true, confirmButtonText = "确定", cancelButtonText = "取消") {
    // return ElMessageBox.confirm(content, '系统提示', {
    //   confirmButtonText: '确定',
    //   cancelButtonText: '取消',
    //   type: 'warning'
    // })
    let titleHtml = title ? `<strong>${title}</strong><br>` : ""
    let contentHtml = content ? `<div class='confirm-content'>${content}</div>` : ""
    return ElMessageBox.confirm(`${titleHtml}${contentHtml}`, "", {
      confirmButtonText,
      cancelButtonText,
      showCancelButton,
      dangerouslyUseHTMLString: true,
      distinguishCancelAndClose: true,
      closeOnClickModal: false,
      type,
      customClass: "el-message-box-danger",
    })
  },
  // 提交内容
  prompt (content) {
    return ElMessageBox.prompt(content, "系统提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
  },
  // 打开遮罩层
  loading (content) {
    loadingInstance = ElLoading.service({
      lock: true,
      text: content,
      background: "rgba(0, 0, 0, 0.7)",
    })
  },
  // 关闭遮罩层
  closeLoading () {
    loadingInstance.close()
  },
}
