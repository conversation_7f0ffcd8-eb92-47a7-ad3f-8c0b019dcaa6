<template>
  <div class="iframe-container" v-loading="loading">
    <div class="box-title-line-b" v-if="title">
      <span>{{ title }}</span>
    </div>
    <TemplateIframeView
      height="95%"
      ref="iframeView"
      :essTemplateId="templateInfo.essTemplateId"
    />
  </div>
</template>

<script setup lang="ts" name="TemplateIframe">
import TemplateIframeView from "@/views/template/templateManagement/templateIframe.vue"

interface TemplateInfo {
  essTemplateId: string | number
  [key: string]: any
}

const props = defineProps<{
  templateInfo: TemplateInfo
  title?: string
}>()

const iframeView = ref(null)
const loading = ref<boolean>(false)

// 获取模版预览地址
const getTemplatePreviewUrl = async () => {
  iframeView.value?.getTemplatePreviewUrl()
}

getTemplatePreviewUrl()

defineExpose({
  getTemplatePreviewUrl,
})
</script>

<style scoped lang="scss"></style>
