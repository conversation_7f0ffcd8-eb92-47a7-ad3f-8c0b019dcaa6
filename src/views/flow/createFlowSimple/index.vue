<template>
  <div class="create-flow-wrapper">
    <FlowTempHisList
      class="mr-20px"
      :historyAPI="historyAPI"
      :hisExtData="hisExtData"
      @change="getNodesByDefFlowId"
    />
    <div class="flow-simple-design" v-loading="loading">
      <FlowSimpleDesign ref="flowSimpleDesignRef" v-model:nodes="flowNodes" />
    </div>
  </div>
</template>

<script setup>
import {
  getSimpleFlowNodesByConfigKey,
  getSimpleFlowNodes,
} from "@/api/jsonflow/def-flow"

const FlowSimpleDesign = defineAsyncComponent(() =>
  import("./components/FlowSimpleDesign.vue")
)
const FlowTempHisList = defineAsyncComponent(() =>
  import("./components/FlowTempHisList.vue")
)

defineOptions({
  inheritAttrs: false,
})

const props = defineProps({
  // 流程配置
  configKey: {
    type: String,
    default: "",
  },
  // 流程ID
  defFlowId: {
    type: String,
    default: "",
  },
  // 历史流程参数
  hisExtData: {
    type: Object,
    default: () => ({}),
  },
  // 历史流程接口
  historyAPI: {
    type: Function,
  },
})

const loading = ref(true)

const flowNodes = ref([]) // 流程节点

// 初始化流程
function initFlow() {
  if (props.defFlowId) {
    getNodesByDefFlowId(props.defFlowId)
  } else {
    getFlowConfig()
  }
}

async function getFlowConfig() {
  try {
    loading.value = true
    const res = await getSimpleFlowNodesByConfigKey(props.configKey)
    flowNodes.value = res.object
  } finally {
    loading.value = false
  }
}

/** 根据defFlowId获取流程节点
 * @param {string} defFlowId 流程ID
 */
async function getNodesByDefFlowId(defFlowId) {
  try {
    loading.value = true
    const res = await getSimpleFlowNodes(defFlowId)
    flowNodes.value = res.object
  } finally {
    loading.value = false
  }
}

// 获取节点数据
function getNodes() {
  return flowNodes.value
}

defineExpose({
  initFlow,
  getNodes,
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.create-flow-wrapper {
  display: flex;
  width: 100%;
  height: calc(#{$base-main-page-height} - 85px);
  .flow-simple-design {
    background-color: #fff;
    flex: auto;
    border-radius: 10px;
  }
}
</style>
