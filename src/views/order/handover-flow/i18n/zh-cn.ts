export default {
	handoverFlow: {
		index: "#",
		importhandoverFlowTip: "导入交接流程",
		id: "ID",
		code: "工单编号",
		flowKey: "流程名称",
		type: "交接类型",
		status: "状态",
		createUser: "创建人",
		createTime: "创建时间",
		handoverReason: "交接原因",
		handoverUser: "交接人ID",
		handoverUserDept: "交接人部门",
		receiveDept: "接收部门",
		receiveUser: "接收人",
		flowInstId: "流程实例ID",
		finishTime: "完成时间",
		updateUser: "修改人",
		updateTime: "修改时间",
		delFlag: "删除标识",

		inputIdTip: "请输入ID",
		inputCodeTip: "请输入编号",
		inputFlowKeyTip: "请输入业务类型",
		inputTypeTip: "请输入交接类型",
		inputStatusTip: "请输入状态",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputHandoverReasonTip: "请输入交接原因",
		inputHandoverUserTip: "请输入交接人ID",
		inputHandoverUserDeptTip: "请输入交接人部门",
		inputReceiveDeptTip: "请输入接收部门",
		inputReceiveUserTip: "请输入接收人",
		inputFlowInstIdTip: "请输入流程实例ID",
		inputFinishTimeTip: "请输入完成时间",
		inputUpdateUserTip: "请输入修改人",
		inputUpdateTimeTip: "请输入修改时间",
		inputDelFlagTip: "请输入删除标识",

	},
}
