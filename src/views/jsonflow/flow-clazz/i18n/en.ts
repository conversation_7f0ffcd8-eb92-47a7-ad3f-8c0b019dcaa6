export default {
	flowClazz: {
		index: "#",
		importflowClazzTip: "import FlowClazz",
		id: "id",
		sort: "sort",
		defFlowId: "defFlowId",
		flowNodeId: "flowNodeId",
		clazz: "clazz",
		httpUrl: "httpUrl",
		httpMethod: "httpMethod",
		methods: "methods",
		valType: "valType",
		varKeyVal: "varKeyVal",
		operator: "operator",
		varVal: "varVal",
		remark: "remark",
		createUser: "createUser",
		createTime: "createTime",
		flowKey: "flowKey",
		type: "type",
		delFlag: "delFlag",

		inputIdTip: "input id",
		inputSortTip: "input sort",
		inputDefFlowIdTip: "input defFlowId",
		inputFlowNodeIdTip: "input flowNodeId",
		inputClazzTip: "input clazz",
		inputHttpUrlTip: "input httpUrl",
		inputHttpMethodTip: "input httpMethod",
		inputMethodsTip: "input methods",
		inputValTypeTip: "input valType",
		inputVarKeyValTip: "input varKeyVal",
		inputOperatorTip: "input operator",
		inputVarValTip: "input varVal",
		inputRemarkTip: "input remark",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputFlowKeyTip: "input flowKey",
		inputTypeTip: "input type",
		inputDelFlagTip: "input delFlag",

	},
}
