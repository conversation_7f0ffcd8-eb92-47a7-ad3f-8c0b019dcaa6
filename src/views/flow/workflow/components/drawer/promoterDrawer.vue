<template>
	<el-drawer
		v-model="visible"
		:append-to-body="true"
		:title="$t('flow.initiator')"
		class="set_promoter"
		:show-close="false"
		:size="550"
		:before-close="savePromoter"
		@open="openEvent"
	>
		<div class="demo-drawer__content">
			<el-tabs type="border-card">
				<el-tab-pane label="设置发起人">
					<select-show v-model:org-list="starterConfig.nodeUserList" type="org" :multiple="true" />
				</el-tab-pane>
				<el-tab-pane label="表单权限">
					<form-perm :form-perm="starterConfig.formPerms" />
				</el-tab-pane>
			</el-tabs>
		</div>
	</el-drawer>
</template>
<script setup>
import selectShow from "@/components/OrgSelector/index.vue"
import { useFlowStore } from "../../stores/flow"
import { useStore } from "../../stores/index"
import FormPerm from "./components/formPerm.vue"

let store = useStore()

let starterConfig = ref({})
let flowStore = useFlowStore()

let starterConfigData = computed(() => store.starterConfigData)
watch(starterConfigData, val => {
	starterConfig.value = val.value
})
const step2FormList = computed(() => {
	return flowStore.step2
})

const openEvent = () => {
	let value = step2FormList.value
	var arr = {}
	let formPerms = starterConfig.value.formPerms
	for (var item of value) {
		arr[item.field] = "E"

		if (formPerms[item.field]) {
			arr[item.field] = formPerms[item.field]
		}
		if (item.type === "Layout") {
			let value1 = item.props.value
			for (var it of value1) {
				arr[it.field] = "E"
				if (formPerms[it.field]) {
					arr[it.field] = formPerms[it.field]
				}
			}
		}
	}
	starterConfig.value.formPerms = arr
}

let { setPromoter, setStarterConfig } = store
let promoterDrawer = computed(() => store.promoterDrawer)
let visible = computed({
	get() {
		return promoterDrawer.value
	},
	set() {
		closeDrawer()
	},
})

const savePromoter = () => {
	setStarterConfig({
		value: starterConfig.value,
		flag: true,
		id: starterConfigData.value.id,
	})
	closeDrawer()
}
const closeDrawer = () => {
	setPromoter(false)
}
</script>
