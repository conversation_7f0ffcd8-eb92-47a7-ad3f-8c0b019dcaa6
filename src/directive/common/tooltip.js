/*
 * @Author: ljn
 * @Date: 2024-11-29 11:45:27
 * @LastEditors: ljn
 * @LastEditTime: 2025-06-26 16:58:38
 * @Description: 文本超出显示 v-tooltip
 */

let tooltipDom

const ellipsisDirective = {
	mounted(el, bindings) {
		bindEvent(el, bindings)
	},
	updated(el, bindings) {
		bindEvent(el, bindings)
	},
	unmounted() {
		removeTooltip()
	},
}

function bindEvent(el, bindings) {
	// 先移除上一次绑定的事件
	el.removeEventListener("mouseenter", handleMouseEnter)
	el.removeEventListener("mouseleave", removeTooltip)

	if (bindings.value === false) {
		return
	}
	
	// 判断是否使用中间省略
	const isMiddle = bindings.modifiers.middle

	// 给当前元素设置超出隐藏
	el.style.overflow = "hidden"
	el.style.textOverflow = "ellipsis"
	
	if (isMiddle) {
		// 中间省略的样式
		el.style.whiteSpace = "nowrap"
		
		// 保存原文本和当前样式
		const text = el.textContent
		const originStyles = el.getAttribute('style') || ''
		
		// 创建包装元素
		const wrapper = document.createElement('div')
		wrapper.style.cssText = `
			position: relative;
			width: 100%;
			height: 100%;
			overflow: hidden;
		`
		
		// 创建显示元素
		const displayEl = document.createElement('span')
		displayEl.style.cssText = `
			display: block;
			position: relative;
			background: inherit;
			text-align: justify;
			height: 100%;
			overflow: hidden;
			top: -100%;
		`
		displayEl.setAttribute('title', text)
		
		// 设置::before伪元素的样式（需要通过添加样式表实现）
		const styleEl = document.createElement('style')
		const styleId = `middle-ellipsis-${Date.now()}`
		displayEl.classList.add(styleId)
		
		styleEl.textContent = `.${styleId}::before {
			content: attr(title);
			width: 50%;
			float: right;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			direction: rtl;
		}`
		document.head.appendChild(styleEl)
		
		// 设置文本内容
		displayEl.textContent = text
		
		// 清空原元素内容，添加新的结构
		el.textContent = ''
		
		// 插入元素
		wrapper.appendChild(displayEl)
		el.appendChild(wrapper)
		
		// 恢复原始样式
		if (originStyles) {
			el.setAttribute('style', originStyles)
			el.style.overflow = "hidden"
		}
	} else {
		// 普通的末尾省略
		el.style.whiteSpace = "nowrap"
	}

	// 如果超出，绑定鼠标移入移出事件
	if (el.scrollWidth > el.offsetWidth) {
		el.addEventListener("mouseenter", handleMouseEnter)
		// 鼠标移出 将提示信息移除
		el.addEventListener("mouseleave", removeTooltip)
	}
}

/** 鼠标移入事件 */
function handleMouseEnter(e) {
	if (!tooltipDom) {
		// 创建浮层元素
		tooltipDom = document.createElement("div")
		// 将浮层插入到body中
		document.body.appendChild(tooltipDom)
	}
	const maxWidth = 600
	let cssText = `
          max-width: ${maxWidth}px;
          overflow: auto;
          position: fixed;
          background: #262627;
          color: #fff;
          border-radius: 4px;
          line-height: 20px;
          padding: 10px;
          display: block;
          font-size: 12px;
          z-index: 99999;
          word-break: break-all;
        `
	// 根据鼠标移入位置判断浮层位于左侧还是右侧，避免遮挡
	if (window.innerWidth - e.clientX < maxWidth) {
		cssText += `right:${window.innerWidth - e.clientX}px;`
	}
	else {
		cssText += `left:${e.clientX + 20}px;`
	}
	// 根据鼠标移入位置判断浮层位于上方还是下方，避免遮挡
	if (window.innerHeight - e.clientY < 600) {
		cssText += `bottom:${window.innerHeight - e.clientY}px;`
	}
	else {
		cssText += `top:${e.clientY}px;`
	}

	tooltipDom.style.cssText = cssText
	// 浮层中的文字
	tooltipDom.innerHTML = e.currentTarget.innerText || e.currentTarget.textContent
}

function removeTooltip() {
	// 隐藏浮层
	if (tooltipDom) {
		tooltipDom.style.display = "none"
	}
}

export default ellipsisDirective
