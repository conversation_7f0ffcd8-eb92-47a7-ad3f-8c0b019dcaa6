<template>
  <div class="file-preview-container" v-loading="loading">
    <FilePreview :previewUrl="previewUrl" />
  </div>
</template>

<script setup>
const FilePreview = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileLunch/components/FilePreview.vue")
)
import { getDraftPreviewUrlAPI } from "@/api/ess/fileManagement/file"
const route = useRoute()
const flowId = route.query.flowId
const loading = ref(true)
const previewUrl = ref("")
const getData = async () => {
  try {
    const res = await getDraftPreviewUrlAPI(flowId)
    previewUrl.value = res.object
  } finally {
    loading.value = false
  }
}
getData()
</script>

<style scoped lang="scss"></style>
