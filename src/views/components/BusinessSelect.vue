<!--
 * @Author: ljn
 * @Date: 2025-02-12 10:33:41
 * @LastEditors: ljn
 * @LastEditTime: 2025-05-23 16:15:52
 * @Description: 业务线下拉选择器
-->
<template>
  <template v-if="!showText">
    <el-select
      v-if="type == 'select'"
      v-model="model"
      clearable
      :placeholder="placeholder"
      popper-class="custom-header"
      :loading="loading"
      v-bind="$attrs"
    >
      <template #header>
        <div class="business-select-header">
          <div
            v-for="item in business_ascription"
            :key="item.value"
            :class="{ active: item.value == ascription }"
            @click="ascription = item.value"
          >
            <span class="title">{{ item.label }}</span>
          </div>
        </div>
      </template>
      <el-option
        v-for="item in filterList"
        :key="item.id"
        :label="item.businessLineName"
        :value="item.id"
      />
    </el-select>
    <div v-if="type !== 'select'" v-loading="loading" class="business-line-box">
      <div v-for="item in ascriptionOptions" class="business-line-box__item">
        <div class="title">{{ item.label }}：</div>
        <el-radio-group
          v-if="type === 'radio'"
          :model-value="model"
          v-bind="$attrs"
          @input="changeRadio"
        >
          <el-radio v-for="i in getBusinessList(item.value)" :key="i.id" :value="i.id">
            {{ i.businessLineName }}
          </el-radio>
        </el-radio-group>
        <el-checkbox-group v-if="type == 'checkbox'" v-model="model" v-bind="$attrs">
          <el-checkbox v-for="i in getBusinessList(item.value)" :key="i.id" :value="i.id">
            {{ i.businessLineName }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
  </template>

  <!-- 回显文字 -->
  <div v-if="showText">
    {{ businessText }}
  </div>
</template>

<script setup>
import { getBusinessLineList } from "@/api/ess/baseSet/businessLine"
const { proxy } = getCurrentInstance()
const { business_ascription } = proxy.useDict("business_ascription")
const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
  },
  placeholder: {
    type: String,
    default: "请选择",
  },
  type: {
    type: String,
    default: "select", // select | radio | checkbox
  },
  ascriptionType: {
    // 归属 如果传入归属则数据进行筛选 // all | single
    default: "all",
  },
  // 只展示文字
  showText: {
    type: Boolean,
    default: false,
  },
  beforeRadioFunc: {
    type: Function,
  },
})
const model = defineModel("modelValue")
const ascription = defineModel("ascription", { type: [String, Number], default: "0" })
const businessLineList = ref([])
const loading = ref(false)

// 切换业务线的时候，把校级清空
watch(
  () => props.ascription,
  (val) => {
    if (props.type === "checkbox" && props.ascriptionType === "single") {
      let selectArr = getBusinessList(ascription.value)
      model.value = selectArr
        .filter((item) => model.value.includes(item.id))
        .map((item) => item.id)
    }
  }
)

// 获取业务线
async function getAll() {
  try {
    loading.value = true
    let res = await getBusinessLineList()
    businessLineList.value = res.object
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

// 获取类型
const getAscription = () => {
  if (props.ascriptionType === "single" && props.modelValue && !ascription.value) {
    return businessLineList.value.find((item) => item.id === props.modelValue).ascription
  }
  return ""
}

const filterList = computed(() => {
  return businessLineList.value.filter((item) => item.ascription == +ascription.value)
})

// 只展示业务线名字
const businessText = computed(() => {
  try {
    if (props.modelValue) {
      let businessLineObj = businessLineList.value.find(
        (item) => item.id == props.modelValue
      )
      const ascriptionText = proxy.selectDictLabel(
        business_ascription.value,
        businessLineObj.ascription
      )
      return `${ascriptionText}-${businessLineObj.businessLineName}`
    }
  } catch (error) {
    return ""
  }
})

// 归属数据源
const ascriptionOptions = computed(() => {
  return business_ascription.value.filter((item) =>
    props.ascriptionType !== "all" ? +item.value === +ascription.value : true
  )
})

// 获取对应业务线
const getBusinessList = (type) => {
  return businessLineList.value.filter((item) => +item.ascription === +type)
}

const $emit = defineEmits(["change"])
// 改变业务线
const changeRadio = (e) => {
  let val = e.target._value
  let obj = businessLineList.value.find((item) => item.id === val)
  if (props.beforeRadioFunc) {
    props.beforeRadioFunc(obj)
  } else {
    ascription.value = obj.ascription
    model.value = val
  }
}

defineExpose({
  businessLineList,
  business_ascription,
  getAscription,
})

getAll()
</script>

<style scoped lang="scss">
$lineColor: var(--el-color-primary-light-6);
.business-select-header {
  display: flex;
  font-size: 14px;
  div {
    cursor: pointer;
    margin-right: 20px;
    &.active {
      @apply title-line-b  before:(bg-[var(--el-color-primary-light-6)]);
      span {
        @apply z-10 relative;
      }
    }
  }
}

.business-line-box {
  width: 100%;
  .business-line-box__item {
    background: #f9fafa;
    border-radius: 8px;
    padding: 10px 10px 10px 15px;
    .title {
      font-weight: 600;
      height: 28px;
    }
  }
  .business-line-box__item + .business-line-box__item {
    margin-top: 15px;
  }
}
</style>
