<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      class="white-form-box"
      label-width="80px"
    >
      <el-form-item label="模块类型" prop="moduleType">
        <dict-select v-model="queryParams.moduleType" :options="home_module_type" />
      </el-form-item>
      <el-form-item label="名称" prop="appName">
        <el-input
          v-model="queryParams.appName"
          placeholder="请输入名称"
          clearable
          maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <dict-select v-model="queryParams.status" :options="normal_disable" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <CustomTable
      ref="customTableRef"
      v-model:show-search="showSearch"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :data="tableData"
      custom
      :loading="loading"
      :total="total"
      @reload="getList"
      :opt-width="150"
    >
      <template #actions>
        <el-button
          v-auths="['baseSet:homePage:add']"
          type="primary"
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
        <el-button
          v-auths="['baseSet:homePage:remove']"
          type="danger"
          icon="Delete"
          :disabled="!selections.length"
          @click="handleDelete"
        >
          删除
        </el-button>
      </template>
      <vxe-column type="checkbox" width="40" fixed="left" />
      <vxe-column type="seq" width="70" />
      <vxe-column field="moduleType" title="模块类型" min-width="110">
        <template #default="{ row }">
          {{ selectDictLabel(home_module_type, row.moduleType) }}
        </template>
      </vxe-column>
      <vxe-column field="appName" title="名称" min-width="110" show-overflow />
      <vxe-column field="iconPath" title="图标" min-width="90">
        <template #default="{ row }">
          <el-image
            style="width: 50px; height: 50px"
            v-if="row.iconPath && row.iconPath.includes('.')"
            fit="cover"
            :preview-src-list="[imageApi + row.iconPath]"
            :src="imageApi + row.iconPath"
            preview-teleported
          />
          <ImageSelector v-model="row.iconPath" disabled v-else />
        </template>
      </vxe-column>
      <vxe-column field="appType" title="展示端" min-width="90">
        <template #default="{ row }">
          {{ selectDictLabels(home_app_type, row.appType, ",") }}
        </template>
      </vxe-column>
      <vxe-column field="appAttr" title="属性" min-width="90">
        <template #default="{ row }">
          {{ selectDictLabel(home_app_attr, row.appAttr) }}
        </template>
      </vxe-column>
      <vxe-column field="scope" title="可见范围" min-width="110">
        <template #default="{ row }">
          {{ selectDictLabels(home_app_scope, row.scope, ",") }}
        </template>
      </vxe-column>
      <vxe-column field="createBy" title="创建人" min-width="110" />
      <vxe-column field="createTime" title="创建时间" min-width="170">
        <template #default="{ row }">
          {{ parseTime(row.createTime) }}
        </template>
      </vxe-column>
      <vxe-column field="status" title="状态" min-width="120">
        <template #default="{ row }">
          <div class="flex-y-center">
            <el-switch
              v-model="row.status"
              :active-value="0"
              :inactive-value="1"
              @change="handleStatusChange(row)"
            />
            <span class="u-m-l-5" :class="{ 'text-muted': row.status == 1 }">
              已{{ selectDictLabel(normal_disable, row.status) }}
            </span>
          </div>
        </template>
      </vxe-column>
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '修改',
              click: () => handleUpdate(row),
              permission: ['baseSet:homePage:edit'],
            },
            {
              text: '查看',
              click: () => viewDetails(row),
            },
            {
              text: '删除',
              type: 'danger',
              click: () => handleDelete(row),
              permission: ['baseSet:homePage:remove'],
            },
          ]"
        />
      </template>
    </CustomTable>

    <!-- 新增修改弹窗 -->
    <EditHomeSetModal
      ref="homeSetRef"
      :dict-options="{
        home_module_type,
        home_app_attr,
        home_app_type,
        home_app_scope,
        normal_disable,
        home_app_top,
      }"
      @refresh="handleQuery"
      @change="getList"
    />
  </div>
</template>

<script setup name="HomePage">
import { imageApi } from "@/config/constant"
import {
  indexCfgInfoPage,
  delIndexCfgInfo,
  updateIndexCfgInfoAPI,
} from "@/api/ess/baseSet/indexCfgInfo"
import EditHomeSetModal from "./components/EditHomeSetModal.vue"
import ImageSelector from "@/components/ImageSelector/index.vue"
const { proxy } = getCurrentInstance()
const {
  normal_disable,
  home_module_type,
  home_app_attr,
  home_app_scope,
  home_app_type,
  home_app_top,
} = proxy.useDict(
  "normal_disable",
  "home_module_type",
  "home_app_attr",
  "home_app_type",
  "home_app_scope",
  "home_app_top"
)

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

const homeSetRef = ref(null) // 新增/编辑弹窗

const queryParams = ref({
  appName: null,
  status: null,
  moduleType: null,
  limit: 10,
  page: 1,
})

/** 查询列表 */
function getList() {
  loading.value = true
  indexCfgInfoPage(queryParams.value)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  homeSetRef.value.open()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  homeSetRef.value.open(row)
}

// 查看
function viewDetails(row) {
  homeSetRef.value.open(row, true)
}

// 修改状态
function handleStatusChange(row) {
  let text = +row.status === 1 ? "停用" : "启用" // 字典里面1是停用 0是启用
  proxy.$modal
    .confirm(`确认要${text}${row.appName}吗?`)
    .then(function () {
      return updateIndexCfgInfoAPI(row)
    })
    .then((res) => {
      if (+res.code === 200) {
        getList()
        proxy.$modal.msgSuccess(text + "成功")
      }
    })
    .catch(function () {
      row.status = +row.status === 0 ? 1 : 0
    })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id || selections.value.map((item) => item.id)
  const name = row.appName || selections.value.map((item) => item.appName)
  proxy.$modal
    .confirm('是否确认删除"' + name + '"的数据项？')
    .then(function () {
      return delIndexCfgInfo(id)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

getList()
</script>
