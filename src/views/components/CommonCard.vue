<template>
  <div class="common-card">
    <div class="common-card__header">
      <span>{{ title }}</span>
      <slot name="suffix" />
    </div>
    <slot />
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
})
</script>

<style scoped lang="scss">
.common-card {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 16px;
  &__header {
    @apply title-line;
    margin-bottom: 12px;
    font-weight: bold;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
