<template>
  <DrawerPage v-model="show" title="详情" @back="iframeUrl = null">
    <template #actions>
      <div class="opts-btn">
        <el-button type="primary" @click="showFlowModal" v-if="+fileInfo.flowFlag === 0">
          审批流程
        </el-button>
      </div>
    </template>
    <div v-loading="loading" class="preview">
      <template v-if="iframeUrl">
        <iframe :src="iframeUrl" frameborder="0" width="100%" height="100%" />
      </template>
    </div>

    <FlowViewModal ref="flowViewModalRef" />
  </DrawerPage>
</template>

<script setup>
import { getFlowPreviewUrl } from "@/api/ess/fileManagement/file"
const FlowViewModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowViewModal.vue")
)
const { proxy } = getCurrentInstance()
const show = defineModel(false)

const loading = ref(true)
const iframeUrl = ref(null)
const fileInfo = ref({})
function open(row) {
  fileInfo.value = row
  getUrl()
}

function getUrl() {
  if (!fileInfo.value.essFlowId) return
  getFlowPreviewUrl(fileInfo.value.essFlowId)
    .then((res) => {
      iframeUrl.value = res.object.url
    })
    .finally(() => {
      loading.value = false
    })
}
const flowViewModalRef = ref(null)

// 查看流程
function showFlowModal() {
  flowViewModalRef.value.open(fileInfo.value.defFlowId)
}

onActivated(() => {
  getUrl()
})

onDeactivated(() => {
  iframeUrl.value = null
})

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.preview {
  @apply w-full h-full;
}
.opts-btn {
  :deep(.el-button) {
    @apply h-32px min-h-32px;
  }
}
</style>
