<template>
  <div :style="{ height: height + 'px' }">
    <img :src="errorImg" v-realImg="loadSrc" width="100%" height="100%" fit="contain" />
  </div>
</template>

<script setup name="ShowImageIcon">
const props = defineProps({
  src: {
    required: true,
    type: String,
    default: "",
  },
  folderName: {
    // 文件夹名
    type: String,
    default: "flow-icon",
  },
  height: 30,
})

const loadSrc = computed(() => {
  return new URL(
    `../../assets/images/${props.folderName}/${props.src}.png`,
    import.meta.url
  ).href
})
const errorImg = computed(() => {
  return new URL(`../../assets/images/${props.folderName}/flow-auth.png`, import.meta.url)
    .href
})
</script>

<style scoped lang="scss"></style>
