export default {
	runApplication: {
		index: "#",
		importrunApplicationTip: "import RunApplication",
		id: "id",
		code: "code",
		flowKey: "flowKey",
		icon: "icon",
		formName: "formName",
		groupName: "groupName",
		finishTime: "finishTime",
		status: "status",
		remark: "remark",
		version: "version",
		createUser: "createUser",
		createTime: "createTime",
		tableName: "tableName",
		formInfo: "formInfo",
		sort: "sort",
		defFlowId: "defFlowId",
		flowInstId: "flowInstId",
		formData: "formData",
		formId: "formId",
		updateUser: "updateUser",
		updateTime: "updateTime",
		delFlag: "delFlag",

		inputIdTip: "input id",
		inputCodeTip: "input code",
		inputFlowKeyTip: "input flowKey",
		inputIconTip: "input icon",
		inputFormNameTip: "input formName",
		inputGroupNameTip: "input groupName",
		inputFinishTimeTip: "input finishTime",
		inputStatusTip: "input status",
		inputRemarkTip: "input remark",
		inputVersionTip: "input version",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputTableNameTip: "input tableName",
		inputFormInfoTip: "input formInfo",
		inputSortTip: "input sort",
		inputDefFlowIdTip: "input defFlowId",
		inputFlowInstIdTip: "input flowInstId",
		inputFormDataTip: "input formData",
		inputFormIdTip: "input formId",
		inputUpdateUserTip: "input updateUser",
		inputUpdateTimeTip: "input updateTime",
		inputDelFlagTip: "input delFlag",

	},
}
