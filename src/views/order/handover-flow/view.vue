<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item :label="$t('handoverFlow.code')" prop="code">
						<el-input
							v-model="state.queryForm.code"
							:placeholder="t('handoverFlow.inputCodeTip')"
							style="max-width: 180px"
						/>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.handoverReason')" prop="handoverReason">
						<el-select
							v-model="state.queryForm.handoverReason"
							:placeholder="t('handoverFlow.inputHandoverReasonTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_REASON"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.receiveDept')" prop="receiveDept">
						<el-select
							v-model="state.queryForm.receiveDept"
							:placeholder="t('handoverFlow.inputReceiveDeptTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.receiveDept"
								:key="index"
								:label="item.name"
								:value="item.deptId"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.receiveUser')" prop="receiveUser">
						<el-select
							v-model="state.queryForm.receiveUser"
							:placeholder="t('handoverFlow.inputReceiveUserTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.receiveUser"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.type')" prop="type">
						<el-select
							v-model="state.queryForm.type"
							:placeholder="t('handoverFlow.inputTypeTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_TYPE"
								:key="index"
								:disabled="DIC_PROP.HANDOVER_TYPE[1].value === item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.status')" prop="status">
						<el-select
							v-model="state.queryForm.status"
							:placeholder="t('handoverFlow.inputStatusTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_STATUS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							{{ $t('common.queryBtn') }}
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							{{ $t('common.resetBtn') }}
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.delBtn') }}
						</template>
						<el-button
							v-auth="'order_handoverflow_del'"
							plain
							:disabled="multiple"
							icon="Delete"
							type="primary"
							class="ml10"
							@click="handleDelete(selectObjs)"
						/>
					</el-tooltip>

					<right-toolbar
						v-model:show-search="showSearch"
						:export="'order_handoverflow_export'"
						class="ml10"
						style="float: right;margin-right: 20px"
						@export-excel="exportExcel"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" :label="t('handoverFlow.index')" width="40" />
				<el-table-column prop="code" :label="t('handoverFlow.code')" show-overflow-tooltip />
				<el-table-column prop="flowKey" :label="t('handoverFlow.flowKey')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.flowInstId"
							:value="scope.row.flowKey"
							:value-key="'flowKey'"
							:show-key="'flowName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="type" :label="t('handoverFlow.type')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.HANDOVER_TYPE" :value="scope.row.type" />
					</template>
				</el-table-column>
				<el-table-column prop="status" :label="t('handoverFlow.status')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.HANDOVER_STATUS" :value="scope.row.status" />
					</template>
				</el-table-column>
				<el-table-column prop="createUser" :label="t('handoverFlow.createUser')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.createUser"
							:value="scope.row.createUser"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" :label="t('handoverFlow.createTime')" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="120">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.viewBtn') }}
							</template>
							<el-button
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>

						<el-tooltip v-if="scope.row.status === DIC_PROP.ORDER_STATUS[2].value" placement="top">
							<template #content>
								{{ $t('jfI18n.recallBtn') }}
							</template>
							<el-button
								icon="RefreshLeft"
								text
								type="primary"
								@click="handleRecallReset(scope.row)"
							/>
						</el-tooltip>
						<el-tooltip v-if="scope.row.status === DIC_PROP.ORDER_STATUS[0].value" placement="top">
							<template #content>
								{{ $t('jfI18n.resetBtn') }}
							</template>
							<el-button
								icon="RefreshRight"
								text
								type="primary"
								@click="handleRecallReset(scope.row)"
							/>
						</el-tooltip>

						<el-tooltip v-if="scope.row.status === DIC_PROP.ORDER_STATUS[1].value || scope.row.status === DIC_PROP.ORDER_STATUS[0].value" placement="top">
							<template #content>
								修改
							</template>
							<el-button
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip v-if="scope.row.status === DIC_PROP.ORDER_STATUS[1].value" placement="top">
							<template #content>
								{{ $t('common.delBtn') }}
							</template>
							<el-button
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemHandoverFlowView">
import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchList, delObjs } from "@/api/order/handover-flow"
import { useMessage, useMessageBox } from "@/hooks/message"

import { useI18n } from "vue-i18n"
import { onLoadDicUrl, onLoaded } from "@/flow/components/convert-name/convert"
import { recallReset } from "@/api/jsonflow/run-flow"
import { DIC_PROP } from "@/flow/support/dict-prop"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))
const { t } = useI18n()
// 定义查询字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "receiveDept" }, { key: "receiveUser" })
onMounted(() => {
	onLoad(dicData)
})
// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
	onLoaded: onLoaded({ key: "createUser" }, { key: "flowInstId" }),
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	// 清空多选
	selectObjs.value = []
	getDataList()
}

// 导出excel
const exportExcel = () => {
	downBlobFile("/cloud-order/handover-flow/export", state.queryForm, "handover-flow.xlsx")
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

function handleRecallReset(row) {
	let params = { id: row.flowInstId, flowKey: row.flowKey, status: row.status }
	if (row.status === DIC_PROP.ORDER_STATUS[0].value) {
		recallReset(params).then(() => {
			proxy.$modal.msgSuccess("重发成功")
			getDataList()
		})
		return
	}
	useMessageBox().confirm("是否确认要撤回该工单?")
		.then(() => {
			return recallReset(params)
		})
		.then(() => {
			proxy.$modal.msgSuccess("撤回成功")
			getDataList()
		})
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t("common.delConfirmText"))
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess(t("common.delSuccessText"))
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
}
</script>
