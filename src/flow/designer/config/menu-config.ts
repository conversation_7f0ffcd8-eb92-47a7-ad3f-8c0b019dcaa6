import { CommonNodeType, HighNodeType, LaneNodeType } from "./type"
import { nodeJobSvgIcons } from "../assets/svges"

export const commonNodes = [
	{
		type: CommonNodeType.START,
		nodeName: "开始",
		icon: nodeJobSvgIcons.startMenu,
	},
	{
		type: CommonNodeType.SERIAL,
		nodeName: "串行节点",
		nodeDesc: "（表示流程同时只能流转到一个串行分支）",
		icon: nodeJobSvgIcons.serial,
	},
	{
		type: CommonNodeType.PARALLEL,
		nodeName: "并行节点",
		nodeDesc: "（表示流程同时可以流转到多个并行分支）",
		icon: nodeJobSvgIcons.parallel,
	},
	{
		type: CommonNodeType.END,
		nodeName: "结束",
		icon: nodeJobSvgIcons.endMenu,
	},
]

export const highNodes = [
	{
		type: HighNodeType.VIRTUAL,
		nodeName: "虚拟节点",
		nodeDesc: "（可选的自由节点，不常用）",
		icon: nodeJobSvgIcons.virtual,
	},
	{
		type: HighNodeType.JOB,
		nodeName: "节点任务",
		nodeDesc: "（可选的辅助节点，不常用）",
		icon: nodeJobSvgIcons.job,
	},
	{
		type: CommonNodeType.SERIAL,
		nodeName: "串行网关",
		icon: nodeJobSvgIcons.serialGate,
	},
	{
		type: CommonNodeType.PARALLEL,
		nodeName: "并行网关",
		icon: nodeJobSvgIcons.parallelGate,
	},
	{
		type: HighNodeType.CHILD_FLOW,
		nodeName: "子流程",
		icon: "set-up",
	},
]

export const laneNodes = [
	{
		type: LaneNodeType.Y_LANE,
		nodeName: "纵向泳道",
		icon: nodeJobSvgIcons.yLaneMenu,
	},
	{
		type: LaneNodeType.X_LANE,
		nodeName: "横向泳道",
		icon: nodeJobSvgIcons.xLaneMenu,
	},
]
