<template>
	<el-dialog
		v-model="dialogVisible"
		:title="title"
		width="500px"
		:before-close="handleClose"
		class="dialog-border"
	>
		<template #header>
			<div class="dialog-border-header">
				<svg-icon name="local-dialog-tip-icon" />
				确认{{ title }}文件？
			</div>
		</template>

		<el-form
			ref="formRef"
			:model="form"
			:rules="rules"
			label-position="top"
		>
			<el-form-item :label="`请输入${title}原因`" prop="reason">
				<el-input
					v-model="form.reason"
					type="textarea"
					:rows="6"
					placeholder="请输入内容"
					clearable
					:maxlength="200"
					show-word-limit
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="handleClose">
				取消
			</el-button>
			<el-button type="primary" :loading="loading" @click="submitForm">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { cancelInitiatedFile } from '@/api/ess/fileManagement/file'

const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const loading = ref(false)
const detailsInfo = ref({})
const title = ref('撤销')
const form = ref({
	reason: '',
})

const rules = ref({
	reason: [{ required: true, message: '请输入' + title.value + '原因', trigger: 'blur' }],
})

function open(row, t) {
	dialogVisible.value = true
	detailsInfo.value = { ...row }
	title.value = t
}

function handleClose() {
	dialogVisible.value = false
}

async function submitForm() {
	let { reason } = form.value
	if (!reason) {
		proxy.$modal.msgError('请输入' + title.value + '原因')
		return
	}
	try {
		loading.value = true
		if (title.value === '撤销') {
			await cancelInitiatedFile({ essFlowId: detailsInfo.value.essFlowId, cancelMsg: reason })
			proxy.$modal.msgSuccess('撤销成功')
			proxy.$emit('refresh')
			handleClose()
		}	else{
			handleClose()
    }
	}
	catch (error) {
	}
	finally {
		loading.value = false
	}
}

defineExpose({
	open,
})
</script>

<style scoped lang="scss">
.dialog-border-header {
  display: flex;
  align-items: center;
  font-weight: bold;
  padding-bottom: 15px;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 15px;
    width: 100%;
    height: 1px;
    border-bottom: 1px dashed rgba(112, 112, 112, 0.15);
  }
  .svg-icon {
    color: #f94e4f;
    font-size: 30px;
    flex-shrink: 0;
    margin-right: 10px;
  }
}
</style>

<style lang="scss">
.dialog-border {
  .el-dialog__header {
    position: relative;
  }
  .el-dialog__headerbtn{
    top: 0;
  }
}
</style>
