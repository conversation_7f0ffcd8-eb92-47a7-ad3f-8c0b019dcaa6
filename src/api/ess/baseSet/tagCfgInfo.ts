import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询标签管理配置列表 带分页
export function tagCfgInfoPage(data: Object) {
  return request({
    url: essPrefix + "/tagCfgInfo/listPage",
    method: "post",
    data,
  })
}
// 查询标签管理配置列表
export function getTagCfgInfoListAPI() {
  return request({
    url: essPrefix + "/tagCfgInfo/list",
    method: "get",
  })
}
// 根据标签类型查询标签列表
export function fetchTagFromTypeAPI(tagType: number | string) {
  return request({
    url: essPrefix + "/tagCfgInfo/queryTagCfgInfos/" + tagType,
    method: "get",
  })
}

// 修改标签
export function updateTagCfgInfo(data: Object) {
  return request({
    url: essPrefix + "/tagCfgInfo/edit",
    method: "post",
    data,
  })
}

// 删除
export function delTagCfgInfo(ids: string) {
  return request({
    url: essPrefix + "/tagCfgInfo/" + ids,
    method: "delete",
  })
}

// 查询标签管理配置详细
export function getTagCfgInfo(id: string) {
  return request({
    url: essPrefix + "/tagCfgInfo/" + id,
    method: "get",
  })
}
// 根据条件查询标签用户列表
export function queryTagUserRefList(data: Object) {
  return request({
    url: essPrefix + "/tagCfgInfo/queryTagUserRefList",
    method: "post",
    data,
  })
}

// 查询标签配置列表 带分页
export function getTagCfgListPage(data: Object) {
  return request({
    url: essPrefix + "/tagCfgInfo/listPage",
    method: "post",
    data,
  })
}

// 新增标签配置
export function addTagCfgInfo(data: Object) {
  return request({
    url: essPrefix + "/tagCfgInfo/add",
    method: "post",
    data,
  })
}
