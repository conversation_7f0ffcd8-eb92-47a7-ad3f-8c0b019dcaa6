<template>
  <div>
    <el-form
      ref="dataFormRef"
      v-loading="loading"
      :model="props.currFlowForm"
      :rules="dataRules"
      label-width="120px"
      label-position="left"
      :disabled="operType === 'view'"
      class="form-box"
    >
      <el-form-item label="流程KEY" prop="flowKey">
        <el-input
          v-model="props.currFlowForm.flowKey"
          clearable
          placeholder="请输入流程KEY"
        />
      </el-form-item>

      <el-form-item label="表单名称" prop="formName">
        <el-input
          v-model="props.currFlowForm.formName"
          clearable
          placeholder="请输入表单名称"
        />
      </el-form-item>

      <el-form-item label="图标" prop="icon">
        <ImageSelector type="flow" v-model="props.currFlowForm.icon" />
      </el-form-item>

      <el-form-item label="分组名称" prop="groupName" class="flex-form-item">
        <el-select
          v-model="props.currFlowForm.groupName"
          :disabled="!!validateRunFlow(props)"
          placeholder="请输入分组名称"
          clearable
          filterable
          allow-create
          default-first-option
        >
          <el-option
            v-for="(item, index) in dicData.groupName"
            :key="index"
            :label="item.groupName"
            :value="item.groupName"
          />
        </el-select>
        <el-button type="primary" class="ml-10px" @click="handleAddGroupName">
          新增分组
        </el-button>
      </el-form-item>

      <el-form-item label="表单类型" prop="type">
        <el-select
          v-model="props.currFlowForm.type"
          :disabled="!!validateRunFlow(props)"
          placeholder="请输入表单类型"
          clearable
          filterable
        >
          <el-option
            v-for="(item, index) in DIC_PROP.FORM_TYPE"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-tooltip
        content="当表单设计的页面为空时，可配置本地自定义主表单Vue页面路径"
        placement="top"
        v-if="props.currFlowForm.type === DIC_PROP.FORM_TYPE[1].value"
      >
        <el-form-item label="PC端路径" prop="path">
          <el-input
            v-model="props.currFlowForm.path"
            placeholder="请输入PC端路径"
            clearable
          />
        </el-form-item>
      </el-tooltip>

      <el-form-item label="设置可见范围" prop="range">
        <el-radio-group v-model="range">
          <el-radio :value="1">成员选择</el-radio>
          <el-radio :value="2">范围全选</el-radio>
        </el-radio-group>
      </el-form-item>

      <div class="mb-20px min-w-1200px">
        <UserTreeTransfer
          ref="rangeRef"
          :cols="3"
          :rangeType="range"
          :select-types="['user', 'dept', 'role', 'tag']"
          v-model:ranges="props.currFlowForm.rangeRefs"
          v-model:users="props.currFlowForm.userRefs"
        />
      </div>

      <!-- <el-col :span="16" :sm="{span: 20, offset: 2}"  :xs="{span: 22, offset: 1}" class="mb-1" :offset="4">
          <el-form-item label="关联表名称" prop="tableName" class="flex-form-item">
            <el-tooltip content="若自行调用接口保存表单数据，则此处需为空" placement="top">
              <el-select
                v-model="props.currFlowForm.tableName"
                placeholder="请输入关联表名称"
                clearable
                filterable
                @clear="props.currFlowForm.tableName = null"
              >
                <el-option
                  v-for="(item, index) in dicData.tableName"
                  :key="index"
                  :label="item.tableName"
                  :value="item.tableName"
                />
              </el-select>
            </el-tooltip>

            <el-button type="primary" class="ml-10px w-100px" @click="addUpdateTableName()">
              {{ props.currFlowForm.tableName ? '修改表' : '新增表' }}
            </el-button>
          </el-form-item>
        </el-col>

        <el-col v-if="!validateRunFlow(props)" :span="16" :sm="{span: 20, offset: 2}"  :xs="{span: 22, offset: 1}" class="mb-1" :offset="4">
          <el-form-item label="操作权限" prop="permission">
            <el-select
              v-model="props.currFlowForm.permission"
              placeholder="请选择操作角色, 默认所有人都可以发起该流程"
              clearable
              filterable
              multiple
            >
              <el-option
                v-for="(item, index) in dicData.permission"
                :key="index"
                :label="item.roleName"
                :value="item.roleId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="16" :sm="{span: 20, offset: 2}"  :xs="{span: 22, offset: 1}" class="mb-1" :offset="4">
          <el-form-item label="关联子表名称" prop="subTableName" class="flex-form-item">
            <el-tooltip
              content="当存在多个关联子表时，多个子表名称名顺序与《关联子表属性》顺序一一对应"
              placement="top"
            >
              <el-select
                v-model="props.currFlowForm.subTableName"
                placeholder="请选择关联子表名称"
                clearable
                filterable
                multiple
                @change="changeSubTableName"
              >
                <el-option
                  v-for="(item, index) in dicData.tableName"
                  :key="index"
                  :label="item.tableName"
                  :value="item.tableName"
                >
                  <span>{{ item.tableName }}</span>
                  <span style="float: right;" @click="addUpdateTableName(item.tableName, true)">
                    点击修改
                  </span>
                </el-option>
              </el-select>
            </el-tooltip>

            <el-button type="primary" class="ml-10px" @click="addUpdateTableName(null, true)">
              新增子表
            </el-button>
          </el-form-item>
        </el-col>

        <el-col
          v-if="!validateNull(props.currFlowForm.subTableName)"
          :span="16" :sm="{span: 20, offset: 2}"  :xs="{span: 22, offset: 1}"
          class="mb-1"
          :offset="4"
        >
          <el-tooltip
            content="当关联子表名称存在时，可配置子表中关联【主表主键】的列名，如main_id"
            placement="top"
          >
            <el-form-item label="关联主表列名" prop="subMainField">
              <el-input
                v-model="props.currFlowForm.subMainField"
                placeholder="请输入关联主表列名"
                clearable
              />
            </el-form-item>
          </el-tooltip>
        </el-col>

        <el-col
          v-if="!validateNull(props.currFlowForm.subTableName)"
          :span="16" :sm="{span: 20, offset: 2}"  :xs="{span: 22, offset: 1}"
          class="mb-1"
          :offset="4"
        >
          <el-tooltip
            content="当关联子表名称存在时，可配置主表中关联【子表集合数据】的属性名，如subFormList。多个子表属性名以逗号分割，注意与《关联子表名称》顺序一一对应"
            placement="top"
          >
            <el-form-item label="关联子表属性" prop="mainSubProp">
              <el-input v-model="props.currFlowForm.mainSubProp" placeholder="请输入关联子表属性" clearable />
            </el-form-item>
          </el-tooltip>
        </el-col> -->

      <el-form-item label="表单备注" prop="remark">
        <el-input
          v-model="props.currFlowForm.remark"
          type="textarea"
          placeholder="请输入表单备注"
          clearable
        />
      </el-form-item>

      <el-form-item label="排序值" prop="sort">
        <el-input-number
          v-model="props.currFlowForm.sort"
          :min="1"
          :max="1000"
          placeholder="请输入排序值"
        />
      </el-form-item>

      <el-tooltip
        content="在审批时默认展示的第1个页面。当存在多个默认展示页面时，优先展示排序值最小的"
        placement="top"
        v-if="!validateRunFlow(props)"
      >
        <el-form-item label="默认展示" prop="isActive">
          <el-radio-group v-model="props.currFlowForm.isActive">
            <el-radio
              v-for="(item, index) in DIC_PROP.YES_OR_NO"
              :key="index"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-tooltip>

      <!-- <el-col v-if="!validateRunFlow(props)" :span="16" :sm="{span: 20, offset: 2}"  :xs="{span: 22, offset: 1}" class="mb-1" :offset="4">
          <el-tooltip
            content="在审批时不会显示审批按钮，在页面点提交按钮会自动流转到下一步"
            placement="top"
          >
            <el-form-item label="提交时自动审批" prop="isAutoAudit">
              <el-radio-group v-model="props.currFlowForm.isAutoAudit">
                <el-radio
                  v-for="(item, index) in DIC_PROP.YES_OR_NO"
                  :key="index"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-tooltip>
        </el-col> -->

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="props.currFlowForm.status">
          <el-radio
            v-for="(item, index) in DIC_PROP.TEMP_STATUS"
            :key="index"
            :value="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 编辑、新增  -->
    <create-table ref="createTableRef" @refresh-done="createTableDone" />
  </div>
</template>

<script setup name="FlowApplicationForm">
import { useMessageBox } from "@/hooks/message"
import * as runApplication from "@/api/order/run-application"
import * as flowApplication from "@/api/order/flow-application"
import { deepClone } from "@/utils/index"
import { notifyLeft, stringifyWithFunctions, validateRunFlow } from "@/flow"
import { onLoadDicUrl, onUpdateDicData } from "@/flow/components/convert-name/convert"
import { validateNull } from "@/utils/validate"
import { DIC_PROP } from "../../support/dict-prop"
import { validateFormTypeSave, vueKey } from "@/api/order/order-key-vue"
import { PROP_CONST } from "../../support/prop-const"

const { proxy } = getCurrentInstance()
const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null,
  },
})
// 引入组件
const ImageSelector = defineAsyncComponent(() =>
  import("@/components/ImageSelector/index.vue")
)
const CreateTable = defineAsyncComponent(() =>
  import("@/views/order/create-table/form.vue")
)
const UserTreeTransfer = defineAsyncComponent(() =>
  import("@/views/components/UserTreeTransfer/index.vue")
)

// 定义变量内容
const dataFormRef = ref()
const loading = ref(false)
const operType = ref(false)
const isSubTableNameRef = ref(false)
const range = ref(1) // 默认成员选择
// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl(
  { key: "groupName" },
  { key: "tableName" },
  { key: "permission" }
)
const onUpdate = onUpdateDicData({ key: "groupName" })
onMounted(() => {
  onLoad(dicData)
  let isNoGroupName = !props.currFlowForm.groupName && dicData.groupName?.length > 0
  if (isNoGroupName) {
    props.currFlowForm.groupName = dicData.groupName[0].groupName
  }
})

// 定义校验规则
const dataRules = ref({
  flowKey: [{ required: true, message: "流程KEY不能为空", trigger: "blur" }],
  icon: [{ required: true, message: "表单图标不能为空", trigger: "blur" }],
  type: [{ required: true, message: "表单类型不能为空", trigger: "blur" }],
  formName: [{ required: true, message: "表单名称不能为空", trigger: "blur" }],
  groupName: [{ required: true, message: "分组名称不能为空", trigger: "change" }],
  status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
})

function handleAddGroupName() {
  useMessageBox()
    .prompt("请输入新的分组名称")
    .then(({ value }) => {
      props.currFlowForm.groupName = value
      onUpdate(dicData, props.currFlowForm)
    })
}

function changeSubTableName() {
  props.currFlowForm.subMainField = null
  props.currFlowForm.mainSubProp = null
}

function addUpdateTableName(subTableName, isSubTableName) {
  isSubTableNameRef.value = isSubTableName
  let tableName = isSubTableName ? subTableName : props.currFlowForm.tableName
  if (tableName) {
    if (tableName === PROP_CONST.COMMON.tableName) {
      notifyLeft("当前表为系统表，请勿修改", "warning")
    } else {
      let find = dicData.tableName.find((f) => f.tableName === tableName)
      proxy.$refs.createTableRef.openDialog("edit", find.id)
    }
  } else {
    proxy.$refs.createTableRef.openDialog("add")
  }
}

function createTableDone(tableName) {
  if (isSubTableNameRef.value) {
    isSubTableNameRef.value = false
  } else {
    props.currFlowForm.tableName = tableName
  }

  const onLoad = onLoadDicUrl({ key: "tableName" })
  onLoad(dicData)
}

async function validatePreSave() {
  const valid = await dataFormRef.value?.validate().catch(() => {})
  if (!valid) {
    notifyLeft("请先完善表单设置内容", "warning")
    console.log("表单设置内容不完整")
    return false
  }
  let isSys = props.currFlowForm.type === DIC_PROP.FORM_TYPE[1].value
  if (isSys && !props.currFlowForm.path) {
    notifyLeft("当前表单类型为系统表单，【PC端路径】不能为空", "warning")
    return false
  }
  if (!validateNull(props.currFlowForm.subTableName)) {
    if (!props.currFlowForm.subMainField || !props.currFlowForm.mainSubProp) {
      notifyLeft("请填写 关联主表列名 或者 关联子表属性", "warning")
      return false
    }
  }

  return true
}

function validateOtherSave() {
  let isDesign = props.currFlowForm.type !== DIC_PROP.FORM_TYPE[1].value
  if (isDesign && !props.currFlowForm.formInfo) {
    notifyLeft("当前表单类型为设计表单，请先完善第二项设计的表单内容", "warning")
    return false
  }
  return true
}

const handleSubmit = async (callback, status) => {
  if (status) {
    props.currFlowForm.status = status
  }

  let preSave = await validatePreSave()
  if (!preSave) {
    return
  }

  if (!validateOtherSave()) {
    return
  }

  let formJson = deepClone(props.currFlowForm)
  formJson.formInfo = stringifyWithFunctions(formJson.formInfo)

  validateFormTypeSave(formJson)
  try {
    loading.value = true
    // 判断是否为低代码
    if (validateRunFlow(props) && props.currFlowForm.tableName) {
      await runApplication.putObjNoStatus(formJson)
    } else {
      await flowApplication.addObj(formJson)
    }

    props.currFlowForm.formSetting = true
    notifyLeft("当前表单设置保存成功")
    if (callback) {
      callback()
    }
  } catch (err) {
    proxy.$modal.msgError(err)
  } finally {
    loading.value = false
  }
}

// 暴露变量
defineExpose({
  handleSubmit,
  validatePreSave,
  validateOtherSave,
})
</script>

<style lang="scss" scoped>
.form-box {
  width: 80%;
  margin: 0 auto;
  max-width: 1200px;
  min-width: 1000px;
}
.flex-form-item {
  :deep(.el-form-item__content) {
    flex-wrap: nowrap;
  }
}
</style>
