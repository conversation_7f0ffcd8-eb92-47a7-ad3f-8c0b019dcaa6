<template>
	<div class="app-container dict-container">
		<el-row :gutter="10">
			<el-col :span="12">
				<div class="title">
					字典类型
				</div>
				<DictTypeComp @change="changeType" />
			</el-col>
			<el-col :span="12">
				<div class="title">
					字典数据 {{ form.dictName ? ` - ${form.dictName}` : '' }}
				</div>
				<DictDataComp
					:dict-id="form.dictId"
					:dict-name="form.dictName"
					@change="changeType"
				/>
			</el-col>
		</el-row>
	</div>
</template>

<script setup name="Dict">
import DictTypeComp from "./type.vue"
import DictDataComp from "./data.vue"

const form = reactive({
	dictId: null,
	dictName: "",
})

const changeType = val => {
	form.dictId = val.dictId
	form.dictName = val.dictName
}
</script>

<style lang="scss" scoped>
.dict-container {
  .title{
    @apply title-line;
    margin-bottom: 10px;
    font-size: 18px;
    color: #000;
  }
  :deep(*) {
    .white-form-box {
      .el-input,
      .el-select {
        min-width: 150px;
        width: 150px;
      }
    }
  }
}
</style>
