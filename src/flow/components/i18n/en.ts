export default {
	jfI18n: {
		temp: "Temp",
		invalid: "Invalid",
		pub: "Publish",
		oneBtnDesign: "OneBtn Design",
		viewPageBtn: "View Page",
		onceMoreDesign: "Once More Design",
		designFlow: "Design Flow",
		viewFlow: "View Flow",
		print: "Print",
		printTemplate: "Print Template",
		submit: "Submit",
		viewForm: "View Form",
		initHandover: "Initial Handover",
		initialBtn: "Initiate",
		batchBtn: "Batch Save",
		saveBtn: "Save",
		cancelBtn: "Cancel",
		updateFlow: "Update Flow",
		rejectBtn: "Reject Check",
		receiveBtn: "Confirm Receive",
		recallBtn: "Recall",
		resetBtn: "Reset",
		operate: "Operate",
		more: "More",
	},
	jfAttr: {
		paramValType: "Parameter Value Type",
		varKeyVal: "Form Field OR Fixed Value",
    sysVarKeyVal: 'Sys Field OR Fixed Value',
		subVarKeyVal: "Subform Field",
		subVarKeyVal2: "Subform Field OR Fixed Value",
		requestHeader: "Request Header",
		requestParam: "Request Param",
		returnParam: "Return Param",
	},
	jfForm: {
		urgentTask: "Urgent task",
		urgentSubFlow: "Urgent Sub Flow",
		reduceSign: "Reduce Sign",
	},
}
