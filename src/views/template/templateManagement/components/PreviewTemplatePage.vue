<template>
  <DrawerPage v-model="show" title="预览">
    <div v-loading="loading" class="preview">
      <iframe
        v-if="previewUrl"
        :src="previewUrl"
        frameborder="0"
        width="100%"
        height="100%"
      />
    </div>
  </DrawerPage>
</template>

<script setup name="PreviewTemplatePage">
import { templatePreviewUrl } from "@/api/ess/template/template"
const show = defineModel(false)
const loading = ref(true)
const templateInfo = ref({})
const previewUrl = ref(null)

const open = (row) => {
  templateInfo.value = row
  getTemplatePreviewUrl()
}

const getTemplatePreviewUrl = () => {
  loading.value = true
  templatePreviewUrl(templateInfo.value.essTemplateId)
    .then((res) => {
      previewUrl.value = res.object.url
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  window.addEventListener("message", handleMessage)
  // 获取iframe传过来的信息
})

onBeforeUnmount(() => {
  window.removeEventListener("message", handleMessage)
})

function handleMessage(event) {
  const { messageType, action, embedParams } = event.data
  if (messageType === "NOT_LOGIN") {
    // 监听登录失效
    previewUrl.value = null
    getTemplatePreviewUrl()
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.preview {
  width: 100%;
  height: 100%;
}
</style>
