server_tokens off;  # Nginx 版本信息关闭，避免攻击
client_max_body_size  64m; # 最大上传文件大小!
server {
    listen 51426;
    server_name localhost;

    gzip on;
    gzip_static on; # 需要http_gzip_static_module 模块
    gzip_min_length 1k;
    gzip_comp_level 4;
    gzip_proxied any;
    gzip_types text/plain text/xml text/css;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";

    # 前端打包好的dist目录文件
    # root /data/;
    location /electronic-seal-web {
        root   /data;
        index  index.html index.htm;
        try_files $uri $uri/ /electronic-seal-web/index.html;
    }

    # 静态资源缓存配置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg)$ {
        root /data/electronic-seal-web;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    location /prod-api/ {
        # proxy_pass http://localhost:8001; #注意/后缀
        proxy_pass http://**********:8001/; #注意/后缀
        proxy_connect_timeout 60s;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
        proxy_set_header from "";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto http;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $http_host;
        proxy_set_header from "";
    }

   # 屏蔽所有敏感路径，不用改代码配置开关，双重保护
    location ~* ^/(actuator|swagger-ui|v3/api-docs|swagger-resources|webjars|doc.html) {
        return 403; # 禁止访问
    }

    # 电子印章Websocket映射
    location /ws-ess {
            proxy_pass http://**************:8224/ws-ess;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
    }

    #图片预览
     location /image-api {
        proxy_pass http://************:9000/sign;
    }

}
