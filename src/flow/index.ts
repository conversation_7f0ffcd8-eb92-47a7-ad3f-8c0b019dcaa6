import { validateNull } from "@/utils/validate"
import { ElNotification } from "element-plus"
import printJS from "print-js"
import { DIC_PROP } from "@/flow/support/dict-prop"
import { Local } from "@/utils/storage"
import FcDesigner from "form-create-designer"
import { validateFormTypeSave } from "@/api/order/order-key-vue"


// 判断是否为实例
export const validateRunFlow = (props) => {
  if (props.currFlowForm) return props.currFlowForm.flowInstId
  else {
    if (props.flowData.attrs.orderId) return props.flowData.attrs.id
  }
  return null
}

// 判断是否为实例
export const validateRunFlowId = (props, form) => {
  if (!props.currFlowForm) return form
  let flowInstId = validateRunFlow(props);
  let id = props.currFlowForm.id;
  if (flowInstId) {
    let tableName = props.currFlowForm.tableName;
    // 判断是否为低代码
    form.formId = tableName ? props.currFlowForm.formId: id
    form.flowInstId = props.currFlowForm.flowInstId
  } else {
    form.formId = id
  }
  return form
}

// 初始化流程表单参数
export const initDefFlowFromAttrs = (props, form) => {
  if (!props.currFlowForm) return
  // 流程比表单先保存
  validateFormTypeSave(props.currFlowForm)
  validateRunFlowId(props, form)
  // 表单权限主表单首次未入库
  form.formName = props.currFlowForm.formName
  form.formType = props.currFlowForm.type
  form.formPath = props.currFlowForm.path
  // 判断是否为低代码
  let tableName = props.currFlowForm.tableName;
  form.fromType = tableName ? '0' : '1'
}

// 初始化表单必要参数
export const initFromSomeAttrs = (props, form) => {
  if (!props.currFlowForm) return
  // 字段定义比表单先保存，在initDefFlowFromAttrs之后
  validateRunFlowId(props, form) // 可能会升版本
  form.formName = props.currFlowForm.formName
  form.formType = props.currFlowForm.type
  form.path = props.currFlowForm.path
}

export function getLabelByLanguage(item) {
  let language = Local.get('themeConfig').globalI18n;
  return language === 'en' && item.labelEn ? item.labelEn : item.label
}

export function getJobBtnName(jobBtn) {
  let item = DIC_PROP.JOB_BTNS.find((f) => f.value === jobBtn);
  return item.label
}

export function handleChangeJobType(dicData, item) {
  let exist
  if (item.jobType === DIC_PROP.JOB_USER_TYPE[0].value) {
    // exist = dicData.users.find(f => f.userId === item.roleId);
  } else if (item.jobType === DIC_PROP.JOB_USER_TYPE[1].value) {
    exist = (dicData.roles || []).find(f => f.roleId === item.roleId);
  } 
  // else if (item.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
  //   exist = dicData.posts.find(f => f.postId === item.roleId);
  // } 
  else if (item.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
    exist = (dicData.depts || []).find(f => f.deptId === item.roleId);
  }
  // 当不存在情况已选值
  if (!exist) item.roleId = null
}

export function handleShowRoleNameByJobType(role, isJobType){
  let name = '';
  if (isJobType === '0') {
    name = role.roleName ? role.roleName.substring(0, role.roleName.indexOf('(')) : ''
  } else {
    name = role.roleName ? role.roleName + (isJobType ? '(' + DIC_PROP.JOB_USER_TYPE[1].label + ')' : '') : ''
  }
  return name
}

export function handleShowNameByJobType(role, isJobType){
  let name = '';
  if (role.jobType === DIC_PROP.JOB_USER_TYPE[0].value) {
    if (isJobType === '0') {
      name = role.name ? role.name.substring(0, role.name.indexOf('(')) : ''
    } else {
      name = role.name ? role.name + (isJobType ? '(' + DIC_PROP.JOB_USER_TYPE[0] + ')' : '') : ''
    }
  } else if (role.jobType === DIC_PROP.JOB_USER_TYPE[1].value) {
    name = handleShowRoleNameByJobType(role, isJobType)
  }
  // else if (role.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
  //   if (isJobType === '0') {
  //     name = role.postName ? role.postName.substring(0, role.postName.indexOf('(')) : ''
  //   } else {
  //     name = role.postName ? role.postName + (isJobType ? '(' + getLabelByLanguage(DIC_PROP.JOB_USER_TYPE[2]) + ')' : '') : ''
  //   }
   else if (role.jobType === DIC_PROP.JOB_USER_TYPE[2].value) {
    if (isJobType === '0') {
      name = role.name ? role.name.substring(0, role.name.indexOf('(')) : ''
    } else {
      name = role.name ? role.name + (isJobType ? '(' + DIC_PROP.JOB_USER_TYPE[0] + ')' : '') : ''
    }
  }
  return name
}

/**
 * 请求参数去重，并去掉$与空值
 */
export function paramsFilter(params) {
  if (validateNull(params)) return null
  let res = {}
  let map = new Map(Object.entries(params))
  map.forEach((value, key) => {
    if (key.includes("$") || validateNull(value)) return
    res[key] = value
  })
  return res
}

export function notifyLeft(msg, type = 'success', time = 2000) {
  ElNotification({
    title: '提示',
    message: msg,
    // @ts-ignore
    type: type,
    // @ts-ignore
    position: 'top-right',
    duration: time
  })
}

export function parseRemoveNullKey(jsonStr) {
  const obj = JSON.parse(jsonStr);
  return JSON.stringify(obj, (key, value) => {
    if (validateNull(value)) {
      return undefined; // 返回undefined跳过属性
    }
    return value;
  });
}

export function stringifyRemoveNullKey(object) {
  return JSON.stringify(object, (key, value) => {
    if (validateNull(value)) {
      return undefined; // 返回undefined跳过属性
    }
    return value;
  })
}

export function stringifyWithFunctions(object) {
  return FcDesigner.formCreate.toJson(object)
}

export const parseWithFunctions = (obj, func?) => {
  if (!func) {
    return parseWithString(obj)
  }
  if (typeof obj === "string") {
    return FcDesigner.formCreate.parseJson(obj)
  }
  return obj;
}

export const parseWithString = (obj) => {
  if (typeof obj === "string") {
    return JSON.parse(obj);
  } else {
    return obj;
  }
}

export const printHtml = (id, header, data) => {
  setTimeout(() => {
    printJS({
      type: 'html',
      printable: id,
      header: header,
      targetStyles: ["*"],
      scanStyles: true,
      maxWidth: 1100,
    })
    if (data) data.width = "-"
  }, 100)
}
