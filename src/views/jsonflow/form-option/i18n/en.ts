export default {
	formoption: {
		index: "#",
		importFormOptionTip: " import FormOption",
		id: "id",
		defFlowId: "defFlowId",
		flowNodeId: "flowNodeId",
		flowKey: "flowKey",
		propId: "propId",
		label: "label",
		prop: "prop",
		subForm: "subForm",
		propType: "propType",
		printInfo: "printInfo",
		permType: "permType",
		formType: "formType",
		formId: "formId",
		formName: "formName",
		path: "path",
		type: "type",
		resetBtn: "resetPerm",
		inputIdTip: "input id",
		inputDefFlowIdTip: "input defFlowId",
		inputFlowNodeIdTip: "input flowNodeId",
		inputFlowKeyTip: "input flowKey",
		inputPropIdTip: "input propId",
		inputLabelTip: "input label",
		inputPropTip: "input prop",
		inputSubFormTip: "input subForm",
		inputPropTypeTip: "input propType",
		inputPermTypeTip: "input permType",
		inputFormTypeTip: "input formType",
		inputFormIdTip: "input formId",
		inputFormNameTip: "input formName",
		inputPathTip: "input path",
		inputPrintInfoTip: "input printInfo",
		inputTypeTip: "input type",
	},
}
