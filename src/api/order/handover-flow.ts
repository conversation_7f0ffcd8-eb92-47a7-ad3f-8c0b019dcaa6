import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-order/handover-flow/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-order/handover-flow",
		method: "post",
		data: obj,
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/cloud-order/handover-flow/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/cloud-order/handover-flow/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/cloud-order/handover-flow/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-order/handover-flow",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-order/handover-flow",
		method: "put",
		data: obj,
	})
}

// 驳回选中项
export function checkToReject(obj: any) {
	return request({
		url: "/cloud-order/handover-flow/check/to-reject",
		method: "post",
		data: obj,
	})
}

// 确认接收
export function confirmReceive(obj: any) {
	return request({
		url: "/cloud-order/handover-flow/confirm/receive",
		method: "post",
		data: obj,
	})
}

// 分配参与者
export function distributePerson(obj: any) {
	return request({
		url: "/cloud-order/handover-flow/distribute",
		method: "put",
		data: obj,
	})
}
