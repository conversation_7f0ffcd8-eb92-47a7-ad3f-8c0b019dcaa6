<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="120px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item label="类型" prop="type">
						<el-select
							v-model="form.type"
							placeholder="请选择类型"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.PARAM_RULE_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="值类型" prop="valType">
						<el-select
							v-model="form.valType"
							placeholder="请选择值类型"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.VAL_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="流程ID" prop="defFlowId">
						<el-select
							v-model="form.defFlowId"
							placeholder="请选择流程ID"
							clearable
							filterable
							@change="cascadeChange('defFlowId', ['flowNodeId', 'flowNodeRelId'])"
						>
							<el-option
								v-for="(item, index) in dicData.defFlowId"
								:key="index"
								:label="item.flowName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="节点ID" prop="flowNodeId">
						<el-select
							v-model="form.flowNodeId"
							placeholder="请选择节点ID"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="节点关系ID" prop="flowNodeRelId">
						<el-select
							v-model="form.flowNodeRelId"
							placeholder="请选择节点关系ID"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowNodeRelId"
								:key="index"
								:label="item.label"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="组ID" prop="groupId">
						<el-input v-model="form.groupId" placeholder="请输入组ID" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="组类型" prop="groupsType">
						<el-switch
							v-model="form.groupsType"
							active-value="0"
							inactive-value="1"
							inactive-text="或"
							active-text="且"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="组内类型" prop="groupType">
						<el-switch
							v-model="form.groupType"
							active-value="0"
							inactive-value="1"
							inactive-text="或"
							active-text="且"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="变量键值" prop="varKeyVal">
						<el-input v-model="form.varKeyVal" placeholder="请输入变量键值" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="操作符" prop="operator">
						<el-select
							v-model="form.operator"
							placeholder="请选择操作符"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.OPERATOR"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="变量值" prop="varVal">
						<el-input v-model="form.varVal" placeholder="请输入变量值" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="任务类型" prop="jobType">
						<el-select
							v-model="form.jobType"
							placeholder="请选择任务类型"
							clearable
							filterable
							@change="handleRoleType"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.JOB_USER_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="角色ID" prop="roleId">
						<el-select
							v-model="form.roleId"
							placeholder="请选择角色ID"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in dicData.users"
								v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[0].value"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
							<el-option
								v-for="(item, index) in dicData.roles"
								v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[1].value"
								:key="index"
								:label="item.roleName"
								:value="item.roleId"
							/>
							<!-- <el-option
								v-for="(item, index) in dicData.posts"
								v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value"
								:key="index"
								:label="item.postName"
								:value="item.postId"
							/> -->
							<el-option
								v-for="(item, index) in dicData.depts"
								v-if="form.jobType === DIC_PROP.JOB_USER_TYPE[2].value"
								:key="index"
								:label="item.name"
								:value="item.deptId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="参数来源" prop="paramFrom">
						<el-select
							v-model="form.paramFrom"
							placeholder="请选择参数来源"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.PARAM_FROM"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="参数值类型" prop="paramValType">
						<el-select
							v-model="form.paramValType"
							placeholder="请选择参数值类型"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.PARAM_VAL_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="目标属性" prop="targetProp">
						<el-input v-model="form.targetProp" placeholder="请输入目标属性" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb-1">
					<el-form-item label="参数类型" prop="paramType">
						<el-select
							v-model="form.paramType"
							placeholder="请选择参数类型"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.PARAM_TYPES"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="FlowRuleDialog">
import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/jsonflow/flow-rule"
import { useI18n } from "vue-i18n"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
import { handleChangeJobType } from "@/flow"

const emit = defineEmits(["refresh"])
const { t } = useI18n()

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 提交表单数据
const form = reactive({
	id: "",
	flowKey: "",
	defFlowId: "",
	flowNodeId: "",
	type: "",
	valType: "",
	groupId: "",
	groupsType: "0",
	groupType: "0",
	varKeyVal: "",
	operator: "",
	varVal: "",
	createUser: "",
	createTime: "",
	updateUser: "",
	updateTime: "",
})

// 定义校验规则
const dataRules = ref({
	defFlowId: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
	type: [{ required: true, message: "数据类型不能为空", trigger: "blur" }],
	valType: [{ required: true, message: "条件模式不能为空", trigger: "blur" }],
	groupId: [{ required: true, message: "同组条件ID不能为空", trigger: "blur" }],
	groupsType: [{ required: true, message: "条件组关系不能为空", trigger: "blur" }],
	groupType: [{ required: true, message: "组内条件关系不能为空", trigger: "blur" }],
	varKeyVal: [{ required: true, message: "表单字段不能为空", trigger: "blur" }],
	operator: [{ required: true, message: "运算符不能为空", trigger: "blur" }],
	varVal: [{ required: true, message: "校验值不能为空", trigger: "blur" }],
})

// 定义查询字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "defFlowId" }, { key: "users" }, { key: "roles" }, { key: "posts" }, { key: "depts" })
const onCascade = onCascadeChange(cascadeDic, { key: "defFlowId", cascades: ["flowNodeId", "flowNodeRelId"] })
onMounted(() => {
	onLoad(dicData)
})

function cascadeChange(key, cascades) {
	onCascade(form, { key: key, cascades: cascades })
	if (key === "defFlowId") {
		let find = dicData.defFlowId.find(f => f.id === form.defFlowId)
		if (find) { form.flowKey = find.flowKey }
		else { form.flowKey = null }
	}
}

function handleRoleType() {
	handleChangeJobType(dicData, form)
}

// 打开弹窗
const openDialog = async (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(async () => {
		dataFormRef.value.resetFields()
		// 获取FlowRule信息
		if (id) {
			form.id = id
			await getFlowRuleData(id)
			await onCascade(form)
		}
	})
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true
		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getFlowRuleData = async (id: string) => {
	// 获取数据
	loading.value = true
	let res = await getObj(id)
	Object.assign(form, res.object)
	loading.value = false
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
