<template>
  <ul
    class="vue-contextmenu-listWrapper"
    :class="'vue-contextmenuName-' + props.contextMenuData.menuName"
    @contextmenu.stop=""
  >
    <li
      v-for="item in props.contextMenuData.menulists"
      :key="item.btnName"
      class="context-menu-list"
    >
      <div v-if="item.children && item.children.length > 0" class="has-child">
        <div
          class="parent-name btn-wrapper-simple"
          :class="{ 'no-allow': item.disabled ? item.disabled : false }"
        >
          <i
            :class="item.icoName ? item.icoName : ''"
            class="nav-icon-fontawe"
            :style="item.icoStyle ? item.icoStyle : ''"
          />
          <span class="nav-name-right">{{ item.btnName }}</span>
          <i class="icon" />
        </div>
        <Tree
          :itemchildren="item.children"
          :float="data.floatDirection"
          @childhandler="fnHandler"
        />
      </div>
      <div v-else>
        <div v-if="item.nodeConnect === true" class="node-connect">
          <div @click.stop="fnHandler(item)">
            <img :src="item.icon" :alt="item.btnName" />
            <!--                        <i :class="item.icoName ? item.icoName : ''"></i>-->
            <span>{{ item.btnName }}</span>
          </div>
        </div>
        <div
          v-else
          class="no-child-btn btn-wrapper-simple"
          :class="{ 'no-allow': item.disabled ? item.disabled : false }"
          @click.stop="item.disabled === true ? '' : fnHandler(item)"
        >
          <i
            :class="item.icoName ? item.icoName : ''"
            class="nav-icon-fontawe"
            :style="item.icoStyle ? item.icoStyle : ''"
          />
          <span class="nav-name-right">{{ item.btnName }}</span>
        </div>
      </div>
    </li>
  </ul>
</template>
<script setup lang="ts" name="VueContextMenu">
// 引入组件
const Tree = defineAsyncComponent(() => import('./tree.vue'))

const props = defineProps({
  contextMenuData: {
    type: Object,
    required: false,
    default() {
      return {
        menuName: null,
        axis: {
          x: null,
          y: null
        },
        menulists: [
          {
            fnHandler: '',
            icoName: '',
            btnName: ''
          }
        ]
      }
    }
  },
  transferIndex: {
    type: Number,
    default: 0
  }
})

const data = reactive({
  floatDirection: 'float-statue-1'
})

let menuLists = props.contextMenuData.menulists

function contextMenuDataAxis(val) {
  let x = val.x
  let y = val.y
  let innerWidth = window.innerWidth
  let innerHeight = window.innerHeight
  let index = props.transferIndex
  let menuName = 'vue-contextmenuName-' + props.contextMenuData.menuName
  let menu = document.getElementsByClassName(menuName)[index]
  menu.style.display = 'block'
  let menuHeight = menuLists.length * 30
  let menuWidth = 150
  menu.style.top = (y + menuHeight > innerHeight ? innerHeight - menuHeight : y) + 'px'
  menu.style.left = (x + menuWidth > innerWidth ? innerWidth - menuWidth : x) + 'px'
  /* document.addEventListener('mouseup', function (e) {
            // 解决mac电脑在鼠标右键后会执行mouseup事件
            if (e.button === 0) {
                menu.style.display = 'none'
            }
        }, false) */
  if (x + 2 * menuWidth > innerWidth && y + 2 * menuHeight > innerHeight) {
    data.floatDirection = 'float-status-4'
  }
  if (x + 2 * menuWidth < innerWidth && y + 2 * menuHeight > innerHeight) {
    data.floatDirection = 'float-status-1'
  }
  if (x + 2 * menuWidth > innerWidth && y + 2 * menuHeight < innerHeight) {
    data.floatDirection = 'float-status-3'
  }
  if (x + 2 * menuWidth < innerWidth && y + 2 * menuHeight < innerHeight) {
    data.floatDirection = 'float-status-2'
  }
}

let $emit = defineEmits([
  'flowInfo',
  'paste',
  'setNodeAttr',
  'copyNode',
  'deleteNode',
  'deleteLink',
  'setLinkAttr',
  'setSerialNode',
  'setParallelNode',
  'setSerialGate',
  'setParallelGate',
  'setConnectNode',
  'modifySourceNode',
  'modifyTargetNode'
])

function fnHandler(item) {
  $emit(item.fnHandler)
}

// 监听双向绑定
watch(
  () => props.contextMenuData.axis,
  (val) => {
    contextMenuDataAxis(val)
  }
)
</script>
<style lang="scss" scoped>
.vue-contextmenu-listWrapper {
  display: none;
  position: fixed;
  z-index: 2000;
  background: #2c2c2c;
  top: 0;
  left: 0;
  border-radius: 8px;
  box-shadow: 0 2px 2px 0 #cccccc;
  font-family: 'Courier New', Courier, monospace;
  font-size: 12px;

  padding-left: 10px !important;
  color: white !important;
}

.vue-contextmenu-listWrapper .context-menu-list {
  position: relative;
  text-decoration: none;
  list-style: none;

  background: #2c2c2c;
  margin: 10px 0;
}

.vue-contextmenu-listWrapper .node-connect {
  div {
    display: inline-block;
    cursor: pointer;
    width: 130px;
    position: relative;
    span {
      position: absolute;
      font-size: 14px;
      margin-left: 16px;
      margin-top: 3px;
    }
    i {
      font-size: 25px;
      border: 1px solid white;
      border-radius: 15px;
      margin-left: 10px;
    }
    img {
      display: inline-block;
      width: 25px;
      height: 25px;
      margin-left: 10px;
    }
  }
}

.context-menu-list:hover {
  color: #6e6e6e;
}

.context-menu-list .has-child {
  position: relative;
  cursor: pointer;
  padding: 5px 10px;
}

.context-menu-list:hover > .has-child > .child-ul-wrapper {
  display: block;
}

.parent-name .icon {
  position: absolute;
  display: block;
  top: 4px;
  right: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 4px solid transparent;

  border-left: 8px solid white;
}

.no-child-btn {
  padding: 5px 10px;
  cursor: pointer;
}

.nav-icon-fontawe {
  position: absolute;
  left: 0;
}

.nav-name-right {
  margin: 0 20px;
  height: 16px;
  line-height: 16px;
  display: block;
}

.no-allow {
  color: #d3cfcf;
  cursor: not-allowed;
}

.btn-wrapper-simple {
  position: relative;
  height: 16px;
  line-height: 16px;
}
</style>
