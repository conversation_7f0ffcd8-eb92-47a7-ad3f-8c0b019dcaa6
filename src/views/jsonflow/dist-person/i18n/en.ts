export default {
	distPerson: {
		index: "#",
		importdistPersonTip: "import DistPerson",
		id: "id",
		code: "code",
		roleId: "roleId",
		roleUserId: "dist Person",
		jobType: "jobType",
		userKey: "userKey",
		createUser: "createUser",
		createTime: "createTime",
		flowInstId: "flowInstId",
		flowKey: "flowKey",
		updateUser: "updateUser",
		updateTime: "updateTime",
		jobName: "new jobName",
		sort: "new sort",
		recRoleId: "recRoleId",
		delRoleId: "delRoleId",
		delStartTime: "delStartTime",
		delEndTime: "delEndTime",
		delStatus: "delStatus",

		inputIdTip: "input id",
		inputCodeTip: "input code",
		inputRoleIdTip: "input roleId",
		inputJobTypeTip: "input jobType",
		inputUserKeyTip: "input userKey",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputFlowInstIdTip: "input flowInstId",
		inputFlowKeyTip: "input flowKey",
		inputUpdateUserTip: "input updateUser",
		inputUpdateTimeTip: "input updateTime",
		inputJobNameTip: "input new jobName",
		inputSortTip: "input new sort",
		inputRecRoleIdTip: "input recRoleId",
		inputDelRoleIdTip: "input delRoleId",
		inputDelStartTimeTip: "input delStartTime",
		inputDelEndTimeTip: "input delEndTime",
		inputDelStatusTip: "input delStatus",

	},
}
