import defaultSettings from "@/settings"
import { useDynamicTitle } from "@/utils/dynamicTitle"
import variables from "@/assets/styles/variables.module.scss"
import { handleThemeStyle } from "@/utils/theme"

const {VITE_PUBLIC_PATH, VITE_HOME_LOGO, VITE_LOGO_LOGO} = import.meta.env
const HOMELOGO = new URL(`../../assets/logo/${VITE_HOME_LOGO}.png`, import.meta.url).href
const LOGINLOGO = new URL(`../../assets/logo/${VITE_LOGO_LOGO}.png`, import.meta.url).href

const { sideTheme, showSettings, topNav, tagsView, fixedHeader, sidebarLogo, dynamicTitle }
  = defaultSettings

const storageSetting
  = JSON.parse(localStorage.getItem(VITE_PUBLIC_PATH + "-layout-setting")) || ""

const useSettingsStore = defineStore("settings", {
	state: () => ({
		systemTitle: defaultSettings.title, // 系统名称
		title: "",
		homeLogoUrl: HOMELOGO,
		loginLogoUrl: LOGINLOGO,
		theme: storageSetting.theme || variables.mainColor,
		sideTheme: storageSetting.sideTheme || sideTheme,
		sideBarBackground:
      sideTheme === "theme-dark" ? variables.menuBackground : variables.menuLightBackground, // 侧边栏背景色
		showSettings: showSettings,
		topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
		tagsView: storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
		fixedHeader:
      storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,
		sidebarLogo:
      storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,
		dynamicTitle:
      storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle,
	}),
	actions: {
		// 修改布局设置
		changeSetting(data) {
			const { key, value } = data
			if (this.hasOwnProperty(key)) {
				this[key] = value
			}
		},
		// 设置网页标题
		setTitle(title) {
			this.title = title
			useDynamicTitle()
		},

		// 处理主题样式
		setTheme(e) {
			this.systemTitle = e.name
			e.themeColor && handleThemeStyle(e.themeColor)
			if (e.sidebarNavBackgroundColor) {
				this.sideBarBackground = e.sidebarNavBackgroundColor
			}
			if (e.homeLogoUrl) {
				this.homeLogoUrl = e.homeLogoUrl
			}
			if (e.loginLogoUrl) {
				this.loginLogoUrl = e.loginLogoUrl
			}
		},
	},
})

export default useSettingsStore
