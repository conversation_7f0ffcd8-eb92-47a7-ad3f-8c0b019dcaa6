<template>
  <div class="layout-padding" v-loading="loading">
    <div class="layout-padding-auto layout-padding-view">
      <form-render
        ref="formCreateRef"
        :curr-flow-form="data.currFlowForm"
        :initFormPermPrint="initFormPermPrint"
      />
    </div>
    <footer v-if="data.submitBtn" class="el-dialog__footer">
      <el-button
        type="info"
        class="cancel-btn border-0"
        plain
        v-if="showCancelBtn"
        @click="handleCancel"
      >
        取消
      </el-button>
      <el-button type="primary" class="draft-btn" plain :loading="loading" @click="handleTempStore">
        保存草稿
      </el-button>
      <el-button type="primary" class="submit-btn" :loading="loading" @click="submitForm">
        提交申请
      </el-button>
    </footer>
  </div>
</template>

<script setup lang="ts" name="FlowApplicationInitiate">
import * as flowApplication from '@/api/order/flow-application'
import { paramsFilter } from '@/flow'
import { validateNull } from '@/utils/validate'
import { deepClone } from '@/utils/index'
import { setPropsNull } from '@/flow/support/common'
import {
  doInitData,
  doInitiateForm,
  doTempStore,
  initFormMethods,
  initJobDataByApp
} from '../index'
import { handleFormStartPerm } from '@/flow/utils/form-perm'
import { currFormIsView } from '@/api/order/order-key-vue'

const FormRender = defineAsyncComponent(() => import('@/flow/components/form-create/render.vue'))
const formCreateRef = ref(null)

const $emit = defineEmits(['handleInitiateOrder', 'success', 'cancel'])
const loading = ref(false)
const props = defineProps({
  currFlowForm: {
    type: Object,
    default: {}
  },
  // 是否展示去掉按钮
  showCancelBtn: {
    type: Boolean,
    default: false
  },
  // 是否展示成功操作后的提示
  showSuccessMsg: {
    type: Boolean,
    default: true
  }
})

const data = reactive({
  // 兼容app端监听currFlowForm
  currFlowForm: {
    type: Object,
    default: {}
  },
  submitBtn: true
})

const formCreateApi = ref(null)

const $route = useRoute()
function initJobData() {
  initJobDataByApp($route, handleGetObj, () => {
    data.currFlowForm = props.currFlowForm
  })
}

function handleGetObj(id) {
  flowApplication.getObj(id).then((resp) => {
    let form = resp.object ? resp.object : {}
    Object.assign(data.currFlowForm, form)
  })
}

async function submitForm() {
  await doInitiateForm(loading, props, data, $route, formCreateRef, $emit, saveInitData)
}

function handleTempStore() {
  doTempStore(loading, data, $route, formCreateRef, $emit, saveInitData, props)
}

const methods = initFormMethods(formCreateRef, data)

async function initFormPermPrint(formInfo) {
  loading.value = true
  // 处理表单权限
  let res = await handleFormStartPerm(
    null,
    null,
    formInfo,
    props.currFlowForm.defFlowId,
    null,
    props.currFlowForm.type
  )
  await currFormIsView(methods, res.elTab, true, res.callback, res.widgetList)
  loading.value = false
  return res.elTab
}

function saveInitData(form) {
  form = paramsFilter(form)
  data.currFlowForm.formData = validateNull(form) ? undefined : form
  let formJson = deepClone(data.currFlowForm)
  formJson.formData = JSON.stringify(formJson.formData)
  formJson.formId = formJson.id
  setPropsNull(formJson, 'id', 'status')
  return formJson
}

async function getFormData() {
  return await doInitData(formCreateRef, saveInitData)
}

function handleCancel() {
  $emit('cancel')
}

// 监听双向绑定
watch(
  () => props.currFlowForm.id,
  () => {
    initJobData()
  }
)

onMounted(() => {
  initJobData()
})

defineExpose({
  formCreateRef,
  getFormData
})
</script>

<style lang="scss" scoped>
.el-dialog__footer {
  margin-top: 10px;
}
</style>
