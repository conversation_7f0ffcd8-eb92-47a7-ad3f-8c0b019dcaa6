<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      class="white-form-box"
      label-width="110px"
      @keyup.enter="handleQuery"
    >
      <el-form-item label="系统名称" prop="sysName">
        <el-input
          v-model="queryParams.sysName"
          placeholder="请输入系统名称"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="接入时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="common-input-width"
          clearable
          :default-time="dateDefaultTime"
        />
      </el-form-item>
      <el-form-item label="接入状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="dict in normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="+dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="责任单位" prop="responsibleUnit">
        <el-input
          v-model="queryParams.responsibleUnit"
          placeholder="请输入责任单位"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="联系人" prop="contacts">
        <el-input
          v-model="queryParams.contacts"
          placeholder="请输入联系人"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" :loading="loading">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery" :loading="loading">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <CustomTable
      ref="customTableRef"
      v-model:show-search="showSearch"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :data="tableData"
      custom
      :loading="loading"
      :total="total"
      @reload="getList"
    >
      <template #actions>
        <el-button
          v-auths="['baseSet:apiManage:add']"
          type="primary"
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
        <el-button
          v-auths="['baseSet:apiManage:export']"
          type="primary"
          plain
          class="is-deep"
          icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </template>
      <vxe-column type="checkbox" width="40" fixed="left" />
      <vxe-column type="seq" width="60" />
      <vxe-column field="id" title="系统ID" min-width="150" show-overflow />
      <vxe-column field="sysName" title="系统名称" min-width="160" show-overflow />
      <vxe-column field="acceptTime" title="接入时间" min-width="170" />
      <vxe-column field="responsibleUnit" title="责任单位" min-width="140" />
      <vxe-column field="contacts" title="联系人" min-width="60" show-overflow />
      <vxe-column field="phoneNumber" title="联系电话" min-width="110" />
      <vxe-column field="status" title="接入状态" min-width="60">
        <template #default="{ row }">
          <dict-tag :options="normal_disable" :value="row.status" />
        </template>
      </vxe-column>
      <vxe-column title="操作" field="opt" fixed="right" min-width="150">
        <template #default="{ row }">
          <TableColOptBtn
            :key="Math.random()"
            :buttons="[
              {
                text: '修改',
                click: () => handleUpdate(row),
                permission: ['baseSet:apiManage:edit'],
              },
              {
                text: +row.status === 1 ? '启用' : '停用',
                click: () => handleUpdateStatus(row),
                permission: ['baseSet:apiManage:edit'],
              },
              {
                text: '查看',
                click: () => viewDetails(row),
              },
              {
                text: '停用/启用日志',
                click: () => viewLogs(row),
                permission: ['baseSet:apiManage:log'],
              },
            ]"
          />
        </template>
      </vxe-column>
    </CustomTable>

    <!-- 新增修改弹窗 -->
    <EditApiModal
      ref="apiModalRef"
      :normal_disable="normal_disable"
      @refresh="handleQuery"
      @change="getList"
    />

    <ChangeLogsModal ref="changeLogsRef" :normal_disable="normal_disable" />
  </div>
</template>

<script setup name="ApiManage">
import { interfacePage, delInterface, updateInterface } from "@/api/ess/baseSet/apiManage"
import { essPrefix } from "@/config/constant"
import EditApiModal from "./components/EditApiModal.vue"
import ChangeLogsModal from "./components/ChangeLogsModal.vue"
const { proxy } = getCurrentInstance()
const { normal_disable } = proxy.useDict("normal_disable")

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) {
    return customTableRef.value.getCheckboxRecords()
  }
  return []
})

const apiModalRef = ref(null) // 新增/编辑弹窗
const changeLogsRef = ref(null) // 日志弹窗

const dateRange = ref([])
const queryParams = reactive({
  sysName: null,
  status: null,
  responsibleUnit: null,
  contacts: null,
  limit: 10,
  page: 1,
})

/** 查询列表 */
function getList() {
  loading.value = true
  queryParams.acceptStartTime = dateRange.value?.[0] || null
  queryParams.acceptEndTime = dateRange.value?.[1] || null
  interfacePage(queryParams)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  dateRange.value = []
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  apiModalRef.value.open()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  apiModalRef.value.open(row)
}

// 查看
function viewDetails(row) {
  apiModalRef.value.open(row, true)
}

// 停用/启用日志
function viewLogs(row) {
  changeLogsRef.value.open(row)
}

// 修改状态
function handleUpdateStatus(row) {
  let text = +row.status === 1 ? "启用" : "停用" // 字典里面1是停用 0是启用
  proxy.$modal
    .confirm(`确认要${text}${row.sysName}吗?`)
    .then(function () {
      let reqData = { ...row, status: +row.status === 0 ? 1 : 0 }
      return updateInterface(reqData)
    })
    .then((res) => {
      if (+res.code === 200) {
        getList()
        proxy.$modal.msgSuccess(text + "成功")
      }
    })
    .catch(function () {})
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id || selections.value.map((item) => item.id)
  const name = row.sysName || selections.value.map((item) => item.sysName)
  proxy.$modal
    .confirm('是否确认删除系统名称"' + name + '"的数据项？')
    .then(function () {
      return delInterface(id)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    essPrefix + "/interface/export",
    {
      ...queryParams,
      ids: selections.value.map((item) => item.id),
    },
    `接口_${new Date().getTime()}.xlsx`
  )
}

getList()
</script>
