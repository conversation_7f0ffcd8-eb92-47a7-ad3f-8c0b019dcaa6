<!--
 * @Author: ljn
 * @Date: 2025-02-07 11:41:29
 * @LastEditors: ljn
 * @LastEditTime: 2025-04-17 09:20:51
 * @Description: 切换账号角色组件
-->
<template>
  <div v-loading="loading" class="account-role">
    <div class="account-switch">
      <div class="switch-dept">
        <div
            v-for="item in deptList"
            :key="item.deptId"
            class="dept-item"
            :class="{ active: item.deptId == deptId }"
            @click="handleDeptChange(item)"
        >
          <div class="dept-item__left">
            <div class="dept-name">{{ item.deptName }}</div>
            <div class="role-list">
              <span v-for="role in item.detailsList" :key="role.roleId">
                {{ role.roleName }}
              </span>
            </div>
          </div>
          <div class="checked-icon">
            <el-icon v-show="item.deptId == deptId">
              <CircleCheckFilled/>
            </el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-button v-if="deptList.length > 1" type="primary" @click="handleSave" class="btn-save">
    保存
  </el-button>
</template>

<script setup name="AccountRole">
import { ElLoading } from "element-plus"
import { selectZsRoleDeptInfos, zsSwitchRole } from "@/api/system/user"
import useUserStore from "@/store/modules/user"
import usePermissionStore from "@/store/modules/permission"

const { userInfo, loginDeptInfo } = useUserStore()
const deptId = ref(null)
const loading = ref(false)
const deptList = ref([])

const roleList = computed(() => {
  return deptList.value.find((item) => item.deptId === deptId.value)?.detailsList || []
})

// 点击部门
function handleDeptChange(item) {
  deptId.value = item.deptId
}

// 获取角色部门列表
async function getList() {
  try {
    loading.value = false
    let res = await selectZsRoleDeptInfos(userInfo.userName)
    deptList.value = +res?.code == 200 ? res.object : []
    deptId.value = loginDeptInfo.loginDeptId || null
  } finally {
  }
}

// 保存
async function handleSave() {
  try {
    loading.value = true
    const loadingMask = ElLoading.service({
      lock: true,
      text: "正在切换角色部门...",
      background: "rgba(255, 255, 255, 0.7)",
    })
    let res = await zsSwitchRole({ userId: userInfo.userId, switchDeptId: deptId.value })
    useUserStore().updateToken(res.object.access_token)
    loading.value = false
    await useUserStore().getInfo()
    await usePermissionStore().generateRoutes()
    loadingMask.close()
    location.reload()
  } catch (error) {
    loadingMask.close()
  } finally {
    loading.value = false
  }
}

getList()
</script>

<style scoped lang="scss">

.account-switch {
  box-sizing: border-box;
  width: 700px;

  .switch-dept {
    margin-bottom: 15px;

    .dept-item {
      background: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 12px;
      border: 2px solid #d2f1e8;
      margin-bottom: 15px;
      padding: 15px 15px 30px 15px;
      cursor: pointer;

      .checked-icon {
        flex-shrink: 0;
      }

      .dept-name {
        font-size: 16px;
        font-weight: bold;
      }

      .role-list {
        span {
          color: #24a87e;
          background: #e0f3ed;
          border-radius: 4px;
          padding: 4px 10px;
          margin-top: 15px;
          margin-right: 10px;
          display: inline-block;
          font-size: 14px;
        }
      }

      .checked-icon {
        border: 1.5px solid #d2f1e8;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-icon {
          color: var(--el-color-primary);
          font-size: 22px;
        }
      }

      &.active,
      &:hover {
        background: url("@/assets/images/account/bg-role-act.png") no-repeat center;
        background-size: 101% 105%;
        border-color: var(--el-color-primary);

        .role-list {
          span {
            background: var(--el-color-primary);
            color: #fff;
          }
        }

        .checked-icon {
          border-color: var(--el-color-primary);
        }
      }
    }
  }
}

.dept-radio {
  display: flex;
  justify-content: space-between;

  .el-button {
    flex-shrink: 0;
    width: 90px;
  }
}

.role-list {
  border-radius: 8px;

  :deep(.el-radio) {
    height: 34px;
    background: #fff;
    padding: 0 12px;
    margin-right: 16px;

    .el-radio__input {
      display: none;
    }

    .el-radio__label {
      padding-left: 0;
    }

    &.is-bordered.is-disabled {
      border-color: #c4c5c6;
    }

    &.is-checked {
      background: var(--el-color-primary);

      .el-radio__label {
        color: #fff;
      }
    }
  }
}
.btn-save{
  @apply w-120px mt-40px;
  position: absolute;
  margin-left: -20px;
}
</style>
