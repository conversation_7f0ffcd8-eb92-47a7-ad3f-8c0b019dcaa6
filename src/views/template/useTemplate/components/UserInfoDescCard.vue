<template>
  <div class="login-user-info">
    <div class="flex-y-center font-600">
      <img src="@/assets/images/icon/green-user.png" alt="" class="mr-7px w-15px" />
      {{ currentUserInfo.nickName }}
      {{ currentUserInfo.workNumber ? `(${currentUserInfo.workNumber})` : "" }}
    </div>
    <div class="flex-y-center text-14px color-#38383A">
      <span v-if="currentUserInfo.dept"
        >所在单位：{{ currentUserInfo.dept.deptName }}</span
      >
      <el-divider direction="vertical"></el-divider>
      <span>联系方式：{{ replacePhone(currentUserInfo.phonenumber) }}</span>
    </div>
  </div>
</template>

<script setup>
import { replacePhone } from "@/utils/index"

import useUserStore from "@/store/modules/user"
const userInfo = useUserStore().userInfo

const props = defineProps({
  applyUser: {
    type: Object,
    default: null,
  },
})

const currentUserInfo = computed(() => {
  return props.applyUser ? props.applyUser?.user : userInfo
})
</script>

<style scoped lang="scss">
.login-user-info {
  background: linear-gradient(
    87deg,
    rgba(36, 168, 126, 0.09) 0%,
    rgba(36, 168, 126, 0) 100%
  );
  border-radius: 5px;
  padding: 8px 20px;
  line-height: 26px;
  width: 100%;
}
</style>
