<template>
	<div class="user-base">
		<div class="user-avatar-name">
			<UserAvatar class="user-avatar" />
			<div class="user-name">
				<span>{{ info.user.nickName }}</span>
				<label>{{ selectDictLabel(sys_user_sex, info.user.sex) }}</label>
			</div>
		</div>
		<div class="list-group font-size-14px" xxl="font-size-16px">
			<p class="list-group-item">
				<span>用户名称</span>
				{{ info.user.userName }}
			</p>
			<p class="list-group-item">
				<span>手机号码</span>
				{{ info.user.phonenumber || '-' }}
			</p>
			<p class="list-group-item">
				<span>用户邮箱</span>
				{{ info.user.email || '-' }}
			</p>
			<p class="list-group-item">
				<span>所属部门</span>
				{{ loginDeptInfo.loginDeptName || info.user.dept && info.user.dept.deptName || '-' }}{{ info.postGroup ? ` / ${info.postGroup}` : '' }}
			</p>
			<p class="list-group-item">
				<span>所属角色</span>
				{{ info.loginRoleGroup }}
			</p>
		</div>
	</div>
</template>

<script setup class="UserBaseInfo">
import UserAvatar from "./UserAvatar"
import useUserStore from "@/store/modules/user"

const { proxy } = getCurrentInstance()
const { sys_user_sex } = proxy.useDict("sys_user_sex")

const props = defineProps({
	info: {
		type: Object,
		required: true,
    default: () => ({
      user: {},
      roleGroup: {},
      loginRoleGroup: {},
      postGroup: {},
    })
	},
})
const { loginDeptInfo } = useUserStore()
</script>

<style scoped lang="scss">
.user-base {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.user-avatar-name {
  width: 310px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 180px;
  border-right: 1px dashed rgba(112, 112, 112, 0.4);
  margin-right: 20px;
  .user-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 15px;
    img{
      width: 100%;
      height: 100%;
    }
  }
  .user-name {
    position: relative;
    span {
      font-weight: bold;
      font-size: 18px;
      max-width: 200px;
      display: inline-block;
      @apply text-ellipsis;
    }
    label {
      font-size: 14px;
      position: absolute;
      right: -20px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.list-group {
  flex: auto;
  margin-left: 8%;
  .list-group-item {
    margin-bottom: 20px;
    span:first-child {
      @apply dot color-#6b6b6b mr-4px;
    }
  }
}

</style>
