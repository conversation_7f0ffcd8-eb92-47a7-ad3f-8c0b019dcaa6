import { DIC_PROP } from '@/flow/support/dict-prop'
import { PROP_CONST } from '@/flow/support/prop-const'
import { CommonNodeType, HighNodeType } from '@/flow/designer/config/type'
import { notifyLeft } from '@/flow'
import { useMessageBox } from '@/hooks/message'

/**
 * 常用工具类
 *
 * <AUTHOR>
 */

// 反解析名称时未加载该常量
function revParseWhoseLeaderName(data, whoseLeaderName) {
    if (data.whoseLeader === PROP_CONST.FLOW_METHOD.whoseLeader.userId) {
        data.whoseLeaderName = PROP_CONST.FLOW_METHOD.whoseLeader.username
    } else {
        // data.whoseLeaderName = dicData.users.find(f => f.userId === data.whoseLeader).username;
        data.whoseLeaderName = whoseLeaderName; // 把远程掉用改成取值
    }
}

export function revParseUserKeyValName(props, data, dicData, methods) {
    if (methods.validateCurrSelectDefJob) {
        if (!methods.validateCurrSelectDefJob()) return;
    }
    let userKeyVal = props.currSelect.attributes.attrs.cdata.defJob.userKeyVal;
    let { whoseLeaderName } = props.currSelect.attributes.attrs.cdata.defJob
    if (!userKeyVal) return;
    let userKeyValFrom;
    if (userKeyVal === PROP_CONST.VAR_KEY_VAL.order + 'createUser') {
        userKeyValFrom = DIC_PROP.FLOW_METHOD_TYPE[0].value
        data.activeKey = 'flow-method'
    } else if (userKeyVal.indexOf('getUserDeptLeaderId') !== -1) {
        userKeyValFrom = DIC_PROP.FLOW_METHOD_TYPE[1].value
        data.whoseLeader = userKeyVal.substring(userKeyVal.indexOf('(Long#') + 6, userKeyVal.indexOf(',Integer#'))
        data.leaderLevel = parseInt(userKeyVal.substring(userKeyVal.indexOf(',Integer#') + 9, userKeyVal.indexOf(',String#')))
        data.levelExtract = userKeyVal.substring(userKeyVal.indexOf(',String#') + 8, userKeyVal.indexOf(')'))
        revParseWhoseLeaderName(data, whoseLeaderName); // 把远程掉用改成取值
        data.activeKey = 'flow-method'
    } else if (userKeyVal.indexOf('listUserDeptMultiLeaderId') !== -1) {
        userKeyValFrom = DIC_PROP.FLOW_METHOD_TYPE[2].value
        let lastIndex = userKeyVal.indexOf('String#') - 1;
        data.whoseLeader = userKeyVal.substring(userKeyVal.indexOf('(Long#') + 6, lastIndex)
        userKeyVal = userKeyVal.substr(lastIndex)
        revParseWhoseLeaderName(data, whoseLeaderName); // 把远程掉用改成取值
        lastIndex = userKeyVal.indexOf('Integer#') - 1;
        data.auditEndpoint = userKeyVal.substring(userKeyVal.indexOf('String#') + 7, lastIndex)
        userKeyVal = userKeyVal.substr(lastIndex)
        if (data.auditEndpoint === '1') data.leaderLevel = parseInt(leaderLevel)
        lastIndex = userKeyVal.lastIndexOf('String#') - 1;
        data.seqAuditSort = userKeyVal.substring(userKeyVal.indexOf('String#') + 7, lastIndex)
        userKeyVal = userKeyVal.substr(lastIndex)
        data.levelExtract = userKeyVal.substring(userKeyVal.indexOf(')') -1, userKeyVal.indexOf(')'))
        data.activeKey = 'flow-method'
    } else if (userKeyVal.indexOf('getDeptLeaderId') !== -1) {
        userKeyValFrom = DIC_PROP.FLOW_METHOD_TYPE[3].value
        data.appointDeptId = userKeyVal.substring(userKeyVal.indexOf('(Long#') + 6, userKeyVal.indexOf(',String#'))
        data.levelExtract = userKeyVal.substring(userKeyVal.indexOf(',String#') + 8, userKeyVal.indexOf(')'))
    } else if (props.currSelect.attributes.attrs.cdata.defJob.userKeyValName) {
        let find = DIC_PROP.FLOW_METHOD_TYPE.find(f => f.label === props.currSelect.attributes.attrs.cdata.defJob.userKeyValName);
        // 再次编辑时优先显示为专业模式
        if (find) {
            userKeyValFrom = DIC_PROP.FLOW_METHOD_TYPE[4].value
            data.userKeyVal = userKeyVal
            if (methods.handleUserKeyValFrom) methods.handleUserKeyValFrom(userKeyValFrom)
            data.activeKey = 'flow-method'
        }
    }
    if (userKeyValFrom) {
        data.userKeyValFrom = userKeyValFrom
    } else {
        data.activeKey = 'flow-rule'
    }
}

export function parseUserKeyValName(props, data, methods) {
     if (methods.validateCurrSelectDefJob) {
        if (!methods.validateCurrSelectDefJob()) return;
    }
    let userKeyVal;
    if (data.userKeyValFrom === '0') {
        userKeyVal = PROP_CONST.VAR_KEY_VAL.order + 'createUser'
    } else if (data.userKeyValFrom === '1') {
        userKeyVal = '#distActorServiceImpl.getUserDeptLeaderId(Long#'+ data.whoseLeader +',Integer#'+ data.leaderLevel +',String#'+ data.levelExtract +')'
    } else if (data.userKeyValFrom === '2') {
        let leaderLevel = 'NULL'
        if (data.auditEndpoint === '1') leaderLevel = data.leaderLevel
        let seqAuditSort = 'NULL'
        if (data.seqAuditSort) seqAuditSort = data.seqAuditSort
        userKeyVal = '#distActorServiceImpl.listUserDeptMultiLeaderId(Long#'+ data.whoseLeader +',String#'+ data.auditEndpoint +',Integer#'+ leaderLevel +',String#'+ seqAuditSort +',String#'+ data.levelExtract +')'
    } else if (data.userKeyValFrom === '3') {
        userKeyVal = '#distActorServiceImpl.getDeptLeaderId(Long#'+ data.appointDeptId +',String#'+ data.levelExtract +')'
    } else if (data.userKeyValFrom === '4') {
        userKeyVal = data.userKeyVal
    }
    if (data.userKeyValFrom === '1' || data.userKeyValFrom === '2') {
        if (!data.whoseLeader) {
            if (methods.$message) methods.$message('whoseLeader')
            return
        }
    }
    if (data.userKeyValFrom === '3') {
        if (!data.appointDeptId) {
            if (methods.$message) methods.$message('appointDeptId')
            return
        }
    }
    props.currSelect.attributes.attrs.cdata.defJob.whoseLeaderName = data.whoseLeaderName;
    props.currSelect.attributes.attrs.cdata.defJob.userKeyVal = userKeyVal;
    props.currSelect.attributes.attrs.cdata.defJob.userKeyValName = DIC_PROP.FLOW_METHOD_TYPE.find(f => f.value === data.userKeyValFrom).label;

    props.currSelect.attributes.attrs.cdata.defJob.valType = DIC_PROP.VAL_TYPE[4].value
    // 清空其他参数
    props.currSelect.attributes.attrs.cdata.defJob.condGroups = []
    props.currSelect.attributes.attrs.cdata.defJob.httpParams = []
    props.currSelect.attributes.attrs.cdata.defJob.httpMethod = null
}

export function handleLinkFlowNodeIds(data, props) {
    data.toFlowNodeIds = []
    data.fromFlowNodeIds = []
    let models = window._jfGraph.getElements();
    data.fromFlowNodeId = props.currSelect.attributes.source.id
    data.toFlowNodeId = props.currSelect.attributes.target.id
    // 修正拖拽连线箭头更改目标节点
    props.currSelect.attributes.attrs.cdata.attrs.fromFlowNodeId = data.fromFlowNodeId
    props.currSelect.attributes.attrs.cdata.attrs.toFlowNodeId =  data.toFlowNodeId
    models.forEach(each => {
        if (!validateNodeType(each)) return
        let id = each.id
        let nodeName = each.attributes.attrs.label.text
        if (id !== props.currSelect.attributes.target.id) {
            data.fromFlowNodeIds.push({id, nodeName})
        }
        if (id !== props.currSelect.attributes.source.id) {
            data.toFlowNodeIds.push({id, nodeName})
        }
    })
}

export function changeLinkFlowNodeIds(data, props, methods?, $emit?) {
    useMessageBox()
        .confirm('是否确认修改连线的' + (data.modifyPointType === '0' ? '起点?' : '终点?'))
        .then(() => {
            doLinkFlowNodeIds(data, props, methods, $emit)
        })
}

function doLinkFlowNodeIds(data, props, methods?, $emit?) {
    if (data.modifyPointType === '0') {
        props.currSelect.attributes.attrs.cdata.attrs.fromFlowNodeId = data.fromFlowNodeId
        props.currSelect.set('source', { id: data.fromFlowNodeId });
    } else {
        props.currSelect.attributes.attrs.cdata.attrs.toFlowNodeId = data.toFlowNodeId
        props.currSelect.set('target', { id: data.toFlowNodeId });
    }
    if (methods) methods.handleLinkFlowNodeIds()
    if (window._flowConfig.globalConfig.isSimpleMode === '1') window._jfOperate.layout()
    else notifyLeft('专业模式不会自动调整连线轨迹，有必要时请手动调整', 'warning', 3000)
    if ($emit) $emit("hideAttrConfig", false, '1');
}

export function validateNodeType(currSelect, methods?, isVirtual?) {
    if (methods && !methods.validateCurrSelectAttrs()) return false;
    let type = currSelect.attributes.attrs.cdata.type;
    let noVirtual = type === CommonNodeType.START || type === CommonNodeType.END || type === CommonNodeType.SERIAL || type === CommonNodeType.PARALLEL;
    if (!isVirtual) return noVirtual
    return noVirtual || type === HighNodeType.VIRTUAL
}
