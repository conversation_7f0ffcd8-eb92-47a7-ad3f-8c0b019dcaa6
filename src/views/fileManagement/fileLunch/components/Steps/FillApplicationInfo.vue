<template>
  <div class="fill-application-info">
    <el-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-width="160px"
      :inline="false"
      class="max-w-900px mt-10px"
      v-loading="loading"
      :disabled="formDisabled"
    >
      <!-- 标题字段 - 仅在需要审批时显示 -->
      <template v-if="isApprovalRequired">
        <el-form-item label="标题:" prop="flowTitle">
          <el-input
            v-model="form.flowTitle"
            :maxlength="20"
            show-word-limit
            clearable
            placeholder="请输入标题"
          />
        </el-form-item>
      </template>

      <!-- 发起原由 -->
      <el-form-item label="发起原由:" prop="applyReason">
        <el-input
          type="textarea"
          v-model="form.applyReason"
          :maxlength="1000"
          show-word-limit
          clearable
          placeholder="请输入发起原由"
          :rows="4"
        />
      </el-form-item>

      <!-- 文件所属业务线 -->
      <el-form-item label="文件所属业务线:" prop="businessLineId">
        <BusinessSelect
          v-model="form.businessLineId"
          v-model:ascription="form.ascription"
          type="radio"
          :showText="formDisabled"
        />
      </el-form-item>

      <!-- 申请附件 -->
      <el-form-item label="申请附件:" prop="filePaths">
        <div class="file-upload-view">
          <div
            v-if="currentFileName"
            class="file-view"
            :class="{ 'cursor-pointer': formDisabled }"
            @click="handlePreviewFile"
          >
            <FileIcon :fileName="currentFileName"></FileIcon>
            <span v-tooltip>{{ currentFileName }}</span>
          </div>
          <template v-if="!formDisabled">
            <el-upload
              ref="uploadRef"
              :action="uploadBaseUrl"
              :file-list="fileList"
              :limit="1"
              :accept="acceptedFileTypes"
              :on-exceed="handleExceed"
              :auto-upload="false"
              :show-file-list="false"
              :headers="uploadHeaders"
              :on-change="handleFileChange"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
            >
              <template #trigger>
                <el-button
                  type="primary"
                  :plain="currentFileName ? true : false"
                  class="has-line"
                >
                  {{ currentFileName ? "重新上传" : "上传文件" }}
                </el-button>
              </template>
            </el-upload>
            <el-tooltip placement="right-start">
              <template #content>
                <div class="max-w-[300px]">
                  支持.pdf/.doc/.docx/.jpg/.png/.xls.xlsx/.html
                  文件，图片类型(png/jpg/jpeg)限制大小为5M以下,
                  PDF/word/excel等其他格式限制大小为60M以下
                </div>
              </template>
              <svg-icon
                name="local-question"
                class="ml-5px color-#AAAAAA cursor-pointer"
                :size="18"
              />
            </el-tooltip>
          </template>
          <el-form v-if="formDisabled">
            <el-button type="primary" plain @click="handlePreviewFile">
              文件预览
            </el-button>
          </el-form>
        </div>
      </el-form-item>

      <!-- 是否涉及发起方/签署方填写 -->
      <el-form-item
        label="是否涉及发起方/签署方填写:"
        prop="fillFlag"
        label-width="200px"
      >
        <el-radio-group v-model="form.fillFlag">
          <el-radio value="0">是</el-radio>
          <el-radio value="1">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 盖章后电子文件名称 -->
      <el-form-item label="盖章后电子文件名称:" prop="flowName">
        <el-input
          v-model="form.flowName"
          clearable
          show-word-limit
          placeholder="请输入盖章后电子文件名称"
          :class="{
            'w-85%!': showSetFile,
          }"
        />
        <el-button
          type="primary"
          link
          @click="setFileName"
          v-show="showSetFile"
          class="ml-5px"
        >
          使用附件名
        </el-button>
      </el-form-item>

      <!-- 签署方设置 -->
      <el-form-item label="签署方:" prop="approveDtoList" v-if="!formDisabled">
        <el-button
          type="primary"
          @click="handleSetSignatory"
          :disabled="!currentFileName"
        >
          文件预览并设置签署方
        </el-button>
      </el-form-item>

      <!-- 签署方相关配置 -->
      <template v-if="form.approveDtoList.length">
        <el-form-item label="签署顺序:" prop="unordered">
          <el-radio v-model="form.unordered" label="无序" :value="true" />
          <el-radio v-model="form.unordered" label="有序" :value="false" />
        </el-form-item>

        <el-form-item :label="formDisabled ? '签署方' : ''" prop="approveDtoList">
          <SignatoryTable :unordered="form.unordered" v-model="form.approveDtoList" />
        </el-form-item>

        <!-- 签署截止时间 - 审批流程 -->
        <el-form-item v-if="isApprovalRequired" label="签署截止时间:" prop="deadline">
          审批通过后
          <el-input-number v-model="form.deadline" :min="1" class="ml-5px" />
          <span class="ml-5px">天</span>
        </el-form-item>

        <!-- 签署截止时间 - 直接签署 -->
        <el-form-item v-else label="签署截止时间:" prop="deadlineTime">
          <el-date-picker
            v-model="form.deadlineTime"
            placeholder="请选择签署截止时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            type="datetime"
            :disabledDate="disabledDateFn"
          />
        </el-form-item>

        <!-- 申请人信息 -->
        <el-form-item label="申请人信息:" prop="user" v-if="!currJob">
          <UserInfoDescCard :applyUser="form.loginUser" />
        </el-form-item>
      </template>
    </el-form>

    <!-- 模版预览并设置签署方 -->
    <SetSignatoryModal
      ref="setSignatoryRef"
      :fileInfo="form"
      :fileRaw="fileRaw"
      :ascription="form.ascription"
      @confirm="handleSignatoryConfirm"
    />
    <!-- 模版预览并设置签署方 -->
    <FilePreviewModal
      ref="filePreviewRef"
      v-if="formDisabled"
      :fileInfo="form"
      :fileRaw="fileRaw"
    />
  </div>
</template>

<script setup>
import useOtherStore from "@/store/modules/other"
import { replacePhone, generateUUID, getFileName } from "@/utils/index"
import { getToken } from "@/utils/auth"
import { genFileId } from "element-plus"
import { uploadBaseUrl } from "@/config/constant"
import { queryFormDataByOrderId } from "@/api/order/run-application"
import { getDraftPreviewUrlAPI } from "@/api/ess/fileManagement/file"
import { rule } from "@/utils/validate"

// 异步组件导入
const FileIcon = defineAsyncComponent(() => import("@/components/SvgIcon/FileIcon.vue"))
const SetSignatoryModal = defineAsyncComponent(() => import("../SetSignatoryModal.vue"))
const FilePreviewModal = defineAsyncComponent(() => import("../FilePreviewModal.vue"))
const SignatoryTable = defineAsyncComponent(() => import("../SignatoryTable.vue"))
const UserInfoDescCard = defineAsyncComponent(() =>
  import("@/views/template/useTemplate/components/UserInfoDescCard.vue")
)
const BusinessSelect = defineAsyncComponent(() =>
  import("@/views/components/BusinessSelect.vue")
)

// 组件实例
const { proxy } = getCurrentInstance()

// Props 定义
const props = defineProps({
  draftForm: {
    type: Object,
    default: () => ({}),
  },
  flowFlag: {
    type: Number,
    default: 0,
  },
  currJob: {
    type: Object,
    default: null,
  },
})

// 响应式数据
const loading = ref(false)
const formRef = ref(null)
const uploadRef = ref(null)
const setSignatoryRef = ref(null)
const fileRaw = ref(null) // 上传的文件内容
const fileList = ref([]) // 上传的文件内容

const formDisabled = computed(() => !!props.currJob)
const disabledDateFn = (time) => {
  // 获取今天的日期（去掉时间部分）
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // 将传入的时间也去掉时间部分进行比较
  const inputDate = new Date(time)
  inputDate.setHours(0, 0, 0, 0)

  // 如果输入日期小于今天（不包含今天），则禁用
  return inputDate.getTime() < today.getTime()
}

// 计算属性
const isApprovalRequired = computed(() => props.flowFlag === 0) // 需要审批
const currentFileName = computed(() => {
  if (fileRaw?.value?.name) {
    return fileRaw.value.name
  }
  if (form.value.filePaths?.length) {
    const fileName = getFileName(form.value.filePaths[0])
    return fileName
  }
  return ""
})

// 表单数据
const form = ref({
  flowTitle: null,
  applyReason: null,
  flowName: null,
  deadline: null,
  deadlineTime: null,
  unordered: true, // 默认无序
  approveDtoList: [],
  filePaths: [],
  flowFlag: props.flowFlag,
  businessLineId: null,
  ascription: null,
  fillFlag: 0,
})

// 文件上传相关配置
const acceptedFileTypes = ".pdf,.doc,.docx,.jpg,.png,.xls,.xlsx,.html,.jpeg"
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${getToken()}`,
}))

// 文件大小限制常量
const FILE_SIZE_LIMITS = {
  IMAGE: 5 * 1024 * 1024, // 5MB
  DOCUMENT: 60 * 1024 * 1024, // 60MB
}

// 监听草稿表单变化
watch(
  () => props.draftForm,
  (draftData) => {
    if (!draftData.id) return

    const flowUserRefs = (draftData.flowUserRefs || []).map((item) => ({
      ...item,
      id: item.id || generateUUID(),
      approveName: item.approveName || item.userName,
    }))

    form.value = {
      ...draftData,
      deadline: draftData.signTmpLimitDay ? +draftData.signTmpLimitDay : null,
      deadlineTime: draftData.signLimitTime,
      approveDtoList: flowUserRefs,
    }
  },
  { immediate: true, deep: true }
)

// 表单验证规则
const validatorFile = (rule, value, callback) => {
  if (!form.value.filePaths?.length && !fileRaw.value?.name) {
    callback(new Error("请上传申请附件"))
    return
  }
  callback()
}
const validateDeadlineTime = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请输入截止时间"))
    return
  }

  const now = new Date()
  const selectedTime = new Date(value)

  if (selectedTime.getTime() <= now.getTime()) {
    callback(new Error("截止时间必须晚于当前时间"))
  } else {
    callback()
  }
}

const rules = ref({
  flowTitle: [
    { required: true, message: "请输入标题", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" },
  ],
  applyReason: [{ required: true, message: "请输入发起原由", trigger: "blur" }],
  businessLineId: [
    { required: true, message: "请选择文件所属业务线", trigger: "change" },
  ],
  flowName: [
    { required: true, message: "请输入文件名称", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" },
  ],
  deadline: [{ required: true, message: "请输入截止时间", trigger: "change" }],
  deadlineTime: [{ required: true, validator: validateDeadlineTime, trigger: "change" }],
  approveDtoList: [{ required: true, message: "请选择签署方", trigger: "change" }],
  filePaths: [
    {
      required: true,
      validator: validatorFile,
      message: "请上传申请附件",
      trigger: "change",
    },
  ],
})

const showSetFile = computed(() => {
  return (
    currentFileName.value &&
    form.value.flowName !== currentFileName.value?.split(".")?.[0] &&
    !props.currJob
  )
})

// 流程回显
async function initForm() {
  if (props.currJob?.orderId) {
    loading.value = true
    const res = await queryFormDataByOrderId(props.currJob.orderId)
    const formData = res.object ? res.object : {}
    const previewUrl = await getDraftPreviewUrlAPI(res.object.flowId) // 文件预览地址
    formData.previewUrl = previewUrl.object
    Object.assign(form.value, formData)
    loading.value = false
  }
  const getFileData = useOtherStore().indexFileData
  if (getFileData) {
    fileRaw.value = getFileData
    fileList.value = [getFileData]
  }
}

// ------------------文件上传相关start------------------
// 文件上传相关方法
const uploadPromise = {
  resolve: null,
  reject: null,
}

/**
 * 处理文件超出限制
 */
const handleExceed = (files) => {
  uploadRef.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  uploadRef.value.handleStart(file)
}

/**
 * 验证文件类型和大小
 */
const validateFile = (file) => {
  const allowedTypes = acceptedFileTypes
    .split(",")
    .map((type) => type.replace(".", ""))
    .filter(Boolean)

  const fileType = file.name.split(".").pop().toLowerCase()
  const isImage = ["jpg", "png", "jpeg"].includes(fileType)

  if (!allowedTypes.includes(fileType)) {
    proxy.$modal.msgError("不支持的文件格式，请上传指定类型文件")
    return false
  }

  const maxSize = isImage ? FILE_SIZE_LIMITS.IMAGE : FILE_SIZE_LIMITS.DOCUMENT
  if (file.size > maxSize) {
    const sizeText = isImage ? "5M" : "60M"
    proxy.$modal.msgError(`文件大小不能超过${sizeText}`)
    return false
  }

  return true
}

/**
 * 处理文件选择变化
 */
const handleFileChange = (file) => {
  if (!validateFile(file)) {
    uploadRef.value.clearFiles()
    return
  }
  if (file.status === "ready") {
    if (!form.value.flowName) {
      form.value.flowName = file.name.split(".")[0]
    }
  }
  fileRaw.value = file
}

/**
 * 处理文件上传成功
 */
const handleUploadSuccess = (response) => {
  if (+response.code !== 200) {
    handleUploadError(res)
    return
  }
  form.value.filePaths = [response.object.relativeUrl]
  if (uploadPromise.resolve) {
    uploadPromise.resolve()
    uploadPromise.resolve = null
    uploadPromise.reject = null
  }
}

/**
 * 处理文件上传失败
 */
const handleUploadError = (error) => {
  proxy.$modal.alertWarning(error.object)
  if (uploadPromise.reject) {
    uploadPromise.reject(error)
    uploadPromise.resolve = null
    uploadPromise.reject = null
  }
}

/**
 * 提交文件上传
 */
const submitFile = () => {
  return new Promise((resolve, reject) => {
    uploadPromise.resolve = resolve
    uploadPromise.reject = reject
    uploadRef.value.submit()
  })
}

// 文件预览
const handlePreviewFile = () => {
  if (formDisabled.value) {
    // proxy.$refs.filePreviewRef.open()
    window.open("/fileIframe/preview?flowId=" + form.value.flowId)
  }
}
// 使用附件名称
function setFileName() {
  form.value.flowName = currentFileName.value.split(".")[0]
}
// ------------------文件上传相关end------------------

// 签署方相关方法
/**
 * 处理签署方确认
 */
const handleSignatoryConfirm = (data) => {
  const signatories = data.map((item, index) => ({
    ...item,
    id: item.id || generateUUID(),
    signOrder: index + 1,
  }))

  form.value.approveDtoList = signatories
  formRef.value.clearValidate("approveDtoList")
}

/**
 * 设置签署方
 */
const handleSetSignatory = () => {
  if (!form.value.businessLineId) {
    proxy.$modal.msgWarning("请先选择文件所属业务线!")
    return
  }
  setSignatoryRef.value.open(form.value.approveDtoList)
}

// 表单验证方法
const validForm = () => {
  return new Promise(async (resolve, reject) => {
    try {
      formRef.value.validate(async (valid, val) => {
        if (valid) {
          // 如果有文件但未上传，先上传文件
          if (fileRaw.value?.raw && fileRaw.value?.status === "ready") {
            await submitFile()
          }
          resolve(form.value)
        } else {
          proxy.$modal.msgWarning("请完善表单信息！")
          setTimeout(() => {
            //延时器的作用：避免‘is-error’在以下操作将要执行时还未渲染上
            var obj = Object.keys(val)
            formRef.value.scrollToField(obj[0])
            return false
          }, 100)
          reject(valid)
        }
      })
    } catch (error) {
      reject(error)
    }
  })
}

initForm()
// 暴露方法
defineExpose({
  validForm,
})

onUnmounted(() => {
  useOtherStore().setIndexFileData(null)
})
</script>

<style scoped lang="scss">
.fill-application-info {
  .file-upload-view {
    display: flex;
    align-items: center;

    .file-view {
      display: flex;
      align-items: center;
      background-color: #f0f1f3;
      border-radius: 5px;
      padding: 3px 12px;
      margin-right: 10px;
      max-width: 500px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      img {
        width: 14px;
        object-fit: contain;
        margin-right: 5px;
      }

      span {
        min-width: 40px;
      }
    }
  }
}
</style>
