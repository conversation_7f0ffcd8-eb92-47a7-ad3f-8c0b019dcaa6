<template>
	<div>
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item :label="$t('handoverFlow.handoverReason')" prop="handoverReason">
						<el-select
							v-model="state.queryForm.handoverReason"
							:placeholder="t('handoverFlow.inputHandoverReasonTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_REASON"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.receiveDept')" prop="receiveDept">
						<el-select
							v-model="state.queryForm.receiveDept"
							:placeholder="t('handoverFlow.inputReceiveDeptTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.receiveDept"
								:key="index"
								:label="item.name"
								:value="item.deptId"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.receiveUser')" prop="receiveUser">
						<el-select
							v-model="state.queryForm.receiveUser"
							:placeholder="t('handoverFlow.inputReceiveUserTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.receiveUser"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverFlow.type')" prop="type">
						<el-select
							v-model="state.queryForm.type"
							:placeholder="t('handoverFlow.inputTypeTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('handoverNodeRecord.status')" prop="status">
						<el-select
							v-model="state.queryForm.status"
							:placeholder="t('handoverNodeRecord.inputStatusTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_STATUS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<el-button
						v-if="state.queryForm.retStatus === '1'"
						v-auth="'order_handovernoderecord_edit'"
						icon="edit-pen"
						type="primary"
						class="ml10"
						:loading="state.loading"
						@click="methods.handleUpdateFlow"
					>
						{{ $t('jfI18n.updateFlow') }}
					</el-button>
					<el-button
						v-if="data.isCheckToReject"
						v-auth="'order_handovernoderecord_edit'"
						icon="RefreshLeft"
						type="primary"
						class="ml10"
						:loading="state.loading"
						@click="methods.handleCheckToReject"
					>
						{{ $t('jfI18n.rejectBtn') }}
					</el-button>
					<el-button
						v-if="data.isConfirmReceive"
						v-auth="'order_handovernoderecord_edit'"
						icon="CircleCheck"
						type="primary"
						class="ml10"
						:loading="state.loading"
						@click="methods.handleConfirmReceive"
					>
						{{ $t('jfI18n.receiveBtn') }}
					</el-button>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column
					v-if="data.selection"
					type="selection"
					width="40"
					align="center"
				/>
				<el-table-column type="index" :label="t('handoverNodeRecord.index')" width="40" />
				<el-table-column prop="flowKey" :label="t('handoverNodeRecord.flowKey')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.flowInstId"
							:value="scope.row.flowKey"
							:value-key="'flowKey'"
							:show-key="'flowName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="initiatorId" :label="t('handoverNodeRecord.initiatorId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.initiatorId"
							:value="scope.row.initiatorId"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="runJobId" :label="t('handoverNodeRecord.runJobId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.runJobId"
							:value="scope.row.runJobId"
							:value-key="'id'"
							:show-key="'jobName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="startTime" :label="t('handoverNodeRecord.startTime')" show-overflow-tooltip />
				<el-table-column prop="todoList" :label="t('handoverNodeRecord.todoList')" show-overflow-tooltip>
					<template #default="scope">
						<el-input
							v-model="scope.row.todoList"
							:disabled="!scope.row.$cellEdit"
							type="textarea"
							:placeholder="t('handoverNodeRecord.inputTodoListTip')"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="status" :label="t('handoverNodeRecord.status')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.HANDOVER_STATUS" :value="scope.row.status" />
					</template>
				</el-table-column>
				<el-table-column prop="createUser" :label="t('handoverNodeRecord.createUser')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.createUser"
							:value="scope.row.createUser"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" :label="t('handoverNodeRecord.createTime')" show-overflow-tooltip />
				<el-table-column v-if="data.menu" :label="$t('common.action')" width="200">
					<template #default="scope">
						<el-button
							v-if="!scope.row.$cellEdit"
							v-auth="'order_handovernoderecord_view'"
							text
							type="primary"
							icon="view"
							@click="formDialogRef.openDialog('view', scope.row.id)"
						>
							{{ $t('common.viewBtn') }}
						</el-button>
						<el-button
							v-if="!scope.row.$cellEdit"
							v-auth="'order_handovernoderecord_edit'"
							icon="edit-pen"
							text
							type="primary"
							@click="methods.handleEdit(scope.row, scope.$index, true)"
						>
							修改
						</el-button>
						<el-button
							v-if="scope.row.$cellEdit"
							v-auth="'order_handovernoderecord_add'"
							icon="Check"
							text
							type="primary"
							@click="methods.handleUpdate(scope.row, scope.$index)"
						>
							{{ $t('jfI18n.saveBtn') }}
						</el-button>
						<el-button
							v-if="scope.row.$cellEdit"
							v-auth="'order_handovernoderecord_edit'"
							icon="Close"
							text
							type="primary"
							@click="methods.handleEdit(scope.row, scope.$index, false)"
						>
							{{ $t('jfI18n.cancelBtn') }}
						</el-button>
						<el-button
							v-auth="'order_handovernoderecord_del'"
							icon="delete"
							text
							type="primary"
							@click="handleDelete(scope.row.id, scope.$index)"
						>
							{{
								$t('common.delBtn')
							}}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination
				v-bind="state.pagination"
				@size-change="sizeChangeHandle"
				@current-change="currentChangeHandle"
			/>
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="HandoverNodeRecordFlow">
import { BasicTableProps, useTable } from "@/hooks/table"
import * as handoverNodeRecord from "@/api/order/handover-node-record"
import { useMessage, useMessageBox } from "@/hooks/message"

import { useI18n } from "vue-i18n"
import { onLoadDicUrl, onLoaded } from "@/flow/components/convert-name/convert"
import { validateNull } from "@/utils/validate"
import * as handoverFlow from "@/api/order/handover-flow"
import * as orderVue from "@/api/order/order-key-vue"
import { PROP_CONST } from "@/flow/support/prop-const"
import { setCrudQueryForm } from "@/flow/support/common"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))
const { t } = useI18n()
// 定义查询字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "receiveDept" }, { key: "receiveUser" })
onMounted(() => {
	onLoad(dicData)
})
// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const validate = async () => {
	// 防止被手动修改
	methods.setCrudQueryForm()
	if (!state.queryForm.status || !state.queryForm.type) {
		proxy.$modal.info("请选择交接条件")
		return false
	}
	// 被驳回时查询
	state.queryForm.createUser = props.currJob.createUser
	return true
}

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: handoverNodeRecord.fetchFlowList,
	validate: validate,
	onLoaded: onLoaded({ key: "createUser" }, { key: "initiatorId" }, { key: "flowInstId" }, { key: "runJobId" }),
	descs: ["create_time"],
	createdIsNeed: false,
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	// 清空多选
	selectObjs.value = []
	data.selections = []
	getDataList()
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	data.selections = objs
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

// 删除操作
const handleDelete = async (id: string, index) => {
	if (validateNull(id)) {
		proxy.$modal.msg("还未保存")
		return
	}
	try {
		await useMessageBox().confirm(t("common.delConfirmText"))
	}
	catch {
		return
	}
	try {
		await handoverNodeRecord.delObjs(id)
		state.dataList.splice(index, 1)
		state.pagination!.total = state.dataList.length
		proxy.$modal.msgSuccess(t("common.delSuccessText"))
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
}

const props = defineProps({
	currJob: {
		type: Object,
		default: null,
	},
})
const data = reactive({
	selections: [],
	isCheckToReject: false,
	isConfirmReceive: false,
	menu: false,
	selection: false,
})

const methods = {
	initJobData() {
		state.loading = false
		// 判断是否仅查看
		orderVue.currElTabIsView(null, props.currJob)
		data.menu = false
		data.selection = false
		data.isConfirmReceive = false
		data.isCheckToReject = false
		methods.initSelections()
	},
	initSelections() {
		let hiJob = props.currJob.hiJob
		let find = orderVue.currElTabIsExist(props.currJob)
		if (find.isFormEdit === "0") { hiJob = true }
		let userKey = props.currJob.userKey
		let currJob = PROP_CONST.HANDOVER_FLOW.userKey
		if (userKey === currJob.create_user) {
			// 判断修改工单
			if (!hiJob) { // 为0后带入参数查询不出1的数据
				state.queryForm.retStatus = "1"
				data.menu = true
			}
			else { data.menu = false }
			// 被驳回需全部保存
			props.currJob.resolve = () => {
				let index = state.dataList.findIndex(f => f.retStatus === "1") + 1
				if (index !== 0) {
					proxy.$modal.msg("第 " + index + " 行交接项未修改，请先修改并保存")
					return false
				}
				orderVue.currElTabIsSave(props.currJob, true)
				return true
			}
		}
		else if (userKey === currJob.receive_user || userKey === currJob.curr_dept_manager) {
			data.menu = false
			state.queryForm.retStatus = "0"
			// 判断驳回工单
			if (!hiJob) {
				data.isCheckToReject = true
				data.selection = true
				if (userKey === currJob.curr_dept_manager) {
					orderVue.currElTabIsSave(props.currJob, true)
				}
			}
			// 判断接收工单
			if (userKey === currJob.receive_user && !hiJob) {
				props.currJob.resolveSaves.push(methods.handleConfirmReceive)
				data.isConfirmReceive = true
			}
		}
		else {
			// 其他节点手动完成
			orderVue.currElTabIsSave(props.currJob, true)
		}
		// 初始化
		getDataList()
	},
	async handleUpdate(row, index) {
		try {
			state.loading = true
			// 防止被手动修改
			methods.setCrudQueryForm()
			// 驳回后修改为正常状态
			if (state.queryForm.retStatus === "1") { row.retStatus = "0" }
			let resp = await handoverNodeRecord.putObj(Object.assign({}, state.queryForm, row))
			// 延迟赋值
			setTimeout(() => {
				state.dataList[index].id = resp.object.id
				state.dataList[index].$cellEdit = false
			}, 0)
			proxy.$modal.msgSuccess("操作成功")
		}
		catch (err: any) {
			proxy.$modal.msgError(err.msg)
		}
		finally {
			state.loading = false
		}
	},
	handleUpdateFlow() {
		// 可手动修改的交接参数
		// common.setPropsDataValue(order, state.queryForm, 'handoverReason', 'receiveDept')
		if (!state.queryForm.status || !state.queryForm.type || !state.queryForm.handoverReason || !state.queryForm.receiveDept) {
			proxy.$modal.info("请选择交接条件：交接状态、交接类型、交接原因、接受部门")
			return
		}
		methods.timeoutLoading(3)
		let queryForm = {
			id: props.currJob.orderId,
			code: props.currJob.code,
			flowKey: props.currJob.flowKey,
			flowInstId: props.currJob.flowInstId,
			runJobId: props.currJob.id,
		}
		// 修改工单信息时，任务交接不允许修改类型、状态
		let order = props.currJob.order
		let assign = Object.assign({}, state.queryForm, queryForm, { type: order.type, status: order.status })
		handoverFlow.putObj(assign).then(data => {
			orderVue.currElTabIsSave(props.currJob, true)
			proxy.$modal.msgSuccess("修改成功")
		})
	},
	// 防止被手动修改
	setCrudQueryForm() {
		let order = props.currJob.order
		setCrudQueryForm(state, { prop: "receiveDept", value: order.receiveDept }, { prop: "type", value: order.type },
			{ prop: "status", value: order.status }, { prop: "receiveUser", value: order.receiveUser },
			{ prop: "handoverReason", value: order.handoverReason }, { prop: "orderId", value: props.currJob.orderId })
	},
	handleCheckToReject() {
		// 防止被手动修改
		methods.setCrudQueryForm()
		// 驳回
		if (validateNull(data.selections)) {
			proxy.$modal.info("请选择需驳回的交接项")
			return
		}
		methods.timeoutLoading(3)
		state.queryForm.id = data.selections[0].orderId
		state.queryForm.rejectIds = data.selections.map(m => m.id)
		handoverFlow.checkToReject(state.queryForm).then(data => {
			getDataList()
			proxy.$modal.msgSuccess("驳回成功")
		})
	},
	async handleConfirmReceive() {
		// 防止被手动修改
		methods.setCrudQueryForm()
		// 接收
		if (validateNull(state.dataList)) {
			proxy.$modal.info("请选择交接项")
			return
		}
		methods.timeoutLoading(3)
		state.queryForm.id = state.dataList[0].orderId
		await handoverFlow.confirmReceive(state.queryForm)
		orderVue.currElTabIsSave(props.currJob, true)
		proxy.$modal.msgSuccess("接收成功")
	},
	timeoutLoading(t) {
		state.loading = true
		setTimeout(() => { // 防重复提交
			state.loading = false
		}, t * 1000)
	},
	handleEdit(row, index, bool) {
		row.$cellEdit = bool
	},
}

// 监听双向绑定
watch(
	() => props.currJob.id,
	() => {
		methods.initJobData()
	},
)

onMounted(() => {
	methods.initJobData()
})
</script>
