import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询banner管理配置列表 带分页
export function bannerCfgInfoPage(data: Object) {
	return request({
		url: essPrefix + "/bannerCfgInfo/listPage",
		method: "post",
		data,
	})
}

// 查询banner管理配置列表
export function getBannerCfgInfoList() {
	return request({
		url: essPrefix + "/bannerCfgInfo/list",
		method: "get",
	})
}

//  新增banner
export function addBannerCfgInfo(data: Object) {
	return request({
		url: essPrefix + "/bannerCfgInfo/add",
		method: "post",
		data,
	})
}

// 修改banner
export function updateBannerCfgInfo(data: Object) {
	return request({
		url: essPrefix + "/bannerCfgInfo/edit",
		method: "post",
		data,
	})
}

// 删除
export function delBannerCfgInfo(ids: string) {
	return request({
		url: essPrefix + "/bannerCfgInfo/" + ids,
		method: "delete",
	})
}

// 查询banner管理配置详细
export function getBannerCfgInfo(id: string) {
	return request({
		url: essPrefix + "/bannerCfgInfo/" + id,
		method: "get",
	})
}
