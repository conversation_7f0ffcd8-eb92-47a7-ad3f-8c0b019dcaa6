<template>
  <el-dialog
    title="添加审批人"
    v-model="dialogVisible"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-width="110px"
      :inline="false"
    >
      <el-form-item label="节点ID" prop="nodeId" v-if="form.nodeId">
        <el-input v-model="form.nodeId" disabled></el-input>
      </el-form-item>
      <el-form-item label="节点名称" prop="nodeName">
        <el-input
          v-model="form.nodeName"
          clearable
          :maxlength="10"
          show-word-limit
          placeholder="请输入节点名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="多人审批方式" prop="approveMethod">
        <el-radio-group v-model="form.approveMethod">
          <el-radio label="会签 (需要所有人审批同意)" value="1"></el-radio>
          <el-radio label="或签 (需要一名人审批同意即可)" value="2"></el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="divider-line"></div>
      <div>固定审批人<span class="color-#AAAAAA">（可多选）</span></div>
      <div class="flex-x-between m-y-10px">
        <el-button type="primary" @click="selectUser">选择审批人</el-button>
        <div v-if="form.userOrRole.length">已选{{ form.userOrRole.length }}人</div>
      </div>
      <div class="user-box" v-if="form.userOrRole.length">
        <div
          class="user-tag"
          v-for="(item, index) in form.userOrRole"
          :key="item.userOrRoleId"
        >
          <div class="name">{{ item.userOrRoleName }}</div>
          <img
            @click="removeUser(item, index)"
            src="@/assets/images/icon/delete.png"
            alt=""
          />
        </div>
      </div>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
    </template>

    <UserRolePicker
      ref="userPickerRef"
      :isOnlyOne="false"
      @onSelectItems="changeSelectItems"
    />
  </el-dialog>
</template>

<script setup>
import { queryUserCountByRoleId } from "@/api/system/role"

const UserRolePicker = defineAsyncComponent(() =>
  import("@/flow/components/user-role/picker2.vue")
)

const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)
const form = ref({
  approveMethod: null,
  nodeName: "",
  userOrRole: [],
})
const rules = ref({
  nodeName: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  approveMethod: [{ required: true, message: "请选择审批方式", trigger: "blur" }],
})

const emit = defineEmits(["confirm"])
const open = (row) => {
  form.value = {
    ...form.value,
    ...row,
    userOrRole: row.userOrRole.map((item) => {
      return { ...item, id: item.userOrRoleId }
    }),
  }
  dialogVisible.value = true
}

const handleClose = () => {
  dialogVisible.value = false
  form.value.userOrRole = []
  proxy.resetForm("formRef")
}

// 确认
const handleConfirm = () => {
  formRef.value.validate((valid) => {
    if (!valid) return

    if (!form.value.userOrRole.length) {
      proxy.$modal.msgError("请选择审批人")
      return
    }

    verificationRoleUsers()
  })
}

// 校验当前角色下是否有用户
const verificationRoleUsers = async () => {
  const roles = form.value.userOrRole.filter((i) => i.type === "role")

  if (!roles.length) {
    emitConfirm()
    return
  }

  try {
    loading.value = true
    const requests = roles.map((role) => queryUserCountByRoleId({ roleId: role.roleId }))
    const results = await Promise.all(requests)

    // 找出没有用户的角色ID
    const emptyRoleIds = results
      .filter((res) => +res.object === 0)
      .map((_, index) => roles[index].userOrRoleId)

    if (emptyRoleIds.length) {
      const emptyRoleNames = roles
        .filter((role) => emptyRoleIds.includes(role.userOrRoleId))
        .map((role) => role.userOrRoleName)

      proxy.$modal.msgError(`角色 ${emptyRoleNames.join("、")} 下没有用户，请重新选择`)
      return
    }

    emitConfirm()
  } finally {
    loading.value = false
  }
}

const emitConfirm = () => {
  emit("confirm", form.value)
  handleClose()
}

// 选择审批人
const selectUser = () => {
  proxy.$refs.userPickerRef.onOpen(form.value.userOrRole)
}
// 删除审批人
const removeUser = (item, index) => {
  form.value.userOrRole.splice(index, 1)
}

function changeSelectItems(items) {
  form.value.userOrRole = [...items]
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.user-box {
  background: #f4f4f5;
  height: 100px;
  padding: 20px 0px 20px 20px;
  border-radius: 10px;
  display: flex;
  flex-wrap: wrap;
  .user-tag {
    border: 1px solid #e2e2e2;
    background: #fff;
    border-radius: 4px;
    position: relative;
    height: 26px;
    line-height: 26px;
    padding: 0 6px;
    min-width: 70px;
    text-align: center;
    margin-bottom: 15px;
    margin-right: 15px;
    img {
      width: 14px;
      position: absolute;
      top: -5px;
      right: -5px;
      cursor: pointer;
    }
  }
}
</style>
