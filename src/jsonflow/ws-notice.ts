import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/jsonflow/ws-notice/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/jsonflow/ws-notice",
		method: "post",
		data: obj,
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/jsonflow/ws-notice/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/jsonflow/ws-notice/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/jsonflow/ws-notice/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/jsonflow/ws-notice",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/jsonflow/ws-notice",
		method: "put",
		data: obj,
	})
}
