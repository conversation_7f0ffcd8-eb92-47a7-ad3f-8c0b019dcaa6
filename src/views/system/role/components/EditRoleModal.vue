<template>
	<el-dialog
		v-model="dialogVisible"
		:title="title"
		width="500px"
		append-to-body
	>
		<el-form
			ref="roleRef"
			:model="form"
			:rules="rules"
			label-width="100px"
		>
			<el-form-item label="角色名称" prop="roleName">
				<el-input
					v-model="form.roleName"
					placeholder="请输入角色名称"
					maxlength="50"
					clearable
				/>
			</el-form-item>
			<el-form-item prop="roleKey">
				<template #label>
					<span>
						<el-tooltip
							content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)"
							placement="top"
						>
							<el-icon><question-filled /></el-icon>
						</el-tooltip>
						权限字符
					</span>
				</template>
				<el-input v-model="form.roleKey" placeholder="请输入权限字符" />
			</el-form-item>
			<el-form-item label="角色顺序" prop="roleSort">
				<el-input-number v-model="form.roleSort" controls-position="right" :min="0" />
			</el-form-item>
			<el-form-item label="状态">
				<el-radio-group v-model="form.status">
					<el-radio
						v-for="dict in normal_disable"
						:key="dict.value"
						:value="dict.value"
					>
						{{ dict.label }}
					</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="菜单权限" class="block-tree-item">
				<el-checkbox
					v-model="menuExpand"
					@change="handleCheckedTreeExpand"
				>
					展开/折叠
				</el-checkbox>
				<el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll">
					全选/全不选
				</el-checkbox>
				<el-checkbox v-model="form.menuCheckStrictly" @change="handleCheckedTreeConnect">
					父子联动
				</el-checkbox>
				<el-tree
					ref="menuRef"
					class="tree-border"
					:data="menuOptions"
					show-checkbox
					node-key="id"
					:check-strictly="!form.menuCheckStrictly"
					empty-text="加载中，请稍候"
					:props="{ label: 'label', children: 'children' }"
				/>
			</el-form-item>
			<el-form-item label="备注">
				<el-input
					v-model="form.remark"
					type="textarea"
					placeholder="请输入内容"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="cancel">
				取消
			</el-button>
			<el-button type="primary" :loading="loading" @click="submitForm">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script setup name="EditRoleModal">
import { roleMenuTreeselect, treeselect as menuTreeselect } from "@/api/system/menu"
import { addRole, updateRole, getRole } from "@/api/system/role"
const { proxy } = getCurrentInstance()
const props = defineProps({
	normal_disable: {
		type: Array,
	},
})
const title = ref("")
const form = ref({})
const dialogVisible = ref(false)
const loading = ref(false)
const menuExpand = ref(false)
const menuNodeAll = ref(false)
const rules = ref({
	roleName: [{ required: true, message: "角色名称不能为空", trigger: "blur" }],
	roleKey: [{ required: true, message: "权限字符不能为空", trigger: "blur" }],
	roleSort: [{ required: true, message: "角色顺序不能为空", trigger: "blur" }],
})
const menuRef = ref(null)
const menuOptions = ref([])
const emit = defineEmits(["change"])
const open = item => {
	dialogVisible.value = true
	title.value = item?.id ? "编辑角色" : "新增角色"
	if (item) {
		form.value = item
		getRoleInfo()
	}
	else {
		getMenuTreeselect()
	}
}

const getRoleInfo = () => {
	const roleMenu = getRoleMenuTreeselect(form.value.roleId)
	getRole(form.value.roleId).then(res => {
		form.value = res.data
		form.value.roleSort = Number(form.value.roleSort)
		nextTick(() => {
			roleMenu.then(res => {
				let checkedKeys = res.checkedKeys
				checkedKeys.forEach(v => {
					nextTick(() => {
						menuRef.value.setChecked(v, true, false)
					})
				})
			})
		})
	})
}

/** 根据角色ID查询菜单树结构 */
function getRoleMenuTreeselect(roleId) {
	return roleMenuTreeselect(roleId).then(response => {
		menuOptions.value = response.menus
		return response
	})
}

/** 查询菜单树结构 */
function getMenuTreeselect() {
	menuTreeselect().then(response => {
		menuOptions.value = response.data
	})
}

/** 树权限（展开/折叠） */
function handleCheckedTreeExpand(value) {
	let treeList = menuOptions.value
	for (let i = 0; i < treeList.length; i++) {
		menuRef.value.store.nodesMap[treeList[i].id].expanded = value
	}
}

/** 树权限（全选/全不选） */
function handleCheckedTreeNodeAll(value) {
	menuRef.value.setCheckedNodes(value ? menuOptions.value : [])
}

/** 树权限（父子联动） */
function handleCheckedTreeConnect(value) {
	form.value.menuCheckStrictly = value ? true : false
}

/** 所有菜单节点数据 */
function getMenuAllCheckedKeys() {
	// 目前被选中的菜单节点
	let checkedKeys = menuRef.value.getCheckedKeys()
	// 半选中的菜单节点
	let halfCheckedKeys = menuRef.value.getHalfCheckedKeys()
	checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
	return checkedKeys
}

/** 重置新增的表单以及其他数据  */
function reset() {
	if (menuRef.value != undefined) {
		menuRef.value.setCheckedKeys([])
	}
	menuExpand.value = false
	menuNodeAll.value = false
	form.value = {
		roleId: undefined,
		roleName: undefined,
		roleKey: undefined,
		roleSort: 0,
		status: "0",
		menuIds: [],
		deptIds: [],
		menuCheckStrictly: true,
		deptCheckStrictly: true,
		remark: undefined,
	}
	proxy.resetForm("roleRef")
}

/** 取消按钮 */
function cancel() {
	dialogVisible.value = false
	reset()
}

/** 提交按钮 */
function submitForm() {
	proxy.$refs["roleRef"].validate(valid => {
		if (valid) {
			const menuIds = getMenuAllCheckedKeys()
			const actionUrl = form.value.roleId !== undefined ? updateRole : addRole
			const successMessage = form.value.roleId !== undefined ? "修改成功" : "新增成功"
			loading.value = true
			actionUrl({ ...form.value, menuIds })
				.then(res => {
					proxy.$modal.msgSuccess(successMessage)
					cancel()
					emit("change")
				})
				.finally(() => (loading.value = false))
		}
	})
}

defineExpose({
	open,
})
</script>

<style scoped lang="scss">
.block-tree-item {
  :deep(.el-form-item__content) {
    display: block;
  }
}
</style>
