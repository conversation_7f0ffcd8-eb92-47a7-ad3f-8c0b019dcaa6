<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="90px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('handoverNodeRecord.flowKey')" prop="flowKey">
						<el-select
							v-model="form.flowKey"
							:placeholder="t('handoverNodeRecord.inputFlowKeyTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowKey"
								:key="index"
								:label="item.flowName"
								:value="item.flowKey"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('handoverNodeRecord.initiatorId')" prop="initiatorId">
						<el-select
							v-model="form.initiatorId"
							:placeholder="t('handoverNodeRecord.inputInitiatorIdTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in dicData.initiatorId"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('handoverNodeRecord.runJobId')" prop="runJobId">
						<el-select
							v-model="form.runJobId"
							:placeholder="t('handoverNodeRecord.inputRunJobIdTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.runJobId"
								:key="index"
								:label="item.jobName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('handoverNodeRecord.startTime')" prop="startTime">
						<el-date-picker
							v-model="form.startTime"
							disabled
							type="datetime"
							:placeholder="t('handoverNodeRecord.inputStartTimeTip')"
							:value-format="dateTimeStr"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('handoverNodeRecord.todoList')" prop="todoList">
						<el-input v-model="form.todoList" type="textarea" :placeholder="t('handoverNodeRecord.inputTodoListTip')" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('handoverNodeRecord.status')" prop="status">
						<el-select
							v-model="form.status"
							:placeholder="t('handoverNodeRecord.inputStatusTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_STATUS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="HandoverNodeRecordDialog">

import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/order/handover-node-record"
import { useI18n } from "vue-i18n"
import { rule } from "@/utils/validate"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
const emit = defineEmits(["refresh"])

const { t } = useI18n()

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "initiatorId" })
const onCascade = onCascadeChange(cascadeDic, { key: "flowInstId", cascades: ["flowKey", "runNodeId"] }, { key: "runNodeId", cascades: ["runJobId"] })
onMounted(() => {
	onLoad(dicData)
})
// 提交表单数据
const form = reactive({
	flowKey: "",
	initiatorId: "",
	nodeJobId: "",
	startTime: "",
	todoList: "",
	status: "",
})

// 定义校验规则
const dataRules = ref({
	flowKey: [{ required: true, message: "业务类型不能为空", trigger: "blur" }],
	initiatorId: [{ required: true, message: "交接申请人不能为空", trigger: "blur" }],
	nodeJobId: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
	status: [{ required: true, message: "交接状态不能为空", trigger: "blur" }],
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取HandoverNodeRecord信息
	if (id) {
		form.id = id
		getHandoverNodeRecordData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true
		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getHandoverNodeRecordData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
		onCascade(form)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
