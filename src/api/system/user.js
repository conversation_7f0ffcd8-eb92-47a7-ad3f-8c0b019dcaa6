import request from '@/utils/request'
import { parseStrEmpty } from '@/utils/common'
import { eduUser, auth } from '@/config/constant'

// 查询用户列表
export function listUser(query) {
	return request({
		url: eduUser + '/system/user/list',
		method: 'get',
		params: query
	})
}

// 查询用户详细
export function getUser(userId) {
	return request({
		url: eduUser + '/system/user/' + parseStrEmpty(userId),
		method: 'get'
	})
}

// 新增用户
export function addUser(data) {
	return request({
		url: eduUser + '/system/user',
		method: 'post',
		data: data
	})
}

// 修改用户
export function updateUser(data) {
	return request({
		url: eduUser + '/system/user',
		method: 'put',
		data: data
	})
}

// 删除用户
export function delUser(userId) {
	return request({
		url: eduUser + '/system/user/' + userId,
		method: 'delete'
	})
}

// 用户密码重置
export function resetUserPwd(userId, password) {
	const data = {
		userId,
		password
	}
	return request({
		url: eduUser + '/system/user/resetPwd',
		method: 'put',
		data: data
	})
}

// 用户状态修改
export function changeUserStatus(userId, status) {
	const data = {
		userId,
		status
	}
	return request({
		url: eduUser + '/system/user/changeStatus',
		method: 'put',
		data: data
	})
}

// 查询用户个人信息
export function getUserProfile() {
	return request({
		url: eduUser + '/system/user/profile',
		method: 'get'
	})
}

// 修改用户个人信息
export function updateUserProfile(data) {
	return request({
		url: eduUser + '/system/user/profile',
		method: 'put',
		data: data
	})
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
	const data = {
		oldPassword,
		newPassword
	}
	return request({
		url: eduUser + '/system/user/profile/updatePwd',
		method: 'put',
		params: data
	})
}

// 用户头像上传
export function uploadAvatar(data) {
	return request({
		url: eduUser + '/system/user/profile/avatar',
		method: 'post',
		headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
		data: data
	})
}

// 查询授权角色
export function getAuthRole(userId) {
	return request({
		url: eduUser + '/system/user/authRole/' + userId,
		method: 'get'
	})
}

// 保存授权角色
export function updateAuthRole(data) {
	return request({
		url: eduUser + '/system/user/authRole',
		method: 'put',
		params: data
	})
}

// 查询部门下拉树结构
export function deptTreeSelect(params) {
	return request({
		url: eduUser + '/system/dept/treeselect',
		method: 'get',
		params: {
			status: 0, // 0启用 1停用
			...params
		}
	})
}

// ess

// 查询用户信息列表 带分页
export function zsUserListPage(data) {
	return request({
		url: eduUser + '/zsUserInfoApi/zsUserListPage',
		method: 'post',
		data
	})
}
// 查询用户信息列表 带分页---角色选择用户
export function zsList(data) {
	return request({
		url: eduUser + '/system/user/zsList',
		method: 'get',
		params: data
	})
}

// 查询用户角色部门列表信息
export function selectZsRoleDeptInfos(userName) {
	return request({
		url: eduUser + '/zsRoleInfoApi/selectZsRoleDeptInfos/' + userName,
		method: 'get'
	})
}

// 切换角色部门登录
export function zsSwitchRole(data) {
	return request({
		url: auth + '/auth/zsSwitchRole',
		method: 'post',
		data
	})
}
// 根据父级角色编码查询子级角色的所有用户
export function selectUserByParentRoleKey(parentRoleKey) {
	return request({
		url: eduUser + '/system/user/selectUserByRoleKey/' + parentRoleKey,
		method: 'get',
	})
}

// 根据用户Id进行实名提醒
export function reminderUserRealAuth(userId) {
	return request({
		url: eduUser + `/system/user/reminder/${userId}`,
		method: 'get',
	})
}
