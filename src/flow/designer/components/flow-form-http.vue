<template>
  <el-form label-position="left" class="flow-config-attr" label-width="160px">
    <el-form-item label="查询表单Http请求地址">
      <el-input
        v-model="props.flowData.attrs.queryOrder"
        class="input-attr"
        placeholder="可输入全路径或相对路径"
        clearable
      >
        <template #prepend>
          <el-select v-model="props.flowData.attrs.queryMethod">
            <el-option
              v-for="(item, index) in DIC_PROP.HTTP_METHODS"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-input>

      <el-tooltip placement="top">
        <template #content> 查询表单信息接口（可输入全路径或相对路径） </template>
        <el-icon style="margin-left: 10px">
          <QuestionFilled />
        </el-icon>
      </el-tooltip>
    </el-form-item>
    <el-form-item label="更新表单Http请求地址">
      <el-input
        v-model="props.flowData.attrs.updateOrder"
        class="input-attr"
        placeholder="可输入全路径或相对路径"
        clearable
      >
        <template #prepend>
          <el-select v-model="props.flowData.attrs.updateMethod">
            <el-option
              v-for="(item, index) in DIC_PROP.HTTP_METHODS"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-input>

      <el-tooltip placement="top">
        <template #content> 更新表单信息接口（可输入全路径或相对路径） </template>
        <el-icon style="margin-left: 10px">
          <QuestionFilled />
        </el-icon>
      </el-tooltip>
    </el-form-item>

    <el-divider>关联表单Http请求头</el-divider>

    <el-table
      :data="data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[0].value)"
      border
      style="width: 100%; margin-bottom: 10px"
      max-height="500"
    >
      <el-table-column type="index" label="操作" width="80">
        <template #header>
          <el-button
            icon="Plus"
            size="small"
            type="primary"
            circle
            @click="methods.onAddItem(DIC_PROP.PARAM_FROM[0].value, true)"
          />
        </template>
        <template #default="scope">
          <el-button
            icon="Minus"
            size="small"
            type="danger"
            circle
            @click="methods.handleHttpUrlDelete(scope.$index, scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column prop="targetProp" label="请求头" show-overflow-tooltip>
        <template #default="scope">
          <el-input
            v-model="scope.row.targetProp"
            :placeholder="
              scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value
                ? scope.row.varKeyVal
                : null
            "
            clearable
          />
        </template>
      </el-table-column>

      <el-table-column prop="paramValType" label="参数值类型" width="145px">
        <template #default="scope">
          <el-select v-model="scope.row.paramValType" clearable>
            <el-option
              v-for="(item, index) in DIC_PROP.SYS_PARAM_VAL_TYPE"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column prop="varKeyVal" label="系统字段或固定值" show-overflow-tooltip>
        <template #default="scope">
          <el-select
            v-if="scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value"
            v-model="scope.row.varKeyVal"
            clearable
            filterable
          >
            <el-option-group
              v-for="(group, index) in data.allFieldPerms"
              :key="index"
              :label="group.label"
            >
              <el-option
                v-for="(item, index) in group.options"
                :key="index"
                :disabled="item.prop.indexOf('_define_') !== -1"
                :label="item.label"
                :value="item.prop"
              />
            </el-option-group>
          </el-select>

          <el-input v-else v-model="scope.row.varKeyVal" clearable />
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script setup lang="ts" name="FlowHttpParam">
const { proxy } = getCurrentInstance()
import { validateNull } from "@/utils/validate"
import { handleFieldProp } from "../../utils/form-perm"
import { DIC_PROP } from "../../support/dict-prop"
import { PROP_CONST } from "../../support/prop-const"
import { deepClone } from "@/utils/index"

const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null,
  },
  flowData: {
    type: Object,
    default: null,
  },
  attrConfigVisible: null,
})

const data = reactive({
  allFieldPerms: [],
  formFieldPerms: [],
  httpParams: [],
  paramType: "0",
})

// 定义字典
onMounted(async () => {
  if (!props.attrConfigVisible) return
  methods.changeTabPane()
})

const methods = {
  listFormFieldPerms() {
    if (validateNull(data.formFieldPerms)) {
      let formFieldPerms = deepClone(PROP_CONST.SYS_FIELDS)
      handleFieldProp(formFieldPerms, null)
      data.allFieldPerms = [{ label: "系统字段", options: formFieldPerms }]
    }
  },
  onAddItem(paramFrom, isAdd) {
    methods.listFormFieldPerms()
    let value = DIC_PROP.PARAM_VAL_TYPE[0].value
    if (data.httpParams.length > 0) {
      let find = data.httpParams
        .filter((f) => f.paramFrom === paramFrom)
        .find((f) => !f.varKeyVal || (f.paramValType !== value && !f.targetProp))
      if (find) {
        if (isAdd) {
          if (!find.varKeyVal) {
            proxy.$modal.msgWarning("请先填写 表单字段")
            return
          }
          if (find.paramValType !== value && !find.targetProp) {
            proxy.$modal.msgWarning("请先填写 请求头")
            return
          }
        }
      }
      if (!isAdd) {
        data.httpParams.splice(0, data.httpParams.length)
      }
    }
    let obj = {
      paramFrom: paramFrom,
      varKeyVal: null,
      paramValType: value,
      targetProp: null,
    }
    data.httpParams.push(obj)
    methods.changeHttpUrlParams()
  },
  handleHttpUrlDelete(index: number, row: any) {
    let splice = data.httpParams.filter((f) => f.paramFrom === row.paramFrom)
    splice.splice(index, 1)
    data.httpParams = splice
    methods.changeHttpUrlParams()
  },
  validateHttpUrlData() {
    // 兼容老版本
    let httpParams = props.flowData.attrs.orderParams
    if (!httpParams) {
      props.flowData.attrs.orderParams = []
    }
    let queryMethod = props.flowData.attrs.queryMethod
    if (!queryMethod) {
      props.flowData.attrs.queryMethod = "GET"
    }
    let updateMethod = props.flowData.attrs.updateMethod
    if (!updateMethod) {
      props.flowData.attrs.updateMethod = "PUT"
    }
    data.httpParams = props.flowData.attrs.orderParams
  },
  changeHttpUrlParams() {
    props.flowData.attrs.orderParams = data.httpParams
  },
  changeTabPane() {
    methods.validateHttpUrlData()
    methods.listFormFieldPerms()
  },
}

// 监听双向绑定
watch(
  () => props.attrConfigVisible,
  (val) => {
    if (!val) return
    methods.changeTabPane()
  }
)
</script>

<style lang="scss">
@use '../assets/style/flow-attr.scss' as *;
</style>
