<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="90px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item label="流程实例ID" prop="flowInstId">
						<el-input v-model="form.flowInstId" placeholder="请输入流程实例ID" disabled />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="流程名称" prop="flowKey">
						<el-select
							v-model="form.flowKey"
							placeholder="请选择流程名称"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowKey"
								:key="index"
								:label="item.flowName"
								:value="item.flowKey"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="运行节点" prop="runNodeId">
						<el-select
							v-model="form.runNodeId"
							placeholder="请选择运行节点"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.runNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="运行任务" prop="runJobId">
						<el-select
							v-model="form.runJobId"
							placeholder="请选择运行任务"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.runJobId"
								:key="index"
								:label="item.jobName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="审批人" prop="userId">
						<el-select
							v-model="form.userId"
							placeholder="请选择审批人"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in dicData.userId"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="审批意见" prop="remark">
						<el-input v-model="form.remark" type="textarea" placeholder="请输入审批意见" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="CommentDialog">
import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/jsonflow/comment"
import { rule } from "@/utils/validate"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"
const emit = defineEmits(["refresh"])

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "userId" })
const onCascade = onCascadeChange(cascadeDic, { key: "flowInstId", cascades: ["flowKey", "runNodeId"] }, { key: "runNodeId", cascades: ["runJobId"] })
onMounted(() => {
	onLoad(dicData)
})

// 提交表单数据
const form = reactive({
	flowKey: "",
	flowNodeId: "",
	nodeJobId: "",
	userId: "",
	remark: "",
})

// 定义校验规则
const dataRules = ref({
	flowKey: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
	flowNodeId: [{ required: true, message: "节点名称不能为空", trigger: "blur" }],
	nodeJobId: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
	userId: [{ required: true, message: "审批人不能为空", trigger: "blur" }],
	remark: [{ required: true, message: "审批意见不能为空", trigger: "blur" }],
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取Comment信息
	if (id) {
		form.id = id
		getCommentData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true
		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}catch (err: any) {
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getCommentData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
		onCascade(form)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
