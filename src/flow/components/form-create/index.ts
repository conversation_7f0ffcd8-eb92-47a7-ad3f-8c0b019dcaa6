import {validateNull} from "@/utils/validate";

export function handlePrintValue(optionItems, formDatum, value, label) {
    if (!validateNull(optionItems)) {
        if (Array.isArray(formDatum)) {
            let showKeys = ''
            for (let i = 0; i < formDatum.length; i++) {
                let item = optionItems.find(f => f[value] === formDatum[i]);
                if (item) {
                    if (i === formDatum.length -1) showKeys += item[label]
                    else showKeys += item[label] + ","
                }
            }
            return showKeys
        } else {
            let item = optionItems.find(f => f[value] === formDatum);
            if (item) return item[label]
        }
    }
}
