export default {
	handoverFlow: {
		index: "#",
		importhandoverFlowTip: "import HandoverFlow",
		id: "id",
		code: "code",
		flowKey: "flowKey",
		type: "type",
		status: "status",
		createUser: "createUser",
		createTime: "createTime",
		handoverReason: "handoverReason",
		handoverUser: "handoverUser",
		handoverUserDept: "handoverUserDept",
		receiveDept: "receiveDept",
		receiveUser: "receiveUser",
		flowInstId: "flowInstId",
		finishTime: "finishTime",
		updateUser: "updateUser",
		updateTime: "updateTime",
		delFlag: "delFlag",

		inputIdTip: "input id",
		inputCodeTip: "input code",
		inputFlowKeyTip: "input flowKey",
		inputTypeTip: "input type",
		inputStatusTip: "input status",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputHandoverReasonTip: "input handoverReason",
		inputHandoverUserTip: "input handoverUser",
		inputHandoverUserDeptTip: "input handoverUserDept",
		inputReceiveDeptTip: "input receiveDept",
		inputReceiveUserTip: "input receiveUser",
		inputFlowInstIdTip: "input flowInstId",
		inputFinishTimeTip: "input finishTime",
		inputUpdateUserTip: "input updateUser",
		inputUpdateTimeTip: "input updateTime",
		inputDelFlagTip: "input delFlag",

	},
}
