<template>
	<div class="app-container">
		<div class="table-handle-box">
			<div>
				<el-button
					v-auths="['baseSet:appletBanner:add']"
					type="primary"
					icon="Plus"
					@click="handleAdd"
				>
					新增
				</el-button>
				<el-button
					v-auths="['baseSet:appletBanner:remove']"
					type="danger"
					icon="Delete"
					:disabled="!selections.length"
					@click="handleDelete"
				>
					删除
				</el-button>
			</div>
			<right-toolbar v-model:show-search="showSearch" @query-table="getList" />
		</div>

		<div class="white-body-box">
			<el-table
				v-loading="loading"
				class="table-empty"
				:data="tableData"
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="55" align="center" />
				<el-table-column type="index" label="序号" min-width="60" />
				<el-table-column label="名称" prop="bannerName" show-overflow-tooltip />
				<el-table-column label="图片" prop="imgUrl" min-width="180">
					<template #default="{ row }">
						<el-image
							v-if="row.imgUrl"
							style="width: 170px; height: 60px;"
							fit="cover"
							:preview-src-list="[imageApi + row.imgUrl]"
							:src="imageApi + row.imgUrl"
							preview-teleported
						/>
					</template>
				</el-table-column>
				<el-table-column label="排序" prop="sort" />
				<el-table-column label="操作" min-width="120">
					<template #default="scope">
						<el-button
							v-auths="['baseSet:appletBanner:edit']"
							link
							type="primary"
							@click="handleUpdate(scope.row)"
						>
							修改
						</el-button>
						<el-button
							v-auths="['baseSet:appletBanner:remove']"
							link
							type="danger"
							@click="handleDelete(scope.row)"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<pagination
			v-show="total > 0"
			v-model:page="queryParams.page"
			v-model:limit="queryParams.limit"
			:total="total"
			@pagination="getList"
		/>

		<!-- 新增修改弹窗 -->
		<EditBannerModal ref="editBannerRef" @refresh="handleQuery" @change="getList" />
	</div>
</template>

<script setup name="AppletBanner">
import { imageApi } from "@/config/constant"
import { bannerCfgInfoPage, delBannerCfgInfo } from "@/api/ess/baseSet/bannerCfgInfo"
import EditBannerModal from "./components/EditBannerModal.vue"
const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const selections = ref([])
const total = ref(0)
const editBannerRef = ref(null)

const queryParams = reactive({
	limit: 10,
	page: 1,
})

/** 查询列表 */
function getList() {
	loading.value = true
	bannerCfgInfoPage(queryParams)
		.then(res => {
			tableData.value = res.object.records
			total.value = res.object.total
		})
		.finally(() => {
			loading.value = false
		})
}

/** 搜索按钮操作 */
function handleQuery() {
	queryParams.page = 1
	getList()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
	ids.value = selection.map(item => item.id)
	selections.value = selection
}

/** 新增按钮操作 */
function handleAdd() {
	editBannerRef.value.open()
}

/** 修改按钮操作 */
function handleUpdate(row) {
	editBannerRef.value.open(row)
}
/** 删除按钮操作 */
function handleDelete(row) {
	const id = row.id || ids.value
	const name = row.bannerName || selections.value.map(item => item.bannerName)
	proxy.$modal
		.confirm(`是否确认删除"${name}"？`)
		.then(function () {
			return delBannerCfgInfo(id)
		})
		.then(() => {
			getList()
			proxy.$modal.msgSuccess("删除成功")
		})
		.catch(() => {})
}

getList()
</script>
