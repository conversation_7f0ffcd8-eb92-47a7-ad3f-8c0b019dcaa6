<template>
  <div class="app-container">
    <div class="flex-x-between">
      <CustomTabs v-model="currentTab" :options="authTabs" @tab-click="handleTabClick" />
      <el-radio-group
        v-model="radioValue"
        class="radio-box"
        v-if="authRadioOptions.length"
      >
        <el-radio-button
          v-for="item in authRadioOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-radio-group>
    </div>

    <template v-if="$auth.hasPermiOr(['todo:endMine:flow', 'todo:endOther:flow'])">
      <FlowTable ref="flowTableRef" v-show="radioValue === 0" :mine="currentTab" />
    </template>

    <template v-if="$auth.hasPermiOr(['todo:endMine:file', 'todo:endOther:file'])">
      <EndFileTable ref="endFileTableRef" v-show="radioValue === 1" :mine="currentTab" />
    </template>
  </div>
</template>

<script setup name="End">
import { useAgendaTabs } from "../useAgendaTabs"

const FlowTable = defineAsyncComponent(() => import("./components/FlowTable.vue"))
const EndFileTable = defineAsyncComponent(() => import("./components/EndFileTable.vue"))

const flowTableRef = ref(null)
const endFileTableRef = ref(null)

const tabs = [
  {
    label: "我发起的",
    value: 0,
    permissions: ["todo:endMine:list"],
    children: [
      {
        label: "流程",
        value: 0,
        permissions: ["todo:endMine:flow"],
      },
      {
        label: "签署",
        value: 1,
        permissions: ["todo:endMine:file"],
      },
    ],
  },
  {
    label: "他人发起",
    value: 1,
    permissions: ["todo:endOther:list"],
    children: [
      {
        label: "流程",
        value: 0,
        permissions: ["todo:endOther:flow"],
      },
      {
        label: "签署",
        value: 1,
        permissions: ["todo:endOther:file"],
      },
    ],
  },
]

const {
  currentTab,
  radioValue,
  authTabs,
  authRadioOptions,
  handleTabClick,
} = useAgendaTabs(tabs, [flowTableRef, endFileTableRef])
</script>

<style scoped lang="scss">
.radio-box {
  margin-bottom: 15px;
  :deep(.el-radio-button__inner) {
    background-color: var(--el-color-primary-light-8);
    border-color: var(--el-color-primary-light-8);
  }
  :deep(.el-radio-button) {
    --el-border-radius-base: 2px;
  }
}
</style>
