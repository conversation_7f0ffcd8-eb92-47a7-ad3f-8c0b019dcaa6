<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true" class="white-form-box"
             label-width="85px">
      <el-form-item label="文档名称" prop="docName">
        <el-input v-model="queryParams.docName" placeholder="请输入文档名称" clearable maxlength="100"
                  @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="上传时间" prop="updateTimeRange">
        <el-date-picker v-model="queryParams.updateTimeRange" value-format="YYYY-MM-DD HH:mm:ss"
                        format="YYYY-MM-DD HH:mm" type="datetimerange" range-separator="-" start-placeholder="开始时间"
                        end-placeholder="结束时间" class="common-input-width" :default-time="dateDefaultTime"
                        clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" :loading="loading" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="Refresh" :loading="loading" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <CustomTable ref="customTableRef" v-model:page="queryParams.page" v-model:limit="queryParams.limit"
                 :has-toolbar="false" :data="tableData" :loading="loading" :total="total" :opt-width="100"
                 @reload="getList">
      <vxe-column type="seq" width="60"/>
      <vxe-column title="文档名称" field="docName" show-overflow/>
      <vxe-column title="文档内容" field="fileName" show-overflow/>
      <vxe-column title="上传时间" field="uploadTime" show-overflow width="200"/>
      <vxe-column title="上传人" field="uploadUserName" show-overflow/>
      <vxe-column title="版本号" field="version" show-overflow/>
      <template #opts="{ row }">
        <TableColOptBtn :key="Math.random()" :buttons="[
          {
            text: '查看',
            click: () => handleView(row)

          },
          {
            text: '下载',
            click: () => handleDownload(row)
          },
        ]"/>
      </template>
    </CustomTable>

  </div>
</template>

<script setup name="OptManual">
import {
  getNavManualCfgListPage,
  downLoadManualCfgInfoUrl
} from "@/api/ess/doc/manualCfgInfo.ts"
import {
  startDownload
} from "@/api/ess/downloadCenter/downloadCenter.ts"
import { handleBlobFile } from "@/utils"

const {proxy} = getCurrentInstance()
const router = useRouter()

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)


const queryParams = reactive({
  docName: null,
  status: null,
  limit: 10,
  page: 1,
})

/** 查询列表 */
function getList() {
  loading.value = true
  queryParams.uploadTime = queryParams.updateTimeRange?.[0]
  queryParams.uploadEndTime = queryParams.updateTimeRange?.[1]
  getNavManualCfgListPage(queryParams)
      .then(res => {
        tableData.value = res.object.records
        total.value = res.object.total
      })
      .finally(() => {
        loading.value = false
      })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 查看按钮操作 */
function handleView(row) {
  router.push({
    path: '/navUser/optManual/previewDoc',
    state: {
      docId: row.id
    }
  })
}

/** 下载按钮操作 */
function handleDownload(row) {
  downLoadManualCfgInfoUrl(row.id).then(res => {
    startDownload(res.object).then(resp => {
      handleBlobFile(resp.data, row.docName + ".pdf")
    })
  })
}

getList()
</script>
