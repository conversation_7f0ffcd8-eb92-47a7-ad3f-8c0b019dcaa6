import { ref, Ref } from 'vue'
import { getToken } from "@/utils/auth"

// WebSocket实例类型
let ws: WebSocket | null = null
// WebSocket连接状态
const webSocketState: Ref<boolean> = ref(false)
// 心跳连接配置
const heartBeat: Ref<{
  time: number | null
  timeout: number
  reconnect: number
}> = ref({
  time: 5 * 1000, // 心跳时间间隔
  timeout: 3 * 1000, // 心跳超时间隔（要少于心跳间隔）
  reconnect: 10 * 1000 // 断线重连时间
})
// 断线重连定时器
let reconnectTimer: number | null = null
// 当前连接的URL
let currentUrl: string = ''

// WebSocket配置接口
interface WebSocketConfig {
  url: string
  heartbeatTime?: number
  heartbeatTimeout?: number
  reconnectTime?: number
  onMessage?: (data: any) => void
  onOpen?: () => void
  onClose?: () => void
  onError?: (error: Event) => void
}

const useWebSocket = (config?: WebSocketConfig) => {
  const getWsUrl = (url: string) => {
    if (url) {
      return `${import.meta.env.VITE_WS_BASE_API}${url}/${getToken()}`
    } else {
      return ''
    }
  }
  // 初始化配置
  const initConfig = (url: string, options?: Partial<WebSocketConfig>) => {
    currentUrl = getWsUrl(url)
    if (options?.heartbeatTime) {
      heartBeat.value.time = options.heartbeatTime
    }
    if (options?.heartbeatTimeout) {
      heartBeat.value.timeout = options.heartbeatTimeout
    }
    if (options?.reconnectTime) {
      heartBeat.value.reconnect = options.reconnectTime
    }
  }

  // 关闭连接
  const wsClose = (): void => {
    if (ws) {
      ws.close() // 关闭webSocket
    }
    webSocketState.value = false // 设置webSocket状态为关闭
    heartBeat.value.time = null // 清空心跳时间 停止心跳
    if (reconnectTimer) {
      // 关闭重连定时器
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
  }

  // 开启连接
  const onConnect = (url?: string, options?: Partial<WebSocketConfig>): void => {
    const targetUrl = url || currentUrl
    if (!targetUrl) {
      console.error('WebSocket URL is required')
      return
    }

    initConfig(targetUrl, options)
    heartBeat.value.time = heartBeat.value.time || 5 * 1000 // 因为关闭时会置空，所以在这重新初始化一下
    connectWebSocket() // 建立连接
  }

  // 建立连接
  const connectWebSocket = (): void => {
    if (!currentUrl) {
      console.error('WebSocket URL is required')
      return
    }

    // 确保URL使用ws协议
    const url = currentUrl.startsWith('ws://') || currentUrl.startsWith('wss://')
      ? currentUrl
      : currentUrl.replace(/^http/, 'ws')

    ws = new WebSocket(url)
    init() // 初始化
  }

  // 初始化
  const init = (): void => {
    if (!ws) return

    ws.addEventListener('open', () => {
      webSocketState.value = true // socket状态设置为连接，做为后面的断线重连的拦截器
      console.log('WebSocket连接已开启')
      config?.onOpen?.()
      heartBeat.value && heartBeat.value.time ? startHeartBeat(heartBeat.value.time) : '' // 是否启动心跳机制
    })

    ws.addEventListener('message', (e: MessageEvent) => {
      webSocketState.value = true
      try {
        const data = JSON.parse(e.data)
        console.log('收到消息:', data)
        config?.onMessage?.(data)
      } catch (error) {
        console.log('收到消息(非JSON):', e.data)
        config?.onMessage?.(e.data)
      }
    })

    ws.addEventListener('close', (e: CloseEvent) => {
      webSocketState.value = false // socket状态设置为断线
      console.log('WebSocket连接已断开', e)
      config?.onClose?.(e)
    })

    ws.addEventListener('error', (e: Event) => {
      webSocketState.value = false // socket状态设置为断线
      console.log('WebSocket连接发生错误', e)
      config?.onError?.(e)
      reconnectWebSocket() // 重连
    })
  }

  // 心跳 time:心跳时间间隔
  const startHeartBeat = (time: number): void => {
    if (!ws || ws.readyState !== 1) return;
    setTimeout(() => {
      // 发送心跳包（如需自定义内容可修改）
      const data = { type: 'heartbeat' };
      webSocketState.value && ws?.send(JSON.stringify(data));
      // 递归继续心跳
      startHeartBeat(time);
    }, time);
  }

  // 重连操作
  const reconnectWebSocket = (): void => {
    reconnectTimer = setTimeout(() => {
      reconnectWs()
    }, heartBeat.value.reconnect)
  }

  const reconnectWs = (): void => {
    console.log('拒绝重连', !heartBeat.value.time)
    if (!heartBeat.value.time) return // 如果主动关闭，则防止重连
    if (!ws) {
      // 第一次执行，初始化
      connectWebSocket()
    }
    if (ws && reconnectTimer) {
      // 防止多个websocket同时执行
      clearTimeout(reconnectTimer)
      reconnectTimer = null
      connectWebSocket()
    }
  }

  // 发送消息
  const sendMessage = (data: any): void => {
    if (ws && webSocketState.value) {
      ws.send(JSON.stringify(data))
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }

  // 获取连接状态
  const getConnectionState = (): boolean => {
    return webSocketState.value
  }

  // 获取当前URL
  const getCurrentUrl = (): string => {
    return currentUrl
  }

  return {
    onConnect,
    wsClose,
    sendMessage,
    getConnectionState,
    getCurrentUrl,
    webSocketState,
    heartBeat
  }
}

export default useWebSocket

