<template>
  <div class="select-user-list" v-loading="loading">
    <div class="list-type" v-if="treeData.length">
      <div
        v-tooltip
        class="list-type-item"
        v-for="item in treeData"
        :class="{ active: item.businessLineId === businessLineId }"
        @click="businessLineId = item.businessLineId"
      >
        {{ item.businessLineName }}
      </div>
    </div>
    <div class="list-content">
      <!-- 多选 -->
      <el-checkbox-group v-model="checked" @change="handleChange" v-if="multiple">
        <el-checkbox
          v-for="item in businessUserList"
          :value="item.userId"
          :key="item.userId"
        >
          <UserNameItem :item="item" class="list-content-item-name" />
        </el-checkbox>
      </el-checkbox-group>
      <!-- 单选 -->
      <el-radio-group v-model="checked" @change="handleChange" v-else>
        <el-radio
          class="list-content-item"
          v-for="item in businessUserList"
          :key="item.userId"
          :value="item.userId"
        >
          <UserNameItem :item="item" class="list-content-item-name" />
        </el-radio>
      </el-radio-group>
      <CustomEmpty v-if="!businessUserList.length" />
    </div>
  </div>
</template>

<script setup>
import { getSchoolLevelStaffAPI } from "@/api/ess/seal/seal"
const UserNameItem = defineAsyncComponent(() => import("./UserNameItem.vue"))
const { proxy } = getCurrentInstance()
const props = defineProps({
  multiple: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
})

const treeData = ref([])
const loading = ref(true)
const businessLineId = ref(null)
const checked = ref()
const previousCheckedList = ref([]) // 上次数据

const currentBusinessLine = computed(
  () => treeData.value.find((i) => i.businessLineId === businessLineId.value) || {}
)
const businessUserList = computed(
  () => currentBusinessLine.value?.sealHoldStaffDtos || []
)
const businessLineName = computed(() => currentBusinessLine.value?.businessLineName || "")

watch(
  () => props.modelValue,
  (newValue) => {
    checked.value = props.multiple
      ? newValue?.map((item) => item.userId) || []
      : newValue[0]?.userId || ""
    previousCheckedList.value = [...checked.value]
  },
  { deep: true, immediate: true }
)

async function getList() {
  try {
    loading.value = true
    const res = await getSchoolLevelStaffAPI()
    treeData.value = res.object
    if (treeData.value.length) {
      businessLineId.value = treeData.value[0].businessLineId
    }
  } finally {
    loading.value = false
  }
}
const emit = defineEmits(["on-select", "on-cancel"])
/**
 * 处理选项变化
 * @param {string|string[]} e - 当前选中值
 */
function handleChange(e) {
  // 获取用户基础信息
  const getUserBaseInfo = (user) => ({
    ...user,
    ascription: 0,
    holdFlag: 1,
    businessId: businessLineId.value,
    businessName: businessLineName.value,
  })

  if (props.multiple) {
    // 处理取消项
    const cancelItems = previousCheckedList.value.filter((i) => !e.includes(i))
    if (cancelItems.length) emit("on-cancel", cancelItems[0])

    // 获取已经选择
    const selectedUsers = businessUserList.value
      .filter((user) => e.includes(user.userId))
      .map((user) => getUserBaseInfo(user))

    // 更新 previousCheckedList 后再触发事件
    previousCheckedList.value = [...e]
    emit("on-select", selectedUsers)
  } else {
    const user = businessUserList.value.find((item) => item.userId === e)
    user && emit("on-select", [getUserBaseInfo(user)])
  }
}

// 清空选择
function clearSelect(e) {
  checked.value = null
}

getList()

defineExpose({
  clearSelect,
})
</script>

<style scoped lang="scss">
.select-user-list {
  display: flex;
  height: 100%;
  .list-type {
    width: 110px;
    flex-shrink: 0;
    text-align: center;
    padding-right: 10px;
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    &-item {
      cursor: pointer;
      border: 1px solid #d8d8d8;
      border-radius: 5px;
      padding: 5px;
      margin-bottom: 10px;

      &.active,
      &:hover {
        background: var(--el-color-primary);
        border-color: var(--el-color-primary);
        color: #fff;
      }
    }
  }
  .list-content {
    flex: auto;
    background: #fff;
    border-radius: 5px;
    height: 100%;
    padding: 15px 5px 15px 15px;
    overflow-y: auto;
    .list-content-item-name {
      width: 120px;
    }
    .el-radio {
      margin-right: 10px;
    }
  }
}
</style>
