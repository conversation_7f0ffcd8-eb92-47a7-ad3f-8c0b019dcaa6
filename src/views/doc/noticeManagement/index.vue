<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      inline
      class="white-form-box"
      label-width="100px"
    >
      <el-form-item label="公告标题" prop="noticeTitle">
        <el-input
          v-model="queryParams.noticeTitle"
          placeholder="请输入公告标题"
          clearable
          maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createBy">
        <user-select v-model="queryParams.createBy" />
      </el-form-item>
      <el-form-item label="公告类型" prop="noticeType">
        <dict-select
          v-model="queryParams.noticeType"
          :options="sys_notice_type"
          value-type="string"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" :loading="loading" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="Refresh" :loading="loading" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :has-toolbar="false"
      :data="noticeList"
      :loading="loading"
      :total="total"
      :opt-width="100"
      @reload="getList"
    >
      <template #actions>
        <el-button
          type="primary"
          icon="plus"
          @click="handleAdd"
          v-auths="['ess:notice:add']"
        >
          新增</el-button
        >
        <el-button
          type="primary"
          plain
          class="is-deep"
          :disabled="selections.length !== 1"
          @click="handleUpdate"
          v-auths="['ess:notice:edit']"
        >
          修改
        </el-button>
        <el-button
          v-auths="['ess:notice:remove']"
          type="danger"
          plain
          @click="handleDelete"
          :disabled="!selections.length"
        >
          删除
        </el-button>
      </template>
      <vxe-column type="checkbox" width="60" fixed="left" />
      <vxe-column type="seq" width="60" />
      <vxe-column title="公告标题" field="noticeTitle" show-overflow />
      <vxe-column title="公告类型" field="noticeType" show-overflow>
        <template #default="{ row }">
          <dict-tag :options="sys_notice_type" :value="row.noticeType" />
        </template>
      </vxe-column>
      <vxe-column title="创建者" field="createName" show-overflow />
      <vxe-column title="创建时间" field="createTime" show-overflow />
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '修改',
              click: () => handleUpdate(row),
              permission: ['ess:notice:edit'],
            },
            {
              text: '删除',
              type: 'danger',
              click: () => handleDelete(row),
              permission: ['ess:notice:remove'],
            },
          ]"
        />
      </template>
    </CustomTable>

    <!--    编辑、新增-->
    <EditNoticeModal
      ref="editNoticeRef"
      :sys_notice_type="sys_notice_type"
      @change="handleQuery"
      @refresh="getList"
    />
  </div>
</template>

<script setup name="NoticeManagement">
import EditNoticeModal from "./components/EditNoticeModal.vue"
import { listNotice, delNotice } from "@/api/ess/doc/noticeManagementApi"

const { proxy } = getCurrentInstance()
const { sys_notice_type } = proxy.useDict("sys_notice_type")

const noticeList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const editNoticeRef = ref(null)

const queryParams = ref({
  page: 1,
  limit: 10,
  noticeTitle: null,
  createBy: null,
})

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

/** 查询公告列表 */
function getList() {
  loading.value = true
  listNotice(queryParams.value).then((response) => {
    noticeList.value = response.object.records
    total.value = response.object.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  editNoticeRef.value.open()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const id = row?.id || selections.value[0].id
  editNoticeRef.value.open({ id })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const noticeIds = row?.id ? [row.id] : selections.value.map((item) => item.id)
  const noticeTitle =
    row.noticeTitle || selections.value.map((item) => item.noticeTitle).join(",")
  proxy.$modal
    .confirm('是否确认删除通知公告"' + noticeTitle + '"的数据项？')
    .then(function () {
      return delNotice(noticeIds)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

getList()
</script>
