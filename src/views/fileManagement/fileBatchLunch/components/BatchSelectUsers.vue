<template>

  <el-dialog title="快速选择签署方" v-model="dialogVisible" width="800px" :before-close="handleClose" append-to-body>


    <div class="form-body-item" v-for="(item, findex) in processedRecipientList" :key="item.value"
      :style="getStyle(item.key, findex)">
      <!-- 如果有多个ENTERPRISE_1_E，则值显示一个标题-->
      <div class="recipient-title" v-if="isFirstOccurrence(item.key, findex)">
        {{ selectDictLabel(recipient_type, item.key) }}
      </div>


      <el-table :data="item.list" :span-method="(params) => mergeCell(params, item.list)"
        :show-header="isFirstOccurrence(item.key, findex)">

        <el-table-column label="签署信息" prop="componentNames" width="120">
          <template #default="{ row }">
            <div class="whitespace-pre-wrap">
              {{ filterComponentNames(row.componentInfoDtos) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="涉及组织机构" prop="organization" v-if="item.key === 'ENTERPRISE_1_E'"></el-table-column>

        <el-table-column label="经办人" prop="approveName">
          <template #default="{ row }">
            已选{{ row.templateUsers?.length || 0 }}人
          </template>
        </el-table-column>

        <el-table-column label="操作" prop="opt" width="90">
          <template #default="{ row, $index }">
            <el-button text icon="Switch" type="primary" class="opt-btn" v-if="row.templateUsers?.length > 0"
              @click="openDialog(row, $index, item.key, findex)">
              更改
            </el-button>
            <el-button text icon="Plus" type="primary" class="opt-btn" v-else
              @click="openDialog(row, $index, item.key, findex)">
              添加
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>




    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>

  <!-- 印章管理员 通过handleClearSelect或v-model控制数据 -->
  <SelectSealUserModal v-model="processedRecipientList[active.findex].list[active.index].templateUsers" multiple ref="selectSealUserRef" @select="setTemplateUsers">
  </SelectSealUserModal>

  <!-- 选择全部 -->
  <UserRolePicker izChineseGj="1" :selectTypes="['user']" ref="userPickerRef" title="选择校内签字人（注：外国国籍成员选择功能受限，暂不开放）" @onSelectItems="setTemplateUsers">

  </UserRolePicker>

  <!-- 选择企业  这个通过v-model控制数据-->
  <EnterpriseUserModal ref="enterpriseUserRef" multiple @select="setTemplateUsers"  v-model="processedRecipientList[active.findex].list[active.index].templateUsers"/>
  
  <!-- 校外联系人 这个通过v-model控制数据-->
  <ExtUserModal ref="extUserModalRef" v-model="processedRecipientList[active.findex].list[active.index].templateUsers" multiple @select="setTemplateUsers" />
</template>

<script setup>
defineOptions({
  name: "BatchSelectUsers",
})
const UserRolePicker = defineAsyncComponent(() =>
  import("@/flow/components/user-role/picker2.vue")
)

const SelectSealUserModal = defineAsyncComponent(() =>
  import("@/views/template/useTemplate/components/SelectSealUserModal/index.vue")
)

const EnterpriseUserModal = defineAsyncComponent(() =>
  import("@/views/template/useTemplate/components/EnterpriseUserModal.vue")
)

const ExtUserModal = defineAsyncComponent(() => import("@/views/template/useTemplate/components/ExtUserModal.vue"))

const props = defineProps({
  recipientList: {
    type: Array,
    default: () => [],
  },
  // 修改父组件recipientTemplate
  modelValue: {
    type: Array,
    default: () => [],
  }
})


const { proxy } = getCurrentInstance()

// seal_belong_subject归属主体=中大/中大深圳
const { seal_belong_subject, recipient_type } = proxy.useDict(
  "seal_belong_subject",
  "recipient_type"
)
// 选中的数据
const processedRecipientList = ref([])

// 暂时的
const notRightSelected = ref([]);

const emit = defineEmits(["update:modelValue"])

// 处理数据
function processList(recipientList, sealDict) {
  if (!sealDict || !sealDict.length) return recipientList // 字典没到，直接返回原始
  // 深拷贝，避免直接修改 props
  let result = JSON.parse(JSON.stringify(recipientList))
  let r = []
  result.forEach((item) => {
    if (item.key === "ENTERPRISE_1_E") {
      item.list.forEach((e) => {
        r.push({
          key: 'ENTERPRISE_1_E',
          list: [e]
        })
      })

      //删掉result数组中item.key为ENTERPRISE_1_E的项
      result.splice(result.findIndex(e => e.key === 'ENTERPRISE_1_E'), 1)
    }
  })

  r.forEach(e => {
    e.list.forEach(e1 => {
      sealDict.forEach(e2 => {
        e1 = {
          ...e1,
          organization: e2.label,
          templateUsers: [],
          activeId:e2.value
        }
        e.list.push(e1)
      })
    })
    e.list.splice(0, 1)
  })

  // 合并r和result
  r.push(...result)

  return r
}

// 监听 recipientList 和 seal_belong_subject
watch(
  [() => props.recipientList, () => seal_belong_subject.value],
  ([newList, newDict]) => {
    processedRecipientList.value = processList(newList, newDict)
    // processedRecipientList.value = newList;
  },
  { immediate: true, deep: true }
)
const dialogVisible = ref(false)

/**
 * 打开弹窗方法 - 供父组件调用
 * @param {Object} options - 配置选项
 * @param {Array} options.recipientTemplate - 父组件传入的已选签署方数据
 */
function open({ recipientTemplate }) {
  dialogVisible.value = true;
  // 保存父组件传入的数据，用于取消时恢复
  notRightSelected.value = JSON.parse(JSON.stringify(recipientTemplate));
  // 初始化弹窗内的数据
  if (recipientTemplate.length != 0) {
    processedRecipientList.value = JSON.parse(JSON.stringify(recipientTemplate));
  }
}

/**
 * 关闭弹窗方法
 * 取消操作时调用，不更新父组件数据，仅关闭弹窗
 */
function handleClose() {
  dialogVisible.value = false;
}

/**
 * 确认提交方法
 * 将当前选择的签署方数据提交给父组件并关闭弹窗
 */
function handleSubmit() {
  // 将当前选择的数据保存到父组件
  emit('update:modelValue', JSON.parse(JSON.stringify(processedRecipientList.value)));
  // 关闭弹窗
  dialogVisible.value = false;
}

const filterComponentNames = (componentInfoDtos) => {
  if (!componentInfoDtos || !Array.isArray(componentInfoDtos)) {
    return []
  }
  return componentInfoDtos.map((i) => i.componentName).join("\n")
}

function mergeCell({ row, column, rowIndex, columnIndex }, list) {
  // 假设“签署信息”是第一列
  if (columnIndex === 0) {
    if (rowIndex === 0) {

      return [list.length, 1] // 合并所有行
    } else {
      return [0, 0] // 其他行隐藏
    }
  }
}

//点击表格获取到的key和index
let active = ref({
  key: "",
  index: 0,
  findex: 0
})

const userPickerRef = ref(null);
// 印章选择
const selectSealUserRef = ref(null);
//选择公司
const enterpriseUserRef = ref(null);
//选择校外联系人
const extUserModalRef = ref(null);

//统一获取到选择值
const setTemplateUsers = (value) => {
  const { index, key, findex } = active.value;
  if (key == 'ENTERPRISE_1_E') {
    value.forEach(e => {
      if(currentComponentInfoDtos.value[0].componentType == "SIGN_SIGNATURE"){
        e.userName = e.name;
        e.organization = currentOrganization.value;
        return
      }
        e.organization = e.organizationName;
      })
  } else if (key === "ENTERPRISE_2_E") {
    value.forEach(e => {
      e.userName = e.operatorName;
    })
  }
  processedRecipientList.value[findex].list[index].templateUsers = ref(value);
}

//当已选择后，拿到当前弹窗获得到选择用户数据
const currentTemplateUsers = ref([]);
const currentComponentInfoDtos = ref([]);
const currentOrganization = ref('');

// 表格添加或更改操作
const openDialog = (row, index, key, findex) => {
  active.value = {
    key,
    index,
    findex,
  }

  const { componentInfoDtos } = row;

  currentComponentInfoDtos.value = componentInfoDtos;
  currentTemplateUsers.value = processedRecipientList.value[findex]['list'][index].templateUsers || [];
  currentOrganization.value = row.organization;
  
  if (key == 'ENTERPRISE_1_E') {
    //个人印章
    if (componentInfoDtos[0].componentType == "SIGN_SIGNATURE" &&
      componentInfoDtos.length === 1
    ) {
      userPickerRef.value.onOpen(currentTemplateUsers.value);
    } else {
      selectSealUserRef.value.handleClearSelect();
      selectSealUserRef.value.open(row.activeId);
    }
  } else if (key == 'ENTERPRISE_2_E') {
    if (!currentTemplateUsers.value.length) {
      enterpriseUserRef.value.clearSelected();
    }
    enterpriseUserRef.value.open()
  } else {
    if (!currentTemplateUsers.value.length) {
      extUserModalRef.value.clearSelected();
    }
    extUserModalRef.value.open();
  }

}

// 添加判断是否是第一次出现的方法
function isFirstOccurrence(key, currentIndex) {
  if (key === 'ENTERPRISE_1_E') {
    // 检查当前key是否在之前的元素中出现过
    for (let i = 0; i < currentIndex; i++) {
      if (processedRecipientList.value[i].key === key) {
        return false; // 之前已经出现过，不是第一次
      }
    }
  }
  return true; // 是第一次出现
}




// 根据key和index动态设置按钮颜色
const getStyle = (key, findex) => {
  // 获取到processedRecipientList中key为ENTERPRISE_1_E的长度  
  const ENTERPRISE_1_E_Length = processedRecipientList.value.filter(e => e.key === 'ENTERPRISE_1_E').length;
  
  const bgImgMap = {
    'ENTERPRISE_1_E': new URL('@/assets/images/type-title-1.png', import.meta.url).href,
    'ENTERPRISE_2_E': new URL('@/assets/images/type-title-2.png', import.meta.url).href,
    'INDIVIDUAL_P': new URL('@/assets/images/type-title-3.png', import.meta.url).href
  };

  const colorMap = {
    'ENTERPRISE_1_E': '#24a87e', // 绿色
    'ENTERPRISE_2_E': '#2e89ff', // 蓝色
    'INDIVIDUAL_P': '#ff7b17'        // 橙色
  };

  const backgroundColorMap = {
    'ENTERPRISE_1_E': '#f2f8f7', // 绿色
    'ENTERPRISE_2_E': '#f2f6fc', // 蓝色
    'INDIVIDUAL_P': '#fbf6f3', // 橙色
  };

  const styles = {
    '--btn-color': colorMap[key] || '#24a87e',
    '--background-color': backgroundColorMap[key] || '#f2f8f7',
    '--recipient-title-bg': bgImgMap[key] ? `url(${bgImgMap[key]})` : '',
  }



  // 如果key为ENTERPRISE_1_E的公共样式
  if (key === 'ENTERPRISE_1_E' && ENTERPRISE_1_E_Length > 1 && findex !== 0) {
    styles['border-top'] = '1px solid #f4f4f5'
  }

  if (key == 'ENTERPRISE_1_E') {
    // 如果key为ENTERPRISE_1_E，并且findex为0则将margin-bottom设置为0
    if (ENTERPRISE_1_E_Length - 1 > findex) {
      styles['margin-bottom'] = '0';
    }

    if (findex !== 0) {
      styles['padding-top'] = '0'
    }
  }

  return styles;
};


defineExpose({
  open,
})

</script>

<style scoped lang="scss">
@use "sass:list";



.form-body-item {
  position: relative;
  padding-top: 30px;
  margin: 0 0 30px 5px;

  .recipient-title {
    display: inline-block;
    color: #fff;
    height: 40px;
    line-height: 30px;
    width: 150px;
    text-align: center;
    background-size: 100% 100%;
    font-size: 14px;
    position: absolute;
    top: 0;
    left: -12px;
    z-index: 99;
  }

  $colors: #f2f8f7, #f2f6fc, #fbf6f3;
  $btnColors: #24a87e, #2e89ff, #ff7b17;

  @for $i from 1 through 12 {
    &:nth-child(#{$i}) {

      .recipient-title {
        background-image: var(--recipient-title-bg);
      }

      :deep(.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell) {
        background-color: var(--background-color);
      }

      .opt-btn {
        // 如果当前是第一次出现，则颜色为绿色
        color: var(--btn-color);
        padding-left: 0;

        :deep(span) {
          margin-left: 2px;
        }

        &:hover {
          background: transparent;
        }
      }
    }
  }


  :deep(.el-table) {
    thead th.el-table__cell {
      color: #6b6b6b;
    }

    tr {
      background-color: #fbfbfc;
    }

    .el-table__cell {
      padding: 8px 0;
    }

    tbody .el-table__row:not(:last-child) {
      td.el-table__cell {
        border-bottom: 1px solid #f4f4f5;
      }
    }

    &.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
      background-color: #fbfbfc;
    }

    .el-button {
      padding-left: 0;
    }
  }

  .add-wrapper {
    margin-top: 10px;

    .el-button {
      @apply fw-500 p-x-6px bg-#fff min-h-30px h-30px rounded-4px;

      &:hover {
        background: var(--el-button-text-color);
      }

      :deep(span) {
        margin-left: 2px;
      }
    }
  }
}
</style>