import request from '@/utils/request'
import { essPrefix } from '@/config/constant'

// 获取文件签署链接
export function getFileSignUrl(essFlowId: string) {
	return request({
		url: essPrefix + '/flow/sign/' + essFlowId,
		method: 'get',
	})
}

// 删除草稿箱信息
export function removeDraft(ids: any) {
	return request({
		url: essPrefix + '/flow/draft/remove/' + ids,
		method: 'get',
	})
}

// 预览文件详情
export function getFlowPreviewUrl(essFlowId: string) {
	return request({
		url: essPrefix + '/flow/preview/' + essFlowId,
		method: 'get',
	})
}

// 文件列表
export function getFlowListPage(data: Object) {
	return request({
		url: essPrefix + '/flow/listPage',
		method: 'post',
		data,
	})
}

// 文件撤销
export function cancelInitiatedFile(data: Object) {
	return request({
		url: essPrefix + '/flow/cancel',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'multipart/form-data',
		},
	})
}

// 文件撤销
export function urgingFlow(essFlowId: string) {
	return request({
		url: essPrefix + '/flow/urging/' + essFlowId,
		method: 'get',
	})
}

// 文件批量下载
export function multipleDownloadFile(data: object) {
	return request({
		url: essPrefix + '/flow/batchDownload',
		method: 'post',
		data:data,
		headers: {
			'Content-Type': 'multipart/form-data',
		},
	})
}
// 保存草稿-文件
export function saveFileDraftAPI(data: object) {
	return request({
		url: essPrefix + '/flow/draft',
		method: 'post',
		data,
	})
}
// 获取文件草稿信息详细信息
export function getFileDraftInfoAPI(id: string) {
	return request({
		url: essPrefix + '/flow/draft/' + id,
		method: 'get',
	})
}

// 使用嵌入页发起签署流程-文件
export function getFileInitiateUrlAPI(flowId: string) {
	return request({
		url: essPrefix + '/flow/initiate/' + flowId,
		method: 'get',
	})
}

// 文件草稿箱带分页
export function getFileDraftListAPI(data: object) {
	return request({
		url: essPrefix + '/flow/listDraftPage',
		method: 'post',
    data
	})
}

// 获取草稿文件预览地址
export function getDraftPreviewUrlAPI(flowId: string) {
	return request({
		url: essPrefix + '/flow/draft/previewUrl/' + flowId,
		method: 'get',
	})
}
// 更新草稿-文件 flowType1=文件发起；2=模板发起
export function updateDraftAPI(flowId: string, defFlowId: string, flowType: number) {
	return request({
		url: `${essPrefix}/flow/updateDraft/${flowId}/${defFlowId}/${flowType}`,
		method: 'get',
	})
}
