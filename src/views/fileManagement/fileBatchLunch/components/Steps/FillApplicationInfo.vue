<template>
  <div v-loading="loading">
    <el-form :model="form" ref="formRef" :rules="rules" label-width="160px" :inline="false" class="w-800px mt-10px">
      <template v-if="+templateInfo.flowFlag !== 0">
        <el-form-item  label="标题:" prop="flowTitle">
          <el-input :disabled="!!currJob" v-model.trim="form.flowTitle" :maxlength="20" show-word-limit clearable placeholder="请输入标题"></el-input>
        </el-form-item>
      </template>
      <el-form-item label="申请原由:" prop="applyReason">
        <el-input :disabled="!!currJob" type="textarea" v-model.trim="form.applyReason" :maxlength="255" show-word-limit clearable
          placeholder="请输入申请原由" :rows="4"></el-input>
      </el-form-item>
      <el-form-item label="模版名称:" prop="templateName">
        {{ form.templateName || templateInfo.templateName }}
      </el-form-item>
      <!-- <el-form-item label="盖章后电子文件名称:" prop="flowName">
        <el-input v-model="form.flowName" clearable show-word-limit placeholder="请输入盖章后电子文件名称"></el-input>
      </el-form-item> -->
      <el-form-item label="签署方:" prop="flowBatchPublishId" v-if="!currJob">
        <el-button  type="primary" @click="setSignatory">模版预览并设置签署方</el-button>
      </el-form-item>

      <template v-if="form.flowBatchPublishId">

        <el-form-item :label="`批量发起${contractNum}份：`">
          <el-button type="primary" @click="downloadSignatoryInfo">
            <svg-icon name="local-download-line" :size="16" class="mr-2px" />
            下载签署方
          </el-button>
        </el-form-item>

        <el-form-item label="签署截止时间:" prop="deadline" v-if="+templateInfo.flowFlag !== 0">
          <span>审批通过后</span>
          <el-input-number :disabled="!!currJob" v-model="form.deadline" :min="1" class="ml-5px" />
          <span class="ml-5px">天</span>
        </el-form-item>
        <el-form-item label="签署截止时间:" prop="deadlineTime" v-if="+templateInfo.flowFlag === 0">
          <el-date-picker v-model="form.deadlineTime" placeholder="请选择签署截止时间" value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss" type="datetime"></el-date-picker>
        </el-form-item>
        <el-form-item label="申请人信息:" prop="user" v-if="!currJob">
          <UserInfoDescCard :applyUser="form.loginUser" />
        </el-form-item>
      </template>
    </el-form>

    <!-- 模版预览并设置签署方 -->
    <SetSignatoryModal ref="setSignatoryRef" :templateInfo="templateInfo" @confirm="getSetSignatory" v-if="!currJob" />
  </div>
</template>

<script setup name="FillApplicationInfo">
import { provide } from 'vue'
// 引入节流函数，防止频繁触发
import { throttle } from "lodash-es"
import { generateUUID } from "@/utils/index"
import { queryFormDataByOrderId } from "@/api/order/run-application"
import { rule } from "@/utils/validate"
// 引入API前缀常量
import { essPrefix } from "@/config/constant"

//申请人
const UserInfoDescCard = defineAsyncComponent(() =>
  import("@/views/template/useTemplate/components/UserInfoDescCard.vue")
)
const SetSignatoryModal = defineAsyncComponent(() => import("../SetSignatoryModal.vue"))

const props = defineProps({
  templateInfo: {
    type: Object,
    default: () => ({}),
  },
  draftForm: {
    type: Object,
    default: () => ({}),
  },
  currJob: {
    type: Object,
    default: null,
  },
})
const { proxy } = getCurrentInstance()
const { templateInfo } = toRefs(props)
const loading = ref(false)
const formRef = ref(null)
const form = ref({
  flowTitle: "",
  applyReason: "",
  deadline: null,
  approveDtoList: [],
  flowDisplayType: 1,
  resourceType: 1,
  //上传文件，服务器响应的批序号，不再以approveDtoList作为签署方rule校验，以flowBatchPublishId作为校验
  flowBatchPublishId:""
})

// 流程回显
async function initForm() {
  if (props.currJob?.orderId) {
    loading.value = true
    const res = await queryFormDataByOrderId(props.currJob.orderId)
    const formData = res.object ? res.object : {}
    // assign 是浅拷贝
    Object.assign(form.value, formData)
    loading.value = false
    form.value.templateName = form.value.extParam.templateName
    contractNum.value = formData.extParam.signerCount;
  } else {
    form.value = {
      ...form.value,
      ...templateInfo.value,
      resourceId : templateInfo.value?.id,
      deadline : templateInfo.value?.signTmpLimitDay
    }
    contractNum.value = templateInfo.value?.contractNum
  }
}

watch(
  () => props.draftForm,
  (val) => {
    if (!val.id) return
    const flowUserRefs = (val.flowUserRefs || []).map((item) => {
      return {
        ...item,
        approveName: item.userName,
      }
    })
    form.value = {
      ...val,
      deadline: val.signTmpLimitDay ? +val.signTmpLimitDay : null,
      deadlineTime: val.signLimitTime,
      approveDtoList: flowUserRefs,
    }
  },
  {
    immediate: true,
  }
)

const rules = ref({
  flowTitle: [
    { required: true, message: "请输入标题", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" },
  ],
  applyReason: [{ required: true, message: "请输入申请原由", trigger: "blur" }],
  deadline: [{ required: true, message: "请输入截止时间", trigger: "change" }],
  deadlineTime: [{ required: true, message: "请输入截止时间", trigger: "change" }],
  approveDtoList: [{ required: false, message: "请选择签署方", trigger: "change" }],
  flowBatchPublishId: [{ required: true, message: "请上传签署方信息", trigger: "change" }],
})

// -------- 设置签署方 --------

const setSignatoryRef = ref(null)
// 设置签署方
function setSignatory() {
  setSignatoryRef.value.open(form.value.approveDtoList)
}

// 确认签署方
function getSetSignatory(data, publishId) {
  try {
    // 安全处理publishId
    if (publishId) {
      // 如果publishId已经是对象，直接使用
      if (typeof publishId === 'object') {
        form.value.flowBatchPublishId = publishId;
      } else {
        // 尝试解析JSON字符串
        form.value.flowBatchPublishId = JSON.parse(publishId);
      }
    }
    //当flowBatchPublishId有值时，清空flowBatchPublishId的校验
    formRef.value.clearValidate("flowBatchPublishId");

    // 处理签署方数据，添加ID和签署顺序
    const arr = data.map((item, index) => {
      return {
        ...item,
        id: generateUUID(),
        signOrder: index + 1, // 签署顺序
      }
    })
    form.value.approveDtoList = arr
    formRef.value.clearValidate("approveDtoList")
  } catch (error) {
    // 解析失败时的错误处理
    proxy.$modal.msgError("处理签署方数据失败，请重试");
  }
}

//获取合同总数
// 合同份数
let contractNum = ref(0)

  //传递合同总数
provide('contractNum',contractNum)
//传递到所有子组件
provide('contractNumChange',(val) => {
  contractNum.value = val;
})


// 下载签署方信息
const downloadSignatoryInfo = throttle(() => {
  if(!form.value.flowBatchPublishId){
    proxy.$modal.msgError("请先上传签署方信息")
    return
  }
  proxy.download(
    essPrefix + `/flowInfoBatch/exportOriginData?flowBatchPublishId=${form.value.flowBatchPublishId}`,
    {},
    `签署方信息.xlsx`,
    {
      headers: {
        "Content-Type": "application/json;charset=UTF-8"
      }
    })
}, 800)
/**
 * 父组件自定义校验规则
 * @param isCustomRules 是否自定义校验规则
 * @param {Array} filterRules 过滤规则，传递不需要的规则，从rules中过滤掉
 * @default 默认的rules规则，有flowTitle标题、applyReason内容、flowName文件名称、deadline签署截止时间、deadlineTime签署截止时间、approveDtoList签署方
 * @returns {Promise<Object>} 返回表单数据
 */
function validForm(isCustomRules = false, filterRules = []) {
  //需要等待新规则生效后，再进行校验
  nextTick(() => {
    if (isCustomRules) {
      // filterRules参数可以是多个或者一个的原有rules对象里的属性，过滤rules.value规则
      const filter = Object.keys(rules.value).filter(key => !filterRules.includes(key))
      const newRules = filter.reduce((acc, key) => {
        acc[key] = rules.value[key]
        return acc
      }, {})
      rules.value = newRules
    }
  })

  return new Promise((resolve, reject) => {
    formRef.value.validate(async (valid) => {
      if (valid) {
        resolve(form.value)
      } else {
        proxy.$modal.msgWarning("请完善表单信息！")
        reject(valid)
      }
    })
  })
}
// -------- 设置签署方 --------

onMounted(() => {
  initForm()
})

defineExpose({
  validForm,
  contractNum,
})
</script>

<style scoped lang="scss">
</style>
