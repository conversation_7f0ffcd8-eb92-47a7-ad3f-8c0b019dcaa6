<template>
  <div class="app-container">
    <CustomTabs v-model="currentTab" :options="tabsToAuthList" />

    <DoJobFlowTable v-if="currentTab === 0" />
    <SignFileTable v-if="currentTab === 1" />
  </div>
</template>

<script setup name="TodoMine">
const DoJobFlowTable = defineAsyncComponent(() =>
  import("./components/DoJobFlowTable.vue")
)
const SignFileTable = defineAsyncComponent(() => import("./components/SignFileTable.vue"))
const route = useRoute()
const { proxy } = getCurrentInstance()
const tabs = [
  { label: "待我审批", value: 0, permissions: ["runJob:todo:page"] },
  { label: "待我签署", value: 1, permissions: ["todo:mineFile:list"] },
]
const tabsToAuthList = computed(() =>
  tabs.filter((item) => proxy.$auth.hasPermiOr(item.permissions))
)

const getDefaultTab = () => {
  // 优先根据 query.tab 且有权限，否则取第一个有权限的 tab
  const queryTab = Number(route.query.tab)
  if (!isNaN(queryTab) && tabsToAuthList.value.some((item) => item.value === queryTab)) {
    return queryTab
  }
  return tabsToAuthList.value?.[0]?.value ?? 0
}

const currentTab = ref(getDefaultTab())
</script>

<style scoped lang="scss"></style>
