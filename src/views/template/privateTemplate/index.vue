<template>
  <div class="has-iframe-page">
    <div v-show="!showPreviewPage" class="app-container">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
        class="white-form-box"
        label-width="90px"
        @submit.native.prevent
      >
        <el-form-item label="模板名称" prop="templateName">
          <el-input
            v-model="queryParams.templateName"
            placeholder="请输入模板名称"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属业务线" prop="businessLineId">
          <BusinessSelect v-model="queryParams.businessLineId" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTimeRange">
          <el-date-picker
            v-model="queryParams.createTimeRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="common-input-width"
            clearable
            :default-time="dateDefaultTime"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <CustomTable
        ref="customTableRef"
        v-model:show-search="showSearch"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :data="tableData"
        custom
        :has-toolbar="false"
        :opt-width="120"
        :loading="loading"
        :total="total"
        @reload="getList"
      >
        <vxe-column type="seq" width="70" fixed="left" />
        <vxe-column field="templateName" title="模板名称" fixed="left" min-width="200" />
        <vxe-column field="businessLineName" title="所属业务线" min-width="120" />
        <vxe-column field="templateType" title="模版类型" width="90">
          <template #default="{ row }">
            <dict-tag :options="template_type" :value="row.templateType" />
          </template>
        </vxe-column>
        <vxe-column field="essTemplateId" title="模板id" min-width="160" />
        <vxe-column field="createUser" title="创建人" min-width="100" />
        <vxe-column field="createTime" title="创建时间" width="170" />
        <vxe-column field="flowFlag" title="使用模板流程设置" width="160">
          <template #default="{ row }">
            <div class="flex-y-center">
              {{ selectDictLabel(template_flow_flag, row.flowFlag) }}
              <el-link
                type="success"
                v-if="+row.flowFlag === 1"
                underline="never"
                class="ml-5px"
                @click="toFlowSetting(row)"
                v-auths="['template:privateList:flowSetting']"
              >
                (查看流程)
              </el-link>
            </div>
          </template>
        </vxe-column>
        <template #opts="{ row }">
          <TableColOptBtn
            :key="Math.random()"
            :buttons="[
              {
                text: '预览',
                click: () => handlePreview(row),
                permission: ['template:priList:preview'],
              },
              {
                text: '下载模版',
                click: () => handleDownload(row),
                permission: ['template:priList:download'],
              },
            ]"
          />
        </template>
      </CustomTable>
    </div>

    <!-- 预览 -->
    <PreviewTemplatePage ref="previewRef" v-model="showPreviewPage" />
    <!-- 流程图 -->
    <FlowViewModal ref="flowViewModalRef" />
  </div>
</template>

<script setup name="PrivateTemplate">
import mittBus from "@/utils/mitt"
import { getTemplatePriListPageAPI, downloadTemplate } from "@/api/ess/template/template"

const BusinessSelect = defineAsyncComponent(() =>
  import("@/views/components/BusinessSelect.vue")
)
const PreviewTemplatePage = defineAsyncComponent(() =>
  import("@/views/template/templateManagement/components/PreviewTemplatePage.vue")
)
const FlowViewModal = defineAsyncComponent(() =>
  import("@/views/flowManagement/flowLaunch/components/FlowViewModal.vue")
)

const { proxy } = getCurrentInstance()
const { template_status, template_type, template_flow_flag } = proxy.useDict(
  "template_status",
  "template_type",
  "template_flow_flag"
)

const router = useRouter()
const route = useRoute()

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const selections = ref([])
const total = ref(0)

const previewRef = ref(null) // 预览page
const showPreviewPage = ref(false) // 预览页面

const queryParams = reactive({
  templateName: null,
  templateStatus: null,
  businessLineId: null,
  createTimeRange: [],
  limit: 10,
  page: 1,
})

/** 查询列表 */
function getList() {
  loading.value = true
  queryParams.queryStartTime = queryParams.createTimeRange?.[0]
  queryParams.queryEndTime = queryParams.createTimeRange?.[1]
  getTemplatePriListPageAPI(queryParams)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 配置流程
function toFlowSetting(row) {
  proxy.$refs.flowViewModalRef.open(row.workId)
}

/** 预览按钮操作 */
function handlePreview(row) {
  showPreviewPage.value = true
  previewRef.value.open(row)
}

// 下载模版
async function handleDownload(row) {
  proxy.$download.downloadGetFilename(downloadTemplate(row.id))
}

getList()

onActivated(() => {
  if (route.query.r) {
    getList()
    delete route.query.r
  }
})

// 页面加载时
onMounted(() => {
  mittBus.on("refreshTemplateList", () => {
    getList()
  })
})
// 页面卸载时
onUnmounted(() => {
  mittBus.off("refreshTemplateList", () => {
    getList()
  })
})
</script>
