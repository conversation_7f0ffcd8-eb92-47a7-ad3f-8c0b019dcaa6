import ConvertName from './convert-name/index.vue'
import ConvertRoleName from './convert-name/role-index.vue'
import ConvertGroupName from './convert-name/group-index.vue';
import { App } from 'vue'
import UserSelect from './user-role/user.vue'
import UserPicker from './user-role/picker2.vue'
import UserPickerBtn from './user-role/userPickerBtn.vue'
import RolePicker from './user-role/role.vue'
import PostPicker from './user-role/post.vue'
import DeptPicker from './user-role/dept.vue'
import PhoneInput from './form-create/phone.vue'
import IdCartInput from './form-create/id-card.vue'
import FlowNameInput from './form-create/flow-name.vue'
import FormNameInput from './form-create/form-name.vue'
import FormCodeInput from './form-create/form-code.vue'
import EmailInput from './form-create/email.vue'
import SignInput from "./sign/index.vue";
import TagShowList from './form-create/tag-show-list.vue'


// vite glob导入
const modules: Record<string, () => Promise<unknown>> = import.meta.glob([
  '../../views/jsonflow/*/*.vue',
  '../../views/order/*/*.vue',
  '../../views/template/useTemplate/components/Steps/FillApplicationInfo.vue',
  '../../views/fileManagement/fileLunch/components/Steps/FillApplicationInfo.vue',
  '../../views/fileManagement/fileBatchLunch/components/Steps/FillApplicationInfo.vue'
])

/**
 * 导出全局注册工作流审批表单组件
 * @param app vue 实例
 */
function dynamicImport(app: App) {
  for (const module in modules) {
    // @ts-ignore
    app.component(module, defineAsyncComponent(modules[module]))
  }
  app.component('UserPicker', UserPicker)
  app.component('UserSelect', UserSelect)
  app.component('UserPickerBtn', UserPickerBtn)
  app.component('RolePicker', RolePicker)
  app.component('PostPicker', PostPicker)
  app.component('DeptPicker', DeptPicker)
  app.component('PhoneInput', PhoneInput)
  app.component('IdCartInput', IdCartInput)
  app.component('FlowNameInput', FlowNameInput)
  app.component('FormNameInput', FormNameInput)
  app.component('FormCodeInput', FormCodeInput)
  app.component('EmailInput', EmailInput)
  app.component('SignInput', SignInput)
  app.component('TagShowList', TagShowList)
}

export { ConvertName, ConvertRoleName, ConvertGroupName, dynamicImport }
