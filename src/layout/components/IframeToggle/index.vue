<template>
  <div v-for="(item, index) in tagsViewStore.iframeViews" :key="item.path" class="iframe-layout">
    <inner-link
      v-show="route.path === item.path"
      v-if="isHttp(item.meta.link)"
      :iframe-id="'iframe' + index"
      :src="iframeUrl(item.meta.link, item.query)"
    />
    <template v-else>
      <transition name="fade">
        <component :is="getComponentName(item)" v-show="route.path === item.path" />
      </transition>
    </template>
  </div>
</template>

<script setup>
import InnerLink from '../InnerLink/index'
import useTagsViewStore from '@/store/modules/tagsView'
import { isHttp } from '@/utils/validate'
import usePermissionStore from '@/store/modules/permission'

const route = useRoute()
const tagsViewStore = useTagsViewStore()

const comMap = {} // 存储组件对象
const allIframes = getComponentsAll()

function iframeUrl(url, query) {
  const params = new URLSearchParams(query).toString();
  return params ? `${url}?${params}` : url;
}
 function getComponentName(item) {
  return  comMap[item.name]
}

function getComponentsAll() {
  const routes = usePermissionStore().allRoutes;
  return routes.reduce((iframeArr, item) => {
    const child = item.children?.[0];
    if (child?.meta?.link) {
      child.component.__warnedDefineAsync = true // 去掉defineAsyncComponent警告
      comMap[child.name] = markRaw(child.component);
      iframeArr.push(item);
    }
    return iframeArr;
  }, []);
}
</script>
