<template>
	<el-skeleton v-if="loading" class="icon-skeleton" animated>
		<template #template>
			<div v-for="item in 5" class="icon-skeleton-item">
				<el-skeleton-item variant="image" style="width: 60px; height: 60px;" />
			</div>
		</template>
	</el-skeleton>
</template>

<script setup>
const props = defineProps({
	loading: {
		type: Boolean,
		default: true,
	},
})
</script>

<style scoped lang="scss">
.icon-skeleton {
  display: flex;
  .icon-skeleton-item {
    width: 110px;
    display: flex;
    justify-content: center;
  }
  .el-skeleton-item {
    margin-right: 10px;
  }
}
</style>
