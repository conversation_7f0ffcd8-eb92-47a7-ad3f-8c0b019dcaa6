import auth from '@/plugins/auth'
import router, { constantRoutes, dynamicRoutes } from '@/router'
import { getRouters } from '@/api/menu'
import Layout from '@/layout/index'
import ParentView from '@/components/ParentView'
import InnerLink from '@/layout/components/InnerLink'
import { isHttp } from '@/utils/validate'

// 匹配views里面所有的.vue文件
const modules = import.meta.glob('./../../views/**/!(components)/*.vue')

const usePermissionStore = defineStore('permission', {
	state: () => ({
		allRoutes: [], // 所有路由
		routes: [],
		addRoutes: [],
		defaultRoutes: [],
		topbarRouters: [],
		sidebarRouters: [],
	}),
	actions: {
		setRoutes(routes) {
			this.addRoutes = routes
			this.routes = constantRoutes.concat(routes)
		},
		setDefaultRoutes(routes) {
			this.defaultRoutes = constantRoutes.concat(routes)
		},
		setTopbarRoutes(routes) {
			this.topbarRouters = routes
		},
		setSidebarRouters(routes) {
			this.sidebarRouters = routes
		},
		// 统一路由设置方法
		setRoutesByType(type, routes) {
			const routeMap = {
				main: () => {
					this.addRoutes = routes
					this.routes = constantRoutes.concat(routes)
				},
				default: () => (this.defaultRoutes = constantRoutes.concat(routes)),
				topbar: () => (this.topbarRouters = routes),
				sidebar: () => (this.sidebarRouters = routes),
			}
			routeMap[type]?.()
		},
		async generateRoutes() {
			const { data } = await getRouters()
			await this.generateRoutesToSetData({ data })
			return Promise.resolve()
		},
		// 根据路由进行赋值
		async generateRoutesToSetData({ data }) {
			const routeData = JSON.parse(JSON.stringify(data))
			const sidebarRoutes = filterAsyncRouter(routeData)
			const rewriteRoutes = filterAsyncRouter(routeData, false, true)
			const defaultRoutes = filterAsyncRouter(routeData)

			// 处理动态路由
			const asyncRoutes = filterDynamicRoutes(dynamicRoutes)
			asyncRoutes.forEach(route => router.addRoute(route))

			// 设置各类路由
			this.setRoutesByType('main', rewriteRoutes)
			this.allRoutes = [...asyncRoutes, ...rewriteRoutes]
			this.setRoutesByType('sidebar', constantRoutes.concat(sidebarRoutes))
			this.setRoutesByType('default', sidebarRoutes)
			this.setRoutesByType('topbar', defaultRoutes)

			// 添加非http路由
			rewriteRoutes
				.filter(route => !isHttp(route.path))
				.forEach(route => router.addRoute(route))

			return Promise.resolve()
		},
	},
})

// 处理路由名称
const normalizeRouteName = name => name.includes(':') ? name.split(':')[0].replace(/\/$/, '') : name

// 解析路由组件
function resolveComponent(component) {
	if (typeof component === 'string') {
		// 处理字符串类型的组件
		switch (component) {
			case 'Layout':
				return Layout
			case 'ParentView':
				return ParentView
			case 'InnerLink':
				return InnerLink
			default:
				return loadView(component)
		}
	}
	// 如果已经是组件，直接返回
	return component
}
// 过滤异步路由
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
	return asyncRouterMap.filter(route => {
		route.name = normalizeRouteName(route.name)

		if (type && route.children) {
			route.children = filterChildren(route.children)
		}

		if (route.component) {
			route.component = resolveComponent(route.component)
		}

		if (route.children?.length) {
			route.children = filterAsyncRouter(route.children, route, type)
		}
		else {
			delete route.children
			delete route.redirect
		}

		return true
	})
}

function filterChildren(childrenMap, lastRouter = false) {
	const children = []

	childrenMap.forEach(el => {
		el.name = normalizeRouteName(el.name)

		if (el.children?.length) {
			if (el.component === 'ParentView' && !lastRouter) {
				el.children.forEach(c => {
					c.path = `${el.path}/${c.path}`
					c.name = normalizeRouteName(c.name)

					if (c.children?.length) {
						children.push(...filterChildren(c.children, c))
						return
					}
					children.push(c)
				})
				return
			}
		}

		if (lastRouter) {
			el.path = `${lastRouter.path}/${el.path}`
			if (el.children?.length) {
				children.push(...filterChildren(el.children, el))
				return
			}
		}

		children.push(el)
	})
	return children
}

// 过滤动态路由
export function filterDynamicRoutes(routes) {
	return routes.filter(
		route =>
			(route.permissions && auth.hasPermiOr(route.permissions))
			|| (route.roles && auth.hasRoleOr(route.roles)),
	)
}

// 加载视图组件
export const loadView = view => {
	for (const path in modules) {
		const dir = path.split('views/')[1].split('.vue')[0]
		if (dir === view) {
			return () => modules[path]()
		}
	}
	return null
}
export default usePermissionStore
