<template>
	<div class="dept-tree">
		<div class="dept-search u-m-b-10">
			<el-input
				v-model="deptName"
				placeholder="请输入部门名称"
				clearable
				prefix-icon="Search"
				maxlength="50"
			/>
		</div>
		<div class="dept-tree-box">
			<el-tree
				ref="deptTreeRef"
				:data="deptOptions"
				:props="{ label: 'label', children: 'children' }"
				:expand-on-click-node="false"
				:filter-node-method="filterNode"
				node-key="id"
				highlight-current
				default-expand-all
				@node-click="handleNodeClick"
			>
				<template #default="{ node, data }">
					<span v-tooltip class="custom-tree-node">{{ node.label }}</span>
				</template>
			</el-tree>
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	deptOptions: Array,
})
const { proxy } = getCurrentInstance()
const deptName = ref("")
const deptTreeRef = ref(null)
const emit = defineEmits(["change"])

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
	if (!value) { return true }
	return data.label.indexOf(value) !== -1
}

const setCurrentKey = e => {
	deptTreeRef.value.setCurrentKey(e)
}
/** 根据名称筛选部门树 */
watch(deptName, val => {
	proxy.$refs["deptTreeRef"].filter(val)
})
/** 节点单击事件 */
function handleNodeClick(data) {
	emit("change", data)
}
defineExpose({
	setCurrentKey,
})
</script>

<style scoped lang="scss"></style>
