<template>
  <div>
    <el-upload
        v-if="type == 'url'  && !readOnly"
        :action="uploadUrl"
        :before-upload="handleBeforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        name="file"
        :show-file-list="false"
        :headers="headers"
        class="editor-img-uploader"
    >
      <i ref="uploadRef" class="editor-img-uploader"/>
    </el-upload>
  </div>
  <div class="editor">
    <quill-editor
        ref="quillEditorRef"
        v-model:content="content"
        content-type="html"
        :options="options"
        :style="styles"
        @text-change="handleTextChange"
    />
  </div>
</template>

<script setup>
import { QuillEditor, Quill } from "@vueup/vue-quill"
import "@vueup/vue-quill/dist/vue-quill.snow.css"

import { ImageDrop } from "quill-image-drop-module"

Quill.register("modules/imageDrop", ImageDrop)

import BlotFormatter from "quill-blot-formatter"
import CustomImageSpec, { isDragging } from './CustomImageSpec'

Quill.register("modules/blotFormatter", BlotFormatter)

import { uploadBaseUrl, imageApi } from "@/config/constant"
import { getToken } from "@/utils/auth"

const { proxy } = getCurrentInstance()
const emit = defineEmits(['update:modelValue'])

const quillEditorRef = ref()
const uploadUrl = ref(uploadBaseUrl)
const headers = ref({
  Authorization: "Bearer " + getToken(),
})

// 存储完整URL和相对URL的映射关系
const urlMap = ref(new Map())
// 防止循环更新的标志
const isInternalUpdate = ref(false)

const props = defineProps({
  /* 编辑器的内容 */
  modelValue: {
    type: String,
  },
  /* 高度 */
  height: {
    type: Number,
    default: null,
  },
  /* 最小高度 */
  minHeight: {
    type: Number,
    default: null,
  },
  /* 只读 */
  readOnly: {
    type: Boolean,
    default: false,
  },
  /* 上传文件大小限制(MB) */
  fileSize: {
    type: Number,
    default: 5,
  },
  /* 类型（base64格式、url格式） */
  type: {
    type: String,
    default: "url",
  },
})

const options = ref({
  theme: "snow",
  bounds: document.body,
  debug: "warn",
  modules: {
    // 工具栏配置
    toolbar: props.readOnly ? false : [
      ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
      ["blockquote", "code-block"], // 引用  代码块
      [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
      [{ indent: "-1" }, { indent: "+1" }], // 缩进
      [{ size: ["small", false, "large", "huge"] }], // 字体大小
      [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
      [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
      [{ align: [] }], // 对齐方式
      ["clean"], // 清除文本格式
      ["link", "image", "video"], // 链接、图片、视频
    ],
    imageDrop: !props.readOnly,
    // 图片缩放
    blotFormatter: props.readOnly ? false : {
      specs: [
        CustomImageSpec,
      ],
      toolbar: {
        mainClassName: "blot-formatter__toolbar",
      }
    },
  },
  placeholder: props.readOnly ? "" : "请输入内容",
  readOnly: props.readOnly,
})

const styles = computed(() => {
  let style = {}
  if (props.minHeight) {
    style.minHeight = `${props.minHeight}px`
  }
  if (props.height) {
    style.height = `${props.height}px`
  }

  if (props.readOnly) {
    style.border = 'none'
  }
  return style
})

const content = ref("")

// 将relativeUrl转换为presignedUrl用于显示
function convertRelativeToFullUrl(htmlContent) {
  if (!htmlContent && isDragging) return htmlContent
  return htmlContent.replace(/<img[^>]+src="([^"]*)"[^>]*>/g, (match, src) => {
    if (src && src.startsWith(imageApi)) {
      if (!urlMap.value.has(src)) {
        let relativeUrl = src.replace(imageApi, '')
        if (relativeUrl.includes('?')) {
          relativeUrl = relativeUrl.split('?')[0]
        }
        urlMap.value.set(src, relativeUrl)
      }

      return match
    }
    if (src && src.startsWith('http')) {
      return match
    }

    if (src && !src.startsWith('http')) {

      for (let [displayUrl, relativeUrl] of urlMap.value.entries()) {
        if (relativeUrl === src) {
          return match.replace(src, displayUrl)
        }
      }
      const fullUrl = imageApi + src
      // 存储映射关系
      urlMap.value.set(fullUrl, src)
      return match.replace(src, fullUrl)
    }

    return match
  })
}

// 将presignedUrl转换为relativeUrl用于传递给父组件
function convertFullToRelativeUrl(htmlContent) {
  if (!htmlContent && isDragging) return htmlContent

  return htmlContent.replace(/<img[^>]+src="([^"]*)"[^>]*>/g, (match, src) => {
    if (urlMap.value.has(src)) {
      const relativeUrl = urlMap.value.get(src)
      return match.replace(src, relativeUrl)
    }

    // 去掉前缀和查询参数
    if (src && src.startsWith(imageApi)) {
      let relativeUrl = src.replace(imageApi, '')
      // 只取基础路径部分
      if (relativeUrl.includes('?')) {
        relativeUrl = relativeUrl.split('?')[0]
      }
      return match.replace(src, relativeUrl)
    }

    return match
  })
}

// 判断内容是否为空的函数
function isContentEmpty(content) {
    if (!content) return true
    const textContent = content.replace(/<[^>]*>/g, '').trim()
    return textContent === ''
}

// 处理文本变化
function handleTextChange() {
  if (isInternalUpdate.value) {
    isInternalUpdate.value = false
    return
  }
  //图片处理
  const relativeContent = convertFullToRelativeUrl(content.value)

  // 判断是否为空内容（只包含空的p标签或br标签）
  const releaseContent = isContentEmpty(relativeContent) ? '' : relativeContent

  emit('update:modelValue', releaseContent)
}

watch(
    () => props.modelValue,
    (newVal) => {
      if (isInternalUpdate.value || isDragging.value) {
        isInternalUpdate.value = false
        return
      }

      if (newVal !== undefined && newVal !== null) {
        const fullContent = convertRelativeToFullUrl(newVal)
        if (fullContent !== content.value) {
          isInternalUpdate.value = true
          content.value = fullContent || "<p></p>"
          nextTick(() => {
            isInternalUpdate.value = false
          })
        }
      } else {
        isInternalUpdate.value = true
        content.value = "<p></p>"
        nextTick(() => {
          isInternalUpdate.value = false
        })
      }
    },
    { immediate: true }
)

// 如果设置了上传地址则自定义图片上传事件
onMounted(() => {
  if (props.type == "url" && !props.readOnly) {
    let quill = quillEditorRef.value.getQuill()
    let toolbar = quill.getModule("toolbar")
    toolbar.addHandler("image", value => {
      if (value) {
        proxy.$refs.uploadRef.click()
      } else {
        quill.format("image", false)
      }
    })
  }
})

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  const type = ["image/jpeg", "image/jpg", "image/png", "image/svg"]
  const isJPG = type.includes(file.type)
  // 检验文件格式
  if (!isJPG) {
    proxy.$modal.msgError("图片格式错误!")
    return false
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`)
      return false
    }
  }
  return true
}

// 上传成功处理
function handleUploadSuccess(res, file) {
  if (+res.code == 200) {
    // 获取富文本实例
    let quill = toRaw(quillEditorRef.value).getQuill()
    // 获取光标位置
    let length = quill.selection.savedRange.index
    const presignedUrl = res.object.presignedUrl
    const relativeUrl = res.object.relativeUrl

    const fullDisplayUrl = imageApi + presignedUrl

    // 存储映射关系用于传给父组件
    urlMap.value.set(fullDisplayUrl, relativeUrl)

    quill.insertEmbed(length, "image", fullDisplayUrl)
    // 调整光标到最后
    quill.setSelection(length + 1)
  } else {
    proxy.$modal.msgError("图片插入失败")
  }
}

// 上传失败处理
function handleUploadError() {
  proxy.$modal.msgError("图片插入失败")
}
</script>

<style>
.editor-img-uploader {
  display: none;
}

.editor,
.ql-toolbar {
  white-space: pre-wrap !important;
  line-height: normal !important;
}

.quill-img {
  display: none;
}

.ql-snow .ql-tooltip[data-mode='link']::before {
  content: '请输入链接地址:';
}

.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: '保存';
  padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode='video']::before {
  content: '请输入视频地址:';
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: '14px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='small']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {
  content: '10px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='large']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {
  content: '18px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='huge']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {
  content: '32px';
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: '文本';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {
  content: '标题1';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {
  content: '标题2';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {
  content: '标题3';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {
  content: '标题4';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {
  content: '标题5';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {
  content: '标题6';
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: '标准字体';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='serif']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before {
  content: '衬线字体';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='monospace']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before {
  content: '等宽字体';
}
</style>
