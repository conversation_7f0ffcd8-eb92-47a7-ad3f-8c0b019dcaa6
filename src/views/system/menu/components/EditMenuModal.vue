<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="720px"
    append-to-body
    :before-close="handleClose"
    top="7vh"
  >
    <el-form ref="menuRef" :model="form" :rules="rules" label-width="120px" inline>
      <el-form-item label="上级菜单" class="block-form-item">
        <el-tree-select
          v-model="form.parentId"
          :data="menuOptions"
          :props="{ value: 'menuId', label: 'menuName', children: 'children' }"
          value-key="menuId"
          placeholder="选择上级菜单"
          check-strictly
          :disabled="isDisabled"
          filterable
        />
      </el-form-item>
      <el-form-item label="菜单类型" prop="menuType">
        <el-radio-group v-model="form.menuType" :disabled="isDisabled">
          <el-radio value="M"> 目录 </el-radio>
          <el-radio value="C"> 菜单 </el-radio>
          <el-radio value="F"> 按钮 </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="所属模块" prop="moduleIds" class="block-form-item">
        <el-select
          v-model="form.moduleIds"
          placeholder="请选择所属模块"
          clearable
          filterable
          multiple
          style="width: 100%"
          :disabled="isDisabled"
        >
          <el-option
            v-for="item in moduleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="form.menuType != 'F'"
        label="菜单图标"
        prop="icon"
        class="block-form-item"
      >
        <!-- <el-popover placement="bottom-start" :width="540" trigger="click">
          <template #reference>
            <el-input
              v-model="form.icon"
              placeholder="点击选择图标"
              @blur="showSelectIcon"
              readonly
            >
              <template #prefix>
                <svg-icon
                  v-if="form.icon"
                  :name="form.icon"
                  class="el-input__icon"
                  style="height: 32px; width: 16px;"
                />
                <el-icon v-else style="height: 32px; width: 16px;"><search /></el-icon>
              </template>
              <template #suffix v-if="form.icon">
                <el-icon class="pointer" @click="form.icon = '#'"><Close /></el-icon>
              </template>
            </el-input>
          </template>
          <icon-select ref="iconSelectRef" @selected="selected" :active-icon="form.icon" />
        </el-popover> -->
        <IconSelector v-model="form.icon" />
      </el-form-item>
      <el-form-item label="显示排序" prop="orderNum">
        <el-input-number v-model="form.orderNum" :min="0" :disabled="isDisabled" />
      </el-form-item>
      <el-form-item v-if="form.menuType != 'F'">
        <template #label>
          <span>
            <el-tooltip
              content="选择是外链则路由地址需要以`http(s)://`开头"
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            是否外链
          </span>
        </template>
        <el-radio-group v-model="form.isFrame" :disabled="isDisabled">
          <el-radio value="0"> 是 </el-radio>
          <el-radio value="1"> 否 </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 是否可配置颜色 -->
      <template v-if="form.menuType != 'F' && isMenuIconColor">
        <el-form-item label="图标颜色" prop="iconColor">
          <el-color-picker
            v-model="form.iconColor"
            show-alpha
            :predefine="predefineColors"
          />
        </el-form-item>
        <el-form-item label="图标背景颜色" prop="iconBackgroundColor">
          <el-color-picker
            v-model="form.iconBackgroundColor"
            show-alpha
            :predefine="predefineBgColors"
          />
        </el-form-item>
      </template>

      <el-form-item label="菜单名称" prop="menuName" class="block-form-item">
        <el-input
          v-model="form.menuName"
          placeholder="请输入菜单名称"
          clearable
          maxlength="20"
          :disabled="isDisabled"
        />
      </el-form-item>
      <el-form-item v-if="form.menuType == 'C'" prop="routeName">
        <template #label>
          <span>
            <el-tooltip
              content="默认不填则和路由地址相同：如地址为：`user`，则名称为`User`（注意：因为router会删除名称相同路由，为避免名字的冲突，特殊情况下请自定义，保证唯一性）"
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            路由名称
          </span>
        </template>
        <el-input v-model="form.routeName" placeholder="请输入路由名称" />
      </el-form-item>

      <el-form-item v-if="form.menuType != 'F'" prop="path">
        <template #label>
          <span>
            <el-tooltip
              content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头"
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            路由地址
          </span>
        </template>
        <el-input
          v-model="form.path"
          :disabled="isDisabled"
          placeholder="请输入路由地址"
        />
      </el-form-item>
      <el-form-item v-if="form.menuType == 'C'" prop="component" class="block-form-item">
        <template #label>
          <span>
            <el-tooltip
              content="访问的组件路径，如：`system/user/index`，默认在`views`目录下"
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            组件路径
          </span>
        </template>
        <el-input
          v-model="form.component"
          placeholder="请输入组件路径"
          :disabled="isDisabled"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="form.menuType != 'M'">
        <el-input
          v-model="form.perms"
          placeholder="请输入权限标识"
          :disabled="isDisabled"
          maxlength="100"
        />
        <template #label>
          <span>
            <el-tooltip
              content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)"
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            权限字符
          </span>
        </template>
      </el-form-item>
      <el-form-item v-if="form.menuType == 'C'">
        <el-input
          v-model="form.query"
          placeholder="请输入路由参数"
          maxlength="255"
          clearable
        />
        <template #label>
          <span>
            <el-tooltip
              content='访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`'
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            路由参数
          </span>
        </template>
      </el-form-item>
      <el-form-item v-if="form.menuType == 'C'">
        <template #label>
          <span>
            <el-tooltip
              content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致"
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            是否缓存
          </span>
        </template>
        <el-radio-group v-model="form.isCache">
          <el-radio value="0"> 缓存 </el-radio>
          <el-radio value="1"> 不缓存 </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.menuType != 'F' && !isDisabled">
        <template #label>
          <span>
            <el-tooltip
              content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问"
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            显示状态
          </span>
        </template>
        <el-radio-group v-model="form.visible" :disabled="isDisabled">
          <el-radio v-for="dict in sys_show_hide" :key="dict.value" :value="dict.value">
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!isDisabled">
        <template #label>
          <span>
            <el-tooltip
              content="选择停用则路由将不会出现在侧边栏，也不能被访问"
              placement="top"
            >
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            菜单状态
          </span>
        </template>
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 取消 </el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 确定 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  addMenu,
  delMenu,
  getMenu,
  listMenu,
  updateMenu,
  updateZsResourceByCode,
} from "@/api/system/menu"
import { listModule } from "@/api/system/module"
import defaultSettings from "@/settings"
const IconSelector = defineAsyncComponent(() =>
  import("@/components/IconSelector/index.vue")
)

const { proxy } = getCurrentInstance()
const { sys_show_hide, sys_normal_disable } = proxy.useDict(
  "sys_show_hide",
  "sys_normal_disable"
)

const loading = ref(false)
const isMenuIconColor = defaultSettings?.isMenuIconColor
const title = ref("")
const menuOptions = ref([])
const dialogVisible = ref(false)
const isDisabled = ref(import.meta.env.VITE_SYS_EDIT === "false") // 中大表单禁用 只有system:menu:editEss
const iconSelectRef = ref(null)
const moduleList = ref([]) // 模块列表
const predefineColors = ["#196DFF", "#FF9B0D", "#00D3A7", "#FE6967", "#FBE133"]
const predefineBgColors = [
  "#D1E5FF",
  "#F0E9E0",
  "#F0E5EC",
  "#D1F2F4",
  "#F1DDE4",
  "#F0F1E0",
]
const data = reactive({
  form: {},
  queryParams: {
    menuName: undefined,
    visible: undefined,
  },
  rules: {
    menuName: [{ required: true, message: "菜单名称不能为空", trigger: "blur" }],
    orderNum: [{ required: true, message: "菜单顺序不能为空", trigger: "blur" }],
    path: [{ required: true, message: "路由地址不能为空", trigger: "blur" }],
  },
})

const emit = defineEmits(["change"])

const { form, rules } = toRefs(data)

const open = (row, t = "添加菜单") => {
  reset()
  getTreeselect()
  dialogVisible.value = true
  title.value = t
  if (t === "添加菜单") {
    form.value.parentId = row.menuId || "0"
  } else {
    form.value = { ...row }
    getMenuInfo()
  }
}

const getMenuInfo = () => {
  getMenu(form.value.menuId).then((res) => {
    form.value = res.data
    form.value.orderNum = +res.data.orderNum
    form.value.icon = form.value.icon === "#" ? null : form.value.icon
  })
}

/** 查询菜单下拉树结构 */
function getTreeselect() {
  menuOptions.value = []
  listMenu().then((res) => {
    const menu = { menuId: "0", menuName: "主类目", children: [] }
    menu.children = proxy.handleTree(res.data, "menuId")
    menuOptions.value.push(menu)
  })
}

/** 表单重置 */
function reset() {
  let baseModuleObj = moduleList.value?.find(
    (i) => i.code == import.meta.env.VITE_APP_MODULE
  )
  form.value = {
    menuId: undefined,
    parentId: "0",
    menuName: undefined,
    icon: undefined,
    menuType: "M",
    orderNum: undefined,
    isFrame: "1",
    isCache: "0",
    visible: "0",
    status: "0",
    moduleIds: baseModuleObj ? [baseModuleObj.id] : [], // 默认当前模块
  }
  proxy.resetForm("menuRef")
}

function listAll() {
  listModule().then((res) => {
    moduleList.value = res.object
  })
}

function handleClose() {
  reset()
  dialogVisible.value = false
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate((valid) => {
    if (valid) {
      const updateUrl = isDisabled.value ? updateZsResourceByCode : updateMenu
      const actionsUrl = form.value.menuId != undefined ? updateUrl : addMenu
      form.value.icon = form.value.icon || "#"
      loading.value = true
      actionsUrl(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess(
            form.value.menuId != undefined ? "修改成功" : "添加成功"
          )
          handleClose()
          emit("change")
        })
        .finally(() => {
          loading.value = false
        })
    }
  })
}

listAll()

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.block-form-item {
  width: 660px;
}
</style>
