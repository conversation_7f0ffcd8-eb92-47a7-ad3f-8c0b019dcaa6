<template>
  <div class="app-container">
    <div>
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
        class="white-form-box"
        label-width="100px"
      >
        <el-form-item label="印章编号" prop="sealNo">
          <el-input
            v-model="queryParams.sealNo"
            placeholder="请输入印章编号"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="印章名称" prop="sealName">
          <el-input
            v-model="queryParams.sealName"
            placeholder="请输入印章名称"
            clearable
            maxlength="100"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="印章类型" prop="sealType">
          <dict-select
            v-model="queryParams.sealType"
            :options="seal_type"
            value-type="string"
          />
        </el-form-item>
        <el-form-item label="印章状态" prop="sealStatus">
          <dict-select
            v-model="queryParams.sealStatus"
            :options="seal_status"
            value-type="string"
          />
        </el-form-item>
        <el-form-item label="授权时间" prop="createTimeRange">
          <el-date-picker
            v-model="queryParams.createTimeRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="common-input-width"
            clearable
            :default-time="dateDefaultTime"

          />
        </el-form-item>
        <el-form-item label="归属部门" prop="deptId">
          <dept-picker v-model="queryParams.deptId" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
          <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
        </el-form-item>
      </el-form>

      <CustomTable
        ref="customTableRef"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :data="tableData"
        :has-toolbar="false"
        :loading="loading"
        :total="total"
        @reload="getList"
        :opt-width="80"
      >
        <vxe-column type="seq" width="70" />
        <vxe-column title="印章编号" field="sealNo" min-width="160" />
        <vxe-column title="印章ID" field="essSealId" min-width="160" />
        <vxe-column title="印章归属部门" field="deptName" min-width="160" show-overflow />
        <vxe-column title="印章名称" field="sealName" min-width="180" show-overflow />
        <vxe-column title="印章状态" field="sealStatus" min-width="100">
          <template #default="{ row }">
            <dict-tag :options="seal_status" :value="row.sealStatus" />
          </template>
        </vxe-column>
        <vxe-column title="印章类型" field="sealType" min-width="170">
          <template #default="{ row }">
            {{ selectDictLabel(seal_type, row.sealType) }}
          </template>
        </vxe-column>
        <vxe-column title="授权人" field="authCreateUser" min-width="100" show-overflow />
        <vxe-column title="授权结束时间" field="authEndTime" width="170" />
        <template #opts="{ row }">
          <TableColOptBtn
            :key="Math.random()"
            :buttons="[
              {
                text: '查看',
                click: () => viewDetails(row),
                permission: ['ess:seal:sealDetails'],
              },
            ]"
          />
        </template>
      </CustomTable>
    </div>
  </div>
</template>

<script setup name="MySealList">
import { getMySealList } from "@/api/ess/seal/seal"
import { deptTreeSelect } from "@/api/system/user"
import { essPrefix } from "@/config/constant"
const { proxy } = getCurrentInstance()
const { seal_type, seal_status } = proxy.useDict("seal_type", "seal_status")

const router = useRouter()
const tableData = ref([])
const deptOptions = ref([]) // 部门
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const queryParams = reactive({
  createTimeRange: [],
  deptId: null,
  sealType: null,
  sealStatus: null,
  limit: 10,
  page: 1,
})

/** 查询列表 */
function getList() {
  loading.value = true
  queryParams.createStartTime = queryParams.createTimeRange?.[0]
  queryParams.createEndTime = queryParams.createTimeRange?.[1]
  getMySealList(queryParams)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}
function getDeptList() {
  deptTreeSelect().then((res) => {
    deptOptions.value = res.data
  })
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 查看详情按钮操作 */
function viewDetails(row) {
  router.push(`/ess/sealDetails/${row.id}`)
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    essPrefix + "/seal/export",
    {
      ...queryParams,
    },
    `印章列表_${new Date().getTime()}.xlsx`
  )
}

getList()
getDeptList()
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
}
</style>
