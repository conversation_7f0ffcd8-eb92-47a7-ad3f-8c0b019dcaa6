<template>
  <el-dialog
    v-model="dialogVisible"
    top="20px"
    width="950px"
    :title="data.currFlowForm.formName"
    append-to-body
    @closed="dialogDelFlowInfo(null, true)"
  >
    <!-- 发起申请 -->
    <json-flow-predict
      ref="predict"
      :proxy="proxy"
      @cancel="handleCancel"
      @opened="dialogDelFlowInfo()"
      v-if="dialogVisible"
      class="min-w-400px"
    >
      <template v-slot="slotProps" v-if="data.showInitiateOrder">
        <flow-initiate
          v-if="data.showInitiateOrder"
          ref="flowInitiateRef"
          :curr-flow-form="data.currFlowForm"
          :showSuccessMsg="false"
          @success="handleSuccess"
          @cancel="handleCancel"
          v-show="slotProps.currActive === 'form'"
        />
      </template>
      <template v-slot="slotProps" v-if="data.showHandleForm">
        <custom-form
          ref="form"
          v-show="slotProps.currActive === 'form'"
          :curr-job="data.currFlowForm"
          @onHandleForm="handleInitiateOrder"
        ></custom-form>
      </template>
    </json-flow-predict>
  </el-dialog>
</template>

<script setup>
import { cloneDeep } from "lodash-es"
import { handleCustomForm, vueKey } from "@/api/order/order-key-vue"

const FlowInitiate = defineAsyncComponent(() =>
  import("@/views/order/flow-application/initiate.vue")
)
const CustomForm = defineAsyncComponent(() =>
  import("@/flow/components/custom-form/handle.vue")
)
const JsonFlowPredict = defineAsyncComponent(() =>
  import("@/views/jsonflow/flow-design/predict.vue")
)

defineOptions({
  inheritAttrs: false,
})

const { proxy } = getCurrentInstance()
const router = useRouter()

const dialogVisible = ref(false)

const data = reactive({
  tableData: [],
  tabsData: [],
  showHandleForm: false,
  showInitiateOrder: false,
  showFlowPic: false,
  currFlowForm: {},
})

const isInit = ref(true)
const hasContinue = ref(true) // 是否存在继续
const emit = defineEmits(["cancel", "opened", "success"])
const rowDefaultData = ref({}) // 表单数据
const flowInitiateRef = ref(null)
function open(row, isContinue = true) {
  isInit.value = true
  hasContinue.value = isContinue
  rowDefaultData.value = cloneDeep(row)
  // 判断是否自定义首页
  dialogVisible.value = true
  if (row.path !== vueKey.RunApplicationForm) {
    data.currFlowForm = cloneDeep(row)
    handleCustomForm(data, row)
    data.currFlowForm.operType = "add"
    data.showHandleForm = true
  } else {
    data.showInitiateOrder = true
    data.currFlowForm = cloneDeep(row)
  }
  openPredict(row, true)
}

function handleSuccess(e) {
  openPredict({}, false)
  dialogVisible.value = false
  const submitTxt = ["请前往待办管理处查看审批结果", "前往待办管理-进行中查看"]
  const draftTxt = ["可在【流程管理-发起申请】中查看草稿箱", "确定"]
  const cancelText = hasContinue.value ? "继续创建" : "返回"
  const confirmTxt = e.indexOf("暂存成功") > -1 ? draftTxt : submitTxt
  proxy.$modal
    .confirm(e || "操作成功", confirmTxt[0], "success", true, confirmTxt[1], cancelText)
    .then(() => {
      e.indexOf("暂存成功") === -1 && router.push("/agenda/doing")
      emit("success")
    })
    .catch((action) => {
      emit("success")
      if (action === "cancel") {
        hasContinue.value && open(rowDefaultData.value)
      }
    })
}

function openPredict(row, bool) {
  proxy.$refs.predict?.open(row, bool)
}

function handleCancel() {
  openPredict({}, false)
  data.showInitiateOrder = false
  data.showHandleForm = false
  dialogVisible.value = false
  emit("cancel")
}

function dialogDelFlowInfo(row, bool) {
  proxy.$refs.predict?.delFlowInfo(row, bool)
}

watchEffect(() => {
  if (flowInitiateRef?.value?.formCreateRef && isInit.value) {
    emit("opened", flowInitiateRef?.value?.formCreateRef)
    isInit.value = false
  }
})

defineExpose({
  open,
  flowInitiateRef,
})
</script>

<style scoped lang="scss"></style>
