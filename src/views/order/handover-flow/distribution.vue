<template>
	<div>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="90px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item label="交接类型" prop="type">
						<el-select
							v-model="form.type"
							placeholder="请选择交接类型"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in DIC_PROP.HANDOVER_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="接收部门" prop="receiveDept">
						<el-select
							v-model="form.receiveDept"
							placeholder="请选择接收部门"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in dicData.receiveDept"
								:key="index"
								:label="item.name"
								:value="item.deptId"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item label="接收人" prop="receiveUser">
						<el-select
							v-model="form.receiveUser"
							placeholder="请选择接收人"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in dicData.receiveUser"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'">
			<footer class="el-dialog__footer">
				<span class="dialog-footer">
					<el-button type="primary" :disabled="loading" @click="methods.handleUpdate">确定</el-button>
				</span>
			</footer>
		</template>
	</div>
</template>

<script setup lang="ts" name="HandoverDistributionForm">
const { proxy } = getCurrentInstance()
import { distributePerson, getObj } from "@/api/order/handover-flow"
import { rule } from "@/utils/validate"
import { onLoadDicUrl } from "@/flow/components/convert-name/convert"
import * as orderVue from "@/api/order/order-key-vue"

const emits = defineEmits(["handleJob"])
// 定义变量内容
const dataFormRef = ref()
const loading = ref(false)
const operType = ref(false)
// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "receiveDept" }, { key: "receiveUser" })
onMounted(() => {
	onLoad(dicData)
})
// 提交表单数据
const form = reactive({
	type: "",
	receiveDept: "",
	receiveUser: "",
})

// 定义校验规则
const dataRules = ref({
	type: [{ required: true, message: "交接类型不能为空", trigger: "blur" }],
	receiveDept: [{ required: true, message: "接收部门不能为空", trigger: "blur" }],
	receiveUser: [{ required: true, message: "接收人不能为空", trigger: "blur" }],
})

const props = defineProps({
	currJob: {
		type: Object,
		default: null,
	},
})

const methods = {
	initJobData() {
		orderVue.currElTabIsView(null, props.currJob, methods.handleUpdate)
		methods.handleGetObj(props.currJob.orderId)
	},
	handleGetObj(id) {
		getObj(id).then(response => {
			Object.assign(form, response.data)
			form.runJobId = props.currJob.id
		})
	},
	async handleUpdate() {
		try {
			loading.value = true
			// 转岗或离职交接时需要分配接收人来接收未来任务
			let handoverReason = props.currJob.order.handoverReason
			if (form.isNeedReceive && (handoverReason === "1" || handoverReason === "2") && !form.receiveUser) {
				proxy.$modal.msgError("转岗或离职交接时需要分配接收人来接收未来任务")
				return
			}
			await distributePerson(form)
			orderVue.currElTabIsSave(props.currJob, true, emits)
			proxy.$modal.msgSuccess("操作成功")
		}
		catch (err: any) {
			proxy.$modal.msgError(err.msg)
		}
		finally {
			loading.value = false
		}
	},
}

// 监听双向绑定
watch(
	() => props.currJob.id,
	() => {
		methods.initJobData()
	},
)

onMounted(() => {
	methods.initJobData()
})
</script>

<style lang="scss" scoped>
  .el-dialog__footer {
    text-align: center;

    .dialog-footer {
      text-align: center;
    }
  }
</style>
