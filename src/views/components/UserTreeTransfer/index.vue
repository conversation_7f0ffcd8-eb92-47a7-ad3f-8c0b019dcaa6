<template>
  <div class="user-tree-container">
    <div class="box" :class="{ 'w-33%!': cols === 3 }">
      <div class="box-header"><div class="title">请选择</div></div>
      <div class="box-body">
        <el-select v-model="typeValue" suffix-icon="CaretBottom" class="type-select">
          <el-option
            v-for="item in typeOptions"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          />
        </el-select>

        <template v-for="tree in typeOptions" :key="tree.value">
          <component
            :is="tree.componentName"
            :businessLineType="businessLineType"
            :businessLineId="businessLineId"
            :isOnlyOne="isOnlyOne"
            :allData="typeOptions"
            v-bind="{ ...$attrs, ...tree.compProps }"
            v-model="tree.checkedItems"
            @change="handleTreeCheckChange"
            :ref="(el) => (componentRefs[tree.refName] = el)"
            v-show="typeValue === tree.value"
          >
          </component>
        </template>
      </div>
    </div>
    <div class="box select-user" :class="{ 'w-33%!': cols === 3 }">
      <div class="box-header">
        <div class="flex-x-between">
          <div class="title">
            已选{{ cols === 3 ? "成员" : "" }}({{
              rangeType == 1 || !onlyRange ? getUserCount() : getNonUserCount()
            }})
          </div>
          <div class="box-header__right">
            <el-button
              type="danger"
              link
              @click="cols === 3 ? resetUserSelected() : resetSelected()"
            >
              清空
            </el-button>
          </div>
        </div>
        <el-input
          v-model="selUserKeyword"
          placeholder="请输入关键字"
          clearable
          maxlength="100"
          suffix-icon="Search"
          class="all-user-input"
        />
      </div>
      <TagUserList
        v-bind="$attrs"
        :list="
          cols === 2 && selectTypes.length > 1 && !onlyRange
            ? filterSelectList()
            : filterSelectList('user')
        "
        @remove="removeItem"
      />
    </div>
    <div class="box select-range w-33%!" v-if="cols === 3">
      <div class="box-header">
        <div class="flex-x-between">
          <div class="title">已选范围({{ getNonUserCount() }})</div>
          <div class="box-header__right">
            <el-button
              type="danger"
              link
              @click="cols === 3 ? resetNonUserSelected() : resetSelected()"
            >
              清空
            </el-button>
          </div>
        </div>
        <el-input
          v-model="rangeKeyword"
          placeholder="请输入关键字"
          clearable
          maxlength="100"
          suffix-icon="Search"
          class="all-user-input"
        />
      </div>
      <TagUserList
        v-bind="$attrs"
        :list="filterSelectList('other')"
        :itemSecondarySize="190"
        :gridItems="2"
        @remove="removeItem"
      />
    </div>
  </div>
</template>

<script setup name="UserTreeTransfer">
import { ElSelect, ElOption } from "element-plus"
import TagUserList from "./TagUserList.vue"
import DeptUserTree from "./DeptUserTree.vue"
import RoleTree from "./RoleTree.vue"
import TagTree from "./TagTree.vue"
import { uniqBy } from "lodash-es"

defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  users: {
    // 用户
    type: Array,
    default: () => [],
  },
  ranges: {
    // 其他范围
    type: Array,
    default: () => [],
  },
  // 选择类型： user-用户 dept-组织架构 tag-标签 role-角色
  selectTypes: {
    type: Array,
    default: () => ["user", "role", "tag", "dept"],
  },
  // 业务线类型（传入就会筛选）
  businessLineType: {
    default: null,
  },
  // 业务线id（传入就会筛选）
  businessLineId: {
    default: null,
  },
  // 仅仅只选择范围（标签用）
  onlyRange: {
    type: Boolean,
    default: false,
  },
  // 只单选
  isOnlyOne: {
    type: Boolean,
    default: false,
  },
  // 两列布局
  cols: {
    type: Number,
    default: 2,
  },
  // 三栏布局的类型
  rangeType: {
    type: Number,
    default: 1, // 1是成员 2是范围
  },
})

// const loading = ref(true)
const selUserKeyword = ref(null) // 右侧搜索关键字
const rangeKeyword = ref(null) // 范围搜索关键字
const typeValue = ref(props.selectTypes[0])
const selectList = ref([]) // 右侧选中列表
const componentRefs = ref({})

const defaultType = shallowRef([
  {
    label: "用户",
    value: "user",
    refName: "userRef",
    componentName: DeptUserTree,
    compProps: {},
    checkedItems: [],
  },
  {
    label: "部门",
    value: "dept",
    refName: "deptRef",
    componentName: DeptUserTree,
    compProps: { onlyDept: true },
    checkedItems: [],
  },

  {
    label: "角色",
    value: "role",
    refName: "roleRef",
    componentName: RoleTree,
    compProps: {},
    checkedItems: [],
  },
  {
    label: "标签",
    value: "tag",
    refName: "tagRef",
    componentName: TagTree,
    compProps: {},
    checkedItems: [],
  },
])

const { proxy } = getCurrentInstance()

const typeOptions = shallowRef([...defaultType.value])

const initOptions = (arr) => {
  const filteredOptions = arr.filter((item) => props.selectTypes.includes(item.value))
  typeOptions.value = filteredOptions
}

initOptions(defaultType.value)

// 切换范围全选
watch(
  () => props.rangeType,
  (newVal) => {
    if (props.cols !== 3) return
    if (props.rangeType === 1) {
      typeOptions.value = defaultType.value.filter((item) => item.value === "user")
    } else if (props.rangeType === 2) {
      typeOptions.value = defaultType.value.filter((item) => item.value !== "user")
    }
    typeValue.value = typeOptions.value[0].value
  },
  { immediate: true }
)

watch(
  () => props.businessLineType,
  (val) => {
    if (+val === 1 && (props.users.length || props.ranges.length)) {
      proxy.$modal.msgWarning("切换校级/二级需重新选择!")
      reset()
    }
  }
)

const emit = defineEmits(["update:users", "update:ranges", "change", "clearValidate"])
// 赋值已选数据--用户
const updateUserCheckedKeys = () => {
  nextTick(() => {
    const userIndex = defaultType.value.findIndex((i) => i.value == "user")
    const userArr = props.users.map((i) => {
      return {
        ...i,
        id: i.userId?.startsWith("userId-") ? i.userId : `userId-${i.userId}`,
        type: "user",
      }
    })
    defaultType.value[userIndex].checkedItems = userArr
    selectList.value = uniqBy([...userArr], "id")
  })
}
// 赋值已选数据--其他
const updateRangeCheckedKeys = () => {
  nextTick(() => {
    const rangeTypes = [
      { type: "dept", rangeType: 1 },
      { type: "role", rangeType: 2 },
      { type: "tag", rangeType: 3 },
    ]

    rangeTypes.forEach(({ type, rangeType }) => {
      const index = defaultType.value.findIndex((i) => i.value === type)
      const arr = (props.ranges || [])
        .filter((i) => +i.rangeType === rangeType)
        .map((i) => ({
          ...i,
          type,
          id: i.joinId || i.rangeId || i.id || i.tagId, // 如果是教职工类型 就是joinId，rangeId是它的上级部门id
          name: i.name || i.rangeName,
        }))
      defaultType.value[index].checkedItems = arr
      selectList.value = uniqBy([...selectList.value, ...arr], "id")
    })
  })
}

const initData = () => {
  updateUserCheckedKeys()
  updateRangeCheckedKeys()
}

watch(() => props.users, updateUserCheckedKeys, { immediate: true })
watch(() => props.ranges, updateRangeCheckedKeys, { immediate: true })

// 更新
function updateModelValue() {
  const userList = []
  const rangeList = []

  defaultType.value.forEach((item) => {
    if (item.value === "user") {
      item.checkedItems.forEach((user) => {
        userList.push({
          ...user,
          id: user.id?.startsWith("userId-") ? user.id.replace("userId-", "") : user.id,
        })
      })
    } else {
      rangeList.push(...item.checkedItems)
    }
  })
  emit("update:users", userList)
  emit("update:ranges", rangeList)
  emit("change", selectList.value)
  emit("clearValidate")
}

// 树节点选中
async function handleTreeCheckChange() {
  await nextTick()
  selectList.value = defaultType.value.reduce((result, item) => {
    return result.concat(item.checkedItems)
  }, [])
  updateModelValue()
}
// 优化后的计算属性
const filterSelectList = (type = "all") => {
  // 辅助函数：根据关键字过滤列表
  const filterByKeyword = (list, keyword) => {
    return keyword ? list.filter((item) => item.name?.includes(keyword)) : list
  }

  // 先按关键字过滤
  const filteredList = filterByKeyword(selectList.value, selUserKeyword.value)

  // 如果cols=3，根据type参数进一步过滤
  if (props.cols === 3) {
    if (type === "user") {
      return filteredList.filter((item) => item.type === "user")
    } else if (type === "other") {
      return filteredList.filter((item) => item.type !== "user")
    }
  }
  // 默认返回全部
  return filteredList
}

// 移除已选
function removeItem(item) {
  let index = defaultType.value.findIndex((i) => i.value === item.type)
  defaultType.value[index].checkedItems = defaultType.value[index].checkedItems.filter(
    (i) => i.id !== item.id
  )
  selectList.value = selectList.value.filter((i) => i.id !== item.id)
  updateModelValue()
}

// 重置组件
function reset() {
  selUserKeyword.value = null
  resetSelected()
}

function callComponentMethod(refName, methodName, ...args) {
  const componentInstance = componentRefs.value[refName]
  if (componentInstance && componentInstance[methodName]) {
    componentInstance[methodName](...args)
  } else {
    // console.warn(`Component with ref "${refName}" or method "${methodName}" not found.`)
  }
}

// 清空选中
function resetSelected() {
  selectList.value = []
  defaultType.value.forEach((item) => {
    item.checkedItems = []
    // callComponentMethod(item.refName, "resetQuery")
  })
  updateModelValue()
}

// 清空选中的用户类型项目
function resetUserSelected() {
  // 移除selectList中的用户类型项目
  selectList.value = selectList.value.filter((item) => item.type !== "user")

  // 清空用户类型的checkedItems
  const userIndex = defaultType.value.findIndex((item) => item.value === "user")
  if (userIndex !== -1) {
    defaultType.value[userIndex].checkedItems = []
  }

  updateModelValue()
}

// 清空选中的非用户类型项目
function resetNonUserSelected() {
  // 只保留用户类型项目
  selectList.value = selectList.value.filter((item) => item.type === "user")

  // 清空非用户类型的checkedItems
  defaultType.value.forEach((item) => {
    if (item.value !== "user") {
      item.checkedItems = []
    }
  })

  updateModelValue()
}

// 获取已选数据
function getCheckedNodes() {
  return selectList.value
}

// 获取用户类型的数量
const getUserCount = () => {
  if (!props.onlyRange && props.cols === 2 && props.selectTypes.length > 1) {
    return selectList.value.length
  } else {
    return selectList.value.filter((item) => item.type === "user").length
  }
}

// 获取非用户类型的数量
const getNonUserCount = () => {
  return selectList.value.filter((item) => item.type !== "user").length
}

defineExpose({
  reset,
  getCheckedNodes,
  initData,
})
</script>

<style scoped lang="scss">
.user-tree-container {
  @apply flex justify-between w-100%;
  :deep(.el-loading-mask) {
    @apply bg-#f7f8f9 pt-250px;
  }
  .box-body {
    padding: 0 20px;
    position: relative;
    .type-select {
      position: absolute;
      left: 25px;
      top: 18px;
      transform: translateY(-50%);
      z-index: 10;
      width: 60px;
      :deep(.el-select__wrapper) {
        background: #fff;
        box-shadow: none;
        padding-right: 0;
        min-height: 28px;
        line-height: 28px;
      }
    }
  }
  .box {
    @apply box-border h-460px w-49.2% rounded-12px bg-#f7f8f9 overflow-hidden;
    .title {
      @apply font-bold color-#0c1433;
    }
    .box-header {
      padding: 20px 20px 10px 20px;
      &__right {
        display: flex;
        align-items: center;
        span {
          margin-right: 10px;
          color: #38383a;
        }
      }
    }
  }
  .el-input {
    margin-top: 10px;
    border: 1px solid #d8dde3;
    border-radius: 4px;
    :deep(.el-input__wrapper) {
      box-shadow: none;
    }
  }

  :deep(.user-tree) {
    background: transparent;
    --el-checkbox-height: 30px;
    .user-tree-label {
      @apply text-ellipsis;
    }
    :deep(.el-vl__wrapper) {
      padding: 0 20px;
      box-sizing: border-box;
    }
    :deep(.el-tree-node__expand-icon) {
      color: #7c7c7c;
    }
    :deep(.el-checkbox) {
      --el-checkbox-input-border: 1px solid #969eac;
      --el-checkbox-input-height: 16px;
      --el-checkbox-input-width: 16px;
      .el-checkbox__inner {
        &:after {
          top: 2px;
          left: 5px;
        }
        &:before {
          top: 6px;
        }
      }
    }
  }
}
</style>
