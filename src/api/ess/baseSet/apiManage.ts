import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询接口管理配置列表 带分页
export function interfacePage(data) {
	return request({
		url: essPrefix + "/interface/listPage",
		method: "post",
		data,
	})
}
// 查询接口管理配置列表
export function getInterfaceList() {
	return request({
		url: essPrefix + "/interface/list",
		method: "get",
	})
}

//  新增接口
export function addInterface(data) {
	return request({
		url: essPrefix + "/interface/add",
		method: "post",
		data,
	})
}
// 修改接口
export function updateInterface(data) {
	return request({
		url: essPrefix + "/interface/edit",
		method: "post",
		data,
	})
}

// 删除
export function delInterface(ids) {
	return request({
		url: essPrefix + "/interface/" + ids,
		method: "delete",
	})
}
// 查询接口管理配置详细
export function getInterfaceInfo(id) {
	return request({
		url: essPrefix + "/interface/" + id,
		method: "get",
	})
}
// 生成加密key
export function generateKey() {
	return request({
		url: essPrefix + "/interface/generateKey",
		method: "get",
	})
}

// 生成签名验证token
export function generateToken() {
	return request({
		url: essPrefix + "/interface/generateToken",
		method: "get",
	})
}
// 查询接口启,停用日志列表 带分页
export function getInterfaceOpLog(data) {
	return request({
		url: essPrefix + "/interOpLog/listPage",
		method: "post",
		data,
	})
}

// 查询API管理配置列表 带分页
export function getApiManageListPage(data: Object) {
	return request({
		url: essPrefix + "/apiManage/listPage",
		method: "post",
		data,
	})
}

// 新增API管理配置
export function addApiManage(data: Object) {
	return request({
		url: essPrefix + "/apiManage/add",
		method: "post",
		data,
	})
}

// 修改API管理配置
export function updateApiManage(data: Object) {
	return request({
		url: essPrefix + "/apiManage/edit",
		method: "post",
		data,
	})
}

// 删除API管理配置
export function delApiManage(ids: string) {
	return request({
		url: essPrefix + "/apiManage/" + ids,
		method: "delete",
	})
}
