<template>
  <div class="w-100%">
    <CreateFlowSimple
      :historyAPI="queryOperateTop10"
      :configKey="flowConfigKey.FILE_FLOW_STABLE"
      :defFlowId="templateInfo.workId"
      ref="createFlowSimpleRef"
      :hisExtData="{ flowType: 2 }"
      v-loading="loading"
      v-if="templateInfo.id"
    />
  </div>
</template>

<script setup>
import { queryOperateTop10 } from "@/api/ess/template/template"
import { flowConfigKey } from "@/config/constant"
import { generateFlowByUser } from "@/api/jsonflow/def-flow"

const CreateFlowSimple = defineAsyncComponent(() =>
  import("@/views/flow/createFlowSimple/index.vue")
)
const { proxy } = getCurrentInstance()
const loading = ref(false)
const props = defineProps({
  templateInfo: {
    type: Object,
    default: () => ({}),
  },
})

// 初始化流程
const init = async () => {
  await nextTick()
  setTimeout(() => {
    proxy.$refs.createFlowSimpleRef?.initFlow()
  }, 100)
}

// 保存流程
function submit() {
  return new Promise(async (resolve, reject) => {
    try {
      const nodes = proxy.$refs.createFlowSimpleRef?.getNodes() || []
      loading.value = true
      const flowRes = await generateFlowByUser(nodes, 2)
      resolve(flowRes.object)
    } catch (error) {
      reject(error)
    } finally {
      loading.value = false
    }
  })
}

init()

defineExpose({
  submit,
  init,
})
</script>

<style scoped lang="scss"></style>
