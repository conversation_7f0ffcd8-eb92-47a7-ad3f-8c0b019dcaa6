export default {
	runJob: {
		index: "#",
		importrunJobTip: "import RunJob",
		id: "id",
		sort: "sort",
		defFlowId: "defFlowId",
		parFlowInstId: "parFlowInstId",
		subFlowInstId: "subFlowInstId",
		flowNodeId: "flowNodeId",
		nodeJobId: "nodeJobId",
		jobType: "jobType",
		roleId: "roleId",
		userId: "userId",
		startTime: "startTime",
		endTime: "endTime",
		timeLimit: "timeLimit",
		useTime: "useTime",
		userKey: "userKey",
		jobName: "jobName",
		distFlowNodeId: "distFlowNodeId",
		timeout: "timeout",
		status: "status",
		suspension: "suspension",
		suspensionReason: "suspensionReason",
		invalidReason: "invalidReason",
		flowStatus: "flowStatus",
		isSkipRejected: "isSkipRejected",
		createUser: "createUser",
		createTime: "createTime",
		flowKey: "flowKey",
		flowInstId: "flowInstId",
		runNodeId: "runNodeId",
		isNowRun: "isNowRun",
		signatureType: "signatureType",
		isRead: "isRead",

		inputIdTip: "input id",
		inputSortTip: "input sort",
		inputDefFlowIdTip: "input defFlowId",
		inputParFlowInstIdTip: "input parFlowInstId",
		inputSubFlowInstIdTip: "input subFlowInstId",
		inputFlowNodeIdTip: "input flowNodeId",
		inputNodeJobIdTip: "input nodeJobId",
		inputJobTypeTip: "input jobType",
		inputRoleIdTip: "input roleId",
		inputUserIdTip: "input userId",
		inputStartTimeTip: "input startTime",
		inputEndTimeTip: "input endTime",
		inputUserKeyTip: "input userKey",
		inputJobNameTip: "input jobName",
		inputDistFlowNodeIdTip: "input distFlowNodeId",
		inputTimeoutTip: "input timeout",
		inputStatusTip: "input status",
		inputFlowStatusTip: "input flowStatus",
		inputSuspensionTip: "input suspension",
		inputSuspensionReasonTip: "input suspensionReason",
		inputIsSkipRejectedTip: "input isSkipRejected",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputFlowKeyTip: "input flowKey",
		inputFlowInstIdTip: "input flowInstId",
		inputRunNodeIdTip: "input runNodeId",
		inputIsNowRunTip: "input isNowRun",
		inputSignatureTypeTip: "input signatureType",
		inputIsReadTip: "input isRead",
	},
}
