/**
 * 字典属性常量
 * <AUTHOR>
 */
let paramValTypes = [{
  label: '表单字段',
  value: '0',
}, {
  label: 'SpEL表达式',
  value: '1',
}, {
  label: '固定值',
  value: '2',
}]

let jobTypes = [{
  label: "人员",
  value: "0",
  labelEn: "User",
}, {
  label: "角色",
  value: "1",
  labelEn: "Role",
},
// {
// 	label: "岗位",
// 	value: "2",
// 	labelEn: "Post",
// }, 
{
  label: "部门",
  value: "3",
  labelEn: "Dept",
}
]

let methods = [{
  label: "当开启任务时",
  value: "startJob",
}, {
  label: "当完成任务时",
  value: "completeJob",
}, {
  label: "当开启下一步任务时",
  value: "startNextJob",
}, {
  label: "当开启节点时",
  value: "startNode",
}, {
  label: "当完成节点时",
  value: "completeNode",
}, {
  label: "当开启下一步节点时",
  value: "startNextNode",
}, {
  label: "当任务跳转时",
  value: "anyJump",
}, {
  label: "当任务被跳转时",
  value: "anyJumped",
}, {
  label: "当任务驳回时",
  value: "reject",
}, {
  label: "当任务被驳回时",
  value: "rejected",
}, {
  label: "当需分配参与者时",
  value: "distPerson",
}, {
  label: "退回首节点时",
  value: "backFirst",
}, {
  label: "退回上一步时",
  value: "backPre",
}]

export let DIC_PROP = {
  YES_OR_NO: [{
    label: "否",
    value: "0",
    labelEn: "No",
  }, {
    label: "是",
    value: "1",
    labelEn: "Yes",
  }],
  YES_OR_NO_BOOL: [{
    label: "否",
    value: false,
    labelEn: "No",
  }, {
    label: "是",
    value: true,
    labelEn: "Yes",
  }],
  DELEGATE_STATUS: [{
    label: "暂存",
    value: "-1",
    labelEn: "Temp",
  }, {
    label: "启用",
    value: "0",
    labelEn: "Enable",
  }, {
    label: "停用",
    value: "1",
    labelEn: "Disable",
  }],
  TEMP_STATUS: [{
    label: "暂存",
    value: "-1",
    labelEn: "Temp",
  }, {
    label: "作废",
    value: "0",
    labelEn: "Invalid",
  }, {
    label: "已发布",
    value: "1",
    labelEn: "Publish",
    elTagClass: 'text-primary'
  }],
  ORDER_STATUS: [{
    label: "撤回",
    value: "-2",
  }, {
    label: "暂存",
    value: "-1",
  }, {
    label: "运行中",
    value: "0",
  }, {
    label: "完成",
    value: "1",
  }, {
    label: "作废",
    value: "2",
  }, {
    label: "审批拒绝",
    value: "3",
  }],
  HANDOVER_TYPE: [{
    label: "任务交接",
    value: "-1",
  }, {
    label: "可自定义更多",
    value: "0",
  }],
  HANDOVER_REASON: [{
    label: "日常交接",
    value: "-1",
  }, {
    label: "晋升",
    value: "0",
  }, {
    label: "转岗",
    value: "1",
  }, {
    label: "离职",
    value: "2",
  }, {
    label: "平调",
    value: "3",
  }],
  HANDOVER_STATUS: [{
    label: "撤回",
    value: "-2",
  }, {
    label: "未交接",
    value: "-1",
  }, {
    label: "交接中",
    value: "0",
  }, {
    label: "已交接",
    value: "1",
  }, {
    label: "作废",
    value: "2",
  }, {
    label: "审批拒绝",
    value: "3",
  }],
  NODE_TYPE: [
    {
      label: "开始节点",
      value: "-1",
    }, {
      label: "串行节点",
      value: "0",
    }, {
      label: "并行节点",
      value: "1",
    }, {
      label: "结束节点",
      value: "2",
    }, {
      label: "虚拟节点",
      value: "3",
    }, {
      label: "横向泳道",
      value: "8",
    }, {
      label: "纵向泳道",
      value: "9",
    },
  ],
  LINK_TYPE: [
    {
      label: "节点到节点",
      value: "0",
    }, {
      label: "节点到任务",
      value: "1",
    },
  ],
  FLOW_METHOD_TYPE: [{
    label: "发起人本人",
    value: "0",
  }, {
    label: "单级部门主管",
    value: "1",
  }, {
    label: "多级部门主管",
    value: "2",
  }, {
    label: "指定部门主管",
    value: "3",
  }, {
    label: "表单内人员",
    value: "4",
  }, {
    label: "可自定义更多",
    value: "-1",
  }],
  JOB_MSG_TYPE: [{
    label: "个人任务",
    value: "0",
  }, {
    label: "组任务",
    value: "1",
  }],
  JOB_USER_TYPE: jobTypes,
  JOB_USER_NONE_TYPE: [{
    label: "无",
    value: "-1",
    labelEn: "None",
  },
  ...jobTypes,
  ],
  NODE_APPROVE_METHOD: [{
    label: "会签(需所有节点通过)",
    value: "1",
  }, {
    label: "或签(一个节点通过即可)",
    value: "2",
  }, {
    label: "依次审批(按顺序依次通过)",
    value: "3",
  }],
  APPROVE_METHOD: [{
    label: "会签(需所有审批人同意)",
    value: "1",
  }, {
    label: "或签(一名审批人同意即可)",
    value: "2",
  }, {
    label: "依次审批(按顺序依次审批)",
    value: "3",
  }, {
    label: '票签(自定义完成比率%)',
    value: '4'
  }],
  SIGNATURE_TYPE: [{
    label: "前加签",
    value: "1",
    labelEn: "Before Signature",
  }, {
    label: "后加签",
    value: "2",
    labelEn: "After Signature",
  }, {
    label: "加并签",
    value: "3",
    labelEn: "Sign Together",
  }],
  NODE_SEQUENTIAL_TYPE: [{
    label: "在当前节点之前审批",
    value: "1",
  }, {
    label: "在当前节点之后审批",
    value: "2",
  }, {
    label: "与当前节点同时审批",
    value: "3",
  }],
  NODE_SIGNATURE_TYPE: [{
    label: "前加节点",
    value: "1",
  }, {
    label: "后加节点",
    value: "2",
  }, {
    label: "加并节点",
    value: "3",
  }],
  BELONG_TYPE: [{
    label: "普通任务",
    value: "0",
    labelEn: "Common Task",
  }, {
    label: "抄送任务",
    value: "1",
    labelEn: "Copy Task",
  }, {
    label: "传阅任务",
    value: "2",
    labelEn: "Pass task",
  }],
  JOB_BTNS: [{
    label: "同意",
    type: "primary",
    value: "0",
    labelEn: "Agree",
  }, {
    label: "前加节点",
    value: "13",
    labelEn: "Before Node",
  }, {
    label: "后加节点",
    value: "14",
    labelEn: "After Node",
  }, {
    label: "加并节点",
    value: "15",
    labelEn: "Node Together",
  }, {
    label: "前加签",
    value: "5",
    type: "primary",
    plain: true,
    labelEn: "Before Signature",
  }, {
    label: "后加签",
    value: "6",
    type: "primary",
    plain: true,
    labelEn: "After Signature",
  }, {
    label: "加并签",
    value: "7",
    labelEn: "Sign Together",
  }, {
    label: "退回首节点",
    value: "1",
    labelEn: "Back First",
  }, {
    label: "退回上一步",
    value: "10",
    labelEn: "Back Pre",
  }, {
    label: "任意跳转",
    value: "8",
    labelEn: "Any Jump",
  }, {
    label: "任意驳回",
    value: "2",
    type: "danger",
    plain: true,
    labelEn: "Any Reject",
  }, {
    label: "抄送任务",
    value: "11",
    labelEn: "Carbon Copy",
  }, {
    label: "传阅任务",
    value: "12",
    labelEn: "Pass Read",
  }, {
    label: "下一办理人",
    value: "3",
    labelEn: "Next Handler",
  }, {
    label: "转办任务",
    value: "17",
    labelEn: "Turn Job",
  }, {
    label: "拒绝", // 原终止流程
    value: "18",
    labelEn: "Terminate",
  }, {
    label: "提前结束",
    value: "9",
    labelEn: "Early End",
  }, {
    label: "作废流程",
    value: "4",
    labelEn: "Invalid",
  }],
  ROUTE_ACTIONS: [{
    label: '暂停当前节点',
    value: '-4',
    labelEn: 'STop Current Node'
  }, {
    label: '开启下一节点',
    value: '8',
    labelEn: 'Start Next Node'
  }, {
    label: '通过',
    value: '0',
    labelEn: 'Agree'
  }, {
    label: '驳回到其他节点',
    value: '2',
    labelEn: 'Reject To Node'
  }, {
    label: '退回首节点',
    value: '1',
    labelEn: 'Back First'
  }, {
    label: '退回上一步',
    value: '10',
    labelEn: 'Back Pre'
  }, {
    label: '拒绝', // 原终止流程
    value: '18',
    labelEn: 'Terminate'
  }, {
    label: '提前结束',
    value: '9',
    labelEn: 'Early End'
  }, {
    label: '可自定义更多',
    value: '_define_',
    labelEn: 'Early End'
  }],
  MSG_TYPE: [{
    label: "个人消息",
    value: "0",
  }, {
    label: "群消息",
    value: "1",
  }],
  NODE_STATUS: [{
    label: "未开始",
    value: "-1",
    labelEn: "No Start",
  }, {
    label: "办理中",
    value: "0",
    labelEn: "Running",
    elTagClass: 'text-primary'
  }, {
    label: "完成",
    value: "1",
    labelEn: "Complete",
  }, {
    label: "驳回中",
    value: "2",
    labelEn: "Rejecting",
  }, {
    label: "跳过",
    value: "3",
    labelEn: "Skip",
  }, {
    label: "被驳回",
    value: "9",
    labelEn: "Rejected",
  }],
  MINE_NODE_STATUS: [{
    label: "办理中",
    value: "0",
    labelEn: "Running",
    elTagClass: 'text-primary'
  }, {
    label: "被驳回",
    value: "9",
    labelEn: "Rejected",
  }],
  REJECT_STATUS: [{
    label: "驳回中",
    value: "0",
  }, {
    label: "结束",
    value: "1",
  }],
  REJECT_TYPE: [{
    label: "依次返回",
    value: "0",
  }, {
    label: "直接返回",
    value: "1",
  }],
  OPERATOR: [{
    label: "等于",
    value: "0",
  }, {
    label: "不等于",
    value: "1",
  }, {
    label: "大于",
    value: "2",
  }, {
    label: "大于等于",
    value: "3",
  }, {
    label: "小于",
    value: "4",
  }, {
    label: "小于等于",
    value: "5",
  }, {
    label: "包含",
    value: "6",
  }, {
    label: "不包含",
    value: "7",
  }],
  VAL_TYPE: [{
    label: "普通模式",
    value: "-2",
  }, {
    label: "分配模式",
    value: "-1",
  }, {
    label: "简单模式",
    value: "0",
  }, {
    label: "SpEL模式", // 针对人员为固定模式
    value: "1",
  }, {
    label: "专业模式",
    value: "2",
  }, {
    label: "Http模式",
    value: "3",
  }],
  NOTICE_STATUS: [{
    label: "发起",
    value: "-1",
  }, {
    label: "办理中",
    value: "0",
  }, {
    label: "完成",
    value: "1",
  }],
  TODO_JOB_STATUS: [{
    label: "未开始",
    value: "-1",
  }, {
    label: "办理中",
    value: "0",
  }, {
    label: "驳回中",
    value: "2",
  }, {
    label: "被驳回",
    value: "9",
  }],
  FLOW_STATUS: [{
    label: "撤回",
    value: "-2",
    labelEn: "Recall",
  }, {
    label: "发起",
    value: "-1",
    labelEn: "Initiate",
  }, {
    label: "运行中",
    value: "0",
    labelEn: "Running",
  }, {
    label: "完结",
    value: "1",
    labelEn: "End",
  }, {
    label: "作废",
    value: "2",
    labelEn: "Invalid",
  }, {
    label: "审批拒绝",
    value: "3",
    labelEn: "Terminate",
  }],
  FLOW_STATUS_END: [{
    label: "完结",
    value: "1",
    labelEn: "End",
  }, 
  {
    label: "审批拒绝",
    value: "3",
    labelEn: "Terminate",
  },
  {
    label: "撤回",
    value: "-2",
    labelEn: "Recall",
  },
  ],
  NODE_METHODS: methods,
  FLOW_METHODS: [{
    label: "当流程发起时",
    value: "initiate",
  }, {
    label: "当流程完成时",
    value: "finish",
  }, {
    label: "当流程被撤回时",
    value: "recall",
  }, {
    label: "当流程被终止时",
    value: "terminate",
  }, {
    label: "当流程被作废时",
    value: "invalid",
  }, ...methods],
  CLAZZ_TYPE: [{
    label: "节点事件",
    value: "0",
  }, {
    label: "全局事件",
    value: "1",
  }],
  COLUMN_TYPE: [
    {
      label: "varchar",
      value: "varchar",
    }, {
      label: "bigint",
      value: "bigint",
    }, {
      label: "int",
      value: "int",
    }, {
      label: "double",
      value: "double",
    }, {
      label: "timestamp",
      value: "timestamp",
    }, {
      label: "date",
      value: "date",
    }, {
      label: "float",
      value: "float",
    }, {
      label: "bit",
      value: "bit",
    },
  ],
  LOCK_FLAG: [{
    label: "有效",
    value: "0",
  }, {
    label: "锁定",
    value: "9",
  }],
  DATABASE_TYPE: [{
    label: "MySQL数据库",
    value: "MySQL",
  }, {
    label: "达梦数据库",
    value: "DM",
  }],
  FORM_TYPE: [{
    label: "设计表单",
    value: "0",
  }, {
    label: "系统表单",
    value: "1",
  }],
  FORM_DATA_TYPE: [{
    label: "字段定义",
    value: "0",
  }, {
    label: "权限配置",
    value: "1",
  }, {
    label: "打印设计",
    value: "2",
  }],
  FORM_PERM_TYPE: [{
    label: "隐藏",
    value: "-1",
  }, {
    label: "只读",
    value: "0",
  }, {
    label: "可编辑",
    value: "1",
  }],
  ALL_FORM_PERM_TYPE: [{
    label: "全部只读",
    value: "0",
  }, {
    label: "全部可编辑",
    value: "1",
  }],
  PARAM_RULE_TYPE: [{
    label: "人员规则",
    value: "0",
  }, {
    label: "条件规则",
    value: "1",
  }, {
    label: "父子流程",
    value: "2",
  }, {
    label: "监听事件",
    value: "3",
  }, {
    label: "查询/更新表单Http参数",
    value: "4",
  }, {
    label: "保存子流程表单Http接口",
    value: "5",
  }, {
    label: "更新子流程表单Http接口",
    value: "6",
  }, {
    label: "更新父流程表单Http接口",
    value: "7",
  }, {
    label: '节点路由',
    value: '8'
  }],
  OR_OR_AND: [{
    label: "或",
    value: "0",
  }, {
    label: "且",
    value: "1",
  }],
  PARAM_FROM: [{
    label: "请求头",
    value: "0",
  }, {
    label: "传参",
    value: "1",
  }, {
    label: "回参",
    value: "2",
  }],
  PARAM_VAL_TYPE: paramValTypes,
  SYS_PARAM_VAL_TYPE: [{
    label: '系统字段',
    value: '0',
  }, ...paramValTypes.slice(1)
  ],
  HTTP_METHODS: [{
    label: "GET",
    value: "GET",
  }, {
    label: "POST",
    value: "POST",
  }, {
    label: "PUT",
    value: "PUT",
  }, {
    label: "DELETE",
    value: "DELETE",
  }],
  PARAM_TYPES: [{
    label: "json",
    value: "0",
  }, {
    label: "form",
    value: "1",
  }],
  BELONG_TYPE_STATUS: [
    {
      label: "全部",
      value: "-1",
    },
    {
      label: "待办理",
      value: "0",
    },
    {
      label: "被抄送",
      value: "1",
    },
    {
      label: "待阅",
      value: "2",
    },
    {
      label: "被加签",
      value: "3",
    },
  ]
}
