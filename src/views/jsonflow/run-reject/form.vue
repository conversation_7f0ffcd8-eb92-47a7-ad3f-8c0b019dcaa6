<template>
	<el-dialog
		v-model="visible"
		:title="title"
		width="60%"
		:close-on-click-modal="false"
		draggable
	>
		<el-form
			ref="dataFormRef"
			v-loading="loading"
			:model="form"
			:rules="dataRules"
			label-width="110px"
			:disabled="operType === 'view'"
		>
			<el-row :gutter="24">
				<el-col :span="12" class="mb-1">
					<el-form-item :label="$t('runReject.flowInstId')" prop="flowInstId">
						<el-input v-model="form.flowInstId" :placeholder="t('runReject.inputFlowInstIdTip')" disabled />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runReject.flowKey')" prop="flowKey">
						<el-select
							v-model="form.flowKey"
							:placeholder="t('runReject.inputFlowKeyTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowKey"
								:key="index"
								:label="item.flowName"
								:value="item.flowKey"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runReject.fromRunNodeId')" prop="fromRunNodeId">
						<el-select
							v-model="form.fromRunNodeId"
							:placeholder="t('runReject.inputFromRunNodeIdTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.fromRunNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runReject.toRunNodeId')" prop="toRunNodeId">
						<el-select
							v-model="form.toRunNodeId"
							:placeholder="t('runReject.inputToRunNodeIdTip')"
							clearable
							filterable
							disabled
						>
							<el-option
								v-for="(item, index) in cascadeDic.toRunNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runReject.status')" prop="status">
						<el-select
							v-model="form.status"
							:placeholder="t('runReject.inputStatusTip')"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.REJECT_STATUS"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb-1">
					<el-form-item :label="t('runReject.remark')" prop="remark">
						<el-input v-model="form.remark" type="textarea" :placeholder="t('runReject.inputRemarkTip')" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-if="operType !== 'view'" #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="RunRejectDialog">

import { useMessage } from "@/hooks/message"
import { getObj, addObj, putObj } from "@/api/jsonflow/run-reject"
import { useI18n } from "vue-i18n"
import { rule } from "@/utils/validate"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"

const emit = defineEmits(["refresh"])
const { t } = useI18n()

// 定义变量内容
const dataFormRef = ref()
const visible = ref(false)
const loading = ref(false)
const operType = ref(false)
const title = ref("")
// 定义字典
const cascadeDic = reactive({})
const onCascade = onCascadeChange(cascadeDic, { key: "flowInstId", cascades: ["flowKey", "fromRunNodeId", "toRunNodeId"] })
// 提交表单数据
const form = reactive({
	flowKey: "",
	fromRunNodeId: "",
	toRunNodeId: "",
	status: "",
})

// 定义校验规则
const dataRules = ref({
	flowKey: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
	fromRunNodeId: [{ required: true, message: "来源节点名称不能为空", trigger: "blur" }],
	toRunNodeId: [{ required: true, message: "到达节点名称不能为空", trigger: "blur" }],
	status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
})

// 打开弹窗
const openDialog = (type: string, id: string) => {
	visible.value = true
	operType.value = type
	form.id = ""

  const titleMap = {
    'add': '新增',
    'edit': '编辑',
    'view': '查看'
  }
  title.value = titleMap[type]

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields()
	})

	// 获取RunReject信息
	if (id) {
		form.id = id
		getRunRejectData(id)
	}
}

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {})
	if (!valid) { return false }

	try {
		loading.value = true
		form.id ? await putObj(form) : await addObj(form)
		proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功')
		visible.value = false
		emit("refresh")
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
	finally {
		loading.value = false
	}
}

// 初始化表单数据
const getRunRejectData = (id: string) => {
	// 获取数据
	loading.value = true
	getObj(id).then((res: any) => {
		Object.assign(form, res.object)
		onCascade(form)
	})
		.finally(() => {
			loading.value = false
		})
}

// 暴露变量
defineExpose({
	openDialog,
})
</script>
