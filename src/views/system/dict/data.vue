<template>
  <div>
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      class="white-form-box"
    >
      <el-form-item label="字典标签" prop="dictLabel">
        <el-input
          v-model="queryParams.dictLabel"
          placeholder="请输入字典标签"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <div class="table-handle-box">
      <div>
        <el-button
          v-auths="['system:dict:add']"
          type="primary"
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
        <el-button
          v-auths="['system:dict:edit']"
          type="primary"
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
        <el-button
          v-auths="['system:dict:export']"
          type="primary"
          icon="Download"
          plain
          class="is-deep"
          @click="handleExport"
        >
          导出
        </el-button>
        <el-button
          v-auths="['system:dict:remove']"
          type="danger"
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </div>
      <right-toolbar v-model:show-search="showSearch" @query-table="getList" />
    </div>

    <div class="white-body-box">
      <el-table
        v-loading="loading"
        stripe
        :data="dataList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="字典标签"
          align="center"
          prop="dictLabel"
          show-overflow-tooltip
          min-width="100"
        >
          <template #default="scope">
            <span
              v-if="scope.row.listClass == '' || scope.row.listClass == 'default'"
              :class="scope.row.cssClass"
            >
              {{ scope.row.dictLabel }}
            </span>
            <el-tag
              v-else
              :type="scope.row.listClass == 'primary' ? '' : scope.row.listClass"
              :class="scope.row.cssClass"
            >
              {{ scope.row.dictLabel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="字典键值" align="center" prop="dictValue" />
        <el-table-column label="字典排序" align="center" prop="dictSort" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          show-overflow-tooltip
        />
        <el-table-column label="创建时间" align="center" prop="createTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="110"
          class-name="small-padding fixed-width"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              v-auths="['system:dict:edit']"
              link
              type="primary"
              @click="handleUpdate(scope.row)"
            >
              修改
            </el-button>
            <el-button
              v-auths="['system:dict:remove']"
              link
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="dataRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字典类型">
          <el-input v-model="form.dictType" :disabled="true" clearable maxlength="50" />
        </el-form-item>
        <el-form-item label="数据标签" prop="dictLabel">
          <el-input
            v-model="form.dictLabel"
            placeholder="请输入数据标签"
            clearable
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="数据键值" prop="dictValue">
          <el-input
            v-model="form.dictValue"
            placeholder="请输入数据键值"
            clearable
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="样式属性" prop="cssClass">
          <el-input
            v-model="form.cssClass"
            placeholder="请输入样式属性"
            clearable
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="显示排序" prop="dictSort">
          <el-input-number v-model="form.dictSort" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="回显样式" prop="listClass">
          <el-select v-model="form.listClass" clearable>
            <el-option
              v-for="item in listClassOptions"
              :key="item.value"
              :label="item.label + '(' + item.value + ')'"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            maxlength="500"
            placeholder="请输入内容"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancel"> 取消 </el-button>
        <el-button type="primary" :loading="loading" @click="submitForm">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Data">
import useDictStore from "@/store/modules/dict"
import { optionselect as getDictOptionselect, getType } from "@/api/system/dict/type"
import { listData, getData, delData, addData, updateData } from "@/api/system/dict/data"

const props = defineProps({
  dictId: { type: [Number, String], default: null },
  dictName: { type: String, default: "" },
})
const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

const dataList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const selectionList = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const defaultDictType = ref("")
// 数据标签回显样式
const listClassOptions = ref([
  { value: "default", label: "默认" },
  { value: "primary", label: "主要" },
  { value: "success", label: "成功" },
  { value: "info", label: "信息" },
  { value: "warning", label: "警告" },
  { value: "danger", label: "危险" },
])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dictType: undefined,
    dictLabel: undefined,
    status: undefined,
  },
  rules: {
    dictLabel: [{ required: true, message: "数据标签不能为空", trigger: "blur" }],
    dictValue: [{ required: true, message: "数据键值不能为空", trigger: "blur" }],
    dictSort: [{ required: true, message: "数据顺序不能为空", trigger: "blur" }],
  },
})

const { queryParams, form, rules } = toRefs(data)

watch(
  () => props.dictId,
  (v) => {
    queryParams.value.pageNum = 1
    v && getTypes(v)
  }
)

/** 查询字典类型详细 */
function getTypes(id) {
  getType(id).then((response) => {
    queryParams.value.dictType = response.data.dictType
    defaultDictType.value = response.data.dictType
    getList()
  })
}

/** 查询字典数据列表 */
function getList() {
  loading.value = true
  listData(queryParams.value).then((response) => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    dictCode: undefined,
    dictLabel: undefined,
    dictValue: undefined,
    cssClass: undefined,
    listClass: "default",
    dictSort: 0,
    status: "0",
    remark: undefined,
  }
  proxy.resetForm("dataRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 返回按钮操作 */
function handleClose() {
  const obj = { path: "/system/dict" }
  proxy.$tab.closeOpenPage(obj)
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  queryParams.value.dictType = defaultDictType.value
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加字典数据"
  form.value.dictType = queryParams.value.dictType
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.dictCode)
  selectionList.value = selection
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const dictCode = row.dictCode || ids.value
  getData(dictCode).then((response) => {
    form.value = response.data
    open.value = true
    title.value = "修改字典数据"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["dataRef"].validate((valid) => {
    if (valid) {
      loading.value = true
      let actionUrl = form.value.dictCode ? updateData : addData
      actionUrl(form.value)
        .then((response) => {
          useDictStore().removeDict(queryParams.value.dictType)
          proxy.$modal.msgSuccess(form.value.dictCode ? "修改成功" : "新增成功")
          open.value = false
          getList()
        })
        .finally(() => {
          loading.value = false
        })
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const dictCodes = row.dictCode || ids.value
  const dictNames =
    row.dictLabel || selectionList.value.map((item) => item.dictLabel).join(",")
  proxy.$modal
    .confirm('是否确认删除字典"' + dictNames + '"的数据项？')
    .then(function () {
      return delData(dictCodes)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
      useDictStore().removeDict(queryParams.value.dictType)
    })
    .catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/dict/data/export",
    {
      ...queryParams.value,
    },
    `dict_data_${new Date().getTime()}.xlsx`
  )
}
</script>
