<template>
	<div style="width: 100%;">
		<el-input
			v-model="data.modelValue"
			:disabled="props.disabled"
			clearable
			@blur="changeModelValue"
		/>
	</div>
</template>

<script setup lang="ts" name="UserRolePickerIndex">
import { verifyEmail } from "@/utils/toolsValidate"
const { proxy } = getCurrentInstance()

const props = defineProps({
	modelValue: {
		type: String,
		default: null,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
})

const emits = defineEmits(["update:modelValue"])

const data = reactive({
	modelValue: null,
})

const changeModelValue = () => {
	if (data.modelValue) {
		let boolean = verifyEmail(data.modelValue)
		if (!boolean) {
			data.modelValue = null
			proxy.$modal.msgSuccess("请输入正确的邮箱")
		}
		emits("update:modelValue", data.modelValue)
	}
	else {
		emits("update:modelValue", null)
	}
}

onMounted(() => {
	data.modelValue = props.modelValue
})
</script>
