<template>
	<el-dialog
		v-model="dialogVisible"
		title="分配角色"
		:before-close="handleClose"
		width="900px"
		append-to-body
	>
		<div>
			<div class="form-header">
				基本信息
			</div>
			<el-form :model="form" label-width="80px">
				<el-row>
					<el-col :span="8" :offset="2">
						<el-form-item label="用户昵称" prop="nickName">
							<el-input v-model="form.nickName" disabled />
						</el-form-item>
					</el-col>
					<el-col :span="8" :offset="2">
						<el-form-item label="登录账号" prop="userName">
							<el-input v-model="form.userName" disabled />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<div class="form-header">
				角色信息
			</div>
			<el-table
				v-if="dialogVisible"
				ref="roleRef"
				v-loading="loading"
				:row-key="getRowKey"
				:data="roles.slice((pageNum - 1) * pageSize, pageNum * pageSize)"
				@row-click="clickRow"
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" :reserve-selection="true" width="55" />
				<el-table-column
					label="序号"
					width="55"
					type="index"
					align="center"
				>
					<template #default="scope">
						<span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
					</template>
				</el-table-column>
				<el-table-column label="角色名称" align="center" prop="roleName" />
				<el-table-column label="权限字符" align="center" prop="roleKey" />
				<el-table-column
					label="创建时间"
					align="center"
					prop="createTime"
					width="180"
				>
					<template #default="scope">
						<span>{{ parseTime(scope.row.createTime) }}</span>
					</template>
				</el-table-column>
			</el-table>

			<pagination
				v-show="total > 0"
				v-model:page="pageNum"
				v-model:limit="pageSize"
				:total="total"
			/>
		</div>
		<template #footer>
			<el-button @click="handleClose">
				取消
			</el-button>
			<el-button type="primary" @click="submitForm">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script setup name="UserAuthRoleModal">
const dialogVisible = ref(false)
import { getAuthRole, updateAuthRole } from "@/api/system/user"

const { proxy } = getCurrentInstance()

const loading = ref(true)
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(10)
const roleIds = ref([])
const roles = ref([])
const form = ref({
	nickName: undefined,
	userName: undefined,
	userId: undefined,
})

const emit = defineEmits(["change"])

const open = row => {
	dialogVisible.value = true
	getUserAuthRole(row.userId)
}

// 获取用户角色
const getUserAuthRole = userId => {
	loading.value = true
	getAuthRole(userId)
		.then(response => {
			form.value = response.user
			roles.value = response.roles
			total.value = roles.value.length
			nextTick(() => {
				roles.value.forEach(row => {
					if (row.flag) {
						proxy.$refs["roleRef"].toggleRowSelection(row)
					}
				})
			})
		})
		.finally(() => {
			loading.value = false
		})
}

const handleClose = () => {
	dialogVisible.value = false
	roles.value = []
}
/** 单击选中行数据 */
function clickRow(row) {
	proxy.$refs["roleRef"].toggleRowSelection(row)
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
	roleIds.value = selection.map(item => item.roleId)
}

/** 保存选中的数据编号 */
function getRowKey(row) {
	return row.roleId
}
/** 提交按钮 */
function submitForm() {
	const userId = form.value.userId
	const rIds = roleIds.value.join(",")
	updateAuthRole({ userId: userId, roleIds: rIds }).then(response => {
		proxy.$modal.msgSuccess("授权成功")
		handleClose()
		emit("change")
	})
}

defineExpose({
	open,
})
</script>

<style scoped lang="scss">
.form-header {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
}
</style>
