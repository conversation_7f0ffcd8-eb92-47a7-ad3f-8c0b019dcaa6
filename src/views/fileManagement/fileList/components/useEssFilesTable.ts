export function useEssFilesTable(essFilesTableRef: any) {

  const getFileList = () => {
    essFilesTableRef.value.getList()
  }
  // 重置
  const resetQuery = () => {
    essFilesTableRef.value.resetQuery()
  }
  // 签署
  const handleSign = (row: Object) => {
    essFilesTableRef.value.handleSign(row)
  }
  // 办结
  const handleConclude = (row: Object) => {
    essFilesTableRef.value.handleConclude(row)
  }
  // 详情
  const handleViewDetails = (row: Object) => {
    essFilesTableRef.value.handleViewDetails(row)
  }
  // 催办
  const handlePress = (row: Object) => {
    essFilesTableRef.value.handlePress(row)
  }

  // 撤销
  const handleRevocation = (row: Object) => {
    essFilesTableRef.value.handleRevocation(row)
  }

  // 拒绝
  const handleRejected = (row: Object) => {
    essFilesTableRef.value.handleRejected(row)
  }
  // 批量签署
  const handleBatchSign = (list: Array<any>) => {
    essFilesTableRef.value.handleBatchSign(list)
  }

  return {
    resetQuery,
    handleSign,
    handleBatchSign,
    handleRevocation,
    handleRejected,
    handlePress,
    handleConclude,
    handleViewDetails,
    getFileList,
  }

}
