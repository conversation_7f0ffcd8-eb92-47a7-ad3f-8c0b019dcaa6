<template>
  <div class="file-upload-view" :class="showUpload ? '':'flex'">
    <div class="file-view" v-if="props.fileName && !showUpload">
      <FileIcon fileName="file"></FileIcon>
      <span class="text-ellipsis">{{ props.fileName }}</span>
    </div>
    <!--    嵌套一个form保证可以在disabled情况下点击按钮-->
    <el-form v-if="readonly">
      <el-button type="primary" link icon="Download" plain @click="downLoadFile">
        下载文件
      </el-button>
    </el-form>
    <el-button v-if="!showUpload && !readonly" type="primary" plain @click="reUpload">
      重新上传
    </el-button>
    <FileUpload
        v-show="showUpload"
        v-model="fileUrl"
        v-model:fileName="fileName"
        ref="uploadRef"
        :file-type="['pdf']"
        :showList="false"
        :highlight-tip="false"
        class="upload-file-wrapper"
        upload-dragger
        :fileSize="50"
    />
  </div>
</template>

<script setup>
import {
  startDownload
} from "@/api/ess/downloadCenter/downloadCenter.ts"
import { handleBlobFile } from "@/utils"

const FileIcon = defineAsyncComponent(() => import("@/components/SvgIcon/FileIcon.vue"))

const props = defineProps({
  modelValue: [String, Object, Array],
  fileName: [String, Object, Array],
  readonly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits();
const showUpload = ref(true)
const uploadRef = ref()
const fileUrl = ref(null)
const fileName = ref(null)

// 重新上传
const reUpload = () => {
  uploadRef.value.$el.querySelector('input').click()
}

/** 下载按钮操作 */
function downLoadFile() {
  startDownload(props.modelValue).then(resp => {
    handleBlobFile(resp.data, props.fileName)
  })
}

watch(() => props.modelValue,
    (val) => {
      if (val) {
        showUpload.value = false;
      } else {
        showUpload.value = true;
      }
    }, {deep: true, immediate: true}
);
//监听文件路径
watch(() => fileUrl,
    (val) => {
      emit('update:modelValue', val);
    }, {deep: true, immediate: true}
);
//监听文件名字
watch(() => fileName,
    (val) => {
      emit('update:fileName', val);
    }, {deep: true, immediate: true}
);


</script>

<style scoped lang="scss">
.file-upload-view {
  width: 100%;
  align-items: center;

  .file-view {
    @apply flex items-center bg-#F0F1F3 rounded-5px px-12px py-3px mr-10px text-overflow;
    width: 100%;


    span {
      @apply text-overflow;
      display: inline-block;
      min-width: 40px;
    }
  }
}

</style>
