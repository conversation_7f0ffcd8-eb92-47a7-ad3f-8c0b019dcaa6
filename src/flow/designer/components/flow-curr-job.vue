<template>
	<el-table :data="props.currSelect.attributes.attrs.cdata.defJob.currRunJobs" style="width: 100%">
		<el-table-column type="index" :label="t('createTable.index')" width="80">
			<template #header>
				<el-button
					icon="Plus"
					size="small"
					type="primary"
					circle
					@click="onAddItem"
				/>
			</template>
			<template #default="scope">
				<el-button
					icon="Minus"
					size="small"
					type="danger"
					circle
					@click="handleDelete(scope.$index, scope.row)"
          :disabled="scope.row.isConfigJob === '1'"
				/>
			</template>
		</el-table-column>
		<el-table-column
			prop="sort"
			:label="t('runJob.sort')"
			show-overflow-tooltip
			width="70"
		>
			<template #default="scope">
				<el-input v-model="scope.row.sort" :placeholder="t('runJob.inputSortTip')" />
			</template>
		</el-table-column>
		<el-table-column
			prop="jobName"
			:label="t('runJob.jobName')"
			show-overflow-tooltip
			width="150"
		>
			<template #default="scope">
				<el-input v-model="scope.row.jobName" :placeholder="t('runJob.inputJobNameTip')" />
			</template>
		</el-table-column>
		<el-table-column
			prop="jobType"
			:label="t('runJob.jobType')"
			show-overflow-tooltip
			width="100"
		>
			<template #default="scope">
				<el-select
					v-model="scope.row.jobType"
					:placeholder="t('runJob.inputJobTypeTip')"
					clearable
					filterable
					@change="handleRoleType(scope.row)"
				>
					<el-option
						v-for="(item, index) in DIC_PROP.JOB_USER_NONE_TYPE"
						:key="index"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
			</template>
		</el-table-column>
		<el-table-column
			prop="roleId"
			:label="t('runJob.roleId')"
			show-overflow-tooltip
			width="130"
		>
			<template #default="scope">
				<el-select
					v-model="scope.row.roleId"
					:placeholder="t('runJob.inputRoleIdTip')"
					clearable
					filterable
				>
					<el-option
						v-for="(item, index) in dicData.users"
						v-if="scope.row.jobType === DIC_PROP.JOB_USER_TYPE[0].value"
						:key="index"
						:label="item.name"
						:value="item.userId"
					/>
					<el-option
						v-for="(item, index) in dicData.roles"
						v-if="scope.row.jobType === DIC_PROP.JOB_USER_TYPE[1].value"
						:key="index"
						:label="item.roleName"
						:value="item.roleId"
					/>
					<!-- <el-option
						v-for="(item, index) in dicData.posts"
						v-if="scope.row.jobType === DIC_PROP.JOB_USER_TYPE[2].value"
						:key="index"
						:label="item.postName"
						:value="item.postId"
					/> -->
					<el-option
						v-for="(item, index) in dicData.depts"
						v-if="scope.row.jobType === DIC_PROP.JOB_USER_TYPE[2].value"
						:key="index"
						:label="item.deptName"
						:value="item.deptId"
					/>
				</el-select>
			</template>
		</el-table-column>
		<el-table-column prop="userId" :label="t('runJob.userId')" show-overflow-tooltip>
			<template #default="scope">
				<convert-name
					:options="dicData.users"
					:value="scope.row.userId"
					:value-key="'userId'"
					:show-key="'name'"
				/>
			</template>
		</el-table-column>
		<el-table-column prop="startTime" :label="t('runJob.startTime')" show-overflow-tooltip />
		<el-table-column prop="status" :label="t('runJob.status')" show-overflow-tooltip>
			<template #default="scope">
				<dict-tag :options="DIC_PROP.NODE_STATUS" :value="scope.row.status" />
			</template>
		</el-table-column>
	</el-table>
</template>

<script setup lang="ts" name="FlowCurrJob">
import { useI18n } from 'vue-i18n'
const { proxy } = getCurrentInstance()
import { onLoadDicUrl } from '../../components/convert-name/convert'
import { handleChangeJobType } from '../../index'
import { DIC_PROP } from '../../support/dict-prop'

const { t } = useI18n()

const props = defineProps({
	currSelect: {
		type: Object,
		default: null
	}
})

// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: 'users' }, { key: 'roles' }, { key: 'posts' }, { key: 'depts' })
onMounted(async () => {
	await onLoad(dicData)
})

const onAddItem = () => {
	let jobName = props.currSelect.attributes.attrs.cdata.defJob.jobName
	let obj = { sort: 1, jobName: jobName, jobType: DIC_PROP.JOB_USER_NONE_TYPE[0].value, roleId: null, userId: null,
		startTime: null, status: DIC_PROP.NODE_STATUS[0].value }
	props.currSelect.attributes.attrs.cdata.defJob.currRunJobs.push(obj)
}

const handleDelete = (index: number, row: any) => {
	let currRunJobs = props.currSelect.attributes.attrs.cdata.defJob.currRunJobs
	if (currRunJobs.length === 1) {
		proxy.$modal.msgWarning('当前节点参与者至少需存在一个参与者')
		return
	}
	props.currSelect.attributes.attrs.cdata.defJob.currRunJobs.splice(index, 1)
}

function handleRoleType(row) {
	handleChangeJobType(dicData, row)
}

</script>

<style lang="scss">
  @use '../assets/style/flow-attr.scss' as *;
</style>
