import { download } from "@/utils/request"
import { useDict } from "@/utils/dict"
import {
	parseTime,
	resetForm,
	addDateRange,
	handleTree,
	selectDictLabel,
	selectDictLabels,
} from "@/utils/common"
const dateDefaultTime = [new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]
// 全局方法挂载
export function registerGlobalMethod(app) {
	app.config.globalProperties.useDict = useDict
	app.config.globalProperties.download = download
	app.config.globalProperties.parseTime = parseTime
	app.config.globalProperties.resetForm = resetForm
	app.config.globalProperties.handleTree = handleTree
	app.config.globalProperties.addDateRange = addDateRange
	app.config.globalProperties.selectDictLabel = selectDictLabel
	app.config.globalProperties.selectDictLabels = selectDictLabels
	app.config.globalProperties.dateDefaultTime = dateDefaultTime // 时间选择器默认结束时间到23:59:59
}
