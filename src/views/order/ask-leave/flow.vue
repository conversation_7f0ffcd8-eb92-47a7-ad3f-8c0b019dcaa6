<template>
  <div>
    <el-form
      ref="dataFormRef"
      :model="form"
      :rules="dataRules"
      label-width="90px"
      v-loading="loading"
      :disabled="operType === 'view'"
    >
      <el-row :gutter="24">
        <el-col :span="12" class="mb20px" >
          <el-form-item label="请假类型" prop="type" v-if="!hiddenFields.type">
            <el-input
              v-model="form.type"
              placeholder="请输入请假类型"
              :disabled="disabledFields.type"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20px" v-if="!hiddenFields.startTime">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              type="datetime"
              placeholder="请输入开始时间"
              :disabled="disabledFields.startTime"
              v-model="form.startTime"
              :value-format="dateTimeStr"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20px" v-if="!hiddenFields.endTime">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              type="datetime"
              placeholder="请输入结束时间"
              :disabled="disabledFields.endTime"
              v-model="form.endTime"
              :value-format="dateTimeStr"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20px" v-if="!hiddenFields.days">
          <el-form-item label="请假天数" prop="days">
            <el-input
              v-model="form.days"
              placeholder="请输入请假天数"
              :disabled="disabledFields.days"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20px" v-if="!hiddenFields.carbonCopyPerson">
          <el-form-item label="抄送人" prop="carbonCopyPerson">
            <el-select
              v-model="form.carbonCopyPerson"
              placeholder="请输入抄送人"
              :disabled="disabledFields.carbonCopyPerson"
              clearable
              filterable
              multiple
            >
              <el-option
                v-for="(item, index) in dicData.carbonCopyPerson"
                :key="index"
                :label="item.name"
                :value="item.userId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20px" v-if="!hiddenFields.remark">
          <el-form-item label="请假事由" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入请假事由"
              :disabled="disabledFields.remark"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20px" v-if="!hiddenFields.imgUrls">
          <el-form-item label="上传文档" prop="imgUrls">
            <el-input
              v-model="form.imgUrls"
              placeholder="请输入上传文档"
              :disabled="disabledFields.imgUrls"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template v-if="data.submitBtn">
      <footer class="el-dialog__footer">
        <span class="dialog-footer">
          <el-button type="primary" @click="printForm" v-if="form.printInfo">打印</el-button>
          <el-button type="primary" @click="submitForm" :disabled="loading">提交</el-button>
        </span>
      </footer>
    </template>
    <template v-else>
      <footer class="el-dialog__footer">
        <span class="dialog-footer">
          <el-button type="primary" @click="printForm" v-if="form.printInfo">打印</el-button>
        </span>
      </footer>
    </template>

    <!-- 打印表单 -->
    <el-dialog
      v-model="data.showTinymceView"
      top="20px"
      width="700px"
      :title="data.tinymceTitle"
      append-to-body
      @close="closePrint"
    >
      <tinymce-view v-if="data.showTinymceView" :currFlowForm="form"></tinymce-view>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="AskLeaveForm">
import * as askLeave from '@/api/order/ask-leave'
import * as orderVue from '@/api/order/order-key-vue'
import { onLoadDicUrl, onUpdateDicData } from '@/flow/components/convert-name/convert.ts'
import { handleCustomFormPerm, handleFormPrint } from '@/flow/utils/form-perm'
import { deepClone } from '@/utils/index'
import { initCustomFormMethods } from '../index'
import { getCurrentInstance } from 'vue'

const emits = defineEmits(['handleJob'])
const { proxy } = getCurrentInstance()
// 引入组件
const TinymceView = defineAsyncComponent(() => import('@/flow/components/tinymce/TinymceView.vue'))

// 定义变量内容
const dataFormRef = ref()
const loading = ref(false)
const operType = ref(false)
// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: 'carbonCopyPerson' })
onMounted(async () => {
  await onLoad(dicData)
})

const props = defineProps({
  currJob: {
    type: Object,
    default: null
  }
})

// 提交表单数据
const form = reactive({
  type: '',
  startTime: '',
  endTime: '',
  days: '',
  carbonCopyPerson: [],
  remark: '',
  imgUrls: ''
})

// 定义校验规则
const dataRules = ref({
  startTime: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
  days: [{ required: true, message: '请假天数不能为空', trigger: 'blur' }],
  remark: [{ required: true, message: '请假事由不能为空', trigger: 'blur' }]
})

function initJobData() {
  handleGetObj(props.currJob.orderId)
}

const fieldsPerm = {
  type: false,
  startTime: false,
  endTime: false,
  days: false,
  carbonCopyPerson: false,
  remark: false,
  imgUrls: false
}
// 定义字段显隐
const hiddenFields = reactive(fieldsPerm)
// 定义字段是否可编辑
const disabledFields = reactive(deepClone(fieldsPerm))

function handleGetObj(id) {
  askLeave.getObj(id).then(async (resp) => {
    let formData = resp.object ? resp.object : {}
    Object.assign(form, formData)
    form.runJobId = props.currJob.id
    await initFormPermPrint()
  })
}

const data = reactive({
  showTinymceView: false,
  tinymceTitle: '',
  submitBtn: true
})

const methods = initCustomFormMethods(data, disabledFields, operType)

async function initFormPermPrint() {
  let elTab = orderVue.currElTabIsExist(props.currJob)
  // 处理表单权限
  let res = await handleCustomFormPerm(props, hiddenFields, disabledFields, elTab)
  // 判断是否仅查看
  await orderVue.currElTabIsView(methods, props.currJob, submitForm, res.callback)
  // 采用elTab配置的模板
  await handleFormPrint(form, elTab.type, elTab.id, '1')
}

function printForm() {
  closePrint(true, false)
  data.tinymceTitle = '请假工单'
  data.showTinymceView = true
}

function closePrint(isInit, isSave) {
  if (isInit) {
    form.formData = form
    form.dicData = { carbonCopyPerson: dicData.carbonCopyPerson }
    form['carbonCopyPerson.valueKey'] = 'userId'
    form['carbonCopyPerson.showKey'] = 'name'
  } else {
    delete form.formData
    delete form.dicData
    delete form['carbonCopyPerson.valueKey']
    delete form['carbonCopyPerson.showKey']
  }
  if (isSave) delete form.printInfo
}

async function submitForm() {
  closePrint(false, true)
  try {
    loading.value = true
    await askLeave.putObj(form)
    orderVue.currElTabIsSave(props.currJob, true, emits)
    proxy.$modal.msgSuccess(form.id ? '修改成功' : '添加成功')
  } catch (err) {
    proxy.$modal.msgError(err.msg)
  } finally {
    loading.value = false
  }
}

// 监听双向绑定
watch(
  () => props.currJob.id,
  () => {
    initJobData()
  }
)

onMounted(() => {
  initJobData()
})
</script>

<style lang="scss" scoped>
.el-dialog__footer {
  text-align: center;

  .dialog-footer {
    text-align: center;
  }
}
</style>
