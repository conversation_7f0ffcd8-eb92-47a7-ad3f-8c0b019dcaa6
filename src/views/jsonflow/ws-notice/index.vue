<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item :label="$t('wsNotice.flowInstId')" prop="flowInstId">
						<el-input v-model="state.queryForm.flowInstId" :placeholder="t('wsNotice.inputFlowInstIdTip')" />
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							{{ $t('common.queryBtn') }}
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							{{ $t('common.resetBtn') }}
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<right-toolbar
						v-model:show-search="showSearch"
						:export="'jsonflow_wsnotice_export'"
						class="ml10"
						style="float: right;margin-right: 20px"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" :label="t('wsNotice.index')" width="40" />
				<el-table-column prop="flowInstId" :label="t('wsNotice.flowInstId')" show-overflow-tooltip />
				<el-table-column prop="flowKey" :label="t('wsNotice.flowKey')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.flowInstId"
							:value="scope.row.flowKey"
							:value-key="'flowKey'"
							:show-key="'flowName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="userId" :label="t('wsNotice.userId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.userId"
							:value="scope.row.userId"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="type" :label="t('wsNotice.type')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.MSG_TYPE" :value="scope.row.type" />
					</template>
				</el-table-column>
				<el-table-column prop="jobType" :label="t('wsNotice.jobType')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.JOB_MSG_TYPE" :value="scope.row.jobType" />
					</template>
				</el-table-column>
				<el-table-column prop="status" :label="t('wsNotice.status')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.NOTICE_STATUS" :value="scope.row.status" />
					</template>
				</el-table-column>
				<el-table-column
					prop="data"
					type="textarea"
					:label="t('wsNotice.data')"
					show-overflow-tooltip
				/>
				<el-table-column prop="createUser" :label="t('wsNotice.createUser')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.createUser"
							:value="scope.row.createUser"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" :label="t('wsNotice.createTime')" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="100">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.viewBtn') }}
							</template>
							<el-button
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								修改
							</template>
							<el-button
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.delBtn') }}
							</template>
							<el-button
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemWsNotice">
import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchList, delObjs } from "@/api/jsonflow/ws-notice"
import { useMessage, useMessageBox } from "@/hooks/message"

import { useI18n } from "vue-i18n"
import { onLoaded } from "@/flow/components/convert-name/convert"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))
const { t } = useI18n()

// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
	onLoaded: onLoaded({ key: "createUser" }, { key: "userId" }, { key: "flowInstId" }),
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	getDataList()
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t("common.delConfirmText"))
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess(t("common.delSuccessText"))
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
}
</script>
