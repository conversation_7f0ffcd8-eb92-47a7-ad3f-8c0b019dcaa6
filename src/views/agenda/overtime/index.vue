<template>
  <div class="app-container">
    <CustomTabs v-model="currentTab" :options="tabs" />

    <OverFileTable :mine="currentTab" />
  </div>
</template>

<script setup name="Overtime">
const OverFileTable = defineAsyncComponent(() => import("./components/OverFileTable.vue"))
const route = useRoute()
const { proxy } = getCurrentInstance()
const tabs = [
  { label: "我发起的", value: 0, permissions: ["todo:overtime:mine"] },
  { label: "他人发起", value: 1, permissions: ["todo:overtime:other"] },
]
const tabsToAuthList = computed(() =>
  tabs.filter((item) => proxy.$auth.hasPermiOr(item.permissions))
)

const getDefaultTab = () => {
  // 优先根据 query.tab 且有权限，否则取第一个有权限的 tab
  const queryTab = Number(route.query.tab)
  if (!isNaN(queryTab) && tabsToAuthList.value.some((item) => item.value === queryTab)) {
    return queryTab
  }
  return tabsToAuthList.value?.[0]?.value ?? 0
}

const currentTab = ref(getDefaultTab())
</script>

<style scoped lang="scss"></style>
