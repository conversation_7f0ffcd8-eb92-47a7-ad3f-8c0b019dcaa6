import request from "@/utils/request"
export function fetchList(query?: Object) {
	return request({
		url: "/cloud-jsonflow/flow-rule/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/flow-rule",
		method: "post",
		data: obj,
	})
}

export function getObj(id?: string) {
	return request({
		url: "/cloud-jsonflow/flow-rule/" + id,
		method: "get",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-jsonflow/flow-rule",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/flow-rule",
		method: "put",
		data: obj,
	})
}
