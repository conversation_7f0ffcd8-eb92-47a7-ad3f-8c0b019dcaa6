<template>
  <div v-loading="loading" class="w-full h-full">
    <iframe
      v-if="iframeUrl"
      :src="iframeUrl"
      frameborder="0"
      width="100%"
      height="100%"
    />
  </div>
</template>

<script setup>
import { getFlowPreviewUrl } from "@/api/ess/fileManagement/file"

const route = useRoute()
const loading = ref(true)
const iframeUrl = ref(null)
const essFlowId = ref(route.query.essFlowId)

// 获取地址
async function getUrl() {
  if (!essFlowId.value) return
  try {
    const res = await getFlowPreviewUrl(essFlowId.value)
    iframeUrl.value = res.object.url
  } finally {
    loading.value = false
  }
}

onActivated(() => {
  getUrl()
})

onDeactivated(() => {
  iframeUrl.value = null
})

getUrl()
</script>
