import request from "@/utils/request"
import { eduUser } from "@/config/constant"

// 分页查询模块列表
export function pageModule(query) {
	return request({
		url: eduUser + "/system/module/listPage",
		method: "post",
		data: query,
	})
}

// 查询模块列表
export function listModule(query) {
	return request({
		url: eduUser + "/system/module/list",
		method: "get",
		params: query,
	})
}

// 查询模块详细
export function getModule(id) {
	return request({
		url: eduUser + "/system/module/" + id,
		method: "get",
	})
}

// 新增模块
export function addModule(data) {
	return request({
		url: eduUser + "/system/module/add",
		method: "post",
		data: data,
	})
}

// 修改模块
export function updateModule(data) {
	return request({
		url: eduUser + "/system/module/edit",
		method: "put",
		data: data,
	})
}

// 删除模块
export function delModule(id) {
	return request({
		url: eduUser + "/system/module/" + id,
		method: "delete",
	})
}
