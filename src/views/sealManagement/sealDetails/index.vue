<!--
 * @Author: ljn
 * @Date: 2025-03-06 14:52:45
 * @LastEditors: ljn
 * @LastEditTime: 2025-05-07 09:54:54
 * @Description: 印章详情
-->

<template>
  <div class="app-container">
    <div v-loading="loading" class="seal-base-info">
      <el-image
        :class="['seal-base-info__img', { 'grayscale-image': sealInfo.sealStatus == 'DISABLE' }]"
        fit="contain"
        :src="imageApi + sealInfo.sealFilePath"
      />
      <div class="seal-base-info__right">
        <el-descriptions :column="1">
          <el-descriptions-item label="印章名称">
            {{ sealInfo.sealName }}
          </el-descriptions-item>
          <el-descriptions-item label="印章状态">
            <span v-if="sealInfo.sealStatus == 'SUCCESS'" class="success-status">
              <el-icon><CircleCheckFilled /></el-icon>
              {{ selectDictLabel(seal_status, sealInfo.sealStatus) }}
            </span>
            <dict-tag v-else :options="seal_status" :value="sealInfo.sealStatus" class="inline" />
          </el-descriptions-item>
          <el-descriptions-item label="印章类型">
            {{ selectDictLabel(seal_type, sealInfo.sealType) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ sealInfo.createUser }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ sealInfo.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="印章ID">
            {{ sealInfo.essSealId }}
            <el-icon v-copyText="sealInfo.essSealId" class="copy">
              <CopyDocument />
            </el-icon>
          </el-descriptions-item>
          <el-descriptions-item label="印章归属主体">
            {{ selectDictLabel(seal_belong_subject, sealInfo.sealBelongSubject) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="seal-base-tab">
      <CustomTabs v-model="currentTab" :options="tabs" />
      <div class="seal-base-tab__body">
        <AuthList v-if="currentTab == 0" />
        <AuthLogList v-if="currentTab == 1" />
      </div>
    </div>
  </div>
</template>

<script setup name="SealDetails">
import { getSealInfo } from '@/api/ess/seal/seal'
import { imageApi } from '@/config/constant'
import AuthList from './components/AuthList.vue'
import AuthLogList from './components/AuthLogList.vue'

const { proxy } = getCurrentInstance()
const { seal_type, seal_status, seal_belong_subject } = proxy.useDict(
  'seal_type',
  'seal_status',
  'seal_belong_subject'
)

const route = useRoute()

const sealId = route.params.id
const loading = ref(true)
const sealInfo = ref({})

//  tabs
const currentTab = ref(0)
const tabs = ref([
  {
    label: '印章授权',
    value: 0
  },
  {
    label: '变更记录',
    value: 1
  }
])

// 获取印章详情
async function getSealDetails() {
  try {
    let res = await getSealInfo(sealId)
    sealInfo.value = res.object
    const obj = Object.assign({}, route, { title: sealInfo.value.sealName }) 
    proxy.$tab.updatePage(obj)
  } finally {
    loading.value = false
  }
}

getSealDetails()
</script>

<style scoped lang="scss">
.seal-card {
  background: #fff;
  border-radius: 12px;
  padding: 17px;
  margin-bottom: 15px;
}
.seal-base-info {
  @extend .seal-card;
  display: flex;
  .seal-base-info__right {
    padding-top: 10px;
  }
  .el-image {
    background: #fafbfd;
    border-radius: 12px;
    width: 230px;
    height: 230px;
    margin-right: 40px;
    flex-shrink: 0;
  }
  .grayscale-image {
    filter: grayscale(100%) brightness(99%);
  }
  .success-status {
    i {
      color: #00d184;
      font-size: 17px;
      margin-top: -2px;
      vertical-align: middle;
    }
  }

  :deep(.el-descriptions__cell) {
    .el-descriptions__label {
      width: 90px;
      display: inline-block;
      color: #6b6b6b;
    }
    .el-descriptions__content {
      color: #222;
    }
  }
  .copy {
    color: #aaaaaa;
    font-size: 14px;
    margin-left: 5px;
    vertical-align: middle;
    cursor: pointer;
    &:hover {
      color: var(--el-color-primary);
    }
  }
}

.seal-base-tab {
  @extend .seal-card;
  padding-top: 22px;
}
</style>
