import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-order/handover-node-record/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-order/handover-node-record",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/cloud-order/handover-node-record/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/cloud-order/handover-node-record/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-order/handover-node-record",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-order/handover-node-record",
		method: "put",
		data: obj,
	})
}

// 流程中获取数据
export function fetchFlowList(query: any) {
	return request({
		url: "/cloud-order/handover-node-record/flow/page",
		method: "get",
		params: query,
	})
}

// 批量新增或修改交接任务记录
export function batchSaveOrUpdate(obj: any) {
	return request({
		url: "/cloud-order/handover-node-record/batch",
		method: "post",
		data: obj,
	})
}
