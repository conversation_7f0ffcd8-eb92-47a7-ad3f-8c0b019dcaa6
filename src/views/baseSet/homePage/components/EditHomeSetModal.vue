<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="600px"
      top="8vh"
      :before-close="handleClose"
  >
    <el-form
        ref="formRef"
        :model="form"
        label-width="100px"
        :rules="rules"
        :disabled="formDisabled"
    >
      <el-form-item label="模块类型" prop="moduleType">
        <el-select v-model="form.moduleType" placeholder="请选择" clearable>
          <el-option
              v-for="dict in dictOptions.home_module_type"
              :key="dict.value"
              :label="dict.label"
              :value="+dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="appName">
        <el-input
            v-model="form.appName"
            placeholder="请输入名称"
            clearable
            maxlength="50"
            show-word-limit
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择" clearable>
          <el-option
              v-for="dict in dictOptions.normal_disable"
              :key="dict.value"
              :label="dict.label"
              :value="+dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="展示端" prop="appTypeArr">
        <el-checkbox-group v-model="form.appTypeArr" @change="changeAppType">
          <el-checkbox
              v-for="item in dictOptions.home_app_type"
              :key="item.value"
              :value="item.value"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="属性" prop="appAttr">
        <el-radio-group v-model="form.appAttr">
          <el-radio
              v-for="item in dictOptions.home_app_attr"
              :key="item.value"
              :value="+item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.appTypeArr.includes('0')" label="pc端链接" prop="pcUrl">
        <el-input v-model="form.pcUrl" placeholder="请输入PC端链接" clearable/>
      </el-form-item>
      <el-form-item
          v-if="form.appTypeArr.includes('1')"
          label="小程序链接"
          prop="programUrl"
      >
        <el-input v-model="form.programUrl" placeholder="请输入小程序链接" clearable/>
      </el-form-item>
      <el-form-item label="图标" prop="iconPath">
        <el-image
            fit="cover"
            style="width: 50px; height: 50px; margin-right: 20px"
            :preview-src-list="[imageApi + form.iconPath]"
            :src="imageApi + form.iconPath"
            v-if="form.iconPath && customize"
            preview-teleported
        />
        <ImageSelector
            v-model="form.iconPath"
            @change="customize = false"
            :disabled="formDisabled"
            :customize="customize"
        />
        <div v-if="!formDisabled" class="box-upload">
          <UserAvatar
              :isCircle="false"
              :uploadIcon="true"
              @finish="getUrl"
              :customize="customize"
              :iconUrl="imageApi + form.iconPath"
          ></UserAvatar>
          <span class="text-12px color-[#666]">支持png/jpg/jpeg，限5M内</span>
        </div>
      </el-form-item>
      <el-form-item label="可见范围" prop="scopeArr">
        <el-checkbox-group v-model="form.scopeArr">
          <el-checkbox
              v-for="item in dictOptions.home_app_scope"
              :key="item.value"
              :value="item.value"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort" controls-position="right" :min="0"/>
      </el-form-item>
      <el-form-item label="是否置顶" prop="topped">
        <el-radio-group v-model="form.topped">
          <el-radio
              v-for="item in dictOptions.home_app_top"
              :key="item.value"
              :value="+item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" maxlength="500" :rows="3"/>
      </el-form-item>
    </el-form>
    <template v-if="!formDisabled" #footer>
      <el-button @click="handleClose"> 取消</el-button>
      <el-button type="success" :loading="loading" @click="submitForm('add')">
        保存并新建
      </el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup name="EditHomeSetModal">
import { imageApi } from "@/config/constant"
import ImageSelector from "@/components/ImageSelector/index.vue"
import { addIndexCfgInfoAPI, updateIndexCfgInfoAPI } from "@/api/ess/baseSet/indexCfgInfo"
import { rule } from "@/utils/validate.js";

const { proxy } = getCurrentInstance()

const UserAvatar = defineAsyncComponent(() =>
    import("@/views/system/user/profile/components/UserAvatar.vue")
)
defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  dictOptions: Object,
})
const customize = ref(false)
const title = ref("新增")
const dialogVisible = ref(false)
const loading = ref(false)
const formDisabled = ref(false)
const formRef = ref(null)
const form = ref({
  appAttr: null,
  appName: null,
  appTypeArr: [], // 展示端多选
  iconPath: null,
  moduleType: null,
  pcUrl: null,
  programUrl: null,
  remark: null,
  scope: null,
  scopeArr: [], // 可见范围多选
  status: null,
  topped: null,
})
let _form = toRaw({
  ...form.value,
})
const rules = ref({
  moduleType: [{ required: true, message: "请选择模块类型", trigger: "change" }],
  appName: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  appTypeArr: [{ required: true, message: "请选择展示端", trigger: "change" }],
  appAttr: [{ required: true, message: "请选择属性", trigger: "change" }],
  iconPath: [{ required: true, message: "请选择图标", trigger: "change" }],
  pcUrl: [
    { required: true, message: "请输入PC端链接", trigger: "blur" },
    { validator: validateURL, trigger: "blur" },
  ],
  programUrl: [{ required: true, message: "请输入小程序链接", trigger: "blur" }],
  scopeArr: [{ required: true, message: "请选择可见范围", trigger: "change" }],
  topped: [{ required: true, message: "请选是否置顶", trigger: "change" }],
})
const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/

function validateURL(rule, value, callback) {
  if (!urlPattern.test(value) && form.value.appAttr == 1) {
    // 外链校验
    callback(new Error("请输入有效的外链地址"))
    // } else if(form.value.appAttr == 0 && !value.startsWith('/')){
    //   callback(new Error('请输入有效的内链地址，以/开头'))
    // }else{
    //   callback()
    // }
  } else {
    callback()
  }
}

const emit = defineEmits(["change", "refresh"])

const open = (item, disabled = false) => {
  dialogVisible.value = true
  nextTick(() => {
    if (item?.id) {
      form.value = {
        ...item,
        appTypeArr: item.appType.split(","),
        scopeArr: item.scope.split(","),
      }
      if (form.value.iconPath.includes(".")) {
        customize.value = true
      } else {
        customize.value = false
      }
    }
  })

  formDisabled.value = disabled
  title.value = disabled ? "查看" : item?.id ? "编辑" : "新增"
}

//获取上传后图片url
const getUrl = (res) => {
  form.value.iconPath = res
  customize.value = true
}

// 取消
const handleClose = () => {
  proxy.resetForm("formRef")
  form.value = { ..._form }
  dialogVisible.value = false
}

// 确定
const submitForm = (type = "save") => {
  proxy.$refs["formRef"].validate(async (valid) => {
    if (valid) {
      loading.value = true
      let actionUrl = form.value.id ? updateIndexCfgInfoAPI : addIndexCfgInfoAPI
      form.value.iconPath = form.value.iconPath.split("?")[0]
      form.value.appType = form.value.appTypeArr.join(",")
      form.value.scope = form.value.scopeArr.join(",")
      actionUrl(form.value)
          .then((res) => {
            if (+res.code === 200) {
              proxy.$modal.msgSuccess(form.value.id ? "修改成功" : "新增成功")
              if (type === "add") {
                proxy.resetForm("formRef")
                emit("change")
                title.value = "新增"
              } else {
                handleClose()
                form.value.id ? emit("change") : emit("refresh")
              }
            }
          })
          .finally(() => {
            loading.value = false
          })
    }
  })
}

const changeAppType = (val) => {
  if (val.length === 1) {
    form.value.programUrl = val.includes("0") ? "" : form.value.programUrl
    form.value.pcUrl = val.includes("1") ? "" : form.value.pcUrl
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.box-upload {
  display: flex;
}
</style>
