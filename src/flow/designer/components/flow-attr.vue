<template>
  <div>
    <el-drawer
      v-model="data.attrConfigVisible"
      class="flow-attr-drawer"
      direction="rtl"
      append-to-body
      :size="630"
    >
      <template #header="{ close, titleId, titleClass }">
        <el-form
          label-position="left"
          class="flow-attr flow-config-attr"
          label-width="140px"
        >
          属性配置(注:可双击节点设置)
          <el-form-item
            v-if="
              !validateNull(props.currSelect.attributes) &&
              (props.currSelect.attributes.attrs.cdata.type === CommonNodeType.SERIAL ||
                props.currSelect.attributes.attrs.cdata.type === CommonNodeType.PARALLEL)
            "
            label="同步其他节点配置"
            style="margin-top: 15px !important"
          >
            <el-select
              v-model="props.currSelect.attributes.attrs.cdata.attrs.syncFlowNodeId"
              class="input-attr"
              filterable
              clearable
              @change="methods.changeSyncFlowNodeId"
            >
              <el-option
                v-for="(item, index) in data.syncFlowNodeIds"
                :key="item.id"
                :label="item.nodeName"
                :value="item.id"
              />
            </el-select>
            <el-tooltip placement="top">
              <template #content>支持一键同步其他节点的配置，避免重复配置</template>
              <el-icon style="margin-left: 10px">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </el-form-item>
        </el-form>
      </template>
      <el-tabs v-model="data.activeKey" class="flow-attr">
        <el-tab-pane
          v-if="
            data.activeKey === 'flow-attr' ||
            data.activeKey === 'flow-clazz-attr' ||
            data.activeKey === 'flow-http-attr'
          "
          name="flow-attr"
        >
          <template #label>
            <div>
              <el-icon style="vertical-align: middle; margin-right: 3px">
                <Tools />
              </el-icon>
              <span style="vertical-align: middle">基础属性</span>
            </div>
          </template>
          <el-form label-position="left" class="flow-config-attr" label-width="140px">
            <el-form-item v-if="!props.currFlowForm" label="关联表单">
              <el-select
                v-model="props.flowData.attrs.formId"
                clearable
                filterable
                placeholder="可选择关联表单"
              >
                <el-option
                  v-for="(item, index) in dicData.formId"
                  :key="index"
                  :label="item.formName + ' V' + item.version"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-col :span="19" class="mb-1">
              <el-form-item v-if="!validateRunFlow(props)" label="已有流程">
                <div style="display: flex">
                  <el-select
                    v-model="data.defFlowId"
                    clearable
                    filterable
                    placeholder="可选择已有流程"
                  >
                    <el-option
                      v-for="(item, index) in dicData.defFlows.filter((f) => !f.formId)"
                      :key="index"
                      :label="item.flowKey + ' V' + item.version"
                      :value="item.id"
                    />
                  </el-select>
                  <el-button
                    type="primary"
                    size="small"
                    round
                    style="margin-left: 10px"
                    @click="methods.loadExistFlow"
                  >
                    加载模板
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-form-item label="替换节点页面">
              <el-tooltip
                content="全局替换所有节点中该旧的页面为新的页面"
                placement="top"
              >
                <el-select
                  v-model="data.oldFormId"
                  placeholder="请选择旧的页面"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="(item, index) in dicData.formId"
                    :key="index"
                    :label="item.formName + ' V' + item.version"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-tooltip>
              <el-select
                v-model="data.newFormId"
                v-if="data.oldFormId"
                placeholder="请选择新的页面"
                clearable
                filterable
              >
                <el-option
                  v-for="(item, index) in dicData.formId.filter(
                    (f) => f.id !== data.oldFormId
                  )"
                  :key="index"
                  :label="item.formName + ' V' + item.version"
                  :value="item.id"
                ></el-option>
              </el-select>
              <el-button
                type="primary"
                size="small"
                round
                style="margin-left: 10px"
                @click="methods.replaceFlowNodeFormId"
              >
                确认替换
              </el-button>
            </el-form-item>
            <el-form-item label="当前流程ID">
              <el-input v-model="props.flowData.attrs.id" class="input-attr" disabled />
            </el-form-item>
            <el-form-item
              label="流程名称"
              :rules="{ required: true, message: '流程名称不能为空', trigger: 'blur' }"
            >
              <el-input
                v-model="props.flowData.attrs.flowName"
                class="input-attr"
                clearable
              />
            </el-form-item>
            <el-form-item
              label="流程KEY"
              :rules="{ required: true, message: '流程KEY不能为空', trigger: 'blur' }"
            >
              <el-input
                v-model="props.flowData.attrs.flowKey"
                class="input-attr"
                placeholder="一般为工单实体类名称"
                clearable
              />
            </el-form-item>
            <el-form-item
              label="分组名称"
              :rules="{ required: true, message: '分组名称不能为空', trigger: 'blur' }"
            >
              <el-select
                v-model="props.flowData.attrs.groupName"
                placeholder="一般与表单分组名称相同"
                clearable
                filterable
                allowCreate
                defaultFirstOption
              >
                <el-option
                  v-for="(item, index) in dicData.groupName"
                  :key="index"
                  :label="item.groupName"
                  :value="item.groupName"
                ></el-option>
              </el-select>
              <el-button
                type="primary"
                size="small"
                round
                style="margin-left: 10px"
                @click="methods.handleAddGroupName"
              >
                新增分组
              </el-button>
            </el-form-item>
            <el-form-item label="流程备注">
              <el-input
                v-model="props.flowData.attrs.remark"
                class="input-attr"
                type="textarea"
              />
            </el-form-item>
            <el-form-item label="流程状态">
              <el-radio-group v-model="props.flowData.attrs.status" disabled>
                <el-radio
                  v-for="(item, index) in DIC_PROP.TEMP_STATUS"
                  :key="index"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <!--                        <el-form-item label="流程排序">
                            <el-input-number class="input-attr" v-model="props.flowData.attrs.sort"
                                             :min="1"></el-input-number>
                        </el-form-item>-->
            <el-form-item label="当前流程版本">
              <el-input-number
                v-model="props.flowData.attrs.version"
                class="input-attr"
                disabled
              />
            </el-form-item>
            <el-form-item label="发起后配置独立">
              <el-radio-group v-model="props.flowData.attrs.isIndependent">
                <el-radio
                  v-for="(item, index) in DIC_PROP.YES_OR_NO"
                  :key="index"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
              <el-tooltip
                placement="top"
                content="表示每个发起后的流程实例是否有单独的配置数据，即不会受流程模板配置再次修改的影响"
              >
                <el-icon style="margin-left: 10px">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane
          v-if="
            data.activeKey === 'flow-attr' ||
            data.activeKey === 'flow-clazz-attr' ||
            data.activeKey === 'flow-http-attr'
          "
          name="flow-clazz-attr"
        >
          <template #label>
            <div>
              <el-icon style="vertical-align: middle; margin-right: 3px">
                <Operation />
              </el-icon>
              <span style="vertical-align: middle">全局监听事件</span>
            </div>
          </template>
          <flow-clazz
            :curr-select="props.currSelect"
            :flow-data="props.flowData"
            :clazz-placeholder="'SpringBean名称：GlobalFlowListener实现类'"
            :curr-flow-form="props.currFlowForm"
            :flow-methods="DIC_PROP.FLOW_METHODS"
            :add-type="'1'"
          />
        </el-tab-pane>

        <el-tab-pane
          v-if="
            data.activeKey === 'flow-attr' ||
            data.activeKey === 'flow-clazz-attr' ||
            data.activeKey === 'flow-http-attr'
          "
          name="flow-http-attr"
        >
          <template #label>
            <div>
              <el-icon style="vertical-align: middle; margin-right: 3px">
                <Operation />
              </el-icon>
              <span style="vertical-align: middle">关联表单Http接口</span>
            </div>
          </template>
          <flow-form-http
            :flow-data="props.flowData"
            :curr-flow-form="props.currFlowForm"
            :attrConfigVisible="data.attrConfigVisible"
          />
        </el-tab-pane>

        <el-tab-pane
          v-if="
            !validateNull(props.currSelect.attributes) &&
            (data.activeKey === 'node-attr' ||
              data.activeKey === 'node-clazz-attr' ||
              data.activeKey === 'node-job-attr' ||
              data.activeKey === 'form-attr') &&
            props.currSelect.attributes.attrs.cdata.type !== HighNodeType.JOB
          "
          name="node-attr"
        >
          <template #label>
            <div>
              <el-icon style="vertical-align: middle; margin-right: 3px">
                <Operation />
              </el-icon>
              <span style="vertical-align: middle">节点属性</span>
            </div>
          </template>
          <el-form
            v-if="
              props.currSelect.attributes.attrs.cdata.type &&
              props.currSelect.attributes.attrs.cdata.type !== CommonNodeType.LINK &&
              props.currSelect.attributes.attrs.cdata.type !== HighNodeType.JOB
            "
            label-position="left"
            class="flow-config-attr"
            label-width="160px"
          >
            <el-form-item
              :label="'节点类型' + (methods.canChangeFlowNodeName() ? '(可修改)' : '')"
            >
              <el-select
                v-if="methods.canChangeFlowNodeName()"
                v-model="props.currSelect.attributes.attrs.cdata.type"
                class="input-attr"
                @change="methods.changeFlowNodeName"
              >
                <el-option
                  v-for="(item, index) in props.currSelect.attributes.attrs.cdata.attrs
                    .isGateway === '1'
                    ? highNodes.slice(2, 4)
                    : commonNodes.slice(1, 3)"
                  :key="index"
                  :label="item.nodeName"
                  :value="item.type"
                />
              </el-select>

              <el-tag v-else>
                {{ methods.nodeLabel(props.currSelect) }}
              </el-tag>
            </el-form-item>
            <el-form-item label="节点ID">
              <el-input v-model="props.currSelect.id" class="input-attr" disabled />
            </el-form-item>
            <el-form-item label="节点名称">
              <el-input
                v-if="
                  props.currSelect.attributes.attrs.cdata.type === CommonNodeType.START ||
                  props.currSelect.attributes.attrs.cdata.type === CommonNodeType.END
                "
                v-model="props.currSelect.attributes.attrs.label.text"
                class="input-attr"
                placeholder="请输入节点名称"
                @change="methods.handleNodeOrJobName"
                clearable
              />
              <el-input
                v-else
                v-model="data.nodeOrJobName"
                class="input-attr"
                clearable
                placeholder="请输入节点名称"
                @change="methods.handleNodeOrJobName"
              />
            </el-form-item>

            <el-form-item label="节点路由(自动跳转)">
              <el-tooltip
                content="可根据接口返回值 或 表单数据的字段值，配置对应的路由动作"
                placement="top"
              >
                <el-input
                  class="input-attr"
                  v-if="props.currSelect.attributes.attrs.cdata.attrs.routeKeyValName"
                  placeholder="请点击 ( 路由规则 ) 按钮"
                  v-model="props.currSelect.attributes.attrs.cdata.attrs.routeKeyValName"
                  readonly
                  clearable
                >
                  <template #suffix>
                    <el-icon class="el-input__icon" @click="methods.clearRouteKeyValName">
                      <Delete />
                    </el-icon>
                  </template>
                </el-input>

                <el-input
                  v-if="!props.currSelect.attributes.attrs.cdata.attrs.routeKeyValName"
                  placeholder="请点击 ( 路由规则 ) 按钮"
                  v-model="props.currSelect.attributes.attrs.cdata.attrs.routeKeyVal"
                  class="input-attr"
                  readonly
                  clearable
                >
                  <template #suffix>
                    <el-icon
                      v-if="props.currSelect.attributes.attrs.cdata.attrs.routeKeyVal"
                      class="el-input__icon"
                      @click="methods.clearRouteKeyValName"
                    >
                      <Delete />
                    </el-icon>
                  </template>
                </el-input>
              </el-tooltip>

              <el-tooltip content="点击可选择更多节点路由的方式" placement="bottom">
                <el-button
                  type="primary"
                  size="small"
                  round
                  style="margin-left: 10px"
                  @click="methods.openFlowRoutes(true)"
                >
                  路由规则
                </el-button>
              </el-tooltip>
            </el-form-item>

            <template v-if="props.currSelect.attributes.attrs.cdata.defJob">
              <template
                v-if="
                  props.currSelect.attributes.attrs.cdata.type ===
                    CommonNodeType.SERIAL ||
                  props.currSelect.attributes.attrs.cdata.type === CommonNodeType.PARALLEL
                "
              >
                <el-form-item label="多节点审批方式">
                  <el-tooltip
                    placement="top"
                    content="当下一步同时流转多个节点时，可设置这多个节点的审批方式"
                  >
                    <el-select
                      class="input-attr"
                      v-model="
                        props.currSelect.attributes.attrs.cdata.attrs.nodeApproveMethod
                      "
                    >
                      <el-option
                        v-for="(item, index) in DIC_PROP.NODE_APPROVE_METHOD"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-tooltip>

                  <el-tooltip
                    placement="top"
                    content="当为依次审批时，审批顺序由节点的排序值决定"
                  >
                    <el-icon style="margin-left: 10px"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </el-form-item>

                <el-form-item label="多人审批方式">
                  <el-tooltip
                    placement="top"
                    content="当前节点若存在多个参与者时，可设置这多个参与者的审批方式"
                  >
                    <el-select
                      class="input-attr"
                      v-model="
                        props.currSelect.attributes.attrs.cdata.attrs.approveMethod
                      "
                    >
                      <el-option
                        v-for="(item, index) in DIC_PROP.APPROVE_METHOD"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-tooltip>

                  <el-tooltip
                    placement="top"
                    content="当为依次审批时，审批顺序由任务的排序值决定"
                  >
                    <el-icon style="margin-left: 10px"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </el-form-item>
              </template>

              <el-form-item
                label="票签完成比率%"
                v-if="
                  props.currSelect.attributes.attrs.cdata.attrs.approveMethod ===
                  DIC_PROP.APPROVE_METHOD[3].value
                "
              >
                <el-input-number
                  class="input-attr"
                  :min="1"
                  :max="100"
                  v-model="props.currSelect.attributes.attrs.cdata.attrs.ticketCompRate"
                ></el-input-number>
              </el-form-item>

              <template
                v-if="
                  methods.validateNode(props.currSelect) &&
                  props.currSelect.attributes.attrs.cdata.type !== HighNodeType.VIRTUAL
                "
              >
                <el-form-item label="审批时的按钮">
                  <el-select
                    v-model="props.currSelect.attributes.attrs.cdata.attrs.jobBtns"
                    class="input-attr"
                    collapse-tags
                    collapse-tags-tooltip
                    multiple
                    clearable
                    filterable
                    placeholder="审批时的按钮，若为空则默认审批按钮"
                  >
                    <template v-for="(item, index) in DIC_PROP.JOB_BTNS" :key="index">
                      <el-option :label="item.label" :value="item.value" />
                    </template>
                  </el-select>
                  <el-tooltip
                    placement="top"
                    content="审批时的按钮，默认显示所有审批按钮"
                  >
                    <el-icon style="margin-left: 10px">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </el-form-item>
              </template>
            </template>

            <el-divider v-if="!data.moreNode">
              <el-button
                type="primary"
                link
                @click="methods.showMoreSetting('1')"
                style="margin-left: 10px; font-size: 14px"
              >
                {{ data.moreNode ? "隐藏更多" : "更多配置" }}
              </el-button>
            </el-divider>

            <template v-if="data.moreNode">
              <template v-if="methods.validateNode(props.currSelect)">
                <template v-if="props.currSelect.attributes.attrs.cdata.defJob">
                  <el-form-item label="PC待办页面">
                    <el-select
                      v-model="props.currSelect.attributes.attrs.cdata.attrs.pcTodoUrl"
                      class="input-attr"
                      collapse-tags
                      collapse-tags-tooltip
                      multiple
                      clearable
                      filterable
                      placeholder="请选择PC待办页面"
                    >
                      <el-option
                        v-for="(item, index) in dicData.formId"
                        :key="index"
                        :label="item.formName + ' V' + item.version"
                        :value="item.id"
                      />
                    </el-select>
                    <el-tooltip placement="top">
                      <template #content>
                        1、可以是当前设计的主表单（默认全部可编辑），或者其他表单，或者系统表单。若下拉选项没有期望选择的表单，则可先去【表单设计】新增
                        <br />
                        2、若此属性值为空则默认为当前设计的主表单、查看审批过程、查看流程图
                      </template>
                      <el-icon style="margin-left: 10px">
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </el-form-item>
                </template>
                <el-form-item
                  v-if="
                    props.currSelect.attributes.attrs.cdata.type !== HighNodeType.VIRTUAL
                  "
                  label="节点关联子流程"
                >
                  <el-select
                    v-model="props.currSelect.attributes.attrs.cdata.attrs.subDefFlowId"
                    class="input-attr"
                    filterable
                    clearable
                    @change="methods.handleSubDefFlowId"
                  >
                    <el-option
                      v-for="(item, index) in dicData.subDefFlows"
                      :key="index"
                      :label="item.flowName + ' ' + item.flowKey"
                      :value="item.id"
                    />
                  </el-select>
                  <el-tooltip placement="top">
                    <template #content>
                      当前节点审批完成时, 会自动发起子流程实例暂停父流程当前分支,
                      待子流程完成后重启父流程当前分支
                    </template>
                    <el-icon style="margin-left: 10px">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>

                  <el-tooltip content="父子流程传参与回参默认为空" placement="bottom">
                    <el-button
                      type="primary"
                      size="small"
                      style="margin-left: 10px"
                      @click="methods.openFlowSubParams(true)"
                    >
                      参数
                    </el-button>
                  </el-tooltip>
                </el-form-item>

                <el-form-item label="审批结果通知他人">
                  <!-- <el-select
                    v-model="props.currSelect.attributes.attrs.cdata.attrs.carbonCopy"
                    class="input-attr"
                    multiple
                    clearable
                    filterable
                  >
                    <template v-for="(item, index) in dicData.users" :key="index">
                      <el-option :label="item.username" :value="item.userId" />
                    </template>
                  </el-select> -->
                  <span v-if="data.showCarbonCopyUsers.length" class="m-r-10px">
                    {{ data.showCarbonCopyUsers.map((item) => item.nickName).join("，") }}
                  </span>
                  <el-button type="primary" @click="methods.selectCarbonCopyUsers">
                    选择人员
                  </el-button>
                  <el-tooltip
                    placement="top"
                    content="当前节点审批完成时，将审批结果通知给他人"
                  >
                    <el-icon style="margin-left: 10px">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </el-form-item>
              </template>

              <template
                v-if="
                  props.currSelect.attributes.attrs.cdata.type === HighNodeType.VIRTUAL
                "
              >
                <el-form-item label="被驳回后再次提交时">
                  <el-switch
                    v-model="props.currSelect.attributes.attrs.cdata.attrs.rejectType"
                    disabled
                    active-value="1"
                    inactive-value="0"
                    inactive-text="依次返回"
                    active-text="直接返回"
                  />
                  <el-tooltip
                    placement="top"
                    content="表示被驳回后的节点再次审批时，需要再次经历途中节点【依次返回】到原驳回的节点，还是【直接返回】到原驳回的节点"
                  >
                    <el-icon style="margin-left: 10px">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </el-form-item>
              </template>

              <template v-if="props.currSelect.attributes.attrs.cdata.defJob">
                <template
                  v-if="
                    props.currSelect.attributes.attrs.cdata.type ===
                      CommonNodeType.START ||
                    props.currSelect.attributes.attrs.cdata.type ===
                      CommonNodeType.SERIAL ||
                    props.currSelect.attributes.attrs.cdata.type ===
                      CommonNodeType.PARALLEL
                  "
                >
                  <el-form-item label="被驳回后再次提交时">
                    <el-switch
                      v-model="props.currSelect.attributes.attrs.cdata.attrs.rejectType"
                      active-value="1"
                      inactive-value="0"
                      inactive-text="依次返回"
                      active-text="直接返回"
                    />
                    <el-tooltip
                      placement="top"
                      content="表示被驳回后的节点再次审批时，需要再次经历途中节点【依次返回】到原驳回的节点，还是【直接返回】到原驳回的节点"
                    >
                      <el-icon style="margin-left: 10px">
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </el-form-item>
                </template>
              </template>
              <template
                v-if="
                  props.currSelect.attributes.attrs.cdata.type ===
                    CommonNodeType.SERIAL ||
                  props.currSelect.attributes.attrs.cdata.type === CommonNodeType.PARALLEL
                "
              >
                <el-form-item label="当流程条件不满足时">
                  <el-switch
                    v-model="props.currSelect.attributes.attrs.cdata.attrs.isContinue"
                    active-value="1"
                    inactive-value="0"
                    inactive-text="暂停"
                    active-text="继续下一节点"
                  />
                  <el-tooltip
                    placement="top"
                    content="是否继续下一节点表示，如果继续则当下一节点条件不满足时，会自动继续找下下一节点，以此类推"
                  >
                    <el-icon style="margin-left: 10px">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </el-form-item>
                <template v-if="props.currSelect.attributes.attrs.cdata.defJob">
                  <el-form-item label="相同审批人自动通过">
                    <el-switch
                      v-model="props.currSelect.attributes.attrs.cdata.attrs.isPassSame"
                      active-value="1"
                      inactive-value="0"
                      inactive-text="否"
                      active-text="是"
                    />
                  </el-form-item>
                  <el-form-item label="是否自动审批">
                    <el-switch
                      v-model="props.currSelect.attributes.attrs.cdata.attrs.isAutoAudit"
                      active-value="1"
                      inactive-value="0"
                      inactive-text="否"
                      active-text="是"
                      @change="methods.hideJobFormPerms"
                    />
                  </el-form-item>
                  <el-form-item label="自动流转下一节点">
                    <el-tooltip
                      placement="top"
                      content="当选择否时，该节点之后的节点需要程序去触发"
                    >
                      <el-switch
                        v-model="props.currSelect.attributes.attrs.cdata.attrs.isAutoNext"
                        active-value="1"
                        inactive-value="0"
                        inactive-text="否"
                        active-text="是"
                      />
                    </el-tooltip>
                  </el-form-item>
                </template>
              </template>

              <template
                v-if="props.currSelect.attributes.attrs.cdata.type === CommonNodeType.END"
              >
                <el-form-item label="是否自动结束">
                  <el-switch
                    v-model="props.currSelect.attributes.attrs.cdata.attrs.isAutoAudit"
                    active-value="1"
                    inactive-value="0"
                    inactive-text="否"
                    active-text="是"
                    @change="methods.hideJobFormPerms"
                  />
                </el-form-item>
              </template>

              <template
                v-if="
                  props.currSelect.attributes.attrs.cdata.type === HighNodeType.CHILD_FLOW
                "
              >
                <el-form-item label="子流程KEY">
                  <el-input
                    v-model="props.currSelect.attributes.attrs.cdata.attrs.childFlowKey"
                    class="input-attr"
                    placeholder="请输入子流程KEY"
                  />
                </el-form-item>
              </template>
              <template
                v-if="
                  props.currSelect.attributes.attrs.cdata.type === LaneNodeType.X_LANE ||
                  props.currSelect.attributes.attrs.cdata.type === LaneNodeType.Y_LANE
                "
              />

              <template
                v-if="
                  methods.validateNode(props.currSelect) &&
                  props.currSelect.attributes.attrs.cdata.defJob
                "
              >
                <el-form-item v-if="props.currSelect.attributes.ports" label="节点描述">
                  <el-input
                    v-model="props.currSelect.attributes.attrs.cdata.attrs.description"
                    class="input-attr"
                    placeholder="请输入节点描述"
                    type="textarea"
                    @change="methods.handleNodeDescription"
                  />
                </el-form-item>

                <el-form-item label="节点时限(分钟)">
                  <el-input-number
                    v-model="props.currSelect.attributes.attrs.cdata.attrs.timeout"
                    class="input-attr"
                    placeholder="小于0则不限制"
                  />
                </el-form-item>
                <el-form-item label="节点审批顺序">
                  <el-tooltip
                    placement="top"
                    content="当【多节点审批方式】为依次审批时，审批顺序由节点的排序值决定"
                  >
                    <el-input-number
                      class="input-attr"
                      v-model="props.currSelect.attributes.attrs.cdata.attrs.sort"
                      :min="1"
                    ></el-input-number>
                  </el-tooltip>
                </el-form-item>
              </template>
            </template>
          </el-form>
        </el-tab-pane>

        <el-tab-pane
          v-if="
            !validateNull(props.currSelect.attributes) &&
            methods.validateNodeJob(props.currSelect) &&
            !data.hideJobFormPerm
          "
          name="node-job-attr"
        >
          <template #label>
            <div>
              <el-icon style="vertical-align: middle; margin-right: 3px">
                <User />
              </el-icon>
              <span
                v-if="
                  !validateNull(props.currSelect.attributes) &&
                  props.currSelect.attributes.attrs.cdata.type === HighNodeType.JOB
                "
                style="vertical-align: middle"
              >
                任务属性
              </span>
              <span
                v-if="
                  !validateNull(props.currSelect.attributes) &&
                  props.currSelect.attributes.attrs.cdata.type !== HighNodeType.JOB
                "
                style="vertical-align: middle"
              >
                默认任务属性
              </span>
            </div>
          </template>
          <el-form
            v-if="methods.validateNodeJob(props.currSelect)"
            label-position="left"
            class="flow-config-attr"
            label-width="170px"
          >
            <div
              v-if="props.currSelect.attributes.attrs.cdata.type !== HighNodeType.JOB"
              style="margin: 5px 20px"
            >
              <span style="color: red; font-size: 14px">
                注:当前节点没有单独配置【节点任务】时, 作为节点的默认任务属性配置
              </span>
            </div>

            <div
              v-else-if="
                props.currSelect.attributes.attrs.cdata.type === HighNodeType.JOB
              "
              style="margin: 5px 20px"
            >
              <span style="color: red; font-size: 14px">
                注:当前节点单独配置【节点任务】时, 则节点上配置的默认任务属性失效
              </span>
            </div>

            <el-divider>审批人设置说明</el-divider>

            <div style="margin: 5px 20px">
              1、【审批人】可选人员或角色
              <!-- <br />
              2、【固定参与者】、【参与者取值来源】、【参与者KEY】即代码中指定参与者 -->
            </div>

            <el-form-item label="任务名称">
              <el-input
                v-model="props.currSelect.attributes.attrs.cdata.defJob.jobName"
                class="input-attr"
                placeholder="请输入任务名称"
                @change="methods.handleDefaultJobName"
                clearable
              />
            </el-form-item>
            <el-form-item label="固定审批人(可多选)">
              <el-tooltip
                content="当节点属性【多人审批方式】为依次审批时，审批顺序由选择的顺序决定"
                placement="bottom"
              >
                <el-button
                  size="small"
                  type="primary"
                  class="min-h-30px"
                  @click="methods.onUserRolePicker"
                >
                  {{ methods.validateNullRoleUserId() ? "选择审批人" : "重选" }}
                </el-button>
              </el-tooltip>

              <el-button
                v-if="!methods.validateNullRoleUserId()"
                size="small"
                type="primary"
                @click="methods.clearRoleUserIdValType(true)"
                class="mr-10px min-h-30px"
              >
                删除
              </el-button>

              <ConvertPickerName
                v-if="!methods.validateNullRoleUserId()"
                :value="props.currSelect.attributes.attrs.cdata.defJob.roleUserId"
              />
            </el-form-item>
            <el-form-item label="参与者取值来源">
              <el-input
                v-if="props.currSelect.attributes.attrs.cdata.defJob.userKeyValName"
                v-model="props.currSelect.attributes.attrs.cdata.defJob.userKeyValName"
                class="input-attr"
                placeholder="请点击 ( 人员规则 ) 按钮"
                readonly
                clearable
              >
                <template #suffix>
                  <el-icon class="el-input__icon" @click="methods.clearUserKeyValName">
                    <Delete />
                  </el-icon>
                </template>
              </el-input>

              <el-input
                v-if="!props.currSelect.attributes.attrs.cdata.defJob.userKeyValName"
                v-model="props.currSelect.attributes.attrs.cdata.defJob.userKeyVal"
                placeholder="请点击 ( 人员规则 ) 按钮"
                class="input-attr"
                readonly
                clearable
              >
                <template #suffix>
                  <el-icon
                    v-if="props.currSelect.attributes.attrs.cdata.defJob.userKeyVal"
                    class="el-input__icon"
                    @click="methods.clearUserKeyValName"
                  >
                    <Delete />
                  </el-icon>
                </template>
              </el-input>
              <el-tooltip content="点击可选择更多分配参与者的方式" placement="bottom">
                <el-button
                  type="primary"
                  size="small"
                  class="min-h-30px m-l-10px"
                  @click="methods.openFlowMethods(true)"
                >
                  人员规则
                </el-button>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="参与者KEY">
              <el-select
                v-model="props.currSelect.attributes.attrs.cdata.defJob.userKey"
                class="input-attr"
                filterable
                clearable
                @change="methods.validateUserKeyChange"
              >
                <el-option
                  v-for="(item, index) in dicData.userKeys"
                  :key="item.userKey"
                  :label="item.userKey"
                  :value="item.userKey"
                />
              </el-select>

              <el-button
                type="primary"
                size="small"
                class="m-l-10px min-h-30px"
                @click="methods.handleAddUserKey"
              >
                新增
              </el-button>

              <el-tooltip placement="top">
                <template #content>
                  可作为某个【节点任务】或【节点任务的参与者】标识，若作为【参与者】标识时需后端代码中指定参与者。KEY需含下划线
                </template>
                <el-icon style="margin-left: 10px">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </el-form-item>
            <el-divider v-if="!data.moreJob">
              <el-button
                type="primary"
                link
                @click="methods.showMoreSetting('2')"
                style="font-size: 14px"
              >
                {{ data.moreJob ? "隐藏更多" : "更多配置" }}
              </el-button>
            </el-divider>
            <template v-if="data.moreJob">
              <el-form-item label="任务类型">
                <el-select
                  v-model="props.currSelect.attributes.attrs.cdata.defJob.belongType"
                  class="input-attr"
                  clearable
                >
                  <el-option
                    v-for="(item, index) in DIC_PROP.BELONG_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-tooltip placement="top">
                  <template #content>
                    抄送任务需要参与者审批，传阅任务不需要参与者审批只参与阅览
                  </template>
                  <el-icon style="margin-left: 10px">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="待分配参与者节点">
                <el-select
                  v-model="props.currSelect.attributes.attrs.cdata.defJob.distFlowNodeId"
                  class="input-attr"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="(item, index) in data.flowNodeIds"
                    :key="item.id"
                    :label="item.nodeName"
                    :value="item.id"
                  />
                </el-select>
                <el-tooltip placement="top">
                  <template #content>
                    在当前节点为其他【待分配参与者节点】通过前端《分配参与者》页面分配参与者时配置(
                    可选配置 )
                  </template>
                  <el-icon style="margin-left: 10px">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="被分配后立即运行">
                <el-switch
                  v-model="props.currSelect.attributes.attrs.cdata.defJob.isNowRun"
                  active-value="1"
                  inactive-value="0"
                  inactive-text="否"
                  active-text="是"
                />
                <el-tooltip
                  content="一般与【待分配参与者节点】同时配置，表示待分配参与者节点被分配参与者时，待分配参与者节点的任务是 ( 立即运行 ) 还是 ( 等引擎流转到时才运行 ) "
                  placement="top"
                >
                  <el-icon style="margin-left: 10px">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="被驳回是否可跳过">
                <el-switch
                  v-model="props.currSelect.attributes.attrs.cdata.defJob.isSkipRejected"
                  active-value="1"
                  inactive-value="0"
                  inactive-text="否"
                  active-text="是"
                />
                <el-tooltip
                  content="如果选择是，则表示被驳回后不会开启这个任务"
                  placement="top"
                >
                  <el-icon style="margin-left: 10px">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="任务时限(分钟)">
                <el-input-number
                  v-model="props.currSelect.attributes.attrs.cdata.defJob.timeout"
                  class="input-attr"
                  placeholder="小于0则不限制"
                />
              </el-form-item>
              <el-form-item label="任务审批顺序">
                <el-tooltip
                  placement="top"
                  content="当【多人审批方式】为依次审批时，审批顺序由任务的排序值决定"
                >
                  <el-input-number
                    class="input-attr"
                    v-model="props.currSelect.attributes.attrs.cdata.defJob.sort"
                    :min="1"
                  ></el-input-number>
                </el-tooltip>
              </el-form-item>
            </template>
          </el-form>
        </el-tab-pane>

        <el-tab-pane
          v-if="
            !validateNull(props.currSelect.attributes) && data.activeKey === 'link-attr'
          "
          name="link-attr"
        >
          <template #label>
            <div>
              <el-icon style="vertical-align: middle; margin-right: 3px">
                <Share />
              </el-icon>
              <span style="vertical-align: middle">连线条件</span>
            </div>
          </template>
          <template
            v-if="props.currSelect.attributes.attrs.cdata.type === CommonNodeType.LINK"
          >
            <el-form label-position="left" class="flow-config-attr" label-width="150px">
              <el-form-item label="连线ID">
                <el-input v-model="props.currSelect.id" class="input-attr" disabled />
              </el-form-item>
              <el-form-item label="源节点" style="display: none">
                <el-input
                  v-model="props.currSelect.attributes.source.id"
                  class="input-attr"
                  disabled
                />
              </el-form-item>
              <el-form-item label="目标节点" style="display: none">
                <el-input
                  v-model="props.currSelect.attributes.target.id"
                  class="input-attr"
                  disabled
                />
              </el-form-item>
              <el-form-item label="连线名称">
                <el-input
                  v-model="props.currSelect.attributes.labels[0].attrs.text.text"
                  class="input-attr"
                  @change="methods.linkLabelChange"
                />
              </el-form-item>
              <flow-con-rule
                :currSelect="props.currSelect"
                :currFlowForm="props.currFlowForm"
                :flowData="props.flowData"
                @hideAttrConfig="methods.hideAttrConfig"
                :attrConfigVisible="data.attrConfigVisible"
              ></flow-con-rule>
            </el-form>
          </template>
        </el-tab-pane>
        <el-tab-pane
          v-if="methods.validateNode(props.currSelect) && !data.hideJobFormPerm"
          name="form-attr"
        >
          <template #label>
            <div>
              <el-icon style="vertical-align: middle; margin-right: 3px">
                <View />
              </el-icon>
              <span style="vertical-align: middle">表单权限配置</span>
            </div>
          </template>
          <flow-form-perm
            ref="flowFormPerm"
            :flow-data="props.flowData"
            :form-ids="dicData.formId"
            :curr-flow-form="props.currFlowForm"
            :curr-select="props.currSelect"
          />
        </el-tab-pane>

        <el-tab-pane
          v-if="
            !validateNull(props.currSelect.attributes) &&
            (data.activeKey === 'node-attr' ||
              data.activeKey === 'node-clazz-attr' ||
              data.activeKey === 'node-job-attr' ||
              data.activeKey === 'form-attr') &&
            props.currSelect.attributes.attrs.cdata.type !== HighNodeType.JOB &&
            props.currSelect.attributes.attrs.cdata.type !== LaneNodeType.X_LANE &&
            props.currSelect.attributes.attrs.cdata.type !== LaneNodeType.Y_LANE
          "
          name="node-clazz-attr"
        >
          <template #label>
            <div>
              <el-icon style="vertical-align: middle; margin-right: 3px">
                <Operation />
              </el-icon>
              <span style="vertical-align: middle">节点监听事件</span>
            </div>
          </template>
          <flow-clazz
            :curr-select="props.currSelect"
            :flow-data="props.flowData"
            :clazz-placeholder="'SpringBean名称：NodeJobListener实现类'"
            :curr-flow-form="props.currFlowForm"
            :flow-methods="DIC_PROP.NODE_METHODS"
            :add-type="'0'"
          />
        </el-tab-pane>
      </el-tabs>
    </el-drawer>

    <user-role-picker
      ref="userRolePicker"
      :is-only-one="data.isOnlyOne"
      @on-select-items="methods.onSelectItems"
    />

    <el-drawer
      v-if="data.flowMethodVisible"
      v-model="data.flowMethodVisible"
      class="flow-attr-drawer"
      title="参与者取值来源"
      direction="rtl"
      append-to-body
      :size="630"
      @closed="methods.clearRoleUserIdValType(false)"
    >
      <flow-method
        ref="flowMethod"
        :curr-flow-form="props.currFlowForm"
        :curr-select="props.currSelect"
        :flow-data="props.flowData"
        @open-flow-methods="methods.openFlowMethods"
      />
    </el-drawer>

    <el-drawer
      class="flow-attr-drawer"
      title="节点路由规则"
      direction="rtl"
      append-to-body
      :size="630"
      v-if="data.nodeRouteVisible"
      v-model="data.nodeRouteVisible"
    >
      <flow-node-route
        ref="flowRoute"
        :currFlowForm="props.currFlowForm"
        :currSelect="props.currSelect"
        :flowData="props.flowData"
        @openFlowRoutes="methods.openFlowRoutes"
      ></flow-node-route>
    </el-drawer>

    <el-drawer
      v-if="data.flowSubParamVisible"
      v-model="data.flowSubParamVisible"
      class="flow-attr-drawer"
      title="父子流程传参与回参"
      direction="rtl"
      append-to-body
      :size="630"
    >
      <flow-sub-param
        ref="flowSubParam"
        :curr-flow-form="props.currFlowForm"
        :curr-select="props.currSelect"
        :flow-data="props.flowData"
      />
    </el-drawer>

    <SelectUserTreeModal ref="selectUserTreeRef" @confirm="methods.selectUsersConfirm" />
  </div>
</template>

<script setup lang="ts" name="FlowAttr">
import { CommonNodeType, HighNodeType, LaneNodeType } from "../config/type"
import { useMessage, useMessageBox } from "@/hooks/message"
import { validateNull } from "@/utils/validate"
import { onLoadDicUrl, onUpdateDicData } from "@/flow/components/convert-name/convert"
import { DIC_PROP } from "@/flow/support/dict-prop"
import { PROP_CONST } from "@/flow/support/prop-const"
import { setPropsDataValue, setPropsNullValue } from "../../support/common"
import { gateAttr, syncJobAttr, syncNodeAttr } from "../config/attr-config"
import { revParseUserKeyValName, validateNodeType } from "./index"
import { confirmCancelAndClose, replaceFlowNodeFormId } from "../../utils"
import { validateRunFlowId } from "../../index"
import { commonNodes, highNodes, laneNodes } from "../config/menu-config"
import * as orderVue from "@/api/order/order-key-vue"
import { deepClone } from "@/utils/index"
import { useDeptStore } from "@/store/modules/dept"
import { flatMap, find, map } from "lodash-es"

const { proxy } = getCurrentInstance()
// 引入组件
const UserRolePicker = defineAsyncComponent(
  () => import("../../components/user-role/picker2.vue")
)
const ConvertPickerName = defineAsyncComponent(
  () => import("@/flow/components/convert-picker-name/index.vue")
)
const FlowMethod = defineAsyncComponent(() => import("./flow-method.vue"))
const FlowNodeRoute = defineAsyncComponent(() => import("./flow-node-route.vue"))
const FlowSubParam = defineAsyncComponent(() => import("./flow-sub-param.vue"))
const FlowFormPerm = defineAsyncComponent(() => import("./flow-form-perm.vue"))
const FlowClazz = defineAsyncComponent(() => import("./flow-clazz.vue"))
const FlowConRule = defineAsyncComponent(() => import("./flow-con-rule.vue"))
const FlowFormHttp = defineAsyncComponent(() => import("./flow-form-http.vue"))

const SelectUserTreeModal = defineAsyncComponent(
  () => import("@/views/components/UserTreeTransfer/SelectUserTreeModal.vue")
)

const $emit = defineEmits(["initFlow", "showAttrConfig"])
const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null,
  },
  flowData: {
    type: Object,
    default: null,
  },
  currSelect: {
    type: Object,
    default: {},
  },
})
const data = reactive({
  activeKey: "flow-attr",
  attrConfigVisible: false,
  nodeRouteVisible: false,
  flowMethodVisible: false,
  flowSubParamVisible: false,
  hideJobFormPerm: false,
  defFlowId: null,
  nodeOrJobName: null,
  moreNode: false,
  moreJob: false,
  isOnlyOne: false,
  syncFlowNodeIds: [],
  flowNodeIds: [],
  oldFormId: null,
  newFormId: null,
  showCarbonCopyUsers: [],
})

// 定义字典
const dicData = reactive({ subDefFlows: [] })
const onLoad = onLoadDicUrl(
  { key: "defFlows" },
  { key: "userKeys" },
  { key: "formId" },
  { prefix: "defFlow", key: "groupName" },
  { key: "roles" },
  { key: "depts" }
)
const onUpdate = onUpdateDicData({ key: "userKeys" }, { key: "groupName" })
onMounted(async () => {
  await onLoad(dicData)

  methods.changeTabPane(props.currSelect)
})

const methods = {
  hideAttrConfig(bool, type) {
    $emit("showAttrConfig", bool, type)
  },
  validateCurrSelectAttrs(currSelect?) {
    if (!currSelect) currSelect = props.currSelect
    if (validateNull(currSelect.attributes)) return false
    return true
  },
  validateCurrSelectAttrsAttrs(currSelect?) {
    if (!currSelect) currSelect = props.currSelect
    if (validateNull(currSelect.attributes)) return false
    if (validateNull(currSelect.attributes.attrs.cdata.attrs)) return false
    return true
  },
  validateCurrSelectDefJob(currSelect?) {
    if (!currSelect) currSelect = props.currSelect
    if (validateNull(currSelect.attributes)) return false
    if (validateNull(currSelect.attributes.attrs.cdata.defJob)) return false
    return true
  },
  handleAddUserKey() {
    useMessageBox()
      .prompt("请输入新的参与者KEY")
      .then(({ value }) => {
        let validate = methods.validateUserKeyChange(value)
        if (!validate) {
          return
        }
        onUpdate(dicData, { userKeys: value }, "userKey")
      })
  },
  validateUserKeyChange(key) {
    if (!methods.validateCurrSelectAttrs()) {
      return false
    }
    props.currSelect.attributes.attrs.cdata.defJob.userKey = key
    if (key.indexOf("_") !== -1) {
      methods.handleUserKeyValType()
      return true
    }
    if (key) {
      proxy.$modal.msgWarning("KEY需包含下划线, 请重新输入")
    }
    props.currSelect.attributes.attrs.cdata.defJob.userKey = null

    methods.handleUserKeyValType()
    return false
  },
  handleSyncFlowNodeIds() {
    data.syncFlowNodeIds = []
    if (!methods.validateCurrSelectAttrsAttrs()) return
    props.currSelect.attributes.attrs.cdata.attrs.syncFlowNodeId = null
    let isGateway = props.currSelect.attributes.attrs.cdata.attrs.isGateway
    let models = window._jfGraph.getElements()
    if (validateNull(models)) return
    models.forEach((each) => {
      let cdata = each.attributes.attrs.cdata
      let b =
        cdata.type === CommonNodeType.SERIAL || cdata.type === CommonNodeType.PARALLEL
      if (b && props.currSelect.id !== each.id) {
        let id = each.id
        let nodeName = each.attributes.attrs.label.text + "（ID:" + id + "）"
        let isExist = false
        if (isGateway === "1") {
          if (cdata.attrs.isGateway === "1") isExist = true
        } else {
          if (cdata.attrs.isGateway !== "1") isExist = true
        }
        if (isExist) data.syncFlowNodeIds.push({ id, nodeName })
      }
    })
  },
  changeSyncFlowNodeId(id) {
    let models = window._jfGraph.getElements()
    if (validateNull(models) || !id) return
    let isGateway = props.currSelect.attributes.attrs.cdata.attrs.isGateway
    let cdata = models.find((f) => f.id === id).attributes.attrs.cdata
    let nodeAttrs = []
    if (isGateway !== "1") {
      nodeAttrs = Object.keys(syncNodeAttr)
      if (!validateNull(cdata.defJob)) {
        setPropsNullValue(
          props.currSelect.attributes.attrs.cdata.defJob,
          cdata.defJob,
          ...Object.keys(syncJobAttr)
        )
      }
    } else {
      nodeAttrs = Object.keys(gateAttr)
    }
    setPropsNullValue(
      props.currSelect.attributes.attrs.cdata.attrs,
      cdata.attrs,
      ...nodeAttrs
    )
    proxy.$modal.msgWarning("已同步其他节点的配置，请重新打开查看")
    methods.hideAttrConfig(false, "1")
  },
  handleFlowNodeIds() {
    data.flowNodeIds = []
    let models = window._jfGraph.getElements()
    if (validateNull(models)) return
    models.forEach((each) => {
      let cdata = each.attributes.attrs.cdata
      let b =
        cdata.type === CommonNodeType.SERIAL || cdata.type === CommonNodeType.PARALLEL
      if (b && cdata.attrs.isGateway !== "1" && props.currSelect.id !== each.id) {
        let id = each.id
        let nodeName = each.attributes.attrs.label.text
        data.flowNodeIds.push({ id, nodeName })
      }
    })
  },
  initSubDefFlows() {
    if (validateNull(dicData.defFlows)) return
    dicData.subDefFlows = dicData.defFlows.filter(
      (value, i, arr) =>
        arr.findIndex((x) => x.flowName === value.flowName) === i &&
        value.id !== props.flowData.attrs.id
    )
  },
  openFlowMethods(bool) {
    methods.clearRoleUserIdValType(false)
    data.flowMethodVisible = bool
  },
  openFlowRoutes(bool) {
    data.nodeRouteVisible = bool
  },
  clearRouteKeyValName() {
    props.currSelect.attributes.attrs.cdata.attrs.valType = null
    if (
      props.currSelect.attributes.attrs.cdata.attrs.routeKeyValName ===
      PROP_CONST.VAR_KEY_VAL.routeName
    ) {
      props.currSelect.attributes.attrs.cdata.attrs.condGroups = []
    }
    props.currSelect.attributes.attrs.cdata.attrs.routeKeyValName = null
    props.currSelect.attributes.attrs.cdata.attrs.routeKeyVal = null
  },
  openFlowSubParams(bool) {
    if (!props.currSelect.attributes.attrs.cdata.attrs.subDefFlowId) {
      proxy.$modal.msgWarning("请先选择【节点关联子流程】")
      return
    }
    data.flowSubParamVisible = bool
  },
  handleSubDefFlowId(subDefFlowId) {
    if (subDefFlowId) {
      return
    }
    props.currSelect.attributes.attrs.cdata.attrs.startSubFlow = null
    props.currSelect.attributes.attrs.cdata.attrs.restartSubFlow = null
    props.currSelect.attributes.attrs.cdata.attrs.backParFlow = null
    props.currSelect.attributes.attrs.cdata.attrs.subFlowParams = []
  },
  onSelectItems(items) {
    if (!methods.validateCurrSelectDefJob()) {
      return
    }
    props.currSelect.attributes.attrs.cdata.defJob.roleUserId = [...items].filter((i) => {
      delete i.id
      return i
    })
    console.log(props.currSelect.attributes.attrs.cdata.defJob.roleUserId)
    methods.clearUserKeyValName()
    props.currSelect.attributes.attrs.cdata.defJob.valType = DIC_PROP.VAL_TYPE[3].value
  },
  onUserRolePicker() {
    if (!methods.validateCurrSelectDefJob()) return
    let type = props.currSelect.attributes.attrs.cdata.type
    data.isOnlyOne =
      type !== CommonNodeType.SERIAL &&
      type !== CommonNodeType.PARALLEL &&
      type !== HighNodeType.JOB
    let items =
      props.currSelect.attributes.attrs.cdata.defJob.roleUserId?.map((i) => ({
        ...i,
        name: i.name ?? i.userRoleNameOrUserName,
      })) || []
    console.log(items)
    proxy.$refs.userRolePicker.onOpen(items)
  },
  hideJobFormPerms(val) {
    if (!methods.validateCurrSelectAttrs()) {
      return
    }
    if (validateNull(props.currSelect.attributes)) {
      return false
    }
    data.hideJobFormPerm = val === "1"
  },
  watchHideJobFormPerms(currSelect) {
    if (!methods.validateCurrSelectAttrs()) {
      return
    }
    let type = currSelect.attributes.attrs.cdata.type
    let attrs = currSelect.attributes.attrs.cdata.attrs
    let val = "0"
    if (
      attrs &&
      (type === CommonNodeType.SERIAL ||
        type === CommonNodeType.PARALLEL ||
        CommonNodeType.END)
    ) {
      val = attrs.isAutoAudit
    }
    methods.hideJobFormPerms(val)
  },
  validateNodeJob(currSelect) {
    if (!methods.validateCurrSelectAttrs()) {
      return false
    }
    return (
      (methods.validateNode(currSelect) && currSelect.attributes.attrs.cdata.defJob) ||
      currSelect.attributes.attrs.cdata.type === HighNodeType.JOB
    )
  },
  validateNode(currSelect) {
    return validateNodeType(currSelect, methods, true)
  },
  nodeLabel(currSelect) {
    let type = currSelect.attributes.attrs.cdata.type
    let isGateway = currSelect.attributes.attrs.cdata.attrs.isGateway
    let nodes = isGateway === "1" ? highNodes : commonNodes
    let node = nodes.find((f) => f.type === type)
    if (!node) node = highNodes.find((f) => f.type === type)
    if (!node) node = laneNodes.find((f) => f.type === type)
    return node.nodeName
  },
  canChangeFlowNodeName() {
    return (
      !validateNull(props.currSelect.attributes) &&
      (props.currSelect.attributes.attrs.cdata.type === CommonNodeType.SERIAL ||
        props.currSelect.attributes.attrs.cdata.type === CommonNodeType.PARALLEL)
    )
  },
  changeFlowNodeName(type) {
    props.currSelect.attributes.attrs.cdata.type = type
    let isGateway = props.currSelect.attributes.attrs.cdata.attrs.isGateway
    let nodes = isGateway === "1" ? highNodes : commonNodes
    let node = nodes.find((f) => f.type === type)
    if (props.flowData.attrs.isJobSeparated !== "1" && isGateway !== "1") {
      props.currSelect.attributes.attrs.icon.xlinkHref = node.icon
    } else {
      props.currSelect.attributes.attrs.image.xlinkHref = node.icon
    }
    let defaultNodeName = nodes.find((f) => f.nodeName === data.nodeOrJobName)
    if (defaultNodeName) {
      methods.handleNodeOrJobName(node.nodeName)
      data.nodeOrJobName = node.nodeName
    } else {
      props.currSelect.findView(window._jfPaper).update()
    }
  },
  showMoreSetting(type) {
    if (type === "1") {
      data.moreNode = !data.moreNode
    } else if (type === "2") {
      data.moreJob = !data.moreJob
    }
  },
  replaceFlowNodeFormId() {
    replaceFlowNodeFormId(data.newFormId, data.oldFormId, true)
  },
  handleAddGroupName() {
    useMessageBox()
      .prompt("请输入新的分组名称")
      .then(({ value }) => {
        props.flowData.attrs.groupName = value
        onUpdate(dicData, props.flowData.attrs)
      })
  },
  // 加载已有流程
  async loadExistFlow() {
    if (!data.defFlowId) {
      proxy.$modal.msgWarning("请先选择已有流程")
      return
    }
    let find = dicData.defFlows.find((f) => f.id === data.defFlowId)
    if (!find) {
      proxy.$modal.msgWarning("流程数据不存在")
      return
    }
    let confirmObj = {
      text: "新建流程",
      callback: () => {
        methods.emitInitFlow(find, true)
      },
    }
    let cancelObj = {
      text: "修改流程",
      callback: () => {
        methods.emitInitFlow(find, false)
      },
    }
    confirmCancelAndClose(confirmObj, cancelObj, "是否加载为【新建流程】" + find.flowKey)
  },
  emitInitFlow(find, isNew) {
    $emit("initFlow", find.id, null, null, isNew)
    $emit("showAttrConfig", false)
  },
  isNodeJobSeparated() {
    return !(props.currSelect.attrs && props.currSelect.attrs.flowNodeId)
  },
  showNodeOrJobName(currSelect) {
    if (!currSelect.attributes || !methods.isNodeJobSeparated()) {
      return
    }
    let type = currSelect.attributes.attrs.cdata.type
    let b = type !== CommonNodeType.START && type !== CommonNodeType.END
    if (type === CommonNodeType.LINK || !b) {
      return
    }
    let text = currSelect.attr(window._defShapes.ntext)
    let b2 = type === LaneNodeType.X_LANE || type === LaneNodeType.Y_LANE
    if (b2) {
      text = currSelect.attr(window._defShapes.ltext)
    }
    data.nodeOrJobName = text
  },
  handleNodeOrJobName(value) {
    if (!methods.isNodeJobSeparated()) {
      window._jfOperate.changeJobNoSeparatedText(props.currSelect, value)
      return
    }
    window._jfOperate.changeNodeOrJobNoSeparatedText(props.currSelect, value)
  },
  handleDefaultJobName(value) {
    if (!methods.validateCurrSelectAttrs()) {
      return
    }
    if (props.currSelect.attributes.attrs.cdata.type !== HighNodeType.JOB) {
      return
    }
    methods.handleNodeOrJobName(value)
  },
  handleNodeDescription(value) {
    props.currSelect.attr(window._defShapes.ndtext, value)
  },
  linkLabelChange(label) {
    props.currSelect.labels([{ attrs: { text: { text: label } } }])
    props.currSelect.findView(window._jfPaper).render()
  },
  open(bool) {
    data.attrConfigVisible = bool
  },
  openedNodeFormId() {
    if (Object.keys(props.currSelect).length === 0) return
    if (!methods.validateNode(props.currSelect) || data.hideJobFormPerm) return
    // 判断是否为实例
    let form = validateRunFlowId(props, {})
    let find = dicData.formId.find((f) => f.id === form.formId)
    if (!find && !form.flowInstId) {
      dicData.formId.unshift(props.currFlowForm)
      find = props.currFlowForm
    }
    find.isCurrForm = "1"
    setPropsDataValue(
      find,
      props.currFlowForm,
      "formName",
      "formFieldPerms",
      "formInfo",
      "flowInstId"
    )
    // 设置默认表单ID 或 实例表单ID
    let pcTodoUrl = props.currSelect.attributes.attrs.cdata.attrs.pcTodoUrl
    if (!validateNull(pcTodoUrl)) return
    if (props.currSelect.attributes.attrs.cdata.defJob) {
      let sysPathIds = deepClone(orderVue.vueKeySys.sysPathIds)
      sysPathIds.unshift(find.id)
      props.currSelect.attributes.attrs.cdata.attrs.pcTodoUrl = sysPathIds
    }
  },
  validateNullRoleUserId() {
    return validateNull(props.currSelect.attributes.attrs.cdata.defJob.roleUserId)
  },
  revParseUserKeyValName() {
    if (methods.validateCurrSelectAttrsAttrs()) {
      let nodeValType = props.currSelect.attributes.attrs.cdata.attrs.valType
      let routeKeyVal = props.currSelect.attributes.attrs.cdata.attrs.routeKeyVal
      if (
        DIC_PROP.VAL_TYPE[2].value === nodeValType &&
        routeKeyVal === PROP_CONST.VAR_KEY_VAL.route
      ) {
        props.currSelect.attributes.attrs.cdata.attrs.routeKeyValName =
          PROP_CONST.VAR_KEY_VAL.routeName
      }
    }
    if (!methods.validateCurrSelectDefJob()) return
    let valType = props.currSelect.attributes.attrs.cdata.defJob.valType
    let userKeyVal = props.currSelect.attributes.attrs.cdata.defJob.userKeyVal
    if (
      DIC_PROP.VAL_TYPE[2].value === valType &&
      userKeyVal === PROP_CONST.VAR_KEY_VAL.person
    ) {
      props.currSelect.attributes.attrs.cdata.defJob.userKeyValName =
        PROP_CONST.VAR_KEY_VAL.personName
    } else if (DIC_PROP.VAL_TYPE[4].value === valType && userKeyVal) {
      let data = { userKeyValFrom: null }
      revParseUserKeyValName(props, data, dicData, methods)
      if (!data.userKeyValFrom) return
      props.currSelect.attributes.attrs.cdata.defJob.userKeyValName = DIC_PROP.FLOW_METHOD_TYPE.find(
        (f) => f.value === data.userKeyValFrom
      ).label
    }
  },
  handleUserKeyValValType() {
    let userKeyVal = props.currSelect.attributes.attrs.cdata.defJob.userKeyVal
    let valType = props.currSelect.attributes.attrs.cdata.defJob.valType
    if (userKeyVal && !valType) {
      props.currSelect.attributes.attrs.cdata.defJob.valType = DIC_PROP.VAL_TYPE[4].value
    }
  },
  handleUserKeyValType() {
    let userKey = props.currSelect.attributes.attrs.cdata.defJob.userKey
    let roleUserId = props.currSelect.attributes.attrs.cdata.defJob.roleUserId
    let userKeyVal = props.currSelect.attributes.attrs.cdata.defJob.userKeyVal
    let b = validateNull(roleUserId) && !userKeyVal
    // 存在参与者KEY默认分配模式
    if (b && userKey) {
      props.currSelect.attributes.attrs.cdata.defJob.valType = DIC_PROP.VAL_TYPE[1].value
    } else if (b && !userKey) {
      props.currSelect.attributes.attrs.cdata.defJob.valType = null
    }
  },
  clearRoleUserIdValType(isClear) {
    props.currSelect.attributes.attrs.cdata.defJob.roleUserId = []
    if (isClear) {
      props.currSelect.attributes.attrs.cdata.defJob.valType = null
    }
    methods.handleUserKeyValType()
    methods.handleUserKeyValValType()
  },
  clearUserKeyValName() {
    props.currSelect.attributes.attrs.cdata.defJob.valType = null
    if (
      props.currSelect.attributes.attrs.cdata.defJob.userKeyValName ===
      PROP_CONST.VAR_KEY_VAL.personName
    ) {
      props.currSelect.attributes.attrs.cdata.defJob.condGroups = []
    }
    props.currSelect.attributes.attrs.cdata.defJob.userKeyValName = null
    props.currSelect.attributes.attrs.cdata.defJob.userKeyVal = null

    methods.handleUserKeyValType()
  },
  changeTabPane(val) {
    if (validateNull(val) || Object.keys(val).length === 0) {
      data.activeKey = "flow-attr"
      return
    }
    methods.revParseUserKeyValName()
    methods.showNodeOrJobName(val)
    methods.watchHideJobFormPerms(val)
    methods.handleSyncFlowNodeIds()
    methods.handleFlowNodeIds()
    methods.openedNodeFormId()
    methods.getCarbonCopyUsers()
    let type = val.attributes.attrs.cdata.type
    if (type === CommonNodeType.LINK) {
      data.activeKey = "link-attr"
    } else if (type === HighNodeType.JOB) {
      data.activeKey = "node-job-attr"
    } else if (!type) {
      data.activeKey = "flow-attr"
    } else {
      data.activeKey = "node-attr"
    }
  },

  selectCarbonCopyUsers() {
    proxy.$refs.selectUserTreeRef.open(data.showCarbonCopyUsers)
  },

  selectUsersConfirm(e) {
    data.showCarbonCopyUsers = e
    props.currSelect.attributes.attrs.cdata.attrs.carbonCopy = e.map((i) => i.userId)
  },
  async getCarbonCopyUsers() {
    const res = await useDeptStore().fetchZsDeptTreeInfo()
    let { carbonCopy } = props.currSelect.attributes.attrs.cdata.attrs

    const flattenUsers = flatMap(res, (dept) => {
      const extractUsers = (node) => {
        let users = []
        if (node.users && Array.isArray(node.users)) {
          users = users.concat(node.users)
        }
        if (node.children && Array.isArray(node.children)) {
          users = users.concat(flatMap(node.children, extractUsers))
        }
        return users
      }
      return extractUsers(dept)
    })

    // 根据 carbonCopy 中的 userId 查找用户信息
    data.showCarbonCopyUsers = map(carbonCopy, (userId) => {
      return find(flattenUsers, { userId }) // 使用 lodash 的 find 查找用户
    }).filter(Boolean) // 过滤掉未找到的对象
  },
}

// 监听双向绑定
watch(
  () => props.currSelect,
  (val) => {
    methods.changeTabPane(val)
  }
)
watch(
  () => dicData.defFlows,
  () => {
    methods.initSubDefFlows()
  }
)

// 暴露变量
defineExpose({
  open: methods.open,
})
</script>

<style lang="scss">
@use '../assets/style/flow-attr.scss' as *;
</style>
