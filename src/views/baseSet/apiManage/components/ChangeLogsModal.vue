<template>
	<el-dialog
		v-model="dialogVisible"
		title="停用/启用日志"
		width="750px"
		:before-close="handleClose"
	>
		<el-table
			v-loading="loading"
			stripe
			:data="tableData"
			class="table-empty"
		>
			<el-table-column type="index" label="序号" min-width="60" />
			<el-table-column label="操作类型" prop="opType" width="90">
				<template #default="scope">
					<dict-tag :options="normal_disable" :value="scope.row.opType" />
				</template>
			</el-table-column>
			<el-table-column label="操作时间" prop="opTime" width="180">
				<template #default="{row}">
					{{ parseTime(row.opTime) }}
				</template>
			</el-table-column>
			<el-table-column label="操作人" prop="nickName" />
			<el-table-column label="联系电话" prop="phoneNumber" />
		</el-table>
		<pagination
			v-show="total > 0"
			v-model:page="queryParams.pageNum"
			v-model:limit="queryParams.pageSize"
			:total="total"
			@pagination="getList"
		/>
	</el-dialog>
</template>

<script setup name="ChangeLogsModal">
import { getInterfaceOpLog } from "@/api/ess/baseSet/apiManage"
const props = defineProps({
	normal_disable: Array,
})
const dialogVisible = ref(false)
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const queryParams = reactive({
	page: 1,
	limit: 10,
})

const open = item => {
	dialogVisible.value = true
	queryParams.interfaceId = item.id
	getList()
}

const getList = () => {
	loading.value = true
	getInterfaceOpLog(queryParams)
		.then(res => {
			tableData.value = res.object.records
			total.value = res.object.total
		})
		.finally(() => {
			loading.value = false
		})
}

const handleClose = () => {
	dialogVisible.value = false
	tableData.value = []
	queryParams.page = 1
}

defineExpose({
	open,
})
</script>

<style scoped lang="scss"></style>
