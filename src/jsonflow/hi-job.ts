import request from "@/utils/request"

// 获取已办任务
export function fetchToDonePage(query: any) {
	return request({
		url: "/jsonflow/run-job/to-done/page",
		method: "get",
		params: query,
	})
}

// 获取节点配置信息
export function getToDoneDetail(query: any) {
	return request({
		url: "/jsonflow/run-job/to-done/detail",
		method: "get",
		params: query,
	})
}

// 取回任务
export function retakeJob(obj: any) {
	return request({
		url: "/jsonflow/run-job/retake-job",
		method: "put",
		data: obj,
	})
}
