<!--
 * @Author: ljn
 * @Date: 2024-11-29 10:46:12
 * @LastEditors: ljn
 * @LastEditTime: 2025-05-12 11:17:18
 * @Description: 用户头像修改组件
-->
<template>
  <div>
    <div class="user-info-head">
      <div
        v-if="!uploadIcon"
        class="upload-img"
        @click="editCropper()"
        :class="{ circle: isCircle }"
      >
        <img
          v-realImg="showImage"
          src="@/assets/images/default-user-icon.png"
          title="点击上传"
        />
      </div>
      <div v-else>
        <el-button type="primary" plain class="btn-upload" @click="editCropper()">
          上传
        </el-button>
      </div>
      <el-button plain type="primary" v-if="uploadBtn">上传</el-button>
    </div>
    <el-dialog
      v-model="open"
      :title="title"
      width="500px"
      append-to-body
      @opened="modalOpened"
      @close="closeDialog"
    >
      <div class="cropper-wrapper">
        <vue-cropper
          v-if="visible"
          ref="cropper"
          :img="showImage"
          :info="true"
          :auto-crop="options.autoCrop"
          :auto-crop-width="options.autoCropWidth"
          :auto-crop-height="options.autoCropHeight"
          :fixed-box="options.fixedBox"
          :output-type="options.outputType"
          class="cropper-box"
          @real-time="realTime"
        />
      </div>
      <div class="opt-box">
        <el-button icon="Plus" @click="changeScale(1)" />
        <el-button icon="Minus" @click="changeScale(-1)" />
        <el-button icon="RefreshLeft" @click="rotateLeft()" />
        <el-button icon="RefreshRight" @click="rotateRight()" />

        <el-upload
          action="#"
          :http-request="requestUpload"
          :show-file-list="false"
          :before-upload="beforeUpload"
          style="display: inline-block; margin: 0 10px"
        >
          <el-button>
            选择
            <el-icon class="el-icon--right">
              <Upload />
            </el-icon>
          </el-button>
        </el-upload>
        <el-button type="primary" @click="uploadImg()"> 提 交 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="UserAvatar" lang="ts">
import "vue-cropper/dist/index.css"
import { VueCropper } from "vue-cropper"
import { uploadAndAccess } from "@/api/file/file"
import { uploadAvatar } from "@/api/system/user"
import useUserStore from "@/store/modules/user"
import { imageApi } from "@/config/constant"

interface Props {
  uploadBtn?: boolean // 是否显示上传按钮
  isCircle?: boolean // 是否圆形
  uploadIcon?: boolean //是否是上传图标
  iconUrl?: string //传入得图标路径
  customize?: boolean //图标是否为自定义
}

const userStore = useUserStore()
const { proxy } = getCurrentInstance()

const open = ref(false)
const visible = ref(false)

const props = withDefaults(defineProps<Props>(), {
  uploadBtn: false,
  isCircle: true,
  uploadIcon: false,
  iconUrl: "",
  customize: false,
})
const title = ref(!props.uploadIcon ? "修改头像" : "图标上传")
// 图片裁剪数据
const options = reactive({
  img: props.uploadIcon ? (props.customize ? props.iconUrl : "") : userStore.avatar, // 裁剪图片的地址
  autoCrop: true, // 是否默认生成截图框
  autoCropWidth: 140, // 默认生成截图框宽度
  autoCropHeight: 140, // 默认生成截图框高度
  fixedBox: true, // 固定截图框大小 不允许改变
  outputType: "png", // 默认生成截图为PNG格式
  filename: "avatar", // 文件名称
  previews: {}, // 预览数据
})
const chooseImg = ref(false)
const emit = defineEmits(["finish"])

const showImage = computed(() => {
  return `${chooseImg.value ? "" : imageApi}${options.img}`
})
/** 编辑头像 */
function editCropper() {
  open.value = true
}

/** 打开弹出层结束时的回调 */
function modalOpened() {
  visible.value = true
}

/** 覆盖默认上传行为 */
function requestUpload() {}

/** 向左旋转 */
function rotateLeft() {
  proxy.$refs.cropper.rotateLeft()
}

/** 向右旋转 */
function rotateRight() {
  proxy.$refs.cropper.rotateRight()
}

/** 图片缩放 */
function changeScale(num: any) {
  num = num || 1
  proxy.$refs.cropper.changeScale(num)
}

/** 上传预处理 */
function beforeUpload(file: any) {
  // 允许的图片类型
  const allowedTypes = ["image/jpeg", "image/png", "image/jpg"]
  const fileType = file.type.toLowerCase()

  // 校验文件类型
  if (!allowedTypes.includes(fileType)) {
    proxy.$modal.msgError("仅支持 JPG/JPEG/PNG 格式的图片")
    return false
  }

  // 校验文件扩展名
  const extension = file.name.split(".").pop().toLowerCase()
  if (!["jpg", "jpeg", "png"].includes(extension)) {
    proxy.$modal.msgError("文件扩展名必须是 .jpg/.jpeg/.png")
    return false
  }

  // 校验文件大小
  const isLt = file.size / 1024 / 1024 < 5
  if (!isLt) {
    proxy.$modal.msgError(`上传文件大小不能超过 ${5} MB!`)
    return false
  }

  // 读取文件
  const reader = new FileReader()
  reader.readAsDataURL(file)
  reader.onload = () => {
    options.img = reader.result
    options.filename = file.name
    chooseImg.value = true
  }
  return true
}

/** 上传图片 */
function uploadImg() {
  proxy.$refs.cropper.getCropBlob((data: any) => {
    let formData = new FormData()
    if (props.uploadIcon) {
      formData.append("file", data, options.filename)
      uploadAndAccess(formData).then((response: { imgUrl: any }) => {
        emit("finish", response.object.presignedUrl)
        open.value = false
        visible.value = false
      })
    } else {
      formData.append("avatarfile", data, options.filename)
      uploadAvatar(formData).then((response: { imgUrl: any }) => {
        open.value = false
        options.img = response.imgUrl
        userStore.$patch((state) => {
          state.avatar = options.img
        })
        proxy.$modal.msgSuccess("修改成功")
        visible.value = false
      })
    }
  })
}
/** 实时预览 */
function realTime(data: any) {
  options.previews = data
}

/** 关闭窗口 */
function closeDialog() {
  options.img = userStore.avatar
  options.visible = false
  chooseImg.value = false
}
</script>

<style lang="scss" scoped>
.btn-upload {
  margin: 0 20px;
}
.user-info-head {
  @apply relative block w-100% h-100%;
  .upload-img {
    @apply block relative w-100% h-100% cursor-pointer object-cover;
    &.circle {
      border-radius: 50%;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    &:hover:after {
      content: "+";
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #eee;
      background: rgba(0, 0, 0, 0.5);
      font-size: 24px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      cursor: pointer;
    }
  }
}

.cropper-wrapper {
  height: 350px;
  position: relative;
  .cropper-box {
    width: 100%;
    height: 350px;
  }
}
.opt-box {
  display: flex;
  align-items: center;
  margin-top: 20px;
}
</style>
