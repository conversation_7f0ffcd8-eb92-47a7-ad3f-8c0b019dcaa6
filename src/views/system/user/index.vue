<template>
  <div class="app-container">
    <div class="user-container">
      <!--部门数据-->
      <!-- <div class="white-form-box left-container">
        <DeptTree ref="deptTreeRef" :deptOptions="deptOptions" @change="handleNodeClick" />
      </div> -->
      <!--用户数据-->
      <div class="right-container">
        <el-form
          v-show="showSearch"
          ref="queryRef"
          :model="queryParams"
          :inline="true"
          label-width="68px"
          class="white-form-box"
        >
          <el-form-item label="netId" prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入netId"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="工号" prop="workNumber">
            <el-input
              v-model="queryParams.workNumber"
              placeholder="请输入工号"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="用户名称" prop="userName">
              <el-input
                v-model="queryParams.userName"
                placeholder="请输入用户名称"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item> -->
          <el-form-item label="姓名" prop="nickName">
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入姓名"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input
              v-model="queryParams.phonenumber"
              placeholder="请输入手机号码"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" clearable placeholder="请选择用户状态">
                <el-option
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item> -->
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 220px"
            />
          </el-form-item>
          <el-form-item label="是否实名认证" prop="izRealAuth">
            <el-select v-model="queryParams.izRealAuth" clearable placeholder="请选择">
              <el-option
                v-for="dict in izRealAuthList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="人事部门" prop="deptId">
            <el-tree-select
              v-model="queryParams.deptId"
              :data="deptOptions"
              check-strictly
              :render-after-expand="false"
              value-key="id"
              clearable
            />
          </el-form-item>
          <el-form-item label="角色部门" prop="roleDeptId">
            <el-tree-select
              v-model="queryParams.roleDeptId"
              :data="deptOptions"
              check-strictly
              :render-after-expand="false"
              value-key="id"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">
              搜索
            </el-button>
            <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
          </el-form-item>
        </el-form>

        <CustomTable
          ref="customTableRef"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          :has-toolbar="false"
          :data="userList"
          :loading="loading"
          :total="total"
          :opt-width="100"
          @reload="getList"
        >
          <template #actions>
            <el-button
              v-auths="['system:user:export']"
              type="primary"
              @click="handleExport"
            >
              导出
            </el-button>
          </template>
          <template #toolbar>
            <el-button
              v-auths="['system:user:dataSync']"
              type="primary"
              icon="Refresh"
              class="is-trans"
              plain
              @click="handleDataSync"
              :loading="syncUserLoading"
            >
              {{ syncUserLoading ? "同步中..." : "数据同步" }}
            </el-button>
          </template>
          <vxe-column type="seq" width="70" />
          <vxe-column title="netId" field="userName" show-overflow />
          <vxe-column title="工号" field="workNumber" show-overflow />
          <vxe-column title="姓名" field="nickName" show-overflow />
          <vxe-column title="手机号码" field="phonenumber" width="150" show-overflow />
          <vxe-column title="是否实名认证" field="izRealAuth" width="120" show-overflow>
            <template #default="{ row }">
              <dict-tag :options="izRealAuthList" :value="row.izRealAuth" />
            </template>
          </vxe-column>
          <vxe-column title="角色" field="roleName" show-overflow />
          <vxe-column title="角色部门" field="roleDeptName" show-overflow />
          <vxe-column title="人事部门" field="deptName" show-overflow />
          <vxe-column title="创建时间" field="createTime" width="150" show-overflow>
            <template #default="{ row }">
              <span>{{ parseTime(row.createTime) }}</span>
            </template>
          </vxe-column>
          <template #opts="{ row }">
            <el-button
              type="primary"
              size="small"
              v-if="row.izRealAuth === '0' && row.izChineseGj === '1'"
              @click="handleRealAuth(row)"
              v-auths="['system:user:realauth']"
            >
              实名提醒
            </el-button>
          </template>
        </CustomTable>
      </div>
    </div>

    <!-- 添加或修改用户配置对话框 -->
    <EditUserModal ref="editUserModalRef" :dept-options="deptOptions" @change="getList" />

    <!-- 分配角色 -->
    <UserAuthRoleModal ref="userAuthRoleModalRef" @change="getList" />

    <!-- 导入对话框 -->
    <ImportFile
      ref="importFileRef"
      :temp-url="eduUser + '/system/user/importTemplate'"
      :upload-url="eduUser + '/system/user/importData'"
      title="用户导入"
      @change="handleQuery"
    />
  </div>
</template>

<script setup name="User">
import { eduUser } from "@/config/constant"
import { syncUserInfos } from "@/api/syncdata/syncdata"
import EditUserModal from "./components/EditUserModal.vue"
import UserAuthRoleModal from "./components/UserAuthRoleModal.vue"
import DeptTree from "./components/DeptTree.vue"
import {
  changeUserStatus,
  zsUserListPage,
  resetUserPwd,
  delUser,
  deptTreeSelect,
  reminderUserRealAuth,
} from "@/api/system/user"

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

const userList = ref([])
const loading = ref(true)
const syncUserLoading = ref(false)
const switchState = ref(false)
const showSearch = ref(true)
const ids = ref([])
const selectionList = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])
const deptOptions = ref([])
const importFileRef = ref(null)
const editUserModalRef = ref(null)
const userAuthRoleModalRef = ref(null)
// const deptTreeRef = ref(null)

const izRealAuthList = [
  {
    label: "否",
    value: "0",
    elTagClass: "text-danger",
  },
  {
    label: "是",
    value: "1",
    elTagClass: "text-primary",
  },
]

const queryParams = ref({
  page: 1,
  limit: 10,
  userName: null,
  phonenumber: null,
  status: null,
  deptId: null,
  nickName: null,
  roleDeptId: null,
  deptId: null,
})

/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect().then((response) => {
    deptOptions.value = response.data
  })
}

/** 查询用户列表 */
function getList() {
  loading.value = true
  queryParams.value.createTimeStart = dateRange.value?.[0]
  queryParams.value.createTimeEnd = dateRange.value?.[1]
  zsUserListPage(queryParams.value)
    .then((res) => {
      userList.value = res.records
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}

// 数据同步
async function handleDataSync() {
  syncUserLoading.value = true
  syncUserInfos({}).then((response) => {
    if (response.code == 200) {
      getList()
    }
    loading.value = false
    syncUserLoading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  queryParams.value.deptId = undefined
  // proxy.$refs.deptTreeRef.setCurrentKey(null)
  handleQuery()
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.userId || ids.value
  const nickNames =
    row.nickName || selectionList.value.map((item) => item.nickName).join(",")
  proxy.$modal
    .confirm('是否确认删除用户"' + nickNames + '"的数据项？')
    .then(function () {
      return delUser(userIds)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    eduUser + "/zsUserInfoApi/writeZsUserListExcelForXParallel",
    {
      ...queryParams.value,
    },
    `用户_${new Date().getTime()}.xlsx`
  )
}

// fix el-switch auto change
const beforeChangeColumn = () => {
  switchState.value = true
  return switchState.value
}

/** 用户状态修改  */
function handleStatusChange(row) {
  if (!switchState.value) {
    return
  }
  let text = row.status === "1" ? "停用" : "启用" // 字典里面1是停用 0是启用
  proxy.$modal
    .confirm(`确认要"${text}" "${row.userName}"用户吗?`)
    .then(() => {
      changeStatus(row)
    })
    .catch(function () {
      row.status = row.status === "0" ? "1" : "0"
    })
}

const changeStatus = async (row) => {
  let res = await changeUserStatus(row.userId, row.status)
  if (+res.code == 200) {
    row.status = row.status === "0" ? "1" : "0"
    proxy.$modal.msgSuccess(text + "成功")
  }
}

/** 跳转角色分配 */
function handleAuthRole(row) {
  userAuthRoleModalRef.value.open(row)
}

/** 重置密码按钮操作 */
function handleResetPwd(row) {
  proxy
    .$prompt('请输入"' + row.userName + '"的新密码', "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      closeOnClickModal: false,
      inputPattern: /^.{5,20}$/,
      inputErrorMessage: "用户密码长度必须介于 5 和 20 之间",
      inputValidator: (value) => {
        if (/<|>|"|'|\||\\/.test(value)) {
          return "不能包含非法字符：< > \" ' \\\ |"
        }
      },
    })
    .then(({ value }) => {
      resetUserPwd(row.userId, value).then((response) => {
        proxy.$modal.msgSuccess("修改成功，新密码是：" + value)
      })
    })
    .catch(() => {})
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId)
  selectionList.value = selection
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 导入按钮操作 */
function handleImport() {
  importFileRef.value.handleImport()
}

/** 新增按钮操作 */
function handleAdd() {
  editUserModalRef.value.open()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const userId = row.userId || ids.value
  editUserModalRef.value.open(userId)
}

/** 实名按钮操作 */
function handleRealAuth(row) {
  reminderUserRealAuth(row.userId).then((res) => {
    if (res.code === "200") {
      proxy.$modal.msgSuccess(`提醒用户${row.nickName}实名成功！`)
    }
  })
}

getDeptTree()
getList()
</script>

<style lang="scss" scoped>
/*
.user-container {
  display: flex;
  justify-content: space-between;
  align-items: start;
  width: 100%;
  .left-container {
    width: 300px;
    margin-right: 20px;
    flex-shrink: 0;
    height: 90vh;
  }
  .right-container {
     width: calc(100% - 320px);
  }
}
*/
</style>
