<template>
  <div class="navbar">
    <div
      v-if="!settingsStore.topNav && settingsStore.homeLogoUrl"
      class="logo-wrapper"
      :class="{ 'cursor-pointer': !disabled }"
      @click="!disabled && $router.push('/')"
    >
      <img :src="settingsStore.homeLogoUrl" />
      <span>{{ settingsStore.systemTitle }}</span>
    </div>

    <top-nav
      v-if="settingsStore.topNav"
      id="topmenu-container"
      class="topmenu-container"
    />

    <div class="right-menu" v-if="!disabled">
      <div
        v-auths="['doc:optManual:list']"
        class="right-menu-item"
        @click="$router.push('/navUser/optManual')"
      >
        <img src="@/assets/images/icon/book.png" alt="" />
        操作手册
      </div>
      <div
        v-auths="['download:list:query']"
        class="right-menu-item"
        @click="$router.push('/navUser/downloadCenter')"
      >
        <img src="@/assets/images/icon/download.png" alt="" />
        下载中心
      </div>
      <div
        v-auths="['system:notice:list']"
        class="right-menu-item"
        @click="$router.push('/navUser/notice')"
      >
        <img src="@/assets/images/icon/notice.png" alt="" />
        消息
      </div>
      <AvatarDropdown />
    </div>
  </div>
</template>

<script setup lang="ts">
import TopNav from "@/components/TopNav"
import useAppStore from "@/store/modules/app"
import useSettingsStore from "@/store/modules/settings"
import AvatarDropdown from "@/layout/components/AvatarDropdown"

interface Props {
  disabled?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

const appStore = useAppStore()
const settingsStore = useSettingsStore()

function toggleSideBar() {
  appStore.toggleSideBar()
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;
@import url("@/assets/fonts/font.scss");

.navbar {
  height: $base-nav-height;
  overflow: hidden;
  position: fixed;
  z-index: 2001;
  top: 0;
  left: 0;
  width: 100%;
  background: var(--el-color-primary);
  color: #fff;
}

.logo-wrapper {
  height: 100%;
  display: inline-flex;
  align-items: center;
  margin-left: 20px;
  font-size: 24px;
  font-family: "DingTalk";

  img {
    height: 70%;
    object-fit: contain;
    margin-right: 10px;
  }
}

.right-menu {
  float: right;
  height: 100%;
  display: flex;
  align-items: center;

  .right-menu-item {
    margin-right: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;

    img {
      height: 16px;
      width: 16px;
      object-fit: contain;
      flex-shrink: 0;
      margin-right: 5px;
    }
  }
}
</style>
