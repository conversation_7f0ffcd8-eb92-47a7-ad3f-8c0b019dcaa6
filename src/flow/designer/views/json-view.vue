<template>
	<div>
		<el-drawer
			v-model="data.viewJsonVisible"
			class="flow-header-drawer"
			title="导入导出"
			direction="rtl"
			append-to-body
			:size="600"
			@close="methods.onClose"
		>
			<div style="padding: 24px">
				<div>当前JSON数据:</div>
				<json-view
					:value="data.flowData"
					:expand-depth="3"
					boxed
					copyable
				/>

				<div style="margin-top: 12px;">
					导入导出数据:
				</div>
				<el-input
					v-model="data.flowDataJson"
					type="textarea"
					minlength="10"
					@change="methods.editFlowDataJson"
				/>

				<el-divider />
				<el-button :style="{ marginRight: '8px' }" @click="methods.tempSave">
					导出数据
				</el-button>
				<el-button type="primary" @click="methods.onLoad">
					导入数据
				</el-button>
			</div>
		</el-drawer>
	</div>
</template>

<script setup lang="ts" name="JsonView">
import JsonView from 'vue-json-viewer'
const { proxy } = getCurrentInstance()
import { stringifyRemoveNullKey } from '../../index'

const $emit = defineEmits(['loadFlow'])

const data = reactive({
	viewJsonVisible: false,
	flowData: {},
	flowDataJson: '',
})

const methods = {
	open(flowData) {
		flowData = stringifyRemoveNullKey(flowData)
		data.flowData = flowData && !window.isWebTest ? { contact: '演示环境不能操作，如需了解联系我们' } : JSON.parse(flowData)
		data.viewJsonVisible = true
	},
	onClose() {
		data.viewJsonVisible = false
	},
	// 编辑框
	editFlowDataJson(value) {
		data.flowDataJson = value
	},
	// 导出数据
	tempSave() {
		if (!data.flowData.attrs) { return }
		let tempObj = Object.assign({}, data.flowData)
		data.flowDataJson = stringifyRemoveNullKey(tempObj)
	},
	// 导入数据
	onLoad() {
		if (!data.flowDataJson) {
			proxy.$modal.msgWarning('请先导入数据')
			return
		}
		$emit('loadFlow', JSON.parse(data.flowDataJson))
		methods.onClose()
	},
}

// 暴露变量
defineExpose({
	open: methods.open,
})
</script>

<style lang="scss">
  @use "../../../flow/components/style/flow-drawer.scss" as *;
</style>
