export default {
	defFlow: {
		index: "#",
		importdefFlowTip: "import DefFlow",
		id: "id",
		flowName: "flowName",
		flowKey: "flowKey",
		groupName: "groupName",
		status: "status",
		remark: "remark",
		version: "version",
		createUser: "createUser",
		createTime: "createTime",
		sort: "sort",
		isIndependent: "isIndependent",
		updateUser: "updateUser",
		updateTime: "updateTime",
		delFlag: "delFlag",

		inputIdTip: "input id",
		inputFlowNameTip: "input flowName",
		inputFlowKeyTip: "input flowKey",
		inputGroupNameTip: "input groupName",
		inputStatusTip: "input status",
		inputRemarkTip: "input remark",
		inputVersionTip: "input version",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputSortTip: "input sort",
		inputIsIndependentTip: "input isIndependent",
		inputUpdateUserTip: "input updateUser",
		inputUpdateTimeTip: "input updateTime",
		inputDelFlagTip: "input delFlag",

	},
}
