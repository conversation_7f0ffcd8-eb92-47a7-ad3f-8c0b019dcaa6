<template>
	<div :style="{ backgroundColor: sidebarBackground }">
		<el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
			<el-menu
				:default-active="activeMenu"
				:collapse="isCollapse"
				:background-color="sidebarBackground"
				:text-color="textColor"
				:unique-opened="true"
				:active-text-color="theme"
				:collapse-transition="false"
				mode="vertical"
			>
				<sidebar-item
					v-for="(route, index) in sidebarRouters"
					:key="route.path + index"
					:item="route"
					:base-path="route.path"
				/>
			</el-menu>
		</el-scrollbar>
	</div>
</template>

<script setup>
import Logo from "./Logo"
import SidebarItem from "./SidebarItem"
import variables from "@/assets/styles/variables.module.scss"
import useAppStore from "@/store/modules/app"
import useSettingsStore from "@/store/modules/settings"
import usePermissionStore from "@/store/modules/permission"

const route = useRoute()
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()

const sidebarRouters = computed(() => permissionStore.sidebarRouters)
const sideTheme = computed(() => settingsStore.sideTheme)
// 侧边栏背景色
const sidebarBackground = computed(() => {
	return useSettingsStore().sideBarBackground
})
// 字体颜色
const textColor = computed(() => {
	let isDark = isDarkColor(sidebarBackground.value)
	return sideTheme === "theme-dark" || isDark ? variables.menuColor : variables.menuLightColor
})
const theme = computed(() => settingsStore.theme)
const isCollapse = computed(() => !appStore.sidebar.opened)
// const isCollapse = false

const activeMenu = computed(() => {
	const { meta, path } = route
	// if set path, the sidebar will highlight the path you set
	if (meta.activeMenu) {
		return meta.activeMenu
	}
	return path
})

// 判断颜色是否是深色
function isDarkColor(color) {
	// 将颜色转换为RGB
	let rgb = [0, 0, 0]
	if (color.startsWith("#")) {
		color
			.substring(1)
			.match(/.{2}/g)
			.forEach((hex, i) => (rgb[i] = parseInt(hex, 16)))
	}
	else {
		// 对于RGB格式的字符串，可以用正则表达式提取RGB值
		let matches = color.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/)
		if (matches) { rgb = matches.slice(1).map(Number) }
	}

	// 转换为YIQ颜色空间
	let yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000

	// 根据YIQ值判断颜色类型
	return yiq < 120
}
</script>
