
import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询可批量发起的模版
export function getTmpBatchPageListAPI(data: object) {
  return request({
    url: essPrefix + "/my/template/listBatchPage",
    method: "post",
    data
  })
}
// 查询批量发起历史列表
export function getFileBatchHisListAPI(params: object) {
  return request({
    url: essPrefix + "/flowInfoBatch/queryBatchSummary",
    method: "get",
    params
  })
}

/**
 * 查询批量发起历史详情
 * @param data 
 */
export function getFileBatchHisDetailAPI(data: object) {
  return request({
    url: essPrefix + "/flowInfoBatch/queryPublishDetail",
    method: "post",
    data
  })
}

// 导出数据
export function exportOriginDataAPI(data: object) {
  return request({
    url: essPrefix + "/flowInfoBatch/exportOriginData",
    method: "post",
    data
  })
}
// 错误列表
export function fileErrorListAPI(data: object) {
  return request({
    url: essPrefix + "/flowInfoBatch/queryErrorList",
    method: "post",
    data
  })
}
// 批量发起文件时，查询校验结果
export function queryValidateResultAPI(params: object) {
  return request({
    url: essPrefix + "/flowInfoBatch/queryValidateResult",
    method: "get",
    params
  })
}


//批量发起保存草稿
export function fileBatchSave(data:object){
  return request({
    url:essPrefix + "/flowInfoBatch/draft",
    method:'post',
    data
  })
}


//批量发起文件
export function startFile(data:object){
  return request({
    url:essPrefix + '/flowInfoBatch/startFile',
    method:'post',
    data
  })
}


// 草稿箱列表
export function getFileBatchDraftListAPI(data:object){
  return request({
    url:essPrefix + '/flowInfoBatch/draftList',
    method:'post',
    data
  })
}


///删除草稿
export function deleteFileBatchDraftAPI(flowBatchPublishId:string){
  return request({
    url:essPrefix + '/flowInfoBatch/deleteDraft?flowBatchPublishId=' + flowBatchPublishId,
    method:'post',
  })
}


// 导入错误数据
export function importErrorDataAPI(data:object){
  return request({
    url:essPrefix + '/flowInfoBatch/importError',
    method:'post',
    data
  })
}


// 确认导入
export function confirmImportAPI(flowBatchPublishId :string){
  return request({
    url:essPrefix + '/flowInfoBatch/confirmImportError?flowBatchPublishId=' + flowBatchPublishId,
    method:'post',
  })
}



