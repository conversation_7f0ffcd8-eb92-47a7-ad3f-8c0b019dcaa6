<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item label="数据类型" prop="type">
						<el-select
							v-model="state.queryForm.type"
							placeholder="请选择数据类型"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.PARAM_RULE_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="数据值类型" prop="type">
						<el-select
							v-model="state.queryForm.valType"
							placeholder="请选择模式"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in DIC_PROP.VAL_TYPE"
								:key="index"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>

					<el-form-item label="流程名称" prop="defFlowId">
						<el-select
							v-model="state.queryForm.defFlowId"
							placeholder="请选择流程名称"
							clearable
							filterable
							@change="cascadeChange('defFlowId', ['flowNodeId', 'flowNodeRelId'])"
						>
							<el-option
								v-for="(item, index) in dicData.defFlowId"
								:key="index"
								:label="item.flowName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="节点名称" prop="flowNodeId">
						<el-select
							v-model="state.queryForm.flowNodeId"
							placeholder="请先选择流程名称"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowNodeId"
								:key="index"
								:label="item.nodeName"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="连线名称" prop="flowNodeRelId">
						<el-select
							v-model="state.queryForm.flowNodeRelId"
							placeholder="请先选择连线名称"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in cascadeDic.flowNodeRelId"
								:key="index"
								:label="item.label"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>

					<el-form-item class="ml2">
						<el-button icon="search" type="primary" @click="getDataList">
							查询
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.addBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_flowrule_add'"
							icon="Plus"
							type="primary"
							class="ml10"
							@click="formDialogRef.openDialog('add')"
						/>
					</el-tooltip>

					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.delBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_flowrule_del'"
							plain
							:disabled="multiple"
							icon="Delete"
							type="primary"
							class="ml10"
							@click="handleDelete(selectObjs)"
						/>
					</el-tooltip>

					<right-toolbar
						v-model:show-search="showSearch"
						class="ml10"
						style="float: right;margin-right: 20px"
						:export="'jsonflow_flowrule_export'"
						@export-excel="exportExcel"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column
					fixed="left"
					type="index"
					:label="t('flowrule.index')"
					width="40"
				/>
				<el-table-column prop="type" :label="t('flowrule.type')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.PARAM_RULE_TYPE" :value="scope.row.type" />
					</template>
				</el-table-column>
				<el-table-column prop="valType" :label="t('flowrule.valType')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.VAL_TYPE" :value="scope.row.valType" />
					</template>
				</el-table-column>
				<el-table-column prop="defFlowId" :label="t('flowrule.defFlowId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="dicData.defFlowId"
							:value="scope.row.defFlowId"
							:value-key="'id'"
							:show-key="'flowName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="flowNodeId" :label="t('flowrule.flowNodeId')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="dicData.flowNodeId"
							:value="scope.row.flowNodeId"
							:value-key="'id'"
							:show-key="'nodeName'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="groupId" :label="t('flowrule.groupId')" show-overflow-tooltip />
				<el-table-column prop="groupsType" :label="t('flowrule.groupsType')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.OR_OR_AND" :value="scope.row.groupsType" />
					</template>
				</el-table-column>
				<el-table-column prop="groupType" :label="t('flowrule.groupType')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.OR_OR_AND" :value="scope.row.groupType" />
					</template>
				</el-table-column>
				<el-table-column prop="varKeyVal" :label="t('flowrule.varKeyVal')" show-overflow-tooltip />
				<el-table-column prop="operator" :label="t('flowrule.operator')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.OPERATOR" :value="scope.row.operator" />
					</template>
				</el-table-column>
				<el-table-column prop="varVal" :label="t('flowrule.varVal')" show-overflow-tooltip />

				<el-table-column prop="jobType" :label="t('flowrule.jobType')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.JOB_USER_TYPE" :value="scope.row.jobType" />
					</template>
				</el-table-column>

				<el-table-column prop="roleId" :label="t('flowrule.roleId')">
					<template #default="scope">
						<convert-role-name
							:options="{users: dicData.users, roles: dicData.roles, posts: dicData.posts, depts: dicData.depts }"
							:value="scope.row"
						/>
					</template>
				</el-table-column>

				<el-table-column prop="paramFrom" :label="t('flowrule.paramFrom')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.PARAM_FROM" :value="scope.row.paramFrom" />
					</template>
				</el-table-column>
				<el-table-column prop="paramValType" :label="t('flowrule.paramValType')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.PARAM_VAL_TYPE" :value="scope.row.paramValType" />
					</template>
				</el-table-column>
				<el-table-column prop="targetProp" :label="t('flowrule.targetProp')" show-overflow-tooltip />
				<el-table-column prop="paramType" :label="t('flowrule.paramType')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.PARAM_TYPES" :value="scope.row.paramType" />
					</template>
				</el-table-column>

				<el-table-column :label="$t('common.action')" fixed="right" width="100">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.viewBtn') }}
							</template>
							<el-button
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								修改
							</template>
							<el-button
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.delBtn') }}
							</template>
							<el-button
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="FlowRule">
import { BasicTableProps, useTable } from "@/hooks/table"
import { fetchList, delObjs } from "@/api/jsonflow/flow-rule"
import { useMessage, useMessageBox } from "@/hooks/message"
import { onCascadeChange, onLoadDicUrl } from "@/flow/components/convert-name/convert"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"))

// 定义查询字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: "defFlowId" }, { key: "flowNodeId" }, { key: "users" }, { key: "roles" }, { key: "posts" }, { key: "depts" })
const onCascade = onCascadeChange(cascadeDic, { key: "defFlowId", cascades: ["flowNodeId", "flowNodeRelId"] })
onMounted(() => {
	onLoad(dicData)
})
function cascadeChange(key, cascades) {
	onCascade(state.queryForm, { key: key, cascades: cascades })
}

// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	queryRef.value.resetFields()
	// 清空多选
	selectObjs.value = []
	getDataList()
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

// 导出excel
const exportExcel = () => {
	downBlobFile("/jsonflow/flow-rule/export", state.queryForm, "flowrule.xlsx")
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm("确定删除吗？")
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess("删除成功")
	}
	catch (err: any) {
		proxy.$modal.msgError(err)
	}
}
</script>
