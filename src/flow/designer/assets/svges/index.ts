import { nodeJobSvgIcons as nodeJobPath } from "./path"

// SVG ICON图标
export const nodeJobSvgIcons = {
  serial: nodeJobPath.serial,
  serialGate: nodeJobPath.serialGate,
  parallel: nodeJobPath.parallel,
  parallelGate: nodeJobPath.parallelGate,
  job: nodeJobPath.job,
  virtual: new URL('@/assets/flow/menu/virtual-icon.svg', import.meta.url).href,
  startMenu: new URL('@/assets/flow/menu/start.svg', import.meta.url).href,
  endMenu: new URL('@/assets/flow/menu/end.svg', import.meta.url).href,
  xLaneMenu: new URL('@/assets/flow/menu/x-lane.svg', import.meta.url).href,
  yLaneMenu: new URL('@/assets/flow/menu/y-lane.svg', import.meta.url).href,
}
