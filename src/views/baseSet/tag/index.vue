<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      class="white-form-box"
      label-width="80px"
      @submit.native.prevent
    >
      <el-form-item label="标签名称" prop="tagName">
        <el-input
          v-model="queryParams.tagName"
          placeholder="请输入标签名称"
          clearable
          maxlength="50"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <CustomTable
      ref="customTableRef"
      v-model:show-search="showSearch"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :data="tableData"
      custom
      :loading="loading"
      :total="total"
      @reload="getList"
    >
      <template #actions>
        <el-button v-auths="['baseSet:tag:add']" type="primary" icon="Plus" @click="handleAdd">
          新增
        </el-button>
        <el-button
          v-auths="['baseSet:tag:remove']"
          type="danger"
          icon="Delete"
          :disabled="!selections.length"
          @click="handleDelete"
        >
          删除
        </el-button>
      </template>
      <vxe-column type="checkbox" width="40" fixed="left" />
      <vxe-column type="seq" width="70" />
      <vxe-column field="tagName" title="标签名称" min-width="150" />
      <vxe-column field="peopleNum" title="成员人数" />
      <vxe-column field="tagType" title="标签类型" >
        <template #default="{ row }">
          <dict-tag :options="business_ascription" :value="row.tagType" />
        </template>
      </vxe-column>
      <vxe-column field="tagStatus" title="标签状态">
        <template #default="{ row }">
          <dict-tag :options="normal_disable" :value="row.tagStatus" />
        </template>
      </vxe-column>
      <vxe-column title="操作" field="opt" fixed="right" min-width="150">
        <template #default="{ row }">
          <TableColOptBtn
            :key="Math.random()"
            :buttons="[
              {
                text: '修改',
                click: () => handleUpdate(row),
                permission: ['baseSet:tag:edit']
              },
              {
                text: '查看',
                click: () => handleView(row)
              },
              {
                text: '删除',
                type: 'danger',
                click: () => handleDelete(row),
                hidden: +row.tagStatus === 0,
                permission: ['baseSet:tag:remove']
              }
            ]"
          />
        </template>
      </vxe-column>
    </CustomTable>

    <!-- 新增/修改弹窗 -->
    <EditTagModal
      ref="tagModalRef"
      :dictOptions="{
        normal_disable,
        business_ascription
      }"
      @refresh="handleQuery"
      @change="getList"
    />
  </div>
</template>

<script setup name="Tag">
import { tagCfgInfoPage, delTagCfgInfo } from '@/api/ess/baseSet/tagCfgInfo'
import EditTagModal from './components/EditTagModal.vue'
const { proxy } = getCurrentInstance()

const { normal_disable, business_ascription } = proxy.useDict(
  'normal_disable',
  'business_ascription'
)

const tableData = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)

const customTableRef = ref(null)
// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

const tagModalRef = ref(null)

const queryParams = reactive({
  tagName: null,
  limit: 10,
  page: 1
})

/** 查询列表 */
function getList() {
  loading.value = true
  tagCfgInfoPage(queryParams)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  tagModalRef.value.open()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  tagModalRef.value.open(row)
}
/** 查看按钮操作 */
function handleView(row) {
  tagModalRef.value.open(row, true)
}
/** 删除按钮操作 */
function handleDelete(row) {
  const allowDelArr = selections.value.filter((i) => +i.tagStatus === 1)
  if (!allowDelArr.length && !row.id) {
    proxy.$modal.msgError("只有未启用的标签可以删除！")
    return
  }
  const id = row.id || allowDelArr.map((item) => item.id)
  const name = row.tagName || allowDelArr.map((item) => item.tagName)
  proxy.$modal
    .confirm(`是否确认删除标签"${name}"？(只有未启用的标签可以删除)`)
    .then(function () {
      return delTagCfgInfo(id)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}

getList()
</script>
