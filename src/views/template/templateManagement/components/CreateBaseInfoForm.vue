<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="auto"
    label-position="left"
    class="u-p-l-20 max-w-1400px min-w-1200px"
    :class="{ 'min-w-1200px': !formDisabled }"
    :disabled="formDisabled"
  >
    <el-form-item label="模版所属业务线:" prop="businessLineId">
      <BusinessSelect
        ref="businessSelectRef"
        v-model="form.businessLineId"
        v-model:ascription="form.ascription"
        type="radio"
        :before-radio-func="changeBusinessLine"
        @change="clearFormVerify"
        :showText="formDisabled"
      />
    </el-form-item>
    <el-form-item
      v-if="createType == 0"
      prop="templateUserDtos"
      class="block-form-item"
      label="设置模版可见范围:"
    >
      <div v-if="!formDisabled">
        <el-radio-group v-model="range">
          <el-radio :value="1">成员选择</el-radio>
          <el-radio :value="2">范围全选</el-radio>
        </el-radio-group>
      </div>
      <div v-if="formDisabled" class="w-100%">
        <div v-if="form.templateUserDtos" class="tag-show-list">
          <div class="tag-show-list__title">
            <span>成员</span>共{{ form.templateUserDtos.length }}人
          </div>
          <TagUserList
            :list="form.templateUserDtos"
            :show-remove="false"
            :itemSecondarySize="150"
            :gridItems="5"
            itemBg
          />
        </div>
        <div v-if="form.templateRangeDtos" class="tag-show-list">
          <div class="tag-show-list__title"><span>范围</span></div>
          <TagUserList
            :list="form.templateRangeDtos"
            :show-remove="false"
            :itemSecondarySize="200"
            :gridItems="5"
            itemBg
          />
        </div>
      </div>
    </el-form-item>
    <UserTreeTransfer
      ref="userTreeTransferRef"
      :cols="3"
      :rangeType="range"
      :businessLineType="form.ascription"
      :businessLineId="form.businessLineId"
      :select-types="['user', 'dept', 'role', 'tag']"
      v-model:ranges="form.templateRangeDtos"
      v-model:users="form.templateUserDtos"
      v-if="!formDisabled && createType == 0"
      @change="clearFormVerify"
    />
    <el-form-item label="使用模板流程设置:" prop="flowFlag" class="mt-10px">
      <dict-select
        v-model="form.flowFlag"
        :options="
          createType == 1
            ? template_flow_flag.filter((i) => +i.value != 0)
            : template_flow_flag
        "
        value-type="string"
        type="radio"
      />
    </el-form-item>
  </el-form>
</template>

<script setup>
const BusinessSelect = defineAsyncComponent(() =>
  import("@/views/components/BusinessSelect.vue")
)
const UserTreeTransfer = defineAsyncComponent(() =>
  import("@/views/components/UserTreeTransfer/index.vue")
)
const TagUserList = defineAsyncComponent(() =>
  import("@/views/components/UserTreeTransfer/TagUserList.vue")
)

const { proxy } = getCurrentInstance()
const { template_flow_flag } = proxy.useDict("template_flow_flag")

const props = defineProps({
  createType: {
    type: Number,
    default: 0, // 0公有 1 私有
  },
  formDisabled: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
  },
})

const userTreeTransferRef = ref(null)
const formRef = ref()
const form = ref({
  businessLineId: "",
  templateUserDtos: [],
  templateRangeDtos: [],
  ascription: null,
})

const range = ref(1)
const businessSelectRef = ref(null)

watch(
  () => props.data,
  (newVal) => {
    form.value = { ...form.value, ...newVal }
  },
  { immediate: true }
)

const validateTemplateUserDtos = (rule, value, callback) => {
  if (value.length === 0 && form.value.templateRangeDtos.length === 0) {
    callback(new Error("请选择模版可见范围"))
    return
  }

  if (!form.value.businessLineId) {
    callback(new Error("请先选择模版所属业务线"))
    return
  }
  callback()
}
const rules = ref({
  businessLineId: [
    { required: true, message: "请选择模版所属业务线", trigger: "change" },
  ],
  templateUserDtos: [
    { validator: validateTemplateUserDtos, trigger: "change", required: true },
  ],
  flowFlag: [{ required: true, message: "请选择模版流程设置", trigger: "change" }],
})

// 改变业务线
const changeBusinessLine = async (selected) => {
  const { ascription, templateUserDtos } = form.value
  if (
    +selected.ascription !== 0 &&
    ascription !== selected.ascription &&
    templateUserDtos.length &&
    props.createType === 0
  ) {
    await proxy.$modal.confirm("切换业务线类型将清空已选人员，是否继续？")
    form.value.templateUserDtos = []
  }
  form.value.ascription = selected.ascription
  form.value.businessLineId = selected.id
}

function clearFormVerify() {
  formRef.value.clearValidate()
}

const validate = (callback) => {
  formRef.value.validate((valid) => {
    callback({ valid, form: form.value })
  })
}
defineExpose({ validate })
</script>

<style scoped lang="scss">
.tag-show-list {
  background: #f7f8f9;
  padding: 10px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  .tag-show-list__title {
    span {
      font-weight: 600;
      margin-right: 10px;
    }
  }
  :deep(.select-list) {
    padding-left: 0;
    min-height: 100px;
    height: auto;
    max-height: 300px;
  }
}
</style>
