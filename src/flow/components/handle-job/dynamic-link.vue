<template>
  <keep-alive v-if="data.currTabComp">
    <component
      :is="data.currTabComp"
      v-if="data.currTabComp"
      :key="props.currElTab.path"
      :curr-job="props.currJob"
      @handle-job="handleJob"
      :showMsg="props.showMsg"
    />
  </keep-alive>
</template>
<script setup lang="ts" name="DynamicLink">
import { deepClone } from "@/utils/index"
const { proxy } = getCurrentInstance()

const emits = defineEmits(["handleJob"])

const props = defineProps({
  currJob: {
    type: Object,
    default: null,
  },
  currElTab: {
    type: Object,
    default: null,
  },
  showMsg: {
    type: Boolean,
    default: true,
  },
})
const data = reactive({
  currTabComp: null,
  preElTab: null,
})

function handLoader() {
  let path = props.currElTab.path
  if (!path) {
    data.currTabComp = null
    proxy.$modal.msgError("不存在的表单，请检查")
    return
  }
  data.currTabComp = `../../views${path}.vue`
}

function handleJob(jobBtn) {
  emits("handleJob", jobBtn)
}

// 监听双向绑定
watch(
  () => props.currElTab.active,
  (val) => {
    let b =
      props.currElTab.active !== data.preElTab.active &&
      props.currElTab.path === data.preElTab.path
    if (b) {
      data.preElTab = deepClone(props.currElTab)
      data.currTabComp = null
    }

    handLoader() // 重载
  }
)

onMounted(() => {
  data.preElTab = deepClone(props.currElTab)
  // 初始化
  if (props.currElTab.path) {
    handLoader()
  }

})

</script>
