<template>
  <div>
    <EssFilesTable
      ref="essFilesTableRef"
      v-model:queryParams="queryParams"
      :tableData="tableData"
      :loading="loading"
      :total="total"
      @query="handleQuery"
    >
      <template #actions>
        <el-button
          v-auths="['file:fileList:lunchAudit']"
          type="primary"
          @click="handleLunch(0, '文件发起(审批)')"
        >
          文件发起(审批)
        </el-button>
        <el-button
          v-auths="['file:fileList:lunch']"
          type="success"
          @click="handleLunch(1, '文件发起(无需审批)')"
        >
          文件发起(无需审批)
        </el-button>
        <el-button
          v-auths="['file:fileList:batchDownload']"
          type="primary"
          @click="handleBatchDownload"
          plain
          class="is-deep"
        >
          批量下载
        </el-button>
        <el-button
          v-auths="['file:fileList:batchLunch']"
          type="primary"
          @click="handleBatchLunch"
          plain
          class="is-deep"
        >
          批量发起
        </el-button>
        <el-button
          v-auths="['file:fileList:BatchSign']"
          type="primary"
          @click="handleBatchSign"
          plain
          class="is-deep"
        >
          批量签署
        </el-button>
      </template>
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '详情',
              click: () => handleViewDetails(row),
              permission: ['file:fileList:preview'],
            },
            {
              text: '签署',
              click: () => handleSign(row),
              permission: ['file:fileList:sign'],
              hidden: !row.showSignBtn,
            },
            {
              text: '解除',
              click: () => handleRemove(row),
              permission: ['file:fileList:release'],
              hidden: !row.showReleaseBtn || true,
            },
            {
              text: '撤销',
              click: () => handleRevocation(row),
              permission: ['file:fileList:cancel'],
              hidden: !row.showCancelBtn,
            },
            {
              text: '审批撤销',
              click: () => handleConclude(row),
              permission: ['file:fileList:conclude'],
              hidden: +row.flowStatus !== 30,
            },
            {
              text: '催办',
              click: () => handlePress(row),
              permission: ['file:initiatedFiles:urging'],
              hidden: !row.showUrgingBtn,
            },
          ]"
        />
      </template>
    </EssFilesTable>
  </div>
</template>

<script setup name="FileList">
import mittBus from "@/utils/mitt"
import { getFlowListPage, multipleDownloadFile } from "@/api/ess/fileManagement/file"
import { useEssFilesTable } from "@/views/fileManagement/fileList/components/useEssFilesTable"

const EssFilesTable = defineAsyncComponent(() =>
  import("@/views/fileManagement/fileList/components/EssFilesTable.vue")
)

const { proxy } = getCurrentInstance()

const router = useRouter()
const tableData = ref([])
const loading = ref(true)

const total = ref(0)
const queryParams = ref({
  page: 1,
  limit: 10,
})

const essFilesTableRef = ref(null)

const {
  handleViewDetails,
  handlePress,
  handleSign,
  handleRevocation,
  handleConclude,
} = useEssFilesTable(essFilesTableRef)

/** 查询列表 */
function getList() {
  loading.value = true
  getFlowListPage(queryParams.value)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery(data) {
  Object.assign(queryParams.value, data)
  getList()
}

/** 文件发起合同 */
function handleLunch(flowFlag, title) {
  proxy.$tab.openPage(title, `/file/fileLunch/add/${flowFlag}`)
}

/** 批量下载 */
const handleBatchDownload = () => {
  const essFilesId = essFilesTableRef.value.selections.map((item) => {
    return item?.essFlowId
  })
  const formData = new FormData()
  formData.append("essFileIds", essFilesId.join(","))
  multipleDownloadFile(formData).then((res) => {
    if (+res.code === 200) {
      proxy.$modal.msgWarning("正在下载文件，请前往下载中心查看")
    }
  })
}

/** 批量发起 */
function handleBatchLunch() {
  router.push("/file/fileBatchLunchList")
}
/** 批量签署 */
function handleBatchSign() {
  router.push("/agenda/mine?tab=1")
}

getList()

// 页面加载时
onMounted(() => {
  mittBus.on("refreshFileList", () => {
    getList()
  })
})
// 页面卸载时
onUnmounted(() => {
  mittBus.off("refreshFileList")
})
</script>
