<template>
  <div class="components-table">
    <el-form
      ref="queryRef"
      :model="queryParams"
      label-width="70px"
      class="white-form-box"
      inline
    >
      <el-form-item label="发起人" prop="createUserName">
        <el-input
          v-model="queryParams.createUserName"
          placeholder="请输入发起人"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="标题" prop="flowTitle">
        <el-input
          v-model="queryParams.flowTitle"
          placeholder="请输入标题"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="工单编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入工单编号"
          clearable
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="queryParams.createTimeRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="common-input-width"
          clearable
          :default-time="dateDefaultTime"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTimeRange">
        <el-date-picker
          v-model="queryParams.endTimeRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="common-input-width"
          clearable
          :default-time="dateDefaultTime"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <dict-select
          v-model="queryParams.status"
          :options="flow_job_end"
          value-type="string"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>
    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      :has-toolbar="false"
      :data="tableData"
      :loading="loading"
      :total="total"
      :opt-width="50"
      @reload="getList"
    >
      <vxe-column type="seq" width="60" />
      <vxe-column title="工单编号" field="code" min-width="100" show-overflow />
      <vxe-column title="发起人" field="createUserName" min-width="120" show-overflow />
      <vxe-column title="标题" field="flowTitle" min-width="160" show-overflow />
      <!-- <vxe-column title="工单编号" field="code" min-width="100" show-overflow /> -->
      <vxe-column title="任务状态" field="status" width="90" show-overflow>
        <template #default="scope">
          <dict-tag :options="flow_job_end" :value="scope.row.status"></dict-tag>
        </template>
      </vxe-column>
      <vxe-column title="发起时间" field="createTime" min-width="120" show-overflow />
      <vxe-column title="结束时间" field="endTime" min-width="120" show-overflow />
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '查看',
              click: () => viewDetails(row),
            },
          ]"
        />
      </template>
    </CustomTable>

    <!-- 查看审批过程 -->
    <!-- <el-dialog
      v-model="data.showComment"
      v-if="data.showComment"
      top="20px"
      width="90%"
      title="查看审批过程"
      append-to-body
    >
      <comment :curr-job="data.currJob"></comment>
    </el-dialog> -->
  </div>
</template>

<script setup>
import { flowFinishPage } from "@/api/jsonflow/run-flow"
import { handleFlowPreview } from "@/flow/support/extend"
import { useFlowJob } from "@/flow/stores/flowJob"

const props = defineProps({
  mine: {
    type: Number,
    default: 0,
  },
})

const { proxy } = getCurrentInstance()

const { flow_job_end } = proxy.useDict("flow_job_end")

const flowJob = useFlowJob()
const $router = useRouter()

const tableData = ref([])
const loading = ref(false)
const total = ref(0)

const queryParams = reactive({
  current: 1,
  size: 10,
  mine: props.mine,
})

async function getList() {
  try {
    loading.value = true
    queryParams.mine = props.mine
    const res = await flowFinishPage(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}
/** 查看 */
function viewDetails(row) {
  // 复制一行，防止原数据被改
  const copyRow = Object.assign({}, row)
  copyRow.id = row.jobId
  copyRow.flowInstId = row.id
  copyRow.isHiJob = "1"
  handleFlowPreview($router, copyRow, true, true, true, flowJob, getList)
}

getList()

defineExpose({
  resetQuery,
})
</script>

<style scoped lang="scss"></style>
