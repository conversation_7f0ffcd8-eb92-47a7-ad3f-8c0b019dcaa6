export default {
	runNode: {
		index: "#",
		importrunNodeTip: "import RunNode",
		id: "id",
		sort: "sort",
		defFlowId: "defFlowId",
		flowNodeId: "flowNodeId",
		startTime: "startTime",
		endTime: "endTime",
		nodeType: "nodeType",
		nodeName: "nodeName",
		subDefFlowId: "subDefFlowId",
		nodeApproveMethod: "nodeApproveMethod",
		rejectType: "rejectType",
		timeout: "timeout",
		status: "status",
		isAutoNext: "isAutoNext",
		isContinue: "isContinue",
		suspension: "suspension",
		createUser: "createUser",
		createTime: "createTime",
		flowInstId: "flowInstId",
		flowKey: "flowKey",

		inputIdTip: "input id",
		inputSortTip: "input sort",
		inputDefFlowIdTip: "input defFlowId",
		inputFlowNodeIdTip: "input flowNodeId",
		inputStartTimeTip: "input startTime",
		inputEndTimeTip: "input endTime",
		inputNodeTypeTip: "input nodeType",
		inputNodeNameTip: "input nodeName",
		inputSubDefFlowIdTip: "input subDefFlowId",
		inputNodeApproveMethodTip: "input nodeApproveMethod",
		inputRejectTypeTip: "input rejectType",
		inputTimeoutTip: "input timeout",
		inputStatusTip: "input status",
		inputIsAutoNextTip: "input isAutoNext",
		inputIsContinueTip: "input isContinue",
		inputSuspensionTip: "input suspension",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputFlowInstIdTip: "input flowInstId",
		inputFlowKeyTip: "input flowKey",

	},
}
