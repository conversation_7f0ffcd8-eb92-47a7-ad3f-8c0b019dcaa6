<template>
	<div>
		<sign
			ref="signRef"
			v-model:bg-color="bgColor"
			:width="800"
			:height="150"
			:is-crop="false"
			:is-clear-bg-color="false"
      :line-width="3"
			:line-color="'#000000'"
		/>
	</div>
	<div style="margin-left: 15px">
		<el-link type="primary" @click.stop="handleReset">
			重新签名
		</el-link>
	</div>
</template>
<script lang="ts" setup name="SignName">
const { proxy } = getCurrentInstance()

const Sign = defineAsyncComponent(() => import("./sign.vue"))

const bgColor = ref("#F6F8FA")
let props = defineProps({
	currJob: {
		type: Object,
		default: {},
	},
})

const handleReset = () => {
	proxy.$refs.signRef.reset()
	props.currJob.signName = null
}

const handleGenerate = async () => {
	await proxy.$refs.signRef.generate()
		.then((res: any) => {
			props.currJob.signName = res
		})
		.catch(() => {
		})
}

defineExpose({ handleGenerate })
</script>
<style scoped lang="less">

</style>
