import { defineConfig, loadEnv } from 'vite'
import { resolve } from 'path'
import createVitePlugins from './vite/plugins'
// import { setPreLoadFile } from "./vite/plugins/hot-dev"
import { createStyleImportPlugin, VxeTableResolve } from 'vite-plugin-style-import'
import { createHtmlPlugin } from 'vite-plugin-html'
import UnoCSS from 'unocss/vite'

const pathResolve = dir => {
	return resolve(__dirname, '.', dir)
}

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
	const env = loadEnv(mode, process.cwd())
	const { VITE_APP_ENV, VITE_PUBLIC_PATH, VITE_IMAGE_PATH, VITE_APP_BASE_API, VITE_WS_BASE_API, VITE_ICO , VITE_BUCKET_NAME}
    = env
	// 判断是否开发环境
	const isDev = env.VITE_APP_ENV === 'development'
  const basePath = VITE_APP_ENV === 'production' ? `/${VITE_PUBLIC_PATH}/` : '/'
  // const basePath = `/${VITE_PUBLIC_PATH}/`
	return {
		// 部署生产环境和开发环境下的URL。
		// 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
		base: basePath,
		plugins: [
			UnoCSS(),
			createVitePlugins(env, command === 'build'),
			createStyleImportPlugin({
				resolves: [VxeTableResolve()] // 配置vxetable 按需加载
			}),
      createHtmlPlugin({
        inject: {
          data: {
            faviconPath: basePath + VITE_ICO
          }
        }
      }),
		],
		build: {
			outDir: VITE_PUBLIC_PATH,
			chunkSizeWarningLimit: 1500, // 代码分包阈值
			minify: isDev ? 'esbuild' : 'terser',
			terserOptions: {
				compress: {
					pure_funcs: ['console.log'], // 只删除 console.log
					drop_debugger: true // 删除 debugger
				},
				format: {
					comments: false // 删除所有注释
				}
			},
      rollupOptions: {
				output: {
					entryFileNames: `assets/[name].[hash].js`,
					chunkFileNames: `assets/[name].[hash].js`,
					assetFileNames: `assets/[name].[hash].[ext]`,
					compact: true,
					manualChunks: {
						vue: ['vue', 'vue-router', 'pinia'],
						echarts: ['echarts'],
					},
				},
			},
		},
		resolve: {
			alias: {
				// 设置路径
				'~': pathResolve('./'),
				// 设置别名
				'@': pathResolve('./src'),
				'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js'
			},
			// https://cn.vitejs.dev/config/#resolve-extensions
			extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
		},
		// vite 相关配置
		server: {
			port: 1024,
			host: true,
			open: true,
			hmr: true, // 启用热更新
			proxy: {
				[VITE_APP_BASE_API]: {
					// target: 'http://************:8001',
					target: 'http://************:8001',
					// target: 'http://ele.free.svipss.top/prod-api',
					// target: `https://test.towere.com.cn/prod-api`,
					changeOrigin: true,
					rewrite: path => path.replace(new RegExp(`^${VITE_APP_BASE_API}`), '')
				},
				[VITE_IMAGE_PATH]: {
					target: 'http://************:9000' + VITE_BUCKET_NAME,
					changeOrigin: true,
					rewrite: path => path.replace(new RegExp(`^${VITE_IMAGE_PATH}`), '')
				},
				[VITE_WS_BASE_API]: {
					target: 'ws://************:8224/ws-ess/',
					changeOrigin: true,
					rewrite: path => path.replace(new RegExp(`^${VITE_WS_BASE_API}`), '')
				}
			}
		},
		css: {
			preprocessorOptions: {
				scss: {
					api: 'modern-compiler',
					silenceDeprecations: ['legacy-js-api'],
					javascriptEnabled: true
					// additionalData: `
					// @use "@/assets/styles/element-theme.scss" as *;
					// @use "@/assets/styles/index.scss" as *;
					// `
				}
			},
			postcss: {
				plugins: [
					{
						postcssPlugin: 'internal:charset-removal',
						AtRule: {
							charset: atRule => {
								if (atRule.name === 'charset') { atRule.remove() }
							}
						}
					}
				]
			}
		},
		define: {
			__VUE_I18N_LEGACY_API__: JSON.stringify(false),
			__VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
			__INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
			__VERSION__: JSON.stringify(process.env.npm_package_version),
			__NEXT_NAME__: JSON.stringify(process.env.npm_package_name)
		},
		optimizeDeps: {
      force: true,
			include: ['vue-virtual-scroller']
		}
	}
})
