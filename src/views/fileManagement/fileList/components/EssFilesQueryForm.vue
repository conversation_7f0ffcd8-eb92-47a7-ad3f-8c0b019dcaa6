<template>
  <el-form
    v-show="showSearch"
    ref="queryRef"
    :model="queryParams"
    :inline="true"
    class="white-form-box"
    label-width="110px"
    @keyup.enter="handleQuery"
  >
    <el-form-item label="文件名称" prop="flowName">
      <el-input
        v-model="queryParams.flowName"
        placeholder="请输入文件名称"
        clearable
        maxlength="100"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="发起方" prop="startUserName">
      <el-input
        v-model="queryParams.startUserName"
        placeholder="请输入发起方"
        clearable
        maxlength="100"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="发起方部门" prop="startDeptId">
      <dept-picker v-model="queryParams.startDeptId" />
    </el-form-item>
    <el-form-item label="签署方部门" prop="signDeptId">
      <dept-picker v-model="queryParams.signDeptId" />
    </el-form-item>
    <el-form-item label="文件状态" prop="flowStatus">
      <dict-select v-model="queryParams.flowStatus" :options="dictOptions.flow_status" />
    </el-form-item>
    <el-form-item label="所属业务线" prop="businessLineId">
      <BusinessSelect ref="businessSelectRef" v-model="queryParams.businessLineId" />
    </el-form-item>
    <el-form-item label="流程发起时间" prop="flowTimeRange">
      <el-date-picker
        v-model="queryParams.flowTimeRange"
        value-format="YYYY-MM-DD HH:mm:ss"
        format="YYYY-MM-DD HH:mm"
        type="datetimerange"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="common-input-width"
        clearable
        :default-time="dateDefaultTime"
      />
    </el-form-item>
    <el-form-item label="签署发起时间" prop="signQryTimeRange">
      <el-date-picker
        v-model="queryParams.signQryTimeRange"
        value-format="YYYY-MM-DD HH:mm:ss"
        format="YYYY-MM-DD HH:mm"
        type="datetimerange"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="common-input-width"
        clearable
        :default-time="dateDefaultTime"
      />
    </el-form-item>
    <el-form-item label="是否需要审批" prop="flowFlag">
      <dict-select v-model="queryParams.flowFlag" :options="dictOptions.ess_yes_no" />
    </el-form-item>
    <el-form-item label="签署截止时间" prop="signLimitQryTimeRange">
      <el-date-picker
        v-model="queryParams.signLimitQryTimeRange"
        value-format="YYYY-MM-DD HH:mm:ss"
        format="YYYY-MM-DD HH:mm"
        type="datetimerange"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="common-input-width"
        clearable
        :default-time="dateDefaultTime"
      />
    </el-form-item>
    <el-form-item label="签署完成时间" prop="signEndQryEndTimeRange">
      <el-date-picker
        v-model="queryParams.signEndQryEndTimeRange"
        value-format="YYYY-MM-DD HH:mm:ss"
        format="YYYY-MM-DD HH:mm"
        type="datetimerange"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="common-input-width"
        clearable
        :default-time="dateDefaultTime"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
      <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
const BusinessSelect = defineAsyncComponent(() =>
  import("@/views/components/BusinessSelect.vue")
)

const { proxy } = getCurrentInstance()

const props = defineProps({
  showSearch: {
    type: Boolean,
    default: true,
  },
  dictOptions: {
    type: Object,
    default: () => ({
      ess_yes_no: [],
      flow_status: [],
    }),
  },
})
const dateDefaultTime = ref([new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)])

const queryParams = reactive({
  flowName: null,
  startDeptId: null,
  signDeptId: null,
  flowTimeRange: [],
  signQryTimeRange: [], //	签署发起时间
  signLimitQryTimeRange: [], // 签署截止时间
  signEndQryEndTimeRange: [], // 签署完成时间
})

const $emit = defineEmits(["query"])

/** 搜索按钮操作 */
function handleQuery() {
  $emit("query", queryParams)
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

defineExpose({
  resetQuery,
})
</script>

<style scoped lang="scss"></style>
