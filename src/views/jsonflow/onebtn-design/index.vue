<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      class="white-form-box"
      :model="queryParams"
      inline
      label-width="90px"
      @keyup.enter="getList"
    >
      <el-form-item label="流程定义ID" prop="defFlowId">
        <el-input
          v-model="queryParams.defFlowId"
          type="number"
          placeholder="请输入流程定义ID"
          oninput="value=value.replace(/[^\d]/g,'')"
          clearable
        />
      </el-form-item>
      <el-form-item label="流程名称" prop="formName">
        <el-input v-model="queryParams.formName" placeholder="请输入流程名称" clearable />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="(item, index) in DIC_PROP.TEMP_STATUS"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="queryTime">
        <el-date-picker
          v-model="queryParams.queryTime"
          value-format="YYYY-MM-DD"
          format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="common-input-width"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" :loading="loading">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery" :loading="loading">重置</el-button>
      </el-form-item>
    </el-form>
    <CustomTable
      ref="customTableRef"
      v-model:show-search="showSearch"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      custom
      :data="tableData"
      :loading="loading"
      :total="total"
      @reload="getList"
    >
      <template #actions>
        <el-button
          v-auths="['flow:flowDesign:add']"
          type="primary"
          @click="handleDesignFlow(true)"
        >
          创建流程
        </el-button>
        <el-button
          v-auths="['flow:flowDesign:del']"
          type="danger"
          plain
          @click="handleDelete(selectObjs)"
        >
          删除
        </el-button>
      </template>
      <vxe-column type="checkbox" width="40" fixed="left" />
      <vxe-column type="seq" width="60" />
      <vxe-column field="defFlowId" title="流程定义ID" show-overflow width="170" />
      <vxe-column field="icon" title="图标" show-overflow width="60">
        <template #default="{ row }">
          <ShowImageIcon :src="row.icon" class="mt-8px" />
        </template>
      </vxe-column>
      <vxe-column field="formName" title="流程名称" show-overflow min-width="180" />
      <vxe-column field="groupName" title="所属分类" show-overflow min-width="100" />
      <vxe-column field="type" title="类型" show-overflow min-width="80">
        <template #default="{ row }">
          <dict-tag :options="DIC_PROP.FORM_TYPE" :value="row.type" />
        </template>
      </vxe-column>
      <vxe-column field="status" title="状态" show-overflow min-width="60">
        <template #default="{ row }">
          <dict-tag :options="DIC_PROP.TEMP_STATUS" :value="row.status" />
        </template>
      </vxe-column>
      <vxe-column field="version" title="版本" show-overflow width="70" />
      <vxe-column title="创建时间" field="createTime" width="170">
        <template #default="{ row }">
          <span>{{ parseTime(row.createTime) }}</span>
        </template>
      </vxe-column>
      <vxe-column field="remark" title="备注" show-overflow min-width="100" />
      <vxe-column title="操作" field="opt" fixed="right" min-width="160">
        <template #default="{ row }">
          <TableColOptBtn
            :buttons="[
              {
                text: '表单信息',
                click: () => formDialogRef.openDialog('view', row.id),
              },
              {
                text: '修改',
                click: () => formDialogRef.openDialog('edit', row.id),
                permission: ['flow:flowDesign:edit'],
              },
              {
                text: '删除',
                click: () => handleDelete([row.id]),
                type: 'danger',
                permission: ['flow:flowDesign:del'],
              },
              {
                text: '查看表单',
                click: () => handleViewOrder(row),
                hidden: !row.tableName,
              },
              {
                text: '查看流程',
                click: () => openPreview(row.defFlowId),
                hidden: !row.defFlowId,
              },
              {
                text: '再次设计',
                click: () => handleDesignFlow(true, row),
                hidden: !['-1', '1'].includes(row.status),
                permission: ['flow:flowDesign:copyAdd'],
              },
            ]"
          />
        </template>
      </vxe-column>
    </CustomTable>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getList" />

    <!-- 表单设计 -->
    <el-drawer
      v-model="data.showDesignFlow"
      class="flow-overflow-drawer"
      direction="rtl"
      append-to-body
      size="100%"
      :with-header="false"
    >
      <flow-form-design
        v-if="data.showDesignFlow"
        :curr-flow-form="data.currFlowForm"
        @handle-design-flow="handleDesignFlow"
      />
    </el-drawer>

    <el-dialog
      v-model="data.showHandleForm"
      top="20px"
      width="90%"
      title="查看表单"
      append-to-body
    >
      <custom-form
        v-if="data.showHandleForm"
        :curr-job="data.currFlowForm"
        @onHandleForm="onHandleForm"
      ></custom-form>
    </el-dialog>

    <!-- 查看表单 -->
    <el-dialog
      v-model="data.showViewOrder"
      top="20px"
      width="90%"
      title="查看表单"
      append-to-body
    >
      <form-render
        v-if="data.showViewOrder"
        :curr-flow-form="data.currFlowForm"
        :render-type="'-1'"
      />
    </el-dialog>

    <!-- 查看流程图 -->
    <el-drawer
      v-model="data.showFlowPic"
      class="flow-overflow-drawer"
      direction="rtl"
      append-to-body
      size="90%"
    >
      <flow-photo v-if="data.showFlowPic" :curr-job="data.currFlowForm" />
    </el-drawer>

    <!-- 打印模板设计器 -->
    <el-drawer
      v-model="data.showTinymceEditor"
      class="tinymce-print-drawer"
      append-to-body
      direction="rtl"
      size="100%"
      :title="data.tinymceTitle"
      @close="getList"
    >
      <tinymce-editor v-if="data.showTinymceEditor" :curr-flow-form="data.currFlowForm" />
    </el-drawer>
  </div>
</template>

<script setup name="systemFlowApplication">
import { fetchList, delObjs, getObj } from "@/api/order/flow-application"
import { onLoadDicUrl, onLoaded } from "@/flow/components/convert-name/convert"
import { deepClone } from "@/utils/index"
import { parseWithFunctions } from "@/flow"
import { utils } from "@/flow/designer/utils/common"
import { handleCustomForm, vueKey } from "@/api/order/order-key-vue"
import * as jsonflow from "@/flow/designer/api/jsonflow"
import * as defFlow from "@/api/jsonflow/def-flow"
import { validateNull } from "@/utils/validate"

const { proxy } = getCurrentInstance()
// 引入组件
const ShowImageIcon = defineAsyncComponent(() =>
  import("@/components/ImageSelector/ShowImageIcon.vue")
)
const FormDialog = defineAsyncComponent(() =>
  import("@/flow/components/onebtn-design/form.vue")
)
const FlowFormDesign = defineAsyncComponent(() =>
  import("@/flow/components/onebtn-design/design.vue")
)
const FormRender = defineAsyncComponent(() =>
  import("@/flow/components/form-create/render.vue")
)
const FlowPhoto = defineAsyncComponent(() =>
  import("@/views/jsonflow/flow-design/view.vue")
)
const CustomForm = defineAsyncComponent(() =>
  import("@/flow/components/custom-form/handle.vue")
)
const TinymceEditor = defineAsyncComponent(() =>
  import("@/flow/components/tinymce/TinymceEditor.vue")
)

// 定义变量内容
const formDialogRef = ref(null)
// 搜索变量
const queryRef = ref(null)
const showSearch = ref(true)

const customTableRef = ref(null)
const selectObjs = computed(() => {
  if (customTableRef.value) {
    return customTableRef.value.getCheckboxRecords().map((i) => i.id)
  }
  return []
})

// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "permission" })
onMounted(() => {
  onLoad(dicData)
})

const tableData = ref([])
const loading = ref(true)
const total = ref(0)

const queryParams = reactive({
  formName: "",
  status: "",
  size: 10,
  current: 1,
  isQueryForm: 1,
  queryTime: [],
  defFlowId: "",
  descs: "create_time",
})

// 获取数据列表
function getList() {
  loading.value = true
  console.log(queryParams)
  fetchList(queryParams)
    .then((res) => {
      tableData.value = res.object.records
      total.value = res.object.total
    })
    .finally(() => {
      loading.value = false
    })
}

function handleQuery() {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 导出excel
const exportExcel = () => {
  downBlobFile(
    "/cloud-order/flow-application/export",
    queryParams,
    "flow-application.xlsx"
  )
}

// 删除操作
const handleDelete = async (ids) => {
  try {
    await proxy.$modal.confirm("确认要删除选中的数据吗？")
  } catch {
    return
  }

  let delIdList = []
  let delList = tableData.value.filter((f) => ids.includes(f.id))
  for (const each of delList) {
    delIdList.push(each.defFlowId)
    let exists = await jsonflow.listRunFlowsByDefFlowId(each.defFlowId, each.version)
    if (!validateNull(exists.data)) {
      proxy.$modal.msgError("当前删除的模型版本已存在流程实例! 请勿删除")
      return
    }
  }

  try {
    await delObjs(ids)
    await defFlow.delObjs(delIdList)
    getList()
    proxy.$modal.msgSuccess("删除成功")
  } catch (err) {
    proxy.$modal.msgError(err.msg)
  }
}

const data = reactive({
  showViewOrder: false,
  showDesignFlow: false,
  showFlowPic: false,
  currFlowForm: {},
  showTinymceEditor: false,
  tinymceTitle: "",
  showHandleForm: false,
})

function openPreview(defFlowId) {
  data.currFlowForm = { defFlowId: defFlowId }
  data.showFlowPic = true
}

function handlePrintTemplate(row) {
  data.currFlowForm = {
    id: row.id,
    formType: row.type,
    formId: row.id,
    formName: row.formName,
    path: row.path,
    isForm: true,
  }
  data.currFlowForm.formInfo = row.formInfo
  data.tinymceTitle = "打印模板设计器（自定义《" + row.formName + "》打印格式）"
  data.showTinymceEditor = true
}
// 初始化表单数据
const getFlowApplicationData = (id) => {
  getObj(id).then((res) => {
    Object.assign(data.currFlowForm, res.object)
  })
}

function handleDesignFlow(bool, row) {
  data.showDesignFlow = bool
  if (bool === false) {
    getList()
    return
  }
  if (row) {
    data.currFlowForm = deepClone(row)
    getFlowApplicationData(row.id)
    data.currFlowForm.formInfo = parseWithFunctions(row.formInfo, true)
    data.currFlowForm.active = "formSetting"
  } else {
    data.currFlowForm = {
      id: utils.getId(),
      active: "formSetting",
      icon: "flow-reg",
      formName: "未命名",
      type: "0",
      isActive: "1",
      isAutoAudit: "0",
      status: "-1",
      version: 1,
      isNew: true,
      sort: 1,
    }
  }
}

function handleViewOrder(row) {
  data.currFlowForm = deepClone(row)
  // 判断是否自定义首页
  if (row.path !== vueKey.RunApplicationForm) {
    handleCustomForm(data, row)
    data.currFlowForm.operType = "view"
    data.showHandleForm = true
  } else {
    data.showViewOrder = true
  }
}

function onHandleForm(type) {
  data.showHandleForm = false
}

getList()
</script>

<style lang="scss">
@use '@/flow/components/style/flow-drawer.scss' as *;
</style>
