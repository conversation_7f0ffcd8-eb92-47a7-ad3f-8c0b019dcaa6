<template>
  <div v-if="data.updateMultiple" style="width: 100%">
    <el-tree-select
      v-model="modelValue"
      :data="deptOptions"
      check-strictly
      :render-after-expand="false"
      value-key="id"
      clearable
      v-loading="loading"
      v-bind="$attrs"
    />
  </div>
</template>

<script setup lang="ts" name="DeptPicker">
import { useDeptStore } from "@/store/modules/dept"
import { validateNull } from "@/utils/validate"
import { handlePrintValue } from "@/flow/components/form-create"

const props = defineProps({
  formCreateInject: {
    type: Object,
    default: {},
  },
  // modelValue: {
  //   type: [Number, String, Array],
  //   default: null
  // },
  // multiple: {
  //   type: Boolean,
  //   default: false
  // },
  // disabled: {
  //   type: Boolean,
  //   default: false
  // },
  // placeholder: {
  //   type: String,
  //   default: '请选择'
  // }
})

const emits = defineEmits(["update:modelValue"])

const modelValue = defineModel()

const loading = ref(true)
const deptStore = useDeptStore()
const deptOptions = ref([])

function getDeptList() {
  loading.value = true
  deptStore
    .fetchDeptTreeSelect({}, false)
    .then((data) => {
      deptOptions.value = data
    })
    .finally(() => {
      loading.value = false
    })
}

const data = reactive({
  modelValue: null,
  updateMultiple: true,
})

// 注入打印设计的内容，该逻辑非必须
const changePrintValue = () => {
  if (!validateNull(data.modelValue)) {
    let printValue = handlePrintValue(
      deptOptions.value,
      data.modelValue,
      "deptId",
      "name"
    )
    if (printValue) props.formCreateInject.printValue = printValue
    else props.formCreateInject.printValue = ""
  } else {
    props.formCreateInject.printValue = ""
  }
}

// 监听双向绑定
watch(
  () => props.multiple,
  (val) => {
    data.updateMultiple = false
    changeModelValue(null)
    // 防止切换时报错
    setTimeout(() => {
      data.updateMultiple = true
    })
  }
)

onMounted(() => {
  data.modelValue = props.modelValue
  changePrintValue()
})

getDeptList()

defineExpose({
  deptOptions,
})
</script>
