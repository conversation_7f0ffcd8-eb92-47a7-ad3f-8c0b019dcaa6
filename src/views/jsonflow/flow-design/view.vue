<template>
  <div id="initClientHeight">
    <el-container class="flow-container" v-loading="loading">
      <el-container>
        <!--        <el-header class="header-view-option">-->
        <!--          <div-->
        <!--              v-if="data.globalConfig.isHideShortcut !== '1'"-->
        <!--              class="header-option__buttons"-->
        <!--          >-->
        <!--            <el-tooltip content="查看快捷键" placement="bottom">-->
        <!--              <el-button-->
        <!--                  style="font-size: 12px"-->
        <!--                  class="header-option-button"-->
        <!--                  icon="QuestionFilled"-->
        <!--                  @click="methods.shortcutHelper"-->
        <!--              >-->
        <!--                查看快捷键-->
        <!--              </el-button>-->
        <!--            </el-tooltip>-->
        <!--          </div>-->
        <!--        </el-header>-->
        <el-main class="p-0!">
          <flow-area-view
              ref="flowArea"
              class="flow-container"
              :class="{'noBg':props.currJob.flowInstId}"
              :curr-select="data.currSelect"
              @init-json-flow-view="initJsonFlowView"
          />
        </el-main>
      </el-container>
    </el-container>
    <!-- 快捷入门 -->
    <shortcut-modal ref="shortcutModal" :shortcut="data.shortcut"/>
  </div>
</template>

<script setup lang="ts" name="JsonFlowView">
import {flowConfig} from "@/flow/designer/config/flow-config";
import {deepClone} from "@/utils/index";
import {CommonNodeType, HighNodeType, LaneNodeType} from "@/flow/designer/config/type";
import * as defFlow from "@/flow/designer/api/jsonflow";
import * as nodeConfig from "@/flow/designer/config/node-config";
import JsonflowDesign from "@jackrolling/jsonflow3";
import {nodeJobSvgIcons} from "@/flow/designer/assets/svges/path";
import {notifyLeft} from "@/flow";
import {validateNull} from "@/utils/validate";
import {initJsonFlowViewByApp} from "@/flow/support/extend";
import {hideVueContextmenuName, initFlowDesignHeight} from "@/flow/utils";
import sealSvg from '@/assets/flow/icons/seal.svg'
import endSuccess from '@/assets/flow/icons/endSuccess.svg'

const {proxy} = getCurrentInstance();
const $route = useRoute();
// 引入组件
const FlowAreaView = defineAsyncComponent(
    () => import("@/flow/designer/components/flow-area-view.vue")
);
const ShortcutModal = defineAsyncComponent(
    () => import("@/flow/designer/views/shortcut.vue")
);

const props = defineProps({
  currJob: {
    type: Object,
    default: {},
  },
});

const loading = ref(true);
const data = reactive({
  globalConfig: flowConfig.globalConfig,
  globalValue: {},
  flowData: deepClone(flowConfig.flowData),
  gridConfig: flowConfig.gridConfig,
  shortcut: flowConfig.viewShortcut,
  comments: {},
  runJobs: {},
  currSelect: {},
  firstNodeId: null,
});

const methods = {
  removeEleTools() {
    window._jfOperate.removeEleTools();
    methods.clearCurrSelect();
    hideVueContextmenuName();
  },
  initClientHeight() {
    nextTick(() => {
      let browserHeight =
          window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight;
      initFlowDesignHeight($route, browserHeight);
    });
  },
  // 初始化流程
  initFlow(currFlowId?: any, callback?: Function) {
    let flowType = "0";
    // 判断编辑
    if (!currFlowId && props.currJob) {
      currFlowId = props.currJob.defFlowId;
      let flowInstId = props.currJob.flowInstId;
      if (flowInstId) {
        currFlowId = flowInstId;
        flowType = "1";
      }
    }
    if (currFlowId)
      defFlow
          .getNodesByIdType(currFlowId, flowType, "0")
          .then((resp) => {
            let defFlowData = resp.object;
            if (!defFlowData.attrs) notifyLeft("当前流程信息不存在");
            else if (!validateNull(defFlowData.nodeList))
              methods.loadFlow(defFlowData, callback);
          })
          .catch((e) => {
            proxy.$modal.msgError("获取流程信息失败");
            console.error(e);
          })
          .finally(() => {
            loading.value = false;
          });
  },
  handleNodeListInfo(loadData) {
    let flowNodes = loadData.graph.cells.filter(
        (f) => f.attrs.cdata.type !== CommonNodeType.LINK
    );

    // 修改样式
    nextTick(() => {
      methods.applyNodeStyles(loadData)
    })

    flowNodes.forEach((node) => {
      if (
          node.attrs.cdata.type === LaneNodeType.X_LANE ||
          node.attrs.cdata.type === LaneNodeType.Y_LANE
      )
        return;

      let status = loadData.attrs.status;
      if (node.ports && node.ports.items.length > 0)
        node.ports.items.forEach((each) => {
          methods.doNodeListInfo(each, true, false, status);
        });

      let isJobSeparated = "0";
      if (loadData.attrs) isJobSeparated = loadData.attrs.isJobSeparated;
      methods.doNodeListInfo(node, false, isJobSeparated, status);
    });
    methods.handleLinkListInfo(loadData, flowNodes);
    data.firstNodeId = flowNodes[0]?.id;
    flowNodes.forEach((node) => {
      if (node.attrs) {
        delete node.attrs.icon;
        delete node.attrs.image;
        delete node.attrs.portImage;
      }
    });
  },

  handleLinkListInfo(loadData, flowNodes) {
    loadData.graph.cells
        .filter((f) => f.attrs.cdata.type === CommonNodeType.LINK)
        .forEach((link) => {
          let source = flowNodes.find((f) => f.id === link.source.id);
          let target = flowNodes.find((f) => f.id === link.target.id);
          let idNodeJob = target.attrs.cdata.type === HighNodeType.JOB;
          if (!idNodeJob) {
            let exists = data.runRejects.filter(
                (f) =>
                    source.attrs.cdata.attrs.runNodeId === f.fromRunNodeId &&
                    target.attrs.cdata.attrs.runNodeId === f.toRunNodeId
            );
            if (validateNull(exists)) return;
          }
        });
  },
  doNodeListInfo(node, isPort, isJobSeparated, status) {
    let findComments = data.comments.filter(
        (f) => f.flowNodeId === node.id || f.nodeJobId === node.id
    );
    if (!validateNull(findComments))
      methods.updateNodeContMenuData(
          findComments,
          isPort ? node.attributes : node,
          node,
          isPort,
          isJobSeparated,
          true
      );
    else {
      console.log(data.runJobs, "runJobs");
      let findRunJobs = data.runJobs.filter(
          (f) => f.flowNodeId === node.id || f.nodeJobId === node.id
      );
      if (!validateNull(findRunJobs)) {
        if (status === "1")
          findRunJobs.forEach((each) => {
            if (each.status === "0" || each.status === "2" || each.status === "9")
              each.status = "-1";
          });

        methods.updateNodeContMenuData(
            findRunJobs,
            isPort ? node.attributes : node,
            node,
            isPort,
            isJobSeparated,
            false
        );
      } else {
        node = isPort ? node.attributes : node;
        let startTimeBtnName = flowConfig.contextMenu.nodeView.menulists[1].btnName;
        node.startTime = startTimeBtnName + "节点信息有误,请核实";
        let userRoleBtnName = flowConfig.contextMenu.nodeView.menulists[2].btnName;
        node.userRoleName = userRoleBtnName + "";
        let userBtnName = flowConfig.contextMenu.nodeView.menulists[3].btnName;
        node.userName = userBtnName + "";
        let remarkBtnName = flowConfig.contextMenu.nodeView.menulists[4].btnName;
        node.remark = remarkBtnName + "";
      }
    }
  },
  updateNodeContMenuData(findNodes, node, source, isPort, isJobSeparated, isComment) {
    let startTimeBtnName = flowConfig.contextMenu.nodeView.menulists[1].btnName;
    let userRoleBtnName = flowConfig.contextMenu.nodeView.menulists[2].btnName;
    let userBtnName = flowConfig.contextMenu.nodeView.menulists[3].btnName;
    let remarkBtnName = flowConfig.contextMenu.nodeView.menulists[4].btnName;

    if (findNodes.find((f) => f.status === "-3")) node.status = "-3";
    else if (findNodes.find((f) => f.status === "-2")) node.status = "-2";
    else if (
        findNodes.find(
            (f) => f.status === "0" || f.status === "9" || f.subFlowStatus === "0"
        )
    )
      node.status = "0";
    else if (findNodes.find((f) => f.status === "2")) node.status = "2";
    else if (findNodes.find((f) => f.status === "1")) node.status = "1";
    else if (findNodes.find((f) => f.status === "3")) node.status = "3";
    else if (findNodes.every((f) => f.status === "-1")) node.status = "-1";

    findNodes.sort((a, b) => b.createTime - a.createTime);
    let length = findNodes.length;
    findNodes.forEach((f, index) => {
      let comma = index > 0 ? "，" : "";
      let startTime = node.startTime ? node.startTime : "";
      if (f.status === "3")
        node.startTime =
            startTime + comma + (f.startTime ? f.startTime : "已被跳过节点或任务");
      else node.startTime = startTime + comma + (f.startTime ? f.startTime : "未开始");
      if (length === index + 1) node.startTime = startTimeBtnName + node.startTime;

      let userName = f.userName,
          userRoleName = "",
          userBtnName2 = "";
      if (userName) userBtnName2 += userName;
      let roleName = f.roleName;
      if (roleName) userRoleName += roleName;
      node.userRoleName =
          (node.userRoleName ? node.userRoleName : "") +
          comma +
          (roleName ? userRoleName : "无");
      node.userName =
          (node.userName ? node.userName : "") + comma + (userName ? userBtnName2 : "无");
      if (length === index + 1) node.userRoleName = userRoleBtnName + node.userRoleName;
      if (length === index + 1) node.userName = userBtnName + node.userName;

      let remark = node.remark ? node.remark : "";
      if (f.status === "-3") node.remark = remark + comma + "已终止";
      else if (f.status === "-2") node.remark = remark + comma + "已作废";
      else if (f.status === "0") node.remark = remark + comma + "正在审批中";
      else if (f.subFlowStatus === "0") node.remark = remark + comma + "子流程中";
      else if (f.status === "9") node.remark = remark + comma + "被驳回中";
      else if (f.status === "1" && (!f.userId || !isComment))
        node.remark = remark + comma + (f.remark ? f.remark : "由【他人】审批");
      else if (f.status === "1")
        node.remark = remark + comma + (f.remark ? f.remark : "已审批");
      else if (f.status === "2") node.remark = remark + comma + "驳回中";
      else if (f.status === "-1") node.remark = remark + comma + "未审批";
      else if (f.status === "3") node.remark = remark + comma + "已被跳过";

      if (length === index + 1) node.remark = remarkBtnName + node.remark;
    });

    if (node.status === "-1") return;
    // 判断是否首节点
    const isFirstNode =
        source && source.id && this && this.firstNodeId && source.id === this.firstNodeId;
    methods.updateNodeJobSeparated(
        node.status,
        node,
        source,
        isPort,
        isJobSeparated,
        isFirstNode
    );
  },
  updateNodeJobSeparated(
      status,
      node,
      source,
      isPort,
      isJobSeparated,
      isFirstNode = false
  ) {
    if (isFirstNode) {
      delete source.attrs.icon;
      delete source.attrs.image;
      delete source.attrs.portImage;
      return; // 首节点不设置icon、stroke等
    }
    let icon = null;
    if (status === "-3" || status === "-2" || status === "3") {
      icon = {xlinkHref: nodeJobSvgIcons.warning};
    } else if (status === "0" || status === "9") {
      icon = {xlinkHref: nodeJobSvgIcons.loading};
      // 执行中，背景色为橙色
      if (source.attrs.body) {
        source.attrs.body.fill = "#FD6406";
      } else {
        source.attrs.body = {fill: "#FD6406"};
      }
    }
    if (
        source.attrs.cdata &&
        (source.attrs.cdata.type === CommonNodeType.SERIAL ||
            source.attrs.cdata.type === CommonNodeType.PARALLEL)
    ) {
      if (source.attrs.cdata.attrs.isGateway === "1") return;
      if (isJobSeparated !== "1") {
        if (icon) source.attrs.icon = icon;
        else delete source.attrs.icon;
      } else if (icon) source.attrs.image = icon;
      else delete source.attrs.image;
    } else if (icon)
      isPort ? (source.attrs.portImage = icon) : (source.attrs.image = icon);
    // 主动清除所有节点的图标（icon/image/portImage）
    delete source.attrs.icon;
    delete source.attrs.image;
    delete source.attrs.portImage;
  },
  // 加载流程
  loadFlow(loadData, callback) {
    window._jfOperate.reInitFlowSetting(data.flowData, loadData, data);
    window._shapes = nodeConfig.initNodeShapes();
    window._jfOperate.handleGraphCells(loadData, "id");

    methods.handleFlowNodeListInfo(loadData, () => {
      data.flowData.attrs = loadData.attrs;
      window._jfGraph.fromJSON(loadData.graph);
      if (callback) callback();
    });

    nextTick(() => {
      methods.applyNodeStyles(loadData)
    })

  },
  // 处理节点样式
  applyNodeStyles(loadData) {
    if (!window._jfGraph) return;


    const cells = window._jfGraph.getCells();
    const flowNodes = cells.filter(cell => {
      const type = cell.get('type');
      const cdataType = cell.get('attrs')?.cdata?.type;
      return type !== 'standard.Link' &&
          cdataType !== LaneNodeType.X_LANE &&
          cdataType !== LaneNodeType.Y_LANE;
    });

    // 获取所有连线
    const links = cells.filter(cell => {
      const type = cell.get('type');
      return type === 'standard.Link';
    });
    // 处理连线样式
    links.forEach((link, idx) => {
      link.attr('line/stroke', '#C6C8C7');
      link.attr('line/strokeWidth', 2);
      const targetElement = link.getTargetElement();
      if (targetElement) {
        link.target({
          id: targetElement.id,
          anchor: {
            name: 'top',
            args: {
              dx: 0,
              dy: -5
            }
          }
        });
      }
    });

    //处理节点样式
    const startColor = "#D7ECE7";
    const endColor = "#24A87E";
    const total = flowNodes.length;

    function hexToRgb(hex) {
      hex = hex.replace("#", "");
      return [
        parseInt(hex.substring(0, 2), 16),
        parseInt(hex.substring(2, 4), 16),
        parseInt(hex.substring(4, 6), 16),
      ];
    }

    function rgbToHex(rgb) {
      return (
          "#" +
          rgb
              .map((x) => {
                const hex = x.toString(16);
                return hex.length === 1 ? "0" + hex : hex;
              })
              .join("")
      );
    }

    function interpolateColor(color1, color2, factor) {
      const rgb1 = hexToRgb(color1);
      const rgb2 = hexToRgb(color2);
      const result = rgb1.map((v, i) => Math.round(v + (rgb2[i] - v) * factor));
      return rgbToHex(result);
    }

    flowNodes.forEach((cell, idx) => {
      // 移除所有图标相关属性
      cell.removeAttr('icon');
      cell.removeAttr('image');
      cell.removeAttr('portImage');

      // 获取节点类型
      const nodeType = cell.get('attrs')?.cdata?.type;
      // 获取节点状态
      const nodeStatus = cell?.attributes?.attrs?.cdata?.attrs?.status || "-1";

      // 跳过开始和结束节点的形状修改
      if (nodeType !== CommonNodeType.START && nodeType !== CommonNodeType.END) {
        // 根据节点类型修改形状
        switch (nodeType) {
          case CommonNodeType.SERIAL:
            cell.resize(170, 55);
            cell.prop('shape', 'rect');
            cell.attr('body/rx', 6);
            cell.attr('body/ry', 6);
            cell.removeAttr('body/refPoints');
            break;
          default:
            cell.attr('body/rx', 6);
            cell.attr('body/ry', 6);
        }
      }

      let fillColor;
      let fontColor = idx === 0 ? "#24A87E" : "#fff";
      // 开始节点和结束节点修改
      if (nodeType === CommonNodeType.START || nodeType === CommonNodeType.END) {
        if (nodeType === CommonNodeType.START) {
          fillColor = startColor;
        } else if (nodeType === CommonNodeType.END) {
          fillColor = endColor;
          cell.attr('label/text', '流程结束');
        } else {
          fillColor = interpolateColor(startColor, endColor, idx / (total - 1));
        }

        cell.resize(120, 40);
        cell.attr('body/fill', fillColor);
        cell.attr('label/textAnchor', 'middle');
        cell.attr('label/verticalAlign', 'middle');
        cell.attr('label/refX', '50%');

      } else {
        let gradientColors;
        // 根据状态设置不同的渐变色
        switch (nodeStatus) {
          case "0":
            // 执行中
            gradientColors = ["#FD6406", "#FFA55C"];
            break;
          case "1":
            // 已执行
            gradientColors = ["#00AA74", "#64D6B1"];
            break;
          default:
            //未执行
            gradientColors = ["#2986FF", "#7BB4FF"];
            break;
        }
        cell.attr('body/fill', {
          type: 'linearGradient',
          stops: [
            {offset: '0%', color: gradientColors[0]},
            {offset: '100%', color: gradientColors[1]}
          ],
          attrs: {
            x1: '0%',
            y1: '50%',
            x2: '100%',
            y2: '50%'
          }
        });
        //设置文字位置
        cell.attr('label', {
          textAnchor: 'start',
          refX: 10,
          refY: '50%',
          yAlignment: 'middle',
          textWrap: {
            width: 130,
            height: 55,
            ellipsis: '...',
            breakWord: true,
            textVerticalAnchor: 'middle'
          }
        });
        //设置节点右边图片
        cell.attr('image/xlinkHref', sealSvg);
        cell.attr('image/width', 16);
        cell.attr('image/height', 16);
        cell.attr('image/refX', '85%');
        cell.attr('image/refY', '39%');
      }
      //设置无边框字体样式
      cell.attr('body/stroke', 'transparent');
      cell.attr('body/strokeWidth', 0);
      cell.attr('label/fill', fontColor);

    });

    // 重新布局确保样式居中
    window._jfOperate.layout(loadData?.attrs?.autoLayout)

    // 居中节点
    const elements = window._jfGraph.getElements();
    if (elements.length === 0) return;
    const paperEl = window._jfPaper.el;
    const containerWidth = paperEl.clientWidth;
    const containerHeight = paperEl.clientHeight;
    if (containerWidth <= 0 || containerHeight <= 0) return;
    const bbox = window._jfGraph.getBBox(elements);

    // 计算偏移量
    const centerX = containerWidth / 2;
    const centerY = containerHeight / 2;
    const contentCenterX = bbox.x + bbox.width / 2;
    const contentCenterY = bbox.y + bbox.height / 2;
    const offsetX = centerX - contentCenterX;
    const offsetY = centerY - contentCenterY;

    if (window._jfPaper.translate) {
      window._jfPaper.translate(offsetX, offsetY);
    }

  },
  handleFlowNodeListInfo(loadData, call) {
    // 流程节点实例
    let flowInstId = props.currJob.flowInstId;
    if (!flowInstId) {
      if (call) call(loadData);
      return;
    }

    defFlow.fetchComment({flowInstId: flowInstId}).then((resp) => {
      data.comments = resp.object;
      defFlow.fetchRunJobs(flowInstId).then((resp) => {
        data.runJobs = resp.object;
        defFlow.fetchRunRejects(flowInstId).then((resp2) => {
          data.runRejects = resp2.object;
          // 处理节点信息
          methods.handleNodeListInfo(loadData);
          if (call) call(loadData);
        });
      });
    });
  },
  // 检测
  checkFlow() {
    let nodes = window._jfGraph.getElements();
    if (nodes.length <= 0) {
      proxy.$modal.msgWarning("流程无任何节点");
      return false;
    }
    let links = window._jfGraph.getLinks();
    if (links.length <= 0) {
      proxy.$modal.msgWarning("流程无任何连线");
      return false;
    }
    return true;
  },
  // 清除画布
  clearAll() {
    methods.removeEleTools();
    data.flowData.nodeList = [];
    data.flowData.linkList = [];
    window._jfGraph.clear();
  },
  setCurrSelect(curr) {
    data.currSelect = curr;
  },
  clearCurrSelect() {
    data.currSelect = {};
  },
  // 快捷入门
  shortcutHelper() {
    proxy.$refs.shortcutModal.open();
  },
  $refs() {
    return proxy.$refs;
  },
};

const exposeObj = {
  gridConfig: data.gridConfig,
  globalValue: data.globalValue,
  setCurrSelect: methods.setCurrSelect,
  clearCurrSelect: methods.clearCurrSelect,
  removeEleTools: methods.removeEleTools,
  notifyLeft: notifyLeft,
  $refs: methods.$refs,
  currJob: props.currJob,
};

function initJsonFlowView() {
  console.log("初始化流程图");
  // 兼容移动端
  initJsonFlowViewByApp($route, props);
  // 实例化
  window._shapes = nodeConfig.initNodeShapes();
  JsonflowDesign.initFlow.initJsonFlowView(exposeObj);
  // 初始化流程图
  methods.initFlow();
}

onBeforeMount(() => {
  window._flowConfig = flowConfig;
  window._eleTools = JsonflowDesign.eleTools.initEleTools(exposeObj);
});
onMounted(() => {
  methods.initClientHeight();
});
</script>

<style lang="scss" scoped>
@use "@/flow/designer/assets/style/flow-designer.scss" as *;

:deep(.joint-paper ) {
  background-color: #F6F8FA !important;
  background-image: url("@/assets/images/bg-flow-designer.png") !important;
}
.noBg :deep(.joint-paper ) {
  background-color: #FFF !important;
  background-image: none !important;
}
</style>
