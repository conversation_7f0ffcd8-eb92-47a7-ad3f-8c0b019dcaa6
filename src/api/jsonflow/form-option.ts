import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-jsonflow/form-option/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/form-option",
		method: "post",
		data: obj,
	})
}

export function savePrintTemp(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/form-option/print-temp",
		method: "post",
		data: obj,
	})
}

export function getObj(id?: string) {
	return request({
		url: "/cloud-jsonflow/form-option/" + id,
		method: "get",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-jsonflow/form-option",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-jsonflow/form-option",
		method: "put",
		data: obj,
	})
}

export function listFormOption(query?: Object) {
	return request({
		url: "/cloud-jsonflow/form-option/option",
		method: "get",
		params: query,
	})
}

export function listStartPerm(query?: Object) {
	return request({
		url: "/cloud-jsonflow/form-option/start",
		method: "get",
		params: query,
	})
}

export function listPrintTemp(query?: Object) {
	return request({
		url: "/cloud-jsonflow/form-option/print-temp",
		method: "get",
		params: query,
	})
}
