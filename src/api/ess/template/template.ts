import request from "@/utils/request"
import { essPrefix } from "@/config/constant"

// 查询模板信息列表 带分页
export function getTemplateListPage(data: Object) {
  return request({
    url: essPrefix + "/template/listPage",
    method: "post",
    data,
    headers: {
      repeatSubmit: false,
    },
  })
}
// 查询私有模板信息列表 带分页
export function getTemplatePriListPageAPI(data: Object) {
  return request({
    url: essPrefix + "/template/listPriPage",
    method: "post",
    data,
    headers: {
      repeatSubmit: false,
    },
  })
}
// 补充模板扩展信息
export function updateTemplateExtend(data: Object) {
  return request({
    url: essPrefix + "/template/extend",
    method: "post",
    data,
  })
}
// 查看模板扩展信息
export function getTemplateExtendInfo(templateId: string) {
  return request({
    url: essPrefix + "/template/extendInfo/" + templateId,
    method: "get",
  })
}
// 创建模版
export function createTemplate(data: Object) {
  return request({
    url: essPrefix + "/template/create",
    method: "post",
    data,
  })
}
// 根据电子签模板ID获取预览模板url
export function templatePreviewUrl(essTemplateId: string) {
  return request({
    url: essPrefix + "/template/previewUrl/" + essTemplateId,
    method: "get",
  })
}
// 根据电子签模板ID获取编辑模板url
export function getEditTemplateUrl(essTemplateId: string) {
  return request({
    url: essPrefix + "/template/editUrl/" + essTemplateId,
    method: "get",
  })
}

// 查看模版
export function getTemplateInfo(id: string) {
  return request({
    url: essPrefix + "/template/" + id,
    method: "get",
  })
}
// 获取模板可见范围信息详细信息
export function getTemplateUserRef(id: string) {
  return request({
    url: essPrefix + "/templateUserRef/getInfo/" + id,
    method: "get",
  })
}

// 模板停用
export function disableTemplate(id: string) {
  return request({
    url: essPrefix + "/template/disable/" + id,
    method: "get",
  })
}

// 下载模版
export function downloadTemplate(id: string) {
  return request({
    url: essPrefix + "/template/download/" + id,
    method: "get",
    responseType: "blob",
  })
}

/**
 * 获取流程配置
 * @param mine 0 查询我自己配置的最近10条记录 1 查询他人为我配置的最近10条记录
 * @param flowType 流程类型：1=文件发起；2=模板发起
 * @returns
 */
export function queryOperateTop10(params:object) {
  return request({
    url: essPrefix + "/template/queryOperateTop10",
    method: "get",
    params,
  })
}

/**
 * 更新模版固定流程配置
 * @param  "templateId": "模板ID", "defFlowId": "流程定义ID"
 * @returns
 */
export function updateTemplateFlow(data: object) {
  return request({
    url: essPrefix + "/template/updateTemplateFlow",
    method: "post",
    data,
  })
}

// 查询模板签署顺序 0-有序 1-无序
export function getTmpSignOrderAPI(templateId: string) {
  return request({
    url: essPrefix + "/template/tmpSignOrder/" + templateId,
    method: "get",
  })
}
