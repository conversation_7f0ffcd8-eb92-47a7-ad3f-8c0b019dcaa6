import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-order/ask-leave/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-order/ask-leave",
		method: "post",
		data: obj,
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/cloud-order/ask-leave/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/cloud-order/ask-leave/" + id,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/cloud-order/ask-leave/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-order/ask-leave",
		method: "delete",
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-order/ask-leave",
		method: "put",
		data: obj,
	})
}
