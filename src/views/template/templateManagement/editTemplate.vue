<template>
  <div class="app-container">
    <div v-loading="loading" class="white-body-box">
      <iframe
        v-if="webUrl"
        ref="iframeRef"
        :src="webUrl"
        frameborder="0"
        width="100%"
        height="100%"
      />
    </div>
  </div>
</template>

<script setup name="EditTemplate">
import { getEditTemplateUrl } from "@/api/ess/template/template"
const { proxy } = getCurrentInstance()
const route = useRoute()
const templateId = ref(route.params.essTemplateId)
const loading = ref(true)
const webUrl = ref(null)
const iframeRef = ref(null)

function getEditUrl() {
  console.log(route)
  if (!templateId.value) {
    return
  }
  // 获取模版信息
  getEditTemplateUrl(templateId.value)
    .then((res) => {
      webUrl.value = res.object.url
    })
    .finally(() => {
      loading.value = false
    })
}
getEditUrl()

// 获取iframe传过来的信息
onMounted(() => {
  window.addEventListener("message", handleMessage)
})

onBeforeUnmount(() => {
  window.removeEventListener("message", handleMessage)
})

function handleMessage(event) {
  const { messageType, action, embedParams } = event.data
  if (messageType === "NOT_LOGIN") {
    proxy.$modal.alert("页面停留时间过长，请重新输入").then(() => {
      getUrl()
    })
  }
  sendMsgToIframe(event)
}

// 向子页面（电子签页面）发送消息
function sendMsgToIframe(evt) {
  if (evt.data.messageType === "PAGE_LOADED") {
    // PAGE_LOADED页面加载完成事件
    // 向子页面（电子签页面）发送消息UI_MESSAGE_CONFIG，开启对应事件的开关
    iframeRef.value.contentWindow.postMessage(
      {
        messageType: "UI_MESSAGE_CONFIG",
        config: {
          MODIFY_TEMPLATE_SUCCESS_BACK: "on", // 创建模板页面成功返回事件
        },
      },
      evt.origin
    ) // 需指定域名 或 设置为 '*'
  }
  if (evt.data.messageType === "MODIFY_TEMPLATE_SUCCESS_BACK") {
    // 用户点击了返回按钮
    let obj = {
      path: `/template/${route.query.to}`,
      query: { r: 1 },
    }
    proxy.$tab.closeOpenPage(obj)
  }
}
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.app-container {
  .white-body-box {
    margin-top: 0;
    min-height: calc(#{$base-main-page-height} - 55px - 40px);
  }
  iframe {
    width: 100%;
    min-height: calc(#{$base-main-page-height} - 55px);
  }
}
</style>
