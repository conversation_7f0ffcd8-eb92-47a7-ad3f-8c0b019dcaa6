export default {
	runFlow: {
		index: "#",
		importrunFlowTip: "导入流程管理",
		id: "流程实例ID",
		initiatorId: "流程申请人",
		defFlowId: "流程名称",
		groupName: "分组名称",
		startTime: "开始时间",
		endTime: "结束时间",
		useTime: "流程用时",
		invalidReason: "作废原因",
		status: "流程状态",
		version: "版本",
		createUser: "创建人",
		createTime: "创建时间",
		orderId: "工单ID",
		flowKey: "流程KEY",
		updateUser: "更新人",
		code: "工单编号",
		formId: "表单ID",
		updateTime: "更新时间",
		flowName: "流程名称",
		remark: "备注",
		isIndependent: "配置数据独立",

		inputIdTip: "请输入主键ID",
		inputInitiatorIdTip: "请输入流程申请人",
		inputDefFlowIdTip: "请输入流程名称",
		inputGroupNameTip: "请输入分组名称",
		inputStartTimeTip: "请输入开始时间",
		inputEndTimeTip: "请输入结束时间",
		inputUseTimeTip: "请输入流程用时",
		inputInvalidReasonTip: "请输入作废原因",
		inputStatusTip: "请输入流程状态",
		inputVersionTip: "请输入版本",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputOrderIdTip: "请输入工单ID",
		inputFlowKeyTip: "请输入流程KEY",
		inputUpdateUserTip: "请输入更新人",
		inputCodeTip: "请输入工单编号",
		inputFormIdTip: "请输入表单ID",
		inputUpdateTimeTip: "请输入更新时间",
		inputFlowNameTip: "请输入流程名称",
		inputRemarkTip: "请输入备注",
		inputIsIndependentTip: "请选择配置数据独立",

	},
}
