<template>
  <div :class="!props.layoutPadding ? '' : 'layout-padding'">
    <div :class="!props.layoutPadding ? '' : 'layout-padding-auto layout-padding-view'">
      <fc-designer
        ref="designer"
        height="100vh"
        :config="config.designer"
        :handle="config.handle"
        class="fc-designer-wrapper"
        @save="props.saveFormInfo"
      />
    </div>
  </div>
</template>

<script setup lang="ts" name="FlowForm">
import FcDesigner from "form-create-designer"
import type { Config, DragRule } from "form-create-designer"
import { rules } from "./rules"
import { validateNull } from "@/utils/validate"
import useUserStore from "@/store/modules/user"
import { notifyLeft } from "../../index"
import { initFcDesignerFetch } from "./api"
import { deepClone } from "@/utils/index"

const { proxy } = getCurrentInstance()
const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null,
  },
  layoutPadding: {
    type: Boolean,
    default: true,
  },
  showSaveBtn: {
    type: Boolean,
    default: false,
  },
  saveFormInfo: {
    type: Function,
    default: null,
  },
})

// 组件引用
const designer = ref<InstanceType<typeof FcDesigner> | null>(null)

const router = useRouter()

// 设计器配置
const config = {
  designer: {
    fieldReadonly: false, // 字段是否只读
    showSaveBtn: props.showSaveBtn,
  },
  locale: null,
}

const methods = {
  syncCurrFlowForm() {
    // 保存表单
    let json = designer.value.getRule()
    if (validateNull(json)) props.currFlowForm.formInfo = null
    else {
      let options = designer.value.getOptions()
      options.widgetList = json
      props.currFlowForm.formInfo = options
    }
  },
  handleSubmit(callback, isNotify) {
    methods.syncCurrFlowForm()
    let json = props.currFlowForm.formInfo
    if (validateNull(json)) {
      if (isNotify) notifyLeft("表单设计请添加组件", "warning")

      return false
    }
    props.currFlowForm.formDesign = true
    if (callback) callback()

    return true
  },
}

const data = reactive({
  globalDsv: {
    type: Object,
    default: {},
  },
})

async function initFcDesigner() {
  // TODO: 隐藏了业务组件
  designer.value.addMenu({ name: "biz", title: "业务组件", list: [] })
  // Add all rules at once
  rules.forEach((rule) => {
    designer.value?.addComponent(rule as DragRule)
  })
  initFcDesignerFetch(designer.value, null, data.globalDsv)
}

function getFormInfo() {
  initFcDesigner()
  data.globalDsv.userInfos = useUserStore().userInfo
  const { formInfo } = props.currFlowForm
  // 解决可能因为加载时间导致的字符串错误
  let newInfo = typeof formInfo == "string" ? JSON.parse(formInfo) : deepClone(formInfo)
  if (validateNull(newInfo)) {
    designer.value.setRule([])
    return
  }
  designer.value.setRule(newInfo.widgetList)
  delete newInfo.widgetList
  designer.value.setOptions(newInfo)
}

onMounted(async () => {
  await nextTick()
  getFormInfo()
})

// 暴露变量
defineExpose({
  handleSubmit: methods.handleSubmit,
})
</script>

<style lang="scss">
@use '@/assets/styles/_variables.scss' as theme;

.fc-designer-wrapper {
  .fc-form-row {
    // 选中效果
    ._fd-drag-tool {
      outline: 1px dashed var(--main-theme) !important;

      &.active {
        outline: 2px solid var(--main-theme) !important;
      }
    }
  }
  ._fd-size-input .el-button {
    min-height: 24px;
  }

  ._fc-designer {
    --fc-tool-border-color: var(--main-theme);
  }

  .el-radio__input.is-checked + .el-radio__label,
  ._fd-fn-list .el-button,
  ._fd-gfc .el-button {
    color: var(--main-theme) !important;
    border-color: var(--main-theme) !important;
  }

  ._fc-l {
    ._fc-l-tab.active {
      color: var(--main-theme) !important;
      border-bottom-color: var(--main-theme);
    }
  }
  ._fc-r {
    ._fc-r-tab.active {
      color: var(--main-theme) !important;
      border-bottom-color: var(--main-theme);
    }
    .el-radio-button {
      --el-radio-button-checked-bg-color: var(--main-theme);
      --el-radio-button-checked-border-color: var(--main-theme);
    }
    .el-radio-button__inner:hover {
      color: var(--main-theme);
    }
  }
  ._fc-l-menu-item.active,
  ._fc-m-tools-l .devices .fc-icon.active,
  ._fc-l-menu-item i:hover {
    color: var(--main-theme) !important;
  }

  ._fc-l-item:hover {
    background-color: var(--main-theme) !important;
  }

  .el-radio__input.is-checked .el-radio__inner {
    background: var(--main-theme) !important;
    border-color: var(--main-theme) !important;

    &::after {
      top: 1px;
      left: 0.5px;
    }
  }

  .el-switch {
    --el-switch-on-color: var(--main-theme);
  }
  ._fd-drag-btn {
    background: var(--main-theme);
  }

  .el-tag--plain.el-tag--success {
    --el-color-success: var(--main-theme) !important;
    --el-tag-border-color: var(--main-theme) !important;
  }

  ._fd-struct .el-button,
  ._fd-validate-btn,
  ._fd-fn-input .el-button,
  ._fd-event .el-button {
    border-color: var(--main-theme) !important;
    color: var(--main-theme) !important;
  }

  .el-slider {
    --el-slider-main-bg-color: var(--main-theme) !important;
  }

  // 设置事件样式
  ._fd-event-l .el-menu-item {
    line-height: 1em !important;
  }

  .el-button--primary.is-link {
    --el-button-text-color: var(--main-theme) !important;
  }
}

._fd-fn-list-dialog,
._fd-struct-con,
._fd-event-dialog,
._fd-gfc-dialog {
  .el-button--primary {
    background-color: var(--main-theme) !important;
    border-color: var(--main-theme) !important;
    &.is-link {
      background: transparent !important;
      border-color: transparent !important;
      color: var(--main-theme) !important;
    }
  }
}
</style>
