<template>
  <el-dialog
    v-model="visible"
    title="选择持有人"
    width="830px"
    :before-close="handleClose"
    append-to-body
  >
    <UserTreeTransfer
      ref="userTreeTransferRef"
      v-model:users="userList"
      :deptId="sealInfo.deptId"
      :selectTypes="['user']"
    />
    <el-form ref="formRef" :model="form" label-width="100px" class="auth-user-form">
      <el-form-item label="授权结束时间" prop="authDeadLineTime">
        <el-date-picker
          v-model="form.authDeadLineTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          type="datetime"
          range-separator="-"
          clearable
        />
        <span class="u-m-l-15 text-info">
          确认后授权立即生效，未选择授权结束时间则默认为长期有效
        </span>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose"> 取消 </el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 确定 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { addAuthUserSeal } from "@/api/ess/seal/sealAuth"
import UserTreeTransfer from "@/views/components/UserTreeTransfer/index.vue"

defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  // 印章信息
  sealInfo: {
    type: Object,
    default: () => {},
  },
})

const { proxy } = getCurrentInstance()

const visible = ref(false)
const loading = ref(false)
const userTreeTransferRef = ref(null)
const formRef = ref(null)

const userList = ref([])

const form = ref({
  sealAuthUser: [],
})

function open(sealId) {
  visible.value = true
  form.value.sealId = sealId
}

function reset() {
  userList.value = []
  userTreeTransferRef.value?.reset()
  proxy.resetForm("formRef")
  visible.value = false
}

function handleClose() {
  reset()
}

const emit = defineEmits(["change"])
async function submitForm() {
  if (!userList.value.length) {
    return proxy.$modal.msgError("请选择授权用户")
  }

  try {
    loading.value = true
    form.value.sealAuthUser = userList.value.map(({ nickName, userId, deptId }) => ({
      holdUserName: nickName,
      holdUserId: userId,
      holdDeptId: deptId,
      authDeadLineTime: form.value.authDeadLineTime,
      authType: Number(!!form.value.authDeadLineTime), // 显式转换为 1/0
    }))

    const res = await addAuthUserSeal(form.value)
    const msg = res?.object?.messages.join(",") || res?.object?.message
    proxy.$modal.msg(msg || "添加成功")
    emit("change")
    handleClose()
  } catch (error) {
    const msg = error?.object?.messages || error?.object?.message || error
    proxy.$modal.msgError(msg || "添加授权失败")
  } finally {
    loading.value = false
  }
}
defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.auth-user-form {
  border-top: 1px dashed #ececec;
  padding-top: 20px;
  margin-top: 20px;
}
</style>
