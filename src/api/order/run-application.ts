import request from "@/utils/request"

export function fetchList(query?: Object) {
	return request({
		url: "/cloud-order/run-application/page",
		method: "get",
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: "/cloud-order/run-application",
		method: "post",
		data: obj,
	})
}

export function tempStore(obj: any) {
	return request({
		url: "/cloud-order/run-application/temp-store",
		method: "post",
		data: obj,
	})
}

export function getObj(id: any) {
	return request({
		url: "/cloud-order/run-application/" + id,
		method: "get",
	})
}

export function getByFlowInstId(flowInstId: any) {
	return request({
		url: "/cloud-order/run-application/flow-inst-id/" + flowInstId,
		method: "get",
	})
}

export function delObj(id: any) {
	return request({
		url: "/cloud-order/run-application/" + id,
		method: "delete",
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: "/cloud-order/run-application",
		method: "delete",
		data: ids,
	})
}

export function putObjNoStatus(obj?: Object) {
	return request({
		url: "/cloud-order/run-application/no-status",
		method: "put",
		data: obj,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: "/cloud-order/run-application",
		method: "put",
		data: obj,
	})
}

// 根据orderId获取表单详情
export function queryFormDataByOrderId(orderId?: string) {
	return request({
		url: "/cloud-order/run-application/queryFormDataByOrderId?orderId=" + orderId,
		method: "get",
	})
}

