<template>
	<div class="app-container" v-loading="loading" element-loading-text="正在加载页面，请稍候！">
		<div class="white-body-box">
			<iframe
				:id="iframeId"
				style="width: 100%; height: 100%;"
				:src="src"
				frameborder="no"
        ref="iframeRef"
			/>
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	src: {
		type: String,
		default: "/",
	},
	iframeId: {
		type: String,
	},
})
const loading = ref(true)
const iframeRef = ref(null)

onMounted(() => {
  if (iframeRef.value) {
    iframeRef.value.onload = () => {
      loading.value = false
    }
  }
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.app-container {
  .white-body-box {
    margin-top: 0;
  }
  iframe {
    width: 100%;
    min-height: calc(#{$base-main-page-height} - 55px);
  }
}
</style>
