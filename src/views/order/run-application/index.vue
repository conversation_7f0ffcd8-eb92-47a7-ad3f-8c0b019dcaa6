<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row v-show="showSearch">
        <el-form ref="queryRef" :model="state.queryForm" :inline="true" @keyup.enter="getDataList">
          <el-form-item label="工单编号" prop="code">
            <el-input
              v-model="state.queryForm.code"
              placeholder="请输入工单编号"
              style="max-width: 180px;"
            />
          </el-form-item>
          <el-form-item label="流程标识" prop="flowKey">
            <el-input
              v-model="state.queryForm.flowKey"
              placeholder="请输入流程标识"
              style="max-width: 180px;"
            />
          </el-form-item>
          <el-form-item label="表单名称" prop="formName">
            <el-input
              v-model="state.queryForm.formName"
              placeholder="请输入表单名称"
              style="max-width: 180px;"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="state.queryForm.status"
              placeholder="请选择状态"
              clearable
              style="max-width: 180px;"
            >
              <el-option
                v-for="(item, index) in DIC_PROP.ORDER_STATUS"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="search" type="primary" @click="getDataList">
              查询
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="mb-2" style="width: 100%;">
          <el-tooltip placement="top">
            <template #content>
              删除
            </template>
            <el-button
              v-auth="'order_runapplication_del'"
              plain
              :disabled="multiple"
              icon="Delete"
              type="primary"
              class="ml10"
              @click="handleDelete(selectObjs)"
            />
          </el-tooltip>

          <right-toolbar
            v-model:show-search="showSearch"
            :export="'order_runapplication_export'"
            class="ml10"
            style="float: right; margin-right: 20px;"
            @export-excel="exportExcel"
            @query-table="getDataList"
          />
        </div>
      </el-row>
      <el-table
        v-loading="state.loading"
        :data="state.dataList"
        style="width: 100%;"
        @selection-change="handleSelectionChange"
        @sort-change="sortChangeHandle"
      >
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="序号" width="40" />
        <el-table-column prop="code" label="工单编号" show-overflow-tooltip />
        <el-table-column prop="flowKey" label="流程标识" show-overflow-tooltip>
          <template #default="scope">
            <convert-name
              :options="dicData.flowKey"
              :value="scope.row.flowKey"
              :value-key="'flowKey'"
              :show-key="'flowName'"
            />
          </template>
        </el-table-column>
        <el-table-column prop="icon" label="图标" show-overflow-tooltip>
          <template #default="scope">
            <SvgIcon :name="scope.row.icon" :size="20" />
          </template>
        </el-table-column>
        <el-table-column prop="formName" label="表单名称" show-overflow-tooltip />
        <el-table-column prop="groupName" label="分组名称" show-overflow-tooltip />
        <el-table-column prop="finishTime" label="完成时间" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="DIC_PROP.ORDER_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" width="200" show-overflow-tooltip />
        <el-table-column prop="version" label="版本" show-overflow-tooltip />
        <el-table-column label="操作" width="170">
          <template #default="scope">
            <el-tooltip placement="top">
              <template #content>
                查看
              </template>
              <el-button text type="primary" icon="view" @click="handleViewOrder(scope.row)" />
            </el-tooltip>

            <el-tooltip placement="top">
              <template #content>
                打印表单
              </template>
              <el-button icon="Document" text type="primary" @click="handleViewOrder(scope.row)" />
            </el-tooltip>

            <el-tooltip placement="top">
              <template #content>
                复制
              </template>
              <el-button
                icon="DocumentCopy"
                text
                type="primary"
                @click="handleInitiateOrder(scope.row, 'copy')"
              />
            </el-tooltip>

            <el-tooltip v-if="scope.row.status === DIC_PROP.ORDER_STATUS[2].value" placement="top">
              <template #content>
                撤回
              </template>
              <el-button
                icon="RefreshLeft"
                text
                type="primary"
                @click="handleRecallReset(scope.row)"
              />
            </el-tooltip>
            <el-tooltip v-if="scope.row.status === DIC_PROP.ORDER_STATUS[0].value" placement="top">
              <template #content>
                重发
              </template>
              <el-button
                icon="RefreshRight"
                text
                type="primary"
                @click="handleRecallReset(scope.row)"
              />
            </el-tooltip>

            <el-tooltip placement="top">
              <template #content>
                查看流程图
              </template>
              <el-button icon="Share" text type="primary" @click="openPreview(scope.row)" />
            </el-tooltip>

            <el-tooltip
              v-if="
                scope.row.status === DIC_PROP.ORDER_STATUS[1].value ||
                scope.row.status === DIC_PROP.ORDER_STATUS[0].value
              "
              placement="top"
            >
              <template #content>
                修改
              </template>
              <el-button
                icon="edit-pen"
                text
                type="primary"
                @click="handleInitiateOrder(scope.row, 'edit')"
              />
            </el-tooltip>
            <el-tooltip v-if="scope.row.status === DIC_PROP.ORDER_STATUS[1].value" placement="top">
              <template #content>
                删除
              </template>
              <el-button icon="delete" text type="primary" @click="handleDelete([scope.row.id])" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-bind="state.pagination"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      />
    </div>

    <!-- 复制工单 -->
    <json-flow-predict ref="predict" :proxy="proxy" @handleInitiateOrder="handleInitiateOrder">
      <template v-slot="slotProps" v-if="data.showInitiateOrder">
        <run-initiate
          ref="form"
          v-show="slotProps.currActive === 'form'"
          :curr-flow-form="data.currFlowForm"
          @handleInitiateOrder="handleInitiateOrder"
        ></run-initiate>
      </template>
      <template v-slot="slotProps" v-if="data.showHandleForm">
        <custom-form
          ref="form"
          v-show="slotProps.currActive === 'form'"
          :curr-job="data.currFlowForm"
          @onHandleForm="handleInitiateOrder"
        ></custom-form>
      </template>
    </json-flow-predict>

    <custom-form
      v-if="data.showHandleForm"
      :curr-job="data.currFlowForm"
      @on-handle-form="handleInitiateOrder"
    />

    <!-- 查看工单 -->
    <el-dialog v-model="data.showViewOrder" top="20px" width="90%" title="查看工单" append-to-body>
      <run-view v-if="data.showViewOrder" :curr-flow-form="data.currFlowForm" />
    </el-dialog>

    <!-- 查看流程图 -->
    <el-drawer
      v-model="data.showFlowPic"
      class="flow-overflow-drawer"
      direction="rtl"
      append-to-body
      size="90%"
    >
      <flow-photo v-if="data.showFlowPic" :curr-job="data.currFlowForm" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts" name="systemRunApplication">
import { BasicTableProps, useTable } from '@/hooks/table'
import * as runApplication from '@/api/order/run-application'
import { useMessage, useMessageBox } from '@/hooks/message'
import { getCurrentInstance } from 'vue'

import { onLoadDicUrl, onLoaded } from '@/flow/components/convert-name/convert'
import * as common from '@/flow/support/common'
import { deepClone } from '@/utils/index'
import { recallReset } from '@/api/jsonflow/run-flow'
import { DIC_PROP } from '@/flow/support/dict-prop'
import { openFlowPreview } from '@/flow/support/extend'
import { handleCustomForm, vueKey } from '@/api/order/order-key-vue'

// 引入组件
const FlowPhoto = defineAsyncComponent(() => import('@/views/jsonflow/flow-design/view.vue'))
const RunInitiate = defineAsyncComponent(() => import('./initiate.vue'))
const RunView = defineAsyncComponent(() => import('./view.vue'))
const CustomForm = defineAsyncComponent(() => import('@/flow/components/custom-form/handle.vue'))
const JsonFlowPredict = defineAsyncComponent(() =>
  import('@/views/jsonflow/flow-design/predict.vue')
)

const { proxy } = getCurrentInstance()!

// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {},
  pageList: runApplication.fetchList,
  onLoaded: onLoaded({ key: 'createUser' }),
  descs: ['create_time']
})

// 定义字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: 'flowKey' })
onMounted(() => {
  onLoad(dicData)
})

//  table hook
const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
  // 清空搜索条件
  queryRef.value?.resetFields()
  // 清空多选
  selectObjs.value = []
  getDataList()
}

// 导出excel
const exportExcel = () => {
  downBlobFile('/cloud-order/run-application/export', state.queryForm, 'run-application.xlsx')
}

// 多选事件
const handleSelectionChange = (objs: any) => {
  selectObjs.value = objs.map(({ id }) => id)
  multiple.value = !objs.length
}

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm('确认要删除选中的数据项吗？')
  } catch {
    return
  }

  try {
    await runApplication.delObjs(ids)
    getDataList()
    proxy.$modal.msgSuccess('删除成功')
  } catch (err) {
    proxy.$modal.msgError(err.msg)
  }
}

const data = reactive({
  showViewOrder: false,
  showInitiateOrder: false,
  showFlowPic: false,
  currFlowForm: {},
  showHandleForm: false
})

function handleViewOrder(row) {
  data.currFlowForm = row
  // 判断是否自定义首页
  if (row.path !== vueKey.RunApplicationForm) {
    handleCustomForm(data, row)
    data.currFlowForm.operType = 'view'
    data.showHandleForm = true
  } else {
    data.showViewOrder = true
  }
}

function handleInitiateOrder(row, operType) {
  if (row === false) {
    getDataList()
    openPredict({}, false)
    data.showInitiateOrder = false
    data.showHandleForm = false
    return
  }
  data.currFlowForm = deepClone(row)
  data.currFlowForm.operType = operType
  let clone = { operType: data.currFlowForm.operType }
  common.handleClone(clone, data.currFlowForm)
  // 判断是否自定义首页
  if (row.path !== vueKey.RunApplicationForm) {
    handleCustomForm(data, row)
    data.showHandleForm = true
  } else {
    data.showInitiateOrder = true
  }
  openPredict(row, true)
}

function openPredict(row, bool) {
  proxy.$refs.predict.open(row, bool)
}

const $router = useRouter()
function openPreview(row) {
  if (row.status === DIC_PROP.ORDER_STATUS[1].value) {
    data.currFlowForm = { defFlowId: row.defFlowId }
    data.showFlowPic = true
  } else {
    openFlowPreview($router, { flowInstId: row.flowInstId }, '1')
  }
}

function handleRecallReset(row) {
  let params = { id: row.flowInstId, flowKey: row.flowKey, status: row.status }
  if (row.status === DIC_PROP.ORDER_STATUS[0].value) {
    recallReset(params).then(() => {
      proxy.$modal.msgSuccess('重发成功')
      getDataList()
    })
    return
  }
  useMessageBox()
    .confirm('是否确认要撤回该工单?')
    .then(() => {
      return recallReset(params)
    })
    .then(() => {
      proxy.$modal.msgSuccess('撤回成功')
      getDataList()
    })
}
</script>

<style lang="scss">
@use '../../../flow/components/style/flow-drawer.scss' as *;
</style>
