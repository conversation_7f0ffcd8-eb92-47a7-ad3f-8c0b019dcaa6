import antfu from '@antfu/eslint-config'

export default antfu(
  {
    stylistic: {
      'scss/no-global-function-names': null,
      indent: 2, // 缩进风格
      quotes: 'single' // 单引号
    },
    vue: {
      overrides: {
        // enforce order of component top-level elements 自定义 Vue 文件中标签的顺序，模板 -> 脚本 -> 样式
        'vue/block-order': [
          'error',
          {
            order: ['template', 'script', 'style']
          }
        ]
      }
    },
    typescript: true,
    jsonc: false,
    yaml: false,
    unocss: true,
    rules: {
      'no-unused-expressions': 'off',
      'curly': ['error', 'multi-line'], // 强制使用花括号的风格
      'brace-style': ['error', '1tbs', { allowSingleLine: true }], // 大括号风格 ["error", "stroustrup"]
      'vue/multi-word-component-names': 'off',
      'vue/no-v-text-v-html-on-component': 'off',
      'vue/padding-line-between-blocks': 'never', // 强制在代码块之间添加空行
    },
    formatters: {
      html: true,
      css: true
    }
  },
  {
    ignores: [
      'node_modules',
      'dist',
      'locales',
      '*.md',
      '**/fixtures' // 忽略特定路径下的文件（如 fixtures 目录）
    ]
  }
)
