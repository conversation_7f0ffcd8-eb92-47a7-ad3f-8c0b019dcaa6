<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
      class="white-form-box"
    >
      <el-form-item label="任务名称" prop="jobName">
        <el-input
          v-model="queryParams.jobName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务分组" prop="jobGroup">
        <dict-select
          v-model="queryParams.jobGroup"
          :options="sys_job_group"
          value-type="string"
        />
      </el-form-item>
      <el-form-item label="执行状态" prop="status">
        <dict-select
          v-model="queryParams.status"
          :options="sys_job_status"
          value-type="string"
        />
      </el-form-item>
      <el-form-item label="执行时间">
        <el-date-picker
          v-model="dateRange"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" :loading="loading">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery" :loading="loading">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <CustomTable
      ref="customTableRef"
      v-model:show-search="showSearch"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :data="jobLogList"
      custom
      :loading="loading"
      :total="total"
      @reload="getList"
    >
      <template #actions>
        <el-button
          v-auths="['monitor:job:remove']"
          type="danger"
          icon="Delete"
          :disabled="!selections.length"
          @click="handleDelete"
        >
          删除
        </el-button>
        <el-button
          v-auths="['monitor:job:remove']"
          type="danger"
          icon="Delete"
          @click="handleClean"
        >
          清空
        </el-button>
      </template>
      <vxe-column type="checkbox" width="40" fixed="left" />
      <vxe-column type="seq" width="70" />
      <vxe-column field="jobLogId" title="日志编号" min-width="150" show-overflow />
      <vxe-column field="jobName" title="任务名称" min-width="150" show-overflow />
      <vxe-column field="jobGroup" title="任务组名" width="90">
        <template #default="{ row }">
          <dict-tag :options="sys_job_group" :value="row.jobGroup" />
        </template>
      </vxe-column>
      <vxe-column
        field="invokeTarget"
        title="调用目标字符串"
        min-width="200"
        show-overflow
      />
      <vxe-column field="jobMessage" title="日志信息" min-width="200" show-overflow />
      <vxe-column field="status" title="执行状态" width="80">
        <template #default="{ row }">
          <dict-tag :options="sys_job_status" :value="row.status" />
        </template>
      </vxe-column>
      <vxe-column field="createTime" title="执行时间" width="180" show-overflow />
      <vxe-column title="操作" field="opt" fixed="right" min-width="90">
        <template #default="{ row }">
          <TableColOptBtn
            :key="Math.random()"
            :buttons="[
              {
                text: '详细',
                click: () => handleView(row),
                permission: ['monitor:job:query'],
              },
            ]"
          />
        </template>
      </vxe-column>
    </CustomTable>

    <JobLogDetail ref="jobLogDetailRef" :sys_job_status="sys_job_status" />
  </div>
</template>

<script setup name="JobLog">
import { listJobLog, delJobLog, cleanJobLog } from "@/api/monitor/jobLog"

const JobLogDetail = defineAsyncComponent(() => import("./components/JobLogDetail"))

defineOptions({
  inheritAttrs: false,
})

const { proxy } = getCurrentInstance()
const { sys_job_group, sys_job_status } = proxy.useDict("sys_job_group", "sys_job_status")

const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const jobLogList = ref([])
const dateRange = ref([])
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  jobName: undefined,
  jobGroup: undefined,
  status: undefined,
})
const queryFormRef = ref()
const customTableRef = ref(null)
const jobLogDetailRef = ref()

// 多选框选中
const selections = computed(() => {
  if (customTableRef.value) return customTableRef.value.getCheckboxRecords()
  return []
})

function getList() {
  loading.value = true
  listJobLog(proxy.addDateRange(queryParams, dateRange.value)).then((response) => {
    jobLogList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryFormRef")
  handleQuery()
}

function handleView(row) {
  proxy.$refs.jobLogDetailRef.open(row)
}

function handleDelete() {
  const jobLogIds = selections.value.map((item) => item.jobLogId)
  const names = selections.value.map((item) => item.jobName).join("、")
  proxy.$modal
    .confirm(`是否确认删除调度日志?`, `当前选择"${names}"`)
    .then(() => {
      return delJobLog(jobLogIds)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
}

function handleClean() {
  proxy.$modal
    .confirm("是否确认清空所有调度日志数据项?")
    .then(() => {
      return cleanJobLog()
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("清空成功")
    })
}

onMounted(() => {
  getList()
})
</script>
