<template>
  <el-form label-position="left" class="flow-attr flow-param-attr" label-width="200px">
    <el-divider>关联子流程Http接口</el-divider>

    <el-form-item label="保存子流程表单Http请求地址">
      <el-input
        v-model="props.currSelect.attributes.attrs.cdata.attrs.startSubFlow"
        class="input-attr"
        placeholder="可输入全路径或相对路径"
        clearable
      >
        <template #prepend>
          <el-select v-model="props.currSelect.attributes.attrs.cdata.attrs.startSubMethod">
            <el-option
              v-for="(item, index) in DIC_PROP.HTTP_METHODS"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-input>

      <el-tooltip placement="top">
        <template #content>
          启动子流程时更新子流程表单接口（可输入全路径或相对路径）
        </template>
        <el-icon style="margin-left: 10px;">
          <QuestionFilled />
        </el-icon>
      </el-tooltip>
    </el-form-item>
    <el-form-item label="更新子流程表单Http请求地址">
      <el-input
        v-model="props.currSelect.attributes.attrs.cdata.attrs.restartSubFlow"
        class="input-attr"
        placeholder="可输入全路径或相对路径"
        clearable
      >
        <template #prepend>
          <el-select v-model="props.currSelect.attributes.attrs.cdata.attrs.restartSubMethod">
            <el-option
              v-for="(item, index) in DIC_PROP.HTTP_METHODS"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-input>

      <el-tooltip placement="top">
        <template #content>
          重入或重启子流程时更新子流程表单接口（可输入全路径或相对路径）
        </template>
        <el-icon style="margin-left: 10px;">
          <QuestionFilled />
        </el-icon>
      </el-tooltip>
    </el-form-item>
    <el-form-item label="更新父流程表单Http请求地址">
      <el-input
        v-model="props.currSelect.attributes.attrs.cdata.attrs.backParFlow"
        class="input-attr"
        placeholder="可输入全路径或相对路径"
        clearable
      >
        <template #prepend>
          <el-select v-model="props.currSelect.attributes.attrs.cdata.attrs.backParMethod">
            <el-option
              v-for="(item, index) in DIC_PROP.HTTP_METHODS"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-input>

      <el-tooltip placement="top">
        <template #content>
          返回父流程时更新父流程表单接口（可输入全路径或相对路径）
        </template>
        <el-icon style="margin-left: 10px;">
          <QuestionFilled />
        </el-icon>
      </el-tooltip>
    </el-form-item>

    <el-divider>关联子流程Http请求头</el-divider>

    <el-table
      :data="data.subFlowParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[0].value)"
      border
      style="width: 100%; margin-bottom: 10px;"
      max-height="500"
      :key="1"
    >
      <el-table-column type="index" label="操作" width="80">
        <template #header>
          <el-button
            icon="Plus"
            size="small"
            type="primary"
            circle
            @click="methods.onAddItem(DIC_PROP.PARAM_FROM[0].value, true)"
          />
        </template>
        <template #default="scope">
          <el-button
            icon="Minus"
            size="small"
            type="danger"
            circle
            @click="methods.handleSubFlowDelete(scope.$index, scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column prop="targetProp" label="请求头" show-overflow-tooltip>
        <template #default="scope">
          <el-input
            v-model="scope.row.targetProp"
            :placeholder="
              scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value
                ? scope.row.varKeyVal
                : null
            "
            clearable
          />
        </template>
      </el-table-column>

      <el-table-column prop="paramValType" label="参数值类型" width="145px">
        <template #default="scope">
          <el-select v-model="scope.row.paramValType" clearable>
            <el-option
              v-for="(item, index) in DIC_PROP.PARAM_VAL_TYPE"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column prop="varKeyVal" label="表单字段或固定值" show-overflow-tooltip>
        <template #default="scope">
          <el-select
            v-if="scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value"
            v-model="scope.row.varKeyVal"
            clearable
            filterable
          >
            <el-option-group
              v-for="(group, index) in data.allFieldPerms"
              :key="index"
              :label="group.label"
            >
              <el-option
                v-for="(item, index) in group.options"
                :key="index"
                :disabled="item.prop.indexOf('_define_') !== -1"
                :label="item.label"
                :value="item.prop"
              />
            </el-option-group>
          </el-select>

          <el-input v-else v-model="scope.row.varKeyVal" clearable />
        </template>
      </el-table-column>
    </el-table>

    <el-divider>父流程传参到子流程</el-divider>

    <el-table
      :data="data.subFlowParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[1].value)"
      border
      style="width: 100%; margin-bottom: 10px;"
      max-height="500"
      :key="2"
    >
      <el-table-column type="index" label="操作" width="80">
        <template #header>
          <el-button
            icon="Plus"
            size="small"
            type="primary"
            circle
            @click="methods.onAddItem(DIC_PROP.PARAM_FROM[1].value, true)"
          />
        </template>
        <template #default="scope">
          <el-button
            icon="Minus"
            size="small"
            type="danger"
            circle
            @click="methods.handleSubFlowDelete(scope.$index, scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column prop="varKeyVal" label="表单字段或固定值" show-overflow-tooltip>
        <template #default="scope">
          <el-select
            v-if="scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value"
            v-model="scope.row.varKeyVal"
            clearable
            filterable
          >
            <el-option-group
              v-for="(group, index) in data.allFieldPerms"
              :key="index"
              :label="group.label"
            >
              <el-option
                v-for="(item, index) in group.options"
                :key="index"
                :disabled="item.prop.indexOf('_define_') !== -1"
                :label="item.label"
                :value="item.prop"
              />
            </el-option-group>
          </el-select>

          <el-input v-else v-model="scope.row.varKeyVal" clearable />
        </template>
      </el-table-column>

      <el-table-column prop="paramValType" label="参数值类型" width="145px">
        <template #default="scope">
          <el-select v-model="scope.row.paramValType" clearable>
            <el-option
              v-for="(item, index) in DIC_PROP.PARAM_VAL_TYPE"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column prop="targetProp" label="子流程表单字段" show-overflow-tooltip>
        <template #default="scope">
          <el-input
            v-model="scope.row.targetProp"
            :placeholder="
              scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value
                ? scope.row.varKeyVal
                : null
            "
            clearable
          />
        </template>
      </el-table-column>
    </el-table>

    <el-divider>子流程回参到父流程</el-divider>

    <el-table
      :data="data.subFlowParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[2].value)"
      border
      style="width: 100%; margin-bottom: 10px;"
      max-height="500"
      :key="3"
    >
      <el-table-column type="index" label="操作" width="80">
        <template #header>
          <el-button
            icon="Plus"
            size="small"
            type="primary"
            circle
            @click="methods.onAddItem(DIC_PROP.PARAM_FROM[2].value, true)"
          />
        </template>
        <template #default="scope">
          <el-button
            icon="Minus"
            size="small"
            type="danger"
            circle
            @click="methods.handleSubFlowDelete(scope.$index, scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column prop="targetProp" label="子流程表单字段或固定值" show-overflow-tooltip>
        <template #default="scope">
          <el-input
            v-model="scope.row.targetProp"
            :placeholder="
              scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value
                ? scope.row.varKeyVal
                : null
            "
            clearable
          />
        </template>
      </el-table-column>

      <el-table-column prop="paramValType" label="参数值类型" width="145px">
        <template #default="scope">
          <el-switch
            v-model="scope.row.paramValType"
            :active-value="DIC_PROP.PARAM_VAL_TYPE[0].value"
            :active-text="DIC_PROP.PARAM_VAL_TYPE[0].label"
            :inactive-value="DIC_PROP.PARAM_VAL_TYPE[2].value"
            :inactive-text="DIC_PROP.PARAM_VAL_TYPE[2].label"
            inline-prompt
          />
        </template>
      </el-table-column>

      <el-table-column prop="varKeyVal" label="表单字段" show-overflow-tooltip>
        <template #default="scope">
          <el-select v-model="scope.row.varKeyVal" clearable filterable>
            <el-option-group
              v-for="(group, index) in data.allFieldPerms"
              :key="index"
              :label="group.label"
            >
              <el-option
                v-for="(item, index) in group.options"
                :key="index"
                :disabled="item.prop.indexOf('_define_') !== -1"
                :label="item.label"
                :value="item.prop"
              />
            </el-option-group>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script setup lang="ts" name="FlowSubParam">
const { proxy } = getCurrentInstance()
import { validateNull } from '@/utils/validate'
import { buildSysFieldsFormOption } from '../../utils/form-perm'
import { DIC_PROP } from '../../support/dict-prop'

const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null
  },
  currSelect: {
    type: Object,
    default: null
  },
  flowData: {
    type: Object,
    default: null
  }
})

const data = reactive({
  allFieldPerms: [],
  formFieldPerms: [],
  subFlowParams: []
})

// 定义字典
onMounted(async () => {
  methods.changeTabPane(props.currSelect)
})

const methods = {
  listFormFieldPerms() {
    if (validateNull(data.formFieldPerms)) {
      buildSysFieldsFormOption(data, props)
    }
  },
  onAddItem(paramFrom, isAdd) {
    methods.listFormFieldPerms()
    let value = DIC_PROP.PARAM_VAL_TYPE[0].value
    if (data.subFlowParams.length > 0) {
      let find = data.subFlowParams
        .filter((f) => f.paramFrom === paramFrom)
        .find((f) => !f.varKeyVal || (f.paramValType !== value && !f.targetProp))
      if (find) {
        if (isAdd) {
          if (!find.varKeyVal) {
            proxy.$modal.msgWarning('请先填写 表单字段')
            return
          }
          if (find.paramValType !== value && !find.targetProp) {
            if (DIC_PROP.PARAM_FROM[0].value === paramFrom) {
              proxy.$modal.msgWarning('请先填写 请求头')
            } else {
              proxy.$modal.msgWarning('请先填写 子流程表单字段')
            }
            return
          }
        }
      }
      if (!isAdd) {
        data.subFlowParams.splice(0, data.subFlowParams.length)
      }
    }
    let obj = { paramFrom: paramFrom, varKeyVal: null, paramValType: value, targetProp: null }
    data.subFlowParams.push(obj)
    methods.changeSubFlowParams()
  },
  handleSubFlowDelete(index: number, row: any) {
    let splice = data.subFlowParams.filter((f) => f.paramFrom === row.paramFrom)
    splice.splice(index, 1)
    if (DIC_PROP.PARAM_FROM[0].value === row.paramFrom) {
      let res = data.subFlowParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[1].value)
      let res2 = data.subFlowParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[2].value)
      splice.push(...res)
      splice.push(...res2)
    } else if (DIC_PROP.PARAM_FROM[1].value === row.paramFrom) {
      let res = data.subFlowParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[0].value)
      let res2 = data.subFlowParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[2].value)
      splice.push(...res)
      splice.push(...res2)
    } else if (DIC_PROP.PARAM_FROM[2].value === row.paramFrom) {
      let res = data.subFlowParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[0].value)
      let res2 = data.subFlowParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[1].value)
      splice.push(...res)
      splice.push(...res2)
    }
    data.subFlowParams = splice
    methods.changeSubFlowParams()
  },
  validateSubFlowData() {
    // 兼容老版本
    let subFlowParams = props.currSelect.attributes.attrs.cdata.attrs.subFlowParams
    if (!subFlowParams) {
      props.currSelect.attributes.attrs.cdata.attrs.subFlowParams = []
    }
    let startSubMethod = props.currSelect.attributes.attrs.cdata.attrs.startSubMethod
    if (!startSubMethod) {
      props.currSelect.attributes.attrs.cdata.attrs.startSubMethod = 'POST'
    }
    let restartSubMethod = props.currSelect.attributes.attrs.cdata.attrs.restartSubMethod
    if (!restartSubMethod) {
      props.currSelect.attributes.attrs.cdata.attrs.restartSubMethod = 'PUT'
    }
    let backParMethod = props.currSelect.attributes.attrs.cdata.attrs.backParMethod
    if (!backParMethod) {
      props.currSelect.attributes.attrs.cdata.attrs.backParMethod = 'PUT'
    }

    data.subFlowParams = props.currSelect.attributes.attrs.cdata.attrs.subFlowParams
  },
  changeSubFlowParams() {
    props.currSelect.attributes.attrs.cdata.attrs.subFlowParams = data.subFlowParams
  },
  changeTabPane(val) {
    methods.validateSubFlowData()
    methods.listFormFieldPerms()
  }
}
// 监听双向绑定
watch(
  () => props.currSelect,
  (val) => {
    if (Object.keys(val).length === 0) {
      return
    }
    methods.changeTabPane(val)
  }
)
</script>

<style lang="scss">
@use '../assets/style/flow-attr.scss' as *;
</style>
