<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row v-show="showSearch">
        <el-form ref="queryRef" :model="state.queryForm" :inline="true" @keyup.enter="getDataList">
          <el-form-item :label="$t('runNode.flowInstId')" prop="flowInstId">
            <el-input
              v-model="state.queryForm.flowInstId"
              :placeholder="t('runNode.inputFlowInstIdTip')"
              style="max-width: 180px;"
              @change="cascadeChange('flowInstId', ['runNodeId'])"
            />
          </el-form-item>
          <el-form-item :label="$t('runNode.id')" prop="id">
            <el-select
              v-model="state.queryForm.id"
              :placeholder="t('runNode.inputIdTip')"
              clearable
              filterable
              style="max-width: 180px;"
            >
              <el-option
                v-for="(item, index) in cascadeDic.runNodeId"
                :key="index"
                :label="item.nodeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('runNode.status')" prop="status">
            <el-select
              v-model="state.queryForm.status"
              :placeholder="t('runNode.inputStatusTip')"
              clearable
              filterable
              style="max-width: 180px;"
            >
              <el-option
                v-for="(item, index) in DIC_PROP.NODE_STATUS"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="search" type="primary" @click="getDataList">
              {{ $t('common.queryBtn') }}
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">
              {{ $t('common.resetBtn') }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="mb-2" style="width: 100%;">
          <el-tooltip placement="top">
            <template #content>
              {{ $t('common.addBtn') }}
            </template>
            <el-button
              v-auth="'jsonflow_runnode_add'"
              icon="Plus"
              type="primary"
              class="ml10"
              @click="formDialogRef.openDialog('add')"
            />
          </el-tooltip>
          <el-tooltip placement="top">
            <template #content>
              {{ $t('common.delBtn') }}
            </template>
            <el-button
              v-auth="'jsonflow_runnode_del'"
              plain
              :disabled="multiple"
              icon="Delete"
              type="primary"
              class="ml10"
              @click="handleDelete(selectObjs)"
            />
          </el-tooltip>

          <right-toolbar
            v-model:show-search="showSearch"
            :export="'jsonflow_runnode_export'"
            class="ml10"
            style="float: right; margin-right: 20px;"
            @query-table="getDataList"
          />
        </div>
      </el-row>
      <el-table
        v-loading="state.loading"
        :data="state.dataList"
        style="width: 100%;"
        @selection-change="handleSelectionChange"
        @sort-change="sortChangeHandle"
      >
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" :label="t('runNode.index')" width="40" />
        <el-table-column prop="sort" :label="t('runNode.sort')" show-overflow-tooltip />
        <el-table-column prop="flowInstId" :label="t('runNode.flowInstId')" show-overflow-tooltip />
        <el-table-column prop="defFlowId" :label="t('runNode.defFlowId')" show-overflow-tooltip>
          <template #default="scope">
            <convert-name
              :options="state.dicData.flowInstId"
              :value="scope.row.defFlowId"
              :value-key="'defFlowId'"
              :show-key="'flowName'"
            />
          </template>
        </el-table-column>
        <el-table-column prop="nodeName" :label="t('runNode.nodeName')" show-overflow-tooltip />
        <el-table-column prop="startTime" :label="t('runNode.startTime')" show-overflow-tooltip />
        <el-table-column prop="endTime" :label="t('runNode.endTime')" show-overflow-tooltip />
        <el-table-column prop="nodeType" :label="t('runNode.nodeType')" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="DIC_PROP.NODE_TYPE" :value="scope.row.nodeType" />
          </template>
        </el-table-column>
        <el-table-column
          prop="subDefFlowId"
          :label="t('runNode.subDefFlowId')"
          show-overflow-tooltip
        >
          <template #default="scope">
            <convert-name
              :options="dicData.defFlowId"
              :value="scope.row.subDefFlowId"
              :value-key="'id'"
              :show-key="'flowName'"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="nodeApproveMethod"
          :label="t('runNode.nodeApproveMethod')"
          show-overflow-tooltip
        >
          <template #default="scope">
            <dict-tag
              :options="DIC_PROP.NODE_APPROVE_METHOD"
              :value="scope.row.nodeApproveMethod"
            />
          </template>
        </el-table-column>
        <!--                <el-table-column prop="rejectType" :label="t('runNode.rejectType')"  show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.REJECT_TYPE" :value="scope.row.rejectType"></dict-tag>
                    </template>
                </el-table-column>-->
        <el-table-column prop="timeout" :label="t('runNode.timeout')" show-overflow-tooltip />
        <el-table-column prop="status" :label="t('runNode.status')" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="DIC_PROP.NODE_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <!--                <el-table-column prop="isAutoNext" :label="t('runNode.isAutoNext')"  show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.YES_OR_NO" :value="scope.row.isAutoNext"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="isContinue" :label="t('runNode.isContinue')"  show-overflow-tooltip>
                    <template #default="scope">
                        <dict-tag :options="DIC_PROP.YES_OR_NO" :value="scope.row.isContinue"></dict-tag>
                    </template>
                </el-table-column>-->
        <el-table-column prop="suspension" :label="t('runNode.suspension')" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="DIC_PROP.YES_OR_NO" :value="scope.row.suspension" />
          </template>
        </el-table-column>
        <!--                <el-table-column prop="createUser" :label="t('runNode.createUser')"  show-overflow-tooltip>
                    <template #default="scope">
                        <convert-name :options="state.dicData.createUser" :value="scope.row.createUser"
                                      :valueKey="'userId'" :showKey="'name'"></convert-name>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" :label="t('runNode.createTime')"  show-overflow-tooltip/>-->
        <el-table-column :label="$t('common.action')" width="120">
          <template #default="scope">
            <el-tooltip placement="top">
              <template #content>
                {{ $t('common.viewBtn') }}
              </template>
              <el-button
                text
                type="primary"
                icon="view"
                @click="formDialogRef.openDialog('view', scope.row.id)"
              />
            </el-tooltip>
            <el-tooltip placement="top">
              <template #content>
                修改
              </template>
              <el-button
                icon="edit-pen"
                text
                type="primary"
                @click="formDialogRef.openDialog('edit', scope.row.id)"
              />
            </el-tooltip>
            <el-tooltip placement="top">
              <template #content>
                {{ $t('common.delBtn') }}
              </template>
              <el-button icon="delete" text type="primary" @click="handleDelete([scope.row.id])" />
            </el-tooltip>

            <el-tooltip v-if="scope.row.status === '0' || scope.row.status === '9'" placement="top">
              <template #content>
                催办节点
              </template>
              <el-button icon="Bell" text type="primary" @click="remind(scope.row, '0')" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-bind="state.pagination"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
  </div>
</template>

<script setup lang="ts" name="systemRunNode">
import { BasicTableProps, useTable } from '@/hooks/table'
import { fetchList, delObjs } from '@/api/jsonflow/run-node'
import { useMessage, useMessageBox } from '@/hooks/message'

import { useI18n } from 'vue-i18n'
import {
  onCascadeChange,
  onCascaded,
  onLoadDicUrl,
  onLoaded
} from '@/flow/components/convert-name/convert'
import * as runNode from '@/api/jsonflow/run-node'

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'))
const { t } = useI18n()
const { proxy } = getCurrentInstance()
// 定义查询字典
const dicData = reactive({})
const cascadeDic = reactive({})
const onLoad = onLoadDicUrl({ key: 'defFlowId' })
const onCascade = onCascadeChange(cascadeDic, { key: 'flowInstId', cascades: ['runNodeId'] })
onMounted(() => {
  onLoad(dicData)
})
function cascadeChange(key, cascades) {
  onCascade(state.queryForm, { key: key, cascades: cascades })
}
// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {},
  pageList: fetchList,
  onLoaded: onLoaded({ key: 'createUser' }, { key: 'flowInstId' }),
  descs: ['create_time']
})

//  table hook
const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
  // 清空搜索条件
  queryRef.value?.resetFields()
  // 清空多选
  selectObjs.value = []
  getDataList()
}

// 多选事件
const handleSelectionChange = (objs: any) => {
  selectObjs.value = objs.map(({ id }) => id)
  multiple.value = !objs.length
}

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm(t('common.delConfirmText'))
  } catch {
    return
  }

  try {
    await delObjs(ids)
    getDataList()
    proxy.$modal.msgSuccess(t('common.delSuccessText'))
  } catch (err) {
    proxy.$modal.msgError(err.msg)
  }
}

function remind(row, type) {
  row.remindType = type
  runNode.remind(row).then(() => {
    proxy.$modal.msgSuccess('催办成功')
  })
}
</script>
