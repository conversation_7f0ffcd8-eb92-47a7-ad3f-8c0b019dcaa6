import { deepClone } from "@/utils/index"

 const clazzAttr = {
	clazzes: [],
}

 const fieldsAttr = {
	formFieldPerms: [],
	formId: null,
}

 const nodeJobLinkAttr = {
	condGroups: [],
	httpMethod: null,
	httpParams: [],
}

 const subFlowAttr = {
	subFlowParams: [],
	startSubFlow: null,
	restartSubFlow: null,
	backParFlow: null,
	startSubMethod: null,
	restartSubMethod: null,
	backParMethod: null,
}

 const flowNodeAttr = {
	...deepClone(clazzAttr),
  ...deepClone(nodeJobLinkAttr),
	...deepClone(fieldsAttr),
	...deepClone(subFlowAttr),
}

 const commonAttr = {
	jobBtns: null,
	carbonCopy: null,
	approveMethod: "1",
  ticketCompRate: 100,
  routeKeyVal: null,
  valType: null,
	...deepClone(flowNodeAttr),
	pcTodoUrl: null,
	subDefFlowId: null,
	timeout: 0,
	sort: 1,
}

export const startAttr = {
	...deepClone(commonAttr),
	rejectType: "0",
}

export const syncNodeAttr = {
	...deepClone(commonAttr),
	nodeApproveMethod: "1",
	isAutoNext: "1",
	rejectType: "0",
	isContinue: "0",
	isAutoAudit: "0",
	isPassSame: "0",
}

export const nodeAttr = {
	description: "",
	...deepClone(syncNodeAttr),
}

export const gateAttr = {
	...deepClone(flowNodeAttr),
	nodeApproveMethod: "1",
	isAutoNext: "1",
	isAutoAudit: "1",
	isGateway: "1",
}

const baseJobAttr = {
  ...deepClone(nodeJobLinkAttr),
  valType: '-2',
	userKey: null,
	userKeyVal: null,
	isNowRun: "0",
	timeout: 0,
	isSkipRejected: "0",
  belongType: null,
	sort: 1,
}

export const jobAttr = {
	jobName: "节点任务",
	...deepClone(baseJobAttr),
	distFlowNodeId: null,
}

// currRunJobs
export const syncJobAttr = {
	...deepClone(baseJobAttr),
	userKeyValName: null,
	roleUserId: [],
}

export const endAttr = {
	...deepClone(commonAttr),
	isAutoAudit: "1",
}

export const virtualAttr = {
	...deepClone(commonAttr),
	rejectType: "1",
}

export const highAttr = {
	childFlowKey: null,
	childOrderId: null,
}

export const linkAttr = {
	  ...deepClone(nodeJobLinkAttr),
	valType: null,
	varKeyVal: null,
}

export const laneAttr = {
	id: null,
	nodeName: null,
}
