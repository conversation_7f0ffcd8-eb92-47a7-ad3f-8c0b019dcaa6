<template>
  <div>
    <el-input
      v-model="condition"
      placeholder="请输入关键字"
      clearable
      maxlength="100"
      suffix-icon="Search"
      @input="onQueryChanged"
      class="filter-input"
    >
      <slot name="input-prepend" :loading="loading"></slot>
    </el-input>
    <div v-loading="loading">
      <el-tree-v2
        class="user-tree"
        v-bind="treeCommonProps"
        :empty-text="loading.value ? '加载中...' : '暂无数据'"
        :data="treeData"
        :default-checked-keys="defaultCheckedKeys"
        :filter-method="filterTreeMethod"
        :check-strictly="true"
      >
        <template #default="{ node, data }">
          <span class="user-tree-label">{{ node.label }}</span>
        </template>
      </el-tree-v2>
    </div>
  </div>
</template>

<script setup>
import { getExcludeRolesInfosAPI } from "@/api/system/role"
const { proxy } = getCurrentInstance()
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 业务线类型（传入就会筛选）
  businessLineType: {
    default: null,
  },
  // 业务线id（传入就会筛选）
  businessLineId: {
    default: null,
  },
  // 是否只允许选择单个
  isOnlyOne: {
    type: Boolean,
    default: false,
  },
  // 所有数据
  allData: {
    type: Array,
    default: () => [],
  },
})
const treeRef = ref(null)
const loading = ref(true)

const treeProps = {
  value: "id",
  label: "name",
  children: "children",
  isLeaf: "isLeaf",
}
// 提取树组件公共配置
const treeCommonProps = {
  "node-key": "id",
  ref: treeRef,
  showCheckbox: true,
  props: treeProps,
  height: 350,
  "item-size": 30,
  "empty-text": loading.value ? "" : "暂无数据",
  onCheckChange: handleTreeCheckChange,
}

const condition = ref(null)
const treeData = ref([]) // 树节点数据

const defaultCheckedKeys = ref([])
const selectList = ref([])

const emit = defineEmits(["update:modelValue", "change"])

watch(
  () => props.businessLineType,
  (val) => {
    if (!props.businessLineId) {
      getRoleList()
    }
  }
)

watch(
  () => props.businessLineId,
  (val) => {
    getRoleList()
  }
)

// 赋值已选数据
const updateCheckedKeys = () => {
  nextTick(() => {
    selectList.value = [...props.modelValue]
    defaultCheckedKeys.value = selectList.value.map((item) => item.id)
    treeRef.value.setCheckedKeys(defaultCheckedKeys.value)
  })
}

watch(() => props.modelValue, updateCheckedKeys, { immediate: true, deep: true })

async function getRoleList() {
  const { object } = await getExcludeRolesInfosAPI({
    roleType: props.businessLineType ?? null,
    businessLineId: props.businessLineId,
  })
  treeData.value = transformRoleData(object || [])
  loading.value = false
}

function handleTreeCheckChange(data, checked) {
  if (
    props.isOnlyOne &&
    (selectList.value.length === 1 ||
      props.allData.some((type) => type.checkedItems.length))
  ) {
    proxy.$modal.msgWarning("当前只允许选择一个选项")
    treeRef.value.setChecked(data.id, false)
    return
  }
  const selectNodes = treeRef.value.getCheckedNodes()
  selectList.value = selectNodes
  emit("update:modelValue", selectList.value)
  emit("change", selectList.value)
}

function transformRoleData(arr) {
  return arr.map((item) => {
    return {
      ...item,
      rangeId: item.id,
      rangeName: item.name,
      rangeType: 2, // 角色标识---模版/标签需要
      type: "role",
      jobType: 1, // 角色标识---工作流需要
      children: transformRoleData(item.children || []),
    }
  })
}

function onQueryChanged(query) {
  treeRef.value?.filter(query)
  if (!query) {
    treeRef.value?.setExpandedKeys([])
  }
}

// 统一过滤逻辑
function matchKeyword(data, keyword) {
  if (!keyword) return true
  return [data.name].some((item) => item?.toLowerCase().includes(keyword.toLowerCase()))
}

// 过滤树
function filterTreeMethod(value, data) {
  return matchKeyword(data, value)
}

// 重置查询条件
function resetQuery() {
  condition.value = null
}

getRoleList()

defineExpose({
  resetQuery,
})
</script>

<style scoped lang="scss">
.filter-input {
  margin-bottom: 10px;
  :deep(.el-input__wrapper) {
    padding-left: 80px;
  }
}
</style>
