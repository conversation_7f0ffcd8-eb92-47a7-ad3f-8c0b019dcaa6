<template>
  <el-divider>请求头</el-divider>
  <el-table
    :data="data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[0].value)"
    border
    style="width: 100%; margin-bottom: 10px;"
    max-height="500"
  >
    <el-table-column type="index" label="操作" width="80">
      <template #header>
        <el-button
          icon="Plus"
          size="small"
          type="primary"
          circle
          @click="methods.onAddItem(DIC_PROP.PARAM_FROM[0].value, true)"
        />
      </template>
      <template #default="scope">
        <el-button
          icon="Minus"
          size="small"
          type="danger"
          circle
          @click="methods.handleHttpUrlDelete(scope.$index, scope.row)"
        />
      </template>
    </el-table-column>

    <el-table-column prop="targetProp" label="请求头" show-overflow-tooltip>
      <template #default="scope">
        <el-input
          v-model="scope.row.targetProp"
          :placeholder="
            scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value ? scope.row.varKeyVal : null
          "
          clearable
        />
      </template>
    </el-table-column>

    <el-table-column prop="paramValType" label="参数值类型" width="145px">
      <template #default="scope">
        <el-select v-model="scope.row.paramValType" clearable>
          <el-option
            v-for="(item, index) in DIC_PROP.PARAM_VAL_TYPE"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>
    </el-table-column>

    <el-table-column prop="varKeyVal" label="表单字段或固定值" show-overflow-tooltip>
      <template #default="scope">
        <el-select
          v-if="scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value"
          v-model="scope.row.varKeyVal"
          clearable
          filterable
        >
          <el-option-group
            v-for="(group, index) in data.allFieldPerms"
            :key="index"
            :label="group.label"
          >
            <el-option
              v-for="(item, index) in group.options"
              :key="index"
              :disabled="item.prop.indexOf('_define_') !== -1"
              :label="item.label"
              :value="item.prop"
            />
          </el-option-group>
        </el-select>

        <el-input v-else v-model="scope.row.varKeyVal" clearable />
      </template>
    </el-table-column>
  </el-table>

  <el-divider>请求参数</el-divider>

  <el-form-item label="参数类型 :">
    <el-radio-group v-model="data.paramType">
      <el-radio
        v-for="(item, index) in DIC_PROP.PARAM_TYPES"
        :key="index"
        :value="item.value"
        style="width: 56px;"
        @change="methods.handleParamType"
      >
        {{ item.label }}
      </el-radio>
    </el-radio-group>

    <el-tooltip placement="top">
      <template #content>表示以指定的【参数类型】传递【请求参数】</template>
      <el-icon style="margin-left: 20px;">
        <QuestionFilled />
      </el-icon>
    </el-tooltip>
  </el-form-item>

  <el-table
    :data="data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[1].value)"
    border
    style="width: 100%; margin-bottom: 10px;"
    max-height="500"
  >
    <el-table-column type="index" label="操作" width="80">
      <template #header>
        <el-button
          icon="Plus"
          size="small"
          type="primary"
          circle
          @click="methods.onAddItem(DIC_PROP.PARAM_FROM[1].value, true)"
        />
      </template>
      <template #default="scope">
        <el-button
          icon="Minus"
          size="small"
          type="danger"
          circle
          @click="methods.handleHttpUrlDelete(scope.$index, scope.row)"
        />
      </template>
    </el-table-column>

    <el-table-column prop="targetProp" label="请求参数" show-overflow-tooltip>
      <template #default="scope">
        <el-input
          v-model="scope.row.targetProp"
          :placeholder="
            scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value ? scope.row.varKeyVal : null
          "
          clearable
        />
      </template>
    </el-table-column>

    <el-table-column prop="paramValType" label="参数值类型" width="145px">
      <template #default="scope">
        <el-select v-model="scope.row.paramValType" clearable>
          <el-option
            v-for="(item, index) in DIC_PROP.PARAM_VAL_TYPE"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>
    </el-table-column>

    <el-table-column prop="varKeyVal" label="表单字段或固定值" show-overflow-tooltip>
      <template #default="scope">
        <el-select
          v-if="scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value"
          v-model="scope.row.varKeyVal"
          clearable
          filterable
        >
          <el-option-group
            v-for="(group, index) in data.allFieldPerms"
            :key="index"
            :label="group.label"
          >
            <el-option
              v-for="(item, index) in group.options"
              :key="index"
              :disabled="item.prop.indexOf('_define_') !== -1"
              :label="item.label"
              :value="item.prop"
            />
          </el-option-group>
        </el-select>

        <el-input v-else v-model="scope.row.varKeyVal" clearable />
      </template>
    </el-table-column>
  </el-table>

  <el-divider>返回值</el-divider>

  <div v-if="props.httpParamType === DIC_PROP.PARAM_RULE_TYPE[0].value" style="margin: 10px 13px;">
    <span style="font-size: 14px;">
      注: 当前Http请求的返回值可以为对象 或 数组，{{ PROP_CONST.TEXT_DESC.condMethodExplain5 }}
    </span>
  </div>

  <div v-if="props.httpParamType === DIC_PROP.PARAM_RULE_TYPE[8].value" style="margin: 10px 13px;">
    <span style="color: #409eff; font-size: 14px;">
      注: 当前Http请求的返回值可以为对象 或 数组，{{ PROP_CONST.TEXT_DESC.condMethodExplain6 }}
    </span>
  </div>

  <div v-if="props.httpParamType === DIC_PROP.PARAM_RULE_TYPE[1].value" style="margin: 10px 13px;">
    <span style="font-size: 14px;">
      注: 当前Http请求的返回值为字符串 1 ( 满足 ) 或 0 ( 不满足 )
    </span>
  </div>

  <el-table
    v-if="
      props.httpParamType !== DIC_PROP.PARAM_RULE_TYPE[0].value &&
      props.httpParamType !== DIC_PROP.PARAM_RULE_TYPE[1].value &&
      props.httpParamType !== DIC_PROP.PARAM_RULE_TYPE[8].value
    "
    :data="data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[2].value)"
    border
    style="width: 100%; margin-bottom: 10px;"
    max-height="500"
  >
    <el-table-column type="index" label="操作" width="80">
      <template #header>
        <el-button
          icon="Plus"
          size="small"
          type="primary"
          circle
          @click="methods.onAddItem(DIC_PROP.PARAM_FROM[2].value, true)"
        />
      </template>
      <template #default="scope">
        <el-button
          icon="Minus"
          size="small"
          type="danger"
          circle
          @click="methods.handleHttpUrlDelete(scope.$index, scope.row)"
        />
      </template>
    </el-table-column>

    <el-table-column prop="targetProp" label="返回值" show-overflow-tooltip>
      <template #default="scope">
        <el-input
          v-model="scope.row.targetProp"
          :placeholder="
            scope.row.paramValType === DIC_PROP.PARAM_VAL_TYPE[0].value ? scope.row.varKeyVal : null
          "
          clearable
        />
      </template>
    </el-table-column>

    <el-table-column prop="paramValType" label="参数值类型" width="145px">
      <template #default="scope">
        <el-switch
          v-model="scope.row.paramValType"
          :active-value="DIC_PROP.PARAM_VAL_TYPE[0].value"
          :active-text="DIC_PROP.PARAM_VAL_TYPE[0].label"
          :inactive-value="DIC_PROP.PARAM_VAL_TYPE[2].value"
          :inactive-text="DIC_PROP.PARAM_VAL_TYPE[2].label"
          inline-prompt
        />
      </template>
    </el-table-column>

    <el-table-column prop="varKeyVal" :label="表单字段" show-overflow-tooltip>
      <template #default="scope">
        <el-select v-model="scope.row.varKeyVal" clearable filterable>
          <el-option-group
            v-for="(group, index) in data.allFieldPerms"
            :key="index"
            :label="group.label"
          >
            <el-option
              v-for="(item, index) in group.options"
              :key="index"
              :disabled="item.prop.indexOf('_define_') !== -1"
              :label="item.label"
              :value="item.prop"
            />
          </el-option-group>
        </el-select>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts" name="FlowHttpParam">
import { useI18n } from 'vue-i18n'
const { proxy } = getCurrentInstance()
import { validateNull } from '@/utils/validate'
import { buildSysFieldsFormOption } from '../../utils/form-perm'
import { DIC_PROP } from '../../support/dict-prop'
import { PROP_CONST } from '../../support/prop-const'

const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null
  },
  httpParam: {
    type: Object,
    default: null
  },
  httpParamType: {
    type: String,
    default: null
  },
  flowData: {
    type: Object,
    default: null
  }
})

const data = reactive({
  allFieldPerms: [],
  formFieldPerms: [],
  httpParams: [],
  paramType: '0'
})

// 定义字典
onMounted(async () => {
  methods.changeTabPane(props.httpParam)
})

const methods = {
  initParamType() {
    let find = data.httpParams.find((f) => f.paramFrom === DIC_PROP.PARAM_FROM[1].value)
    if (find) {
      data.paramType = find.paramType
    }
  },
  handleParamType(val) {
    let splice = data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[1].value)
    splice.forEach((each) => (each.paramType = val))
    let res = data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[0].value)
    let res2 = data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[2].value)
    splice.push(...res)
    splice.push(...res2)
    data.httpParams = splice
    methods.changeHttpUrlParams()
  },
  listFormFieldPerms() {
    if (validateNull(data.formFieldPerms)) {
      buildSysFieldsFormOption(data, props)
    }
  },
  onAddItem(paramFrom, isAdd) {
    methods.listFormFieldPerms()
    let value = DIC_PROP.PARAM_VAL_TYPE[0].value
    if (data.httpParams.length > 0) {
      let find = data.httpParams
        .filter((f) => f.paramFrom === paramFrom)
        .find((f) => !f.varKeyVal || (f.paramValType !== value && !f.targetProp))
      if (find) {
        if (isAdd) {
          if (!find.varKeyVal) {
            proxy.$modal.msgWarning('请先填写 表单字段')
            return
          }
          if (find.paramValType !== value && !find.targetProp) {
            if (DIC_PROP.PARAM_FROM[0].value === paramFrom) {
              proxy.$modal.msgWarning('请先填写 请求头')
            } else if (DIC_PROP.PARAM_FROM[1].value === paramFrom) {
              proxy.$modal.msgWarning('请先填写 请求参数')
            } else {
              proxy.$modal.msgWarning('请先填写 返回值')
            }
            return
          }
        }
      }
      if (!isAdd) {
        data.httpParams.splice(0, data.httpParams.length)
      }
    }
    let obj = {
      paramFrom: paramFrom,
      varKeyVal: null,
      paramValType: value,
      targetProp: null
    }
    if (paramFrom === DIC_PROP.PARAM_FROM[1].value) {
      obj.paramType = data.paramType
    }
    data.httpParams.push(obj)
    methods.changeHttpUrlParams()
  },
  handleHttpUrlDelete(index: number, row: any) {
    let splice = data.httpParams.filter((f) => f.paramFrom === row.paramFrom)
    splice.splice(index, 1)
    if (DIC_PROP.PARAM_FROM[0].value === row.paramFrom) {
      let res = data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[1].value)
      let res2 = data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[2].value)
      splice.push(...res)
      splice.push(...res2)
    } else if (DIC_PROP.PARAM_FROM[1].value === row.paramFrom) {
      let res = data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[0].value)
      let res2 = data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[2].value)
      splice.push(...res)
      splice.push(...res2)
    } else if (DIC_PROP.PARAM_FROM[2].value === row.paramFrom) {
      let res = data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[0].value)
      let res2 = data.httpParams.filter((f) => f.paramFrom === DIC_PROP.PARAM_FROM[1].value)
      splice.push(...res)
      splice.push(...res2)
    }
    data.httpParams = splice
    methods.changeHttpUrlParams()
  },
  validateHttpUrlData() {
    // 兼容老版本
    let httpParams = props.httpParam.httpParams
    if (!httpParams) {
      props.httpParam.httpParams = []
    }
    data.httpParams = props.httpParam.httpParams
  },
  changeHttpUrlParams() {
    props.httpParam.httpParams = data.httpParams
  },
  changeTabPane(val) {
    methods.validateHttpUrlData()
    methods.listFormFieldPerms()
    methods.initParamType()
  }
}
// 监听双向绑定
watch(
  () => props.httpParam,
  (val) => {
    if (Object.keys(val).length === 0) {
      return
    }
    methods.changeTabPane(val)
  }
)

// 暴露变量
defineExpose({
  open: methods.open
})
</script>

<style lang="scss">
@use '../assets/style/flow-attr.scss' as *;
</style>
