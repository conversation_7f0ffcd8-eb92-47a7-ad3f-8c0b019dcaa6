export default {
	flowApplication: {
		index: "#",
		importflowApplicationTip: "导入办公申请",
		id: "主键id",
		flowKey: "流程KEY",
		icon: "表单图标",
		formName: "表单名称",
		groupName: "分组名称",
		tableName: "关联表名称",
		permission: "操作权限",
		remark: "表单备注",
		status: "状态",
		version: "版本",
		createUser: "创建人",
		createTime: "创建时间",
		defFlowId: "流程定义ID",
		formInfo: "表单信息",
		type: "表单类型",
		subTableName: "关联子表名称",
		subMainField: "关联主表列名",
		mainSubProp: "关联子表属性",
		sort: "排序值",
		updateUser: "修改人",
		updateTime: "修改时间",
		delFlag: "删除标",

		inputIdTip: "请输入主键id",
		inputFlowKeyTip: "请输入流程KEY",
		inputIconTip: "请输入表单图标",
		inputFormNameTip: "请输入表单名称",
		inputGroupNameTip: "请输入分组名称",
		inputTableNameTip: "请输入关联表名称",
		inputPermissionTip: "请选择操作角色, 默认所有人都可以发起该流程",
		inputRemarkTip: "请输入表单备注",
		inputStatusTip: "请输入状态",
		inputVersionTip: "请输入版本",
		inputCreateUserTip: "请输入创建人",
		inputCreateTimeTip: "请输入创建时间",
		inputDefFlowIdTip: "请输入流程定义ID",
		inputFormInfoTip: "请输入表单信息",
		inputTypeTip: "请选择表单类型",
		inputSubTableNameTip: "请选择关联子表名称",
		inputSubMainFieldTip: "请输入关联主表列名",
		inputMainSubPropTip: "请输入关联子表属性",
		inputSortTip: "请输入排序值",
		inputUpdateUserTip: "请输入修改人",
		inputUpdateTimeTip: "请输入修改时间",
		inputDelFlagTip: "请输入删除标",

	},
}
