<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="850px"
      :before-close="handleClose"
      top="5vh"
  >
    <div v-loading="loading">
      <el-form
          ref="formRef"
          :model="form"
          label-width="90px"
          :rules="rules"
          inline
          :disabled="formDisabled"
      >
        <el-form-item label="标签名称" prop="tagName" class="w-100%">
          <el-input
              v-model="form.tagName"
              placeholder="请输入标签名称"
              maxlength="50"
              clearable
          />
        </el-form-item>
        <br/>
        <el-form-item label="标签类型" prop="tagType">
          <dict-select
              v-model="form.tagType"
              :options="dictOptions.business_ascription"
          />
        </el-form-item>
        <el-form-item label="标签状态" prop="tagStatus">
          <dict-select
              v-model="form.tagStatus"
              :options="dictOptions.normal_disable"
              type="radio"
          />
        </el-form-item>
        <div class="divider-line"></div>
        <el-form-item
            v-if="!formDisabled"
            label="标签成员"
            label-width="75px"
            class="w-100%"
        >
          <el-button type="primary" plain @click="handleImport" class="absolute right-0">
            成员导入
          </el-button>
        </el-form-item>
        <div v-if="isNil(form.tagType)" class="type-empty">请先选择标签类型</div>
        <template v-if="!loading && !isNil(form.tagType)">
          <UserTreeTransfer
              v-if="!formDisabled"
              ref="userTreeTransferRef"
              :businessLineType="form.tagType"
              :select-types="['user']"
              v-model:users="form.userList"
          />
          <div v-if="formDisabled" class="selected-user-box">
            <div class="m-l-20px">标签成员 (共{{ form.userList.length }}名)</div>
            <TagUserList
                :list="form.userList"
                :show-remove="false"
                :itemSecondarySize="150"
                :gridItems="5"
                itemBg
                class="show-select-list"
            />
          </div>
        </template>
        <div class="divider-line mt-20px mb-10px!"></div>
        <el-form-item
            label="标签可见范围："
            v-if="!formDisabled"
            label-width="110px"
        ></el-form-item>
        <div v-if="isNil(form.tagType)" class="type-empty">请先选择标签类型</div>
        <div v-if="!formDisabled && !isNil(form.tagType)" class="mt-[-10px]">
          <UserTreeTransfer
              ref="rangeRef"
              :businessLineType="form.tagType"
              :itemSecondarySize="190"
              :rangeType="2"
              :gridItems="2"
              onlyRange
              :select-types="['dept', 'role']"
              v-model:ranges="form.rangeRefs"
          />
        </div>
        <div v-if="formDisabled" class="selected-user-box">
          <div class="m-l-20px">标签可见范围</div>
          <TagUserList
              :list="form.rangeRefs"
              :show-remove="false"
              :itemSecondarySize="190"
              :gridItems="4"
              itemBg
              class="show-select-list"
          />
        </div>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button
          type="success"
          :loading="loading"
          @click="submitForm('add')"
          v-if="!formDisabled"
      >
        保存并新建
      </el-button>
      <el-button
          type="primary"
          :loading="loading"
          @click="submitForm"
          v-if="!formDisabled"
      >保存
      </el-button
      >
    </template>

    <!-- 导入对话框 -->
    <ImportFile
        ref="importFileRef"
        :temp-url="essPrefix + '/tagCfgInfo/importTemplate'"
        :upload-url="essPrefix + '/tagCfgInfo/importMember'"
        title="成员导入"
        :show-update-support="false"
        :show-upload-success-msg="false"
        @change="importSuccess"
        :extraData="{ tagType: form.tagType }"
    />
  </el-dialog>
</template>

<script setup name="EditTagModal">
import { uniqBy, isNil } from "lodash-es"
import {
  addTagCfgInfo,
  updateTagCfgInfo,
  getTagCfgInfo,
} from "@/api/ess/baseSet/tagCfgInfo"
import { essPrefix } from "@/config/constant"
import { rule } from "@/utils/validate.js";
const UserTreeTransfer = defineAsyncComponent(() =>
  import("@/views/components/UserTreeTransfer/index.vue")
)
const TagUserList = defineAsyncComponent(() =>
  import("@/views/components/UserTreeTransfer/TagUserList.vue")
)

defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  dictOptions: {
    type: Object,
    default: () => ({}),
  },
})

const { proxy } = getCurrentInstance()

const title = ref("新增标签")
const dialogVisible = ref(false)
const loading = ref(false)
const formDisabled = ref(false) // 表单禁用
const formRef = ref(null)
const importFileRef = ref(null) // 导入ref
const userTreeTransferRef = ref(null) // 用户选择ref
const form = ref({
  tagName: "",
  userList: [],
  tagStatus: 0,
})
const rules = ref({
  tagName: [
    { required: true, message: "请输入标签名称", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  tagStatus: [{ required: true, message: "请选择标签状态", trigger: "change" }],
  tagType: [{ required: true, message: "请输入标签类型", trigger: "change" }],
})
const emit = defineEmits(["change", "refresh"])

const open = (item, disabled = false) => {
  dialogVisible.value = true
  if (item?.id) {
    getInfo(item)
  }
  formDisabled.value = disabled
  title.value = disabled ? "查看标签" : item?.id ? "编辑标签" : "新增标签"
}

const getInfo = async (item) => {
  try {
    loading.value = true
    let res = await getTagCfgInfo(item.id)
    form.value = {
      ...res.object,
      userList: res.object.userList || [],
      rangeRefs: res.object.rangeRefs || [],
    }
    loading.value = false
  } catch (e) {
    loading.value = false
  }
}
// 取消
const handleClose = () => {
  reset()
  dialogVisible.value = false
}

const reset = () => {
  proxy.resetForm("formRef")
  form.value = {
    tagName: "",
    userList: [],
  }
  userTreeTransferRef.value?.reset()
}

const getUserList = () => {
  // form.value.userList = userTreeTransferRef.value?.getSelectList() || []
}

// 确定
const submitForm = (type = "save") => {
  proxy.$refs["formRef"].validate(async (valid) => {
    if (!valid) {
      return
    }
    try {
      loading.value = true
      let actionUrl = form.value.id ? updateTagCfgInfo : addTagCfgInfo
      let res = await actionUrl(form.value)
      if (+res.code === 200) {
        handleSuccess(type)
      }
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  })
}

// 成功处理
const handleSuccess = (type) => {
  proxy.$modal.msgSuccess(form.value.id ? "修改成功" : "新增成功")
  if (type === "add") {
    reset()
    emit("change")
    title.value = "新增标签"
  } else {
    handleClose()
    form.value.id ? emit("change") : emit("refresh")
  }
}

// 导入
const handleImport = () => {
  importFileRef.value.handleImport()
}
// 导入成功
const importSuccess = (res) => {
  if (+res.code === 200) {
    res.object.errorMsg && proxy.$modal.msg(res.object.errorMsg)
    const users = res.object.tagUserRefDtos.map((item) => ({
      ...item,
      id: item.userId,
      name: item.nickName,
    }))
    form.value.userList = uniqBy([...form.value.userList, ...users], "userId")
  }
}
defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.type-empty {
  background: #f7f8f9;
  padding: 30px;
  border-radius: 4px;
  margin-left: 10px;
}

.show-select-list {
  height: 150px;
}
</style>
