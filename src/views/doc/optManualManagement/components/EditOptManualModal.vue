<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="850px"
      :before-close="handleClose"
      top="8vh"
  >
    <el-form ref="formRef" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="文档名称" prop="docName">
        <el-input
            v-model="form.docName"
            placeholder="请输入名称"
            maxlength="30"
            clearable
            show-word-limit
        />
      </el-form-item>
      <el-form-item label="文档内容" prop="fileUrl">
        <DocFileUpload v-model="form.fileUrl" v-model:fileName="form.fileName"/>
      </el-form-item>
      <el-form-item label="所属端" prop="belongType">
        <dict-select
            v-model="form.belongType"
            :options="dictOptions.home_app_type"
            type="checkbox"
            valueType="string"
        />
      </el-form-item>
      <el-col :span="9">
        <el-form-item label="状态" prop="status">
          <dict-select
              v-model="form.status"
              :options="dictOptions.sys_normal_disable"
              valueType="string"
          />
        </el-form-item>
      </el-col>
      <el-col :span="9">
        <el-form-item label="版本号" prop="version">
          <el-input
              v-model="form.version"
              placeholder="请输入版本号"
              clearable
              :maxlength="100"
          />
        </el-form-item>
      </el-col>
      <el-form-item prop="vis" label-position="top">
        <div class="selected-user-box">
          <h4>可见范围</h4>
          <UserTreeTransfer
              ref="userTreeTransferRef"
              :itemSecondarySize="190"
              :rangeType="2"
              :gridItems="2"
              onlyRange
              :select-types="['role']"
              v-model:ranges="form.vis"
          />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 取消</el-button>
      <el-button type="success" :loading="loading" @click="submitAndAdd">
        保存并新建
      </el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  addManualCfgInfo,
  editManualCfgInfo,
  detailManualCfgInfo,
} from "@/api/ess/doc/manualCfgInfo"
import { deleteMinio } from "@/api/file/file"
import UserTreeTransfer from "@/views/components/UserTreeTransfer/index.vue"
import { rule } from "@/utils/validate.js";

const DocFileUpload = defineAsyncComponent(() =>
    import("@/views/doc/components/DocFileUpload.vue")
)

const { proxy } = getCurrentInstance()

const props = defineProps({
  dictOptions: Object,
})

const title = ref("新增")
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)

const form = ref({
  docName: null,
  fileName: null,
  version: null,
  status: null,
  belongType: [],
  vis: [],
})
const userTreeTransferRef = ref(null)

const rules = ref({
  docName: [
    { required: true, message: "请输入文档名称", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  fileUrl: [{ required: true, message: "请上传文档", trigger: "change" }],
  version: [{ required: true, message: "请输入版本号", trigger: "change" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  belongType: [{ required: true, message: "请选择所属端", trigger: "change" }],
  vis: [{ required: true, message: "请选择可见范围", trigger: "change" }],
})
const emit = defineEmits(["change"])

const open = (item) => {
  dialogVisible.value = true
  nextTick(() => {
    if (item?.id) {
      form.value = { ...item, oldUrl: item.fileUrl }
      getDetail(item)
    }
  })
  title.value = item?.id ? "编辑" : "新增"
}

// 获取详情
const getDetail = (item) => {
  detailManualCfgInfo(item.id).then((res) => {
    form.value = { ...form, ...res.object, oldUrl: res.object.fileUrl }
  })
}

// 取消
const handleClose = () => {
  proxy.resetForm("formRef")
  form.value = {
    fileName: "",
    vis: [],
  }
  userTreeTransferRef.value?.reset()
  dialogVisible.value = false
}

// 保存并新建
const submitAndAdd = () => {
  submitForm("add")
}
// 确定
const submitForm = (type = "save") => {
  console.log(form.value)
  proxy.$refs["formRef"].validate(async (valid) => {
    if (!valid) {
      return
    }

    loading.value = true

    // 防止循环序列化报错
    const submitData = {
      docName: form.value.docName,
      fileName: form.value.fileName,
      version: form.value.version,
      status: form.value.status,
      belongType: form.value.belongType,
      fileUrl: form.value.fileUrl,
      vis: form.value.vis,
    }
    if (form.value.id) {
      submitData.id = form.value.id
    }

    const actionUrl = form.value.id ? editManualCfgInfo : addManualCfgInfo
    if (form.value.id && form.value.fileUrl !== form.value.oldUrl) {
      deleteOldFile() // 删除旧文件
    }
    try {
      const res = await actionUrl(submitData)
      if (+res.code === 200) {
        handleSuccess(type)
      }
    } catch (error) {
      console.error("提交表单时出错:", error)
    } finally {
      loading.value = false
    }
  })
}

const handleSuccess = (type) => {
  const message = form.value.id ? "修改成功" : "新增成功"
  proxy.$modal.msgSuccess(message)
  // 保存并新增
  if (type === "add") {
    title.value = "新增"
    proxy.resetForm("formRef")
    form.value.id = null
    emit("change")
  } else {
    handleClose()
    form.value.id ? emit("change") : emit("refresh")
  }
}

const deleteOldFile = async () => {
  try {
    await deleteMinio({ filename: form.value.oldUrl })
  } catch (err) {
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.selected-user-box {
  @apply mt-5px;
  width: 100%;
  border-top: 1px dashed #f1f1f1;

  h4 {
    margin: 10px 0;
  }
}
</style>
