<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form
					ref="queryRef"
					:model="state.queryForm"
					:inline="true"
					@keyup.enter="getDataList"
				>
					<el-form-item :label="$t('distPerson.delRoleId')" prop="delRoleId">
						<el-select
							v-model="state.queryForm.delRoleId"
							:placeholder="t('distPerson.inputDelRoleIdTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.users"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('distPerson.recRoleId')" prop="roleId">
						<el-select
							v-model="state.queryForm.roleId"
							:placeholder="t('distPerson.inputRecRoleIdTip')"
							clearable
							filterable
							style="max-width: 180px"
						>
							<el-option
								v-for="(item, index) in dicData.users"
								:key="index"
								:label="item.name"
								:value="item.userId"
							/>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							{{ $t('common.queryBtn') }}
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">
							{{ $t('common.resetBtn') }}
						</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb-2" style="width: 100%">
					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.addBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_distperson_add'"
							icon="Plus"
							type="primary"
							class="ml10"
							@click="formDialogRef.openDialog('add')"
						/>
					</el-tooltip>
					<el-tooltip placement="top">
						<template #content>
							{{ $t('common.delBtn') }}
						</template>
						<el-button
							v-auth="'jsonflow_distperson_del'"
							plain
							:disabled="multiple"
							icon="Delete"
							type="primary"
							class="ml10"
							@click="handleDelete(selectObjs)"
						/>
					</el-tooltip>

					<right-toolbar
						v-model:show-search="showSearch"
						:export="'jsonflow_distperson_export'"
						class="ml10"
						style="float: right;margin-right: 20px"
						@export-excel="exportExcel"
						@query-table="getDataList"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				style="width: 100%"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" :label="t('distPerson.index')" width="40" />

				<el-table-column prop="delRoleId" :label="t('distPerson.delRoleId')">
					<template #default="scope">
						<convert-name
							:options="dicData.users"
							:value="scope.row.delRoleId"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>

				<el-table-column prop="roleId" :label="t('distPerson.recRoleId')">
					<template #default="scope">
						<convert-name
							:options="dicData.users"
							:value="scope.row.roleId"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>

				<el-table-column prop="delStartTime" :label="t('distPerson.delStartTime')" show-overflow-tooltip />
				<el-table-column prop="delEndTime" :label="t('distPerson.delEndTime')" show-overflow-tooltip />
				<el-table-column prop="delStatus" :label="t('distPerson.delStatus')" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="DIC_PROP.DELEGATE_STATUS" :value="scope.row.delStatus" />
					</template>
				</el-table-column>

				<el-table-column prop="createUser" :label="t('distPerson.createUser')" show-overflow-tooltip>
					<template #default="scope">
						<convert-name
							:options="state.dicData.createUser"
							:value="scope.row.createUser"
							:value-key="'userId'"
							:show-key="'name'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" :label="t('distPerson.createTime')" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="100">
					<template #default="scope">
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.viewBtn') }}
							</template>
							<el-button
								text
								type="primary"
								icon="view"
								@click="formDialogRef.openDialog('view', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								修改
							</template>
							<el-button
								icon="edit-pen"
								text
								type="primary"
								@click="formDialogRef.openDialog('edit', scope.row.id)"
							/>
						</el-tooltip>
						<el-tooltip placement="top">
							<template #content>
								{{ $t('common.delBtn') }}
							</template>
							<el-button
								icon="delete"
								text
								type="primary"
								@click="handleDelete([scope.row.id])"
							/>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemDelPerson">
import { BasicTableProps, useTable } from "@/hooks/table"
import { delegatePage, delObjs } from "@/api/jsonflow/dist-person"
import { useMessage, useMessageBox } from "@/hooks/message"

import { useI18n } from "vue-i18n"
import { onLoadDicUrl, onLoaded } from "@/flow/components/convert-name/convert"

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./del-form.vue"))
const { t } = useI18n()
// 定义查询字典
const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "users" })
onMounted(() => {
	onLoad(dicData)
})
// 定义变量内容
const formDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: delegatePage,
	onLoaded: onLoaded({ key: "createUser" }),
	descs: ["create_time"],
})

//  table hook
const {
	getDataList,
	currentChangeHandle,
	sizeChangeHandle,
	sortChangeHandle,
	downBlobFile,
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields()
	// 清空多选
	selectObjs.value = []
	getDataList()
}

// 导出excel
const exportExcel = () => {
	downBlobFile("/jsonflow/dist-person/export", state.queryForm, "dist-person.xlsx")
}

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map(({ id }) => id)
	multiple.value = !objs.length
}

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t("common.delConfirmText"))
	}
	catch {
		return
	}

	try {
		await delObjs(ids)
		getDataList()
		proxy.$modal.msgSuccess(t("common.delSuccessText"))
	}
	catch (err: any) {
		proxy.$modal.msgError(err.msg)
	}
}
</script>
