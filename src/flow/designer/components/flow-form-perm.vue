<template>
  <el-form
    v-if="methods.validateNode(props.currSelect)"
    label-position="left"
    class="flow-config-attr"
    label-width="130px"
  >
    <el-form-item label="全部只读或可编辑" prop="formPermType">
      <el-tooltip placement="top">
        <template #content>
          1、对当前节点表单的字段全部只读 或 全部可编辑。默认全部可编辑
          <br />
          2、可在下方进一步约束权限，如这里选择全部只读，下方配置某些字段可编辑
        </template>
        <el-radio-group
          v-model="props.currSelect.attributes.attrs.cdata.attrs.formPermType"
        >
          <el-radio
            v-for="(item, index) in DIC_PROP.ALL_FORM_PERM_TYPE"
            :key="index"
            :value="item.value"
            style="width: 135px"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-tooltip>
      <el-tooltip
        placement="top"
        v-if="props.currSelect.attributes.attrs.cdata.attrs.formPermType"
      >
        <template #content>清空后可在下方单个字段配置权限或业务侧自行判断</template>
        <el-button
          text
          type="primary"
          icon="delete"
          @click="props.currSelect.attributes.attrs.cdata.attrs.formPermType = null"
        >
          清空
        </el-button>
      </el-tooltip>
    </el-form-item>
    <el-divider>请选择表单名称</el-divider>
    <el-form-item label="PC待办页面">
      <el-tooltip placement="top">
        <template #content>
          若下拉选项为空或没有期望的表单，请先在节点属性-PC待办页面选择
        </template>
        <el-select
          v-model="props.currSelect.attributes.attrs.cdata.attrs.formId"
          class="input-attr"
          clearable
          filterable
          placeholder="请选择节点属性-PC待办页面"
          @change="methods.changeNodeFormId"
        >
          <template v-for="(item, index) in props.formIds">
            <el-option
              v-if="methods.filterNodeFormId(item)"
              :key="index"
              :label="item.formName + ' V' + item.version"
              :value="item.id"
            />
          </template>
        </el-select>
      </el-tooltip>
      <el-button
        v-if="props.isShowAdd"
        type="primary"
        round
        style="margin-left: 10px"
        @click="methods.addFormPermission"
      >
        新增字段
      </el-button>
      <el-button
        v-if="!props.isShowAdd"
        type="primary"
        size="small"
        round
        style="margin-left: 10px"
        @click="methods.resetFormPerm"
      >
        重置权限
      </el-button>
    </el-form-item>

    <el-divider>参与者可以操作表单字段权限</el-divider>
    <el-empty
      v-if="validateNull(data.formFieldPerms)"
      description="表单字段权限列表为空"
      style="margin: 10px 230px"
    />
    <el-form-item
      v-for="(item, index) in data.formFieldPerms"
      v-else
      :key="index"
      :label="item.label"
    >
      <el-input
        v-if="props.isShowAdd"
        v-model="item.label"
        style="width: 131px; margin-right: 10px"
      />
      <el-input
        v-if="props.isShowAdd"
        v-model="item.prop"
        style="width: 131px; margin-right: 10px"
      />
      <el-radio-group v-model="item.permType">
        <el-radio
          v-for="(item, index) in DIC_PROP.FORM_PERM_TYPE"
          :key="index"
          :value="item.value"
          :disabled="
            props.currSelect.attributes.attrs.cdata.attrs.formPermType === item.value
          "
          style="width: 86px"
        >
          {{ item.label }}
        </el-radio>
      </el-radio-group>
      <el-button
        type="primary"
        size="small"
        round
        @click.prevent="methods.removeFormPermission(item.prop)"
      >
        删除
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts" name="FlowFormPerm">
const { proxy } = getCurrentInstance()
import { validateNull } from "@/utils/validate"
import { parseWithFunctions, validateRunFlow } from "../../index"
import { buildFieldPerms, handleFormFieldPerms } from "../../utils/form-perm"
import { CommonNodeType, HighNodeType } from "../config/type"
import { listFormOption } from "@/api/jsonflow/form-option"
import { DIC_PROP } from "../../support/dict-prop"
import { deepClone } from "@/utils/index"

const props = defineProps({
  currFlowForm: {
    type: Object,
    default: null,
  },
  currSelect: {
    type: Object,
    default: null,
  },
  flowData: {
    type: Object,
    default: null,
  },
  formIds: {
    type: Array,
    default: null,
  },
  isShowAdd: {
    type: Boolean,
    default: false,
  },
})

const data = reactive({
  formFieldPerms: [],
})

onMounted(() => {
  methods.changeTabPane(props.currSelect)
})

const methods = {
  validateCurrSelectAttrs(currSelect?) {
    if (!currSelect) {
      currSelect = props.currSelect
    }
    if (validateNull(currSelect.attributes)) {
      return false
    }
    return true
  },
  validateNode(currSelect) {
    if (!methods.validateCurrSelectAttrs()) {
      return false
    }
    let type = currSelect.attributes.attrs.cdata.type
    return (
      type === CommonNodeType.START ||
      type === CommonNodeType.END ||
      type === CommonNodeType.SERIAL ||
      type === CommonNodeType.PARALLEL ||
      type === HighNodeType.VIRTUAL
    )
  },
  filterNodeFormId(item) {
    let pcTodoUrl = props.currSelect.attributes.attrs.cdata.attrs.pcTodoUrl
    if (!validateNull(pcTodoUrl)) {
      return pcTodoUrl.includes(item.id)
    } else {
      return false
    }
  },
  changeNodeFormId(value) {
    if (!methods.validateNode(props.currSelect)) return
    if (!value) {
      methods.clearPermData()
      return
    }
    data.formFieldPerms = []
    let find = props.formIds.find((f) => f.id === value)
    if (validateNull(find.formInfo)) {
      methods.handleFormFieldPerms(find)
    } else {
      let formInfo = parseWithFunctions(find.formInfo, true)
      buildFieldPerms(data.formFieldPerms, formInfo.widgetList)
    }
    methods.setNodeAttrsFormFieldPerms()
  },
  clearPermData() {
    data.formFieldPerms = []
    delete props.currSelect.attributes.attrs.cdata.attrs.formFieldPerms
  },
  handleFormFieldPerms(find) {
    // 无需前缀
    let isReturn = handleFormFieldPerms(data, $message, find, "")
    if (isReturn) return
    if (find.isCurrForm === "1" && !validateNull(find.formFieldPerms)) {
      methods.buildCustomFormPerm(find.formFieldPerms)
      return
    }
    // 已配置或全部字段
    methods.handleCustomFormPerm(
      find.id,
      null,
      props.flowData.attrs.id,
      props.currSelect.id
    )
  },
  initNodeFormId() {
    if (Object.keys(props.currSelect).length === 0) return
    if (!methods.validateNode(props.currSelect)) return
    let formId = props.currSelect.attributes.attrs.cdata.attrs.formId
    if (!formId) {
      methods.clearPermData()
      return
    }
    // formId必存在权限
    data.formFieldPerms = props.currSelect.attributes.attrs.cdata.attrs.formFieldPerms
  },
  removeFormPermission(prop) {
    if (data.formFieldPerms.length === 0) return
    data.formFieldPerms = data.formFieldPerms.filter((f) => f.prop !== prop)
    methods.setNodeAttrsFormFieldPerms()
  },
  setNodeAttrsFormFieldPerms() {
    // 相同引用
    props.currSelect.attributes.attrs.cdata.attrs.formFieldPerms = data.formFieldPerms
  },
  addFormPermission() {
    let formId = props.currSelect.attributes.attrs.cdata.attrs.formId
    if (!formId) {
      proxy.$modal.msgWarning("请先选择表单名称")
      return
    }
    if (!data.formFieldPerms) {
      data.formFieldPerms = []
      props.currSelect.attributes.attrs.cdata.attrs.formFieldPerms = data.formFieldPerms
    }
    let number = data.formFieldPerms.length + 1
    data.formFieldPerms.unshift({
      prop: "propName" + number,
      label: "请输入字段名称",
    })
  },
  handleCustomFormPerm(formId, type, defFlowId, flowNodeId) {
    if (!type) type = DIC_PROP.FORM_DATA_TYPE[1].value
    // 判断流程实例独立配置
    let flowInstId = validateRunFlow(props)
    listFormOption({
      type: type,
      formType: DIC_PROP.FORM_TYPE[1].value,
      formId: formId,
      flowInstId: flowInstId,
      defFlowId: defFlowId,
      flowNodeId: flowNodeId,
    })
      .then((resp) => {
        methods.buildCustomFormPerm(resp.object)
      })
      .catch(() => {
        proxy.$modal.msgError("获取系统表单字段权限失败")
      })
  },
  buildCustomFormPerm(formFieldPerms) {
    if (!validateNull(formFieldPerms)) {
      // 不影响表单设计信息
      data.formFieldPerms = deepClone(formFieldPerms)
      methods.setNodeAttrsFormFieldPerms()
    } else {
      methods.clearPermData()
      proxy.$modal.msgWarning("当前选择的系统表单无字段信息，请先在表单设计中录入")
    }
  },
  resetFormPerm() {
    let formId = props.currSelect.attributes.attrs.cdata.attrs.formId
    if (!formId) {
      proxy.$modal.msgWarning("请先选择表单名称")
      return
    }
    methods.clearPermData()
    let find = props.formIds.find((f) => f.id === formId)
    if (find.type === DIC_PROP.FORM_TYPE[1].value) {
      if (find.isCurrForm === "1") {
        methods.buildCustomFormPerm(find.formFieldPerms)
      } else {
        methods.handleCustomFormPerm(formId, DIC_PROP.FORM_DATA_TYPE[0].value, null, null)
      }
    } else {
      methods.changeNodeFormId(formId)
    }
  },
  changeTabPane(val) {
    methods.initNodeFormId()
  },
}
// 监听双向绑定
watch(
  () => props.currSelect,
  (val) => {
    if (Object.keys(val).length === 0) {
      return
    }
    methods.changeTabPane(val)
  }
)
</script>

<style lang="scss">
@use '../assets/style/flow-attr.scss' as *;
</style>
