<template>
	<div>
		<el-dialog
			v-if="data.showRoleUsers"
			v-model="data.showRoleUsers"
			top="20px"
			width="70%"
			title="参与者候选人员"
			append-to-body
		>
      <CustomTable
          ref="customTableRef"
          :has-toolbar="false"
          :data="data.dataList"
          :loading="loading"
          noPadding
        >
          <vxe-column type="seq" width="60" />
          <vxe-column title="工号" field="workNumber" show-overflow />
          <vxe-column title="用户名" field="userName" show-overflow />
          <vxe-column title="姓名" field="realNameUser" show-overflow />
          <vxe-column title="手机号" field="phonenumber" show-overflow />
        </CustomTable>
		</el-dialog>

		<template v-for="(item, index) in values">
			<template v-if="!validateNull(props.options) && validateOptionsNotNull()">
				<span v-if="!props.elTagType && !validateJobRoleType(item)" :key="index" :class="props.elTagClass">
					{{ showKeyNameByJobType(item, values , index) }}</span>
				<el-tooltip
					v-if="!props.elTagType && validateJobRoleType(item)"
					:key="index"
					content="点击可查看参与者具体的人员信息"
					placement="top"
				>
					<el-link type="primary" @click="handleShowJobRoleUsers(item)">
						{{ showKeyNameByJobType(item, values , index) }}
					</el-link>
				</el-tooltip>
				<el-tag
					v-if="props.elTagType"
					:key="index * 2"
					:disable-transitions="true"
					:index="index"
					:type="props.elTagType === 'primary' ? null : props.elTagType"
					:class="props.elTagClass"
				>
					{{ showKeyNameByJobType(item, values, index) }}
				</el-tag>
			</template>

			<template v-else-if="validateNull(props.options) && item.jobType">
				<span v-if="!props.elTagType && !validateJobRoleType(item)" :key="index" :class="props.elTagClass">
					{{ showRoleNameByJobType(item, values , index) }}</span>
				<el-tooltip
					v-if="!props.elTagType && validateJobRoleType(item)"
					:key="index"
					content="点击可查看参与者具体的人员信息"
					placement="top"
				>
					<el-link type="primary" @click="handleShowJobRoleUsers(item)">
						{{ showRoleNameByJobType(item, values , index) }}
					</el-link>
				</el-tooltip>
				<el-tag
					v-if="props.elTagType"
					:key="index * 2"
					:disable-transitions="true"
					:index="index"
					:type="props.elTagType === 'primary' ? null : props.elTagType"
					:class="props.elTagClass"
				>
					{{ showRoleNameByJobType(item, values, index) }}
				</el-tag>
			</template>
		</template>
	</div>
</template>

<script setup lang="ts" name="ConvertRoleName">
import { computed } from "vue"
import { validateNull } from "@/utils/validate"
import { DIC_PROP } from "@/flow/support/dict-prop"
import { handleShowNameByJobType, handleShowRoleNameByJobType } from "@/flow"
import { listUserByFlowInst } from "@/api/jsonflow/common"

const props = defineProps({
	// 数据
	options: {
		type: Object as any,
		default: {},
	},
	// 当前的值
	value: [Number, String, Object, Array],
	// type
	elTagType: String,
	// class
	elTagClass: String,
	isJobType: String,
})
const loading = ref(false)
const data = reactive({
	showRoleUsers: false,
	dataList: [],
})

const values = computed(() => {
	if (props.value !== null && typeof props.value !== "undefined") {
		if (Array.isArray(props.value)) { return props.value }
		else if (typeof props.value === "object") {
			return [props.value]
		}
		return [String(props.value)]
	}
	else {
		return []
	}
})

function validateOptionsNotNull() {
	return props.options.users && props.options.roles && props.options.posts && props.options.depts
}

function showRoleNameByJobType(role, values, index) {
	if (role.roleName) {
		let name = handleShowRoleNameByJobType(role, props.isJobType)
		return name + (index !== (values.length - 1) ? "," : "")
	}
	else {
		showNameByJobType(role, values, index)
	}
}

function showNameByJobType(role, values, index) {
	let name = handleShowNameByJobType(role, props.isJobType)
  console.log(role)
	return name + (index !== (values.length - 1) ? "," : "")
}

function validateExist(role) {
	let exist = {}
	if (role.jobType === DIC_PROP.JOB_USER_TYPE[0].value) { // 用户
		exist = props.options.users.find(s => s.userId === role.roleId)
	}
	else if (role.jobType === DIC_PROP.JOB_USER_TYPE[1].value) { // 角色
		exist = props.options.roles.find(s => s.roleId === role.roleId)
	}
	else if (role.jobType === DIC_PROP.JOB_USER_TYPE[2].value) { // 部门
		exist = props.options.depts.find(s => s.deptId === role.roleId)
	}
	exist.jobType = role.jobType
	return exist
}

function handleShowJobRoleUsers(item) {
	if (!item.roleId || !item.jobType) { return }
  loading.value = true  
	data.showRoleUsers = true
	listUserByFlowInst(item.roleId, item.jobType, item.flowInstId).then(resp => {
		data.dataList = resp.object
	}).finally(() => {
    loading.value = false
  })
}

function validateJobRoleType(item) {
	let isRoleType = item.jobType !== DIC_PROP.JOB_USER_NONE_TYPE[0].value && item.jobType !== DIC_PROP.JOB_USER_NONE_TYPE[1].value
	return isRoleType && item.roleId
}

function showKeyNameByJobType(role, values, index) {
	let exist = validateExist(role)
  console.log(exist,values,index)
	return showNameByJobType(exist, values, index)
}

</script>

<style scoped>
    .el-tag + .el-tag {
        margin-left: 10px;
    }
</style>
