<template>
  <div class="file-preview-container">
    <!-- PDF预览 -->
    <VueOfficePdf class="h-100%" v-if="isPdf" :src="fileUrl" />

    <!-- Word文档预览 -->
    <VueOfficeDocx class="h-100%" v-if="isDoc" :src="fileUrl" />

    <!-- Excel预览 -->
    <VueOfficeExcel class="h-100%" v-if="isExcel" :src="fileUrl" />

    <!-- 图片预览 -->
    <el-image
      v-if="isImage"
      class="image-preview"
      fit="contain"
      :src="fileUrl"
      :preview-src="[fileUrl]"
    />

    <!-- HTML预览 -->
    <iframe v-if="isHtml" :src="fileUrl" class="html-preview"></iframe>
  </div>
</template>

<script setup>
import { imageApi } from "@/config/constant"
import VueOfficePdf from "@vue-office/pdf"
import VueOfficeDocx from "@vue-office/docx"
import VueOfficeExcel from "@vue-office/excel"

const props = defineProps({
  fileRaw: {
    type: Object,
    default: () => ({}),
  },
  previewUrl: {
    type: String,
  },
})

// 文件URL
const fileUrl = computed(() => {
  if (props.fileRaw?.raw instanceof Blob) {
    return URL.createObjectURL(props.fileRaw.raw)
  }
  // 如果是字符串URL，直接返回
  if (typeof props.fileRaw?.raw === "string") {
    return props.fileRaw.raw
  }
  if (props.previewUrl) {
    return imageApi + props.previewUrl
  }
  return ""
})

// 文件类型判断
const fileType = computed(() => {
  let name = props.fileRaw?.name || props.previewUrl || ""
  name = name.replace(/[?#].*$/, "")
  return name.split(".").pop().toLowerCase()
})

const isPdf = computed(() => fileType.value === "pdf")
const isDoc = computed(() => ["doc", "docx"].includes(fileType.value))
const isExcel = computed(() => ["xls", "xlsx"].includes(fileType.value))
const isImage = computed(() => ["jpg", "jpeg", "png"].includes(fileType.value))
const isHtml = computed(() => fileType.value === "html")
</script>

<style scoped lang="scss">
.file-preview-container {
  width: 100%;
  height: 95%;
  overflow: auto;

  :deep(.vue-office-pdf-wrapper){
    background: #FFF !important;
  }

  .image-preview {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .html-preview {
    width: 100%;
    height: 100%;
    border: none;
  }
  :deep(.docx-wrapper) {
    width: 100%;
    & > section.docx {
      max-width: 100%;
    }
  }
}
</style>
