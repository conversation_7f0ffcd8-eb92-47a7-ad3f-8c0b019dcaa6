<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      label-width="68px"
      class="white-form-box"
    >
      <el-form-item label="角色编号" prop="roleKey">
        <el-input
          v-model="queryParams.roleKey"
          placeholder="请输入角色编号"
          clearable
          maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入角色名称"
          clearable
          maxlength="100"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <dict-select v-model="queryParams.status" :options="normal_disable" />
      </el-form-item>
      <el-form-item label="创建时间" style="width: 280px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <!-- <div class="table-handle-box">
      <div>
        <el-button type="primary" icon="Plus" @click="handleAdd" v-auths="['system:role:add']">
          新增
        </el-button>
        <el-button
          type="primary"
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-auths="['system:role:edit']"
        >
          修改
        </el-button>
        <el-button
          type="danger"
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-auths="['system:role:remove']"
        >
          删除
        </el-button>
      </div>
      <el-button
        type="primary"
        icon="Refresh"
        plain
        @click="handleDataSync"
        v-auths="['system:role:dataSync']"
      >
        数据同步
      </el-button>
    </div> -->

    <CustomTable
      ref="customTableRef"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      :data="roleList"
      custom
      :loading="loading"
      :total="total"
      :row-config="{
        keyField: 'roleId',
        isHover: true,
      }"
      :tree-config="{
        childrenField: 'children',
        rowField: 'roleId',
        parentField: 'parentId',
        reserve: true,
      }"
      @reload="getList"
      :row-class-name="rowClassName"
    >
      <template #actions>
        <el-button v-auths="['system:role:export']" type="primary" @click="handleExport">
          导出
        </el-button>
        <el-button plain type="primary" class="is-deep" @click="toggleExpandAll">
          展开/折叠
        </el-button>
      </template>
      <template #toolbar>
        <el-button
          v-auths="['system:role:dataSync']"
          type="primary"
          icon="Refresh"
          class="is-trans"
          plain
          @click="handleDataSync"
          :loading="syncUserLoading"
        >
          {{ syncUserLoading ? "同步中" : "数据同步" }}
        </el-button>
      </template>

      <vxe-column type="seq" width="60" fixed="left" />
      <vxe-column field="node" title="" tree-node width="35" />
      <vxe-column field="roleKey" title="角色编号" min-width="210" />
      <vxe-column field="roleName" title="角色名称" min-width="100" show-overflow />
      <vxe-column title="状态" width="100">
        <template #default="scope">
          <dict-tag :options="normal_disable" :value="scope.row.status" />
        </template>
      </vxe-column>
      <vxe-column field="roleType" title="角色类型" min-width="100" show-overflow>
        <template #default="{ row }">
          <dict-tag :options="business_ascription" :value="row.roleType" />
        </template>
      </vxe-column>
      <vxe-column field="remark" title="角色描述" min-width="100" show-overflow />
      <vxe-column field="createTime" title="创建时间" min-width="150" show-overflow />
      <vxe-column
        field="businessLineNames"
        title="业务线"
        min-width="100"
        show-overflow
      />
      <template #opts="{ row }">
        <TableColOptBtn
          :key="Math.random()"
          :buttons="[
            {
              text: '用户',
              click: () => handleAuthUser(row),
              permission: ['system:role:user'],
            },
            {
              text: '数据权限',
              click: () => handleDataScope(row),
              permission: ['system:role:dataScope'],
            },
            {
              text: '菜单权限',
              click: () => handleMenuScope(row),
              permission: ['system:role:menuScope'],
            },
          ]"
        />
      </template>
    </CustomTable>

    <!-- 用户授权  -->
    <AuthUserModal ref="authUserRef" />

    <!-- 添加或修改角色配置对话框  -->
    <EditRoleModal
      ref="roleModalRef"
      :normal_disable="normal_disable"
      @change="getList"
    />

    <!-- 分配用户数据权限  -->
    <EditRoleDataScope
      ref="roleDataScopeRef"
      :business_ascription="business_ascription"
      @change="getList"
    />

    <!-- 分配菜单权限  -->
    <EditMenuScopeModal ref="menuScopeRef" @change="getList" />

    <!-- 新增子角色  -->
    <AddChildRoleModal ref="childRoleRef" @change="getList" />
  </div>
</template>

<script setup name="Role">
import AuthUserModal from "./components/AuthUserModal.vue"
import EditRoleModal from "./components/EditRoleModal.vue"
import EditRoleDataScope from "./components/EditRoleDataScope.vue"
import EditMenuScopeModal from "./components/EditMenuScopeModal.vue"
import AddChildRoleModal from "./components/AddChildRoleModal.vue"
import { eduUser } from "@/config/constant"
import {
  changeRoleStatus,
  delRole,
  delZsRoleInfo,
  zsRoleListPage,
} from "@/api/system/role"
import { syncRoleInfos } from "@/api/syncdata/syncdata"

const { proxy } = getCurrentInstance()
const { normal_disable, business_ascription } = proxy.useDict(
  "normal_disable",
  "business_ascription"
)

const roleList = ref([])
const loading = ref(true)
const syncUserLoading = ref(false)
const showSearch = ref(true)
const ids = ref([])
const selectionList = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])

// 组件ref
const authUserRef = ref(null)
const roleModalRef = ref(null)
const roleDataScopeRef = ref(null)
const menuScopeRef = ref(null)
const childRoleRef = ref(null)

const queryParams = ref({
  page: 1,
  limit: 10,
  roleName: null,
  roleKey: null,
  status: null,
})

const customTableRef = ref(null) // 表格ref
const isExpandAll = ref(false) // 是否全部展开

/** 查询角色列表 */
function getList() {
  loading.value = true
  queryParams.value.createTimeStart = dateRange.value?.[0]
  queryParams.value.createTimeEnd =
    dateRange.value?.[1] != null ? dateRange.value?.[1].split(" ")[0] + " 23:59:59" : null
  zsRoleListPage(proxy.addDateRange(queryParams.value))
    .then(async (response) => {
      roleList.value = response.records.map((item) => ({
        ...item,
        children:
          item.children?.map((child, index, array) => ({
            ...child,
            isLast: index === array.length - 1,
          })) || [],
      }))
      total.value = response.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(`是否确认删除角色"${row.roleName}"?`)
    .then(function () {
      return delZsRoleInfo(row.roleId)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    })
    .catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    eduUser + "/zsRoleInfoApi/zsRoleListExport",
    {
      ...queryParams.value,
    },
    `角色_${new Date().getTime()}.xlsx`
  )
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.roleId)
  selectionList.value = selection
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 角色状态修改 */
function handleStatusChange(row) {
  const actionText = row.status === "0" ? "启用" : "停用"
  const confirmText = `确认要"${actionText}" "${row.roleName}" 角色吗?`
  const successText = `${actionText}成功`

  proxy.$modal
    .confirm(confirmText)
    .then(() => changeRoleStatus(row.roleId, row.status))
    .then(() => proxy.$modal.msgSuccess(successText))
    .catch(() => {
      row.status = row.status === "0" ? "1" : "0"
    })
}

/** 分配用户 */
function handleAuthUser(row) {
  authUserRef.value.open(row)
}

/** 添加角色 */
function handleAdd() {
  roleModalRef.value.open()
}

/** 新增子角色 */
function handleAddChildRole(row) {
  childRoleRef.value.open(row)
}
/** 修改子角色 */
function handleUpdate(row) {
  let parentInfo = roleList.value.find((i) => i.roleId == row.parentId)
  childRoleRef.value.open(parentInfo, row)
}

/** 分配数据权限操作 */
function handleDataScope(row) {
  roleDataScopeRef.value.open(row)
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  isExpandAll.value = !isExpandAll.value
  handleExpand()
}

function handleExpand() {
  if (isExpandAll.value) {
    customTableRef.value?.tableRef?.setAllTreeExpand(true)
  } else {
    customTableRef.value?.tableRef?.clearTreeExpand()
  }
}

function rowClassName({ row, rowIndex }) {
  if (row.parentId && +row.parentId !== 0) {
    return row.isLast ? "last-child-row" : ""
  }
  return ""
}

/** 数据同步 */
async function handleDataSync() {
  try {
    syncUserLoading.value = true
    let res = await syncRoleInfos({})
    if (+res.code == 200) {
      syncUserLoading.value = false
    }
    getList()
  } finally {
    loading.value = false
  }
}

/** 菜单权限 */
function handleMenuScope(row) {
  menuScopeRef.value.open(row)
}

function isChildRole(row) {
  return row.parentId && +row.parentId !== 0
}

getList()
</script>

<style lang="scss" scoped>
:deep(*) {
  .vxe-table--render-default .vxe-body--row.row--stripe > .vxe-body--column {
    background-color: #fff;
  }
  .vxe-body--row.row--level-0:nth-child(even) .vxe-body--column {
    background-color: #f7f8f9;
  }
  .vxe-table--render-default .vxe-body--row.row--stripe.is--expand-tree,
  .vxe-body--row.row--level-1 {
    .vxe-table-icon-caret-right {
      color: var(--el-color-primary);
    }
    .vxe-body--column {
      background-color: var(--el-color-primary-light-9) !important;
      &:first-child {
        border-left: 1px solid var(--el-color-primary-light-5);
      }
      &:last-child {
        border-right: 1px solid var(--el-color-primary-light-5);
      }
    }
  }
  .last-child-row {
    .vxe-body--column {
      border-bottom: 1px solid var(--el-color-primary-light-5);
    }
  }
  .vxe-table--render-default .vxe-body--row.row--stripe.is--expand-tree {
    .vxe-body--column {
      border-top: 1px solid var(--el-color-primary-light-5);
    }
  }
}
</style>
