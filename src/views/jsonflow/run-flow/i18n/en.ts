export default {
	runFlow: {
		index: "#",
		importrunFlowTip: "import RunFlow",
		id: "id",
		initiatorId: "initiatorId",
		defFlowId: "defFlowId",
		groupName: "groupName",
		startTime: "startTime",
		endTime: "endTime",
		useTime: "useTime",
		invalidReason: "invalidReason",
		status: "status",
		version: "version",
		createUser: "createUser",
		createTime: "createTime",
		orderId: "orderId",
		flowKey: "flowKey",
		updateUser: "updateUser",
		code: "code",
		formId: "formId",
		updateTime: "updateTime",
		flowName: "flowName",
		remark: "remark",
		isIndependent: "isIndependent",

		inputIdTip: "input id",
		inputInitiatorIdTip: "input initiatorId",
		inputDefFlowIdTip: "input defFlowId",
		inputGroupNameTip: "input groupName",
		inputStartTimeTip: "input startTime",
		inputEndTimeTip: "input endTime",
		inputUseTimeTip: "input useTime",
		inputInvalidReasonTip: "input invalidReason",
		inputStatusTip: "input status",
		inputVersionTip: "input version",
		inputCreateUserTip: "input createUser",
		inputCreateTimeTip: "input createTime",
		inputOrderIdTip: "input orderId",
		inputFlowKeyTip: "input flowKey",
		inputUpdateUserTip: "input updateUser",
		inputCodeTip: "input code",
		inputFormIdTip: "input formId",
		inputUpdateTimeTip: "input updateTime",
		inputFlowNameTip: "input flowName",
		inputRemarkTip: "input remark",
		inputIsIndependentTip: "input isIndependent",
	},
}
