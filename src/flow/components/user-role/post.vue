<template>
	<div v-if="data.updateMultiple" style="width: 100%">
		<el-select
			v-model="data.modelValue"
			clearable
			filterable
			:multiple="props.multiple"
			:disabled="props.disabled"
			@change="changeModelValue"
		>
			<el-option
				v-for="(item, index) in dicData.posts"
				:key="index"
				:label="item.postName"
				:value="item.postId"
			/>
		</el-select>
	</div>
</template>

<script setup lang="ts" name="UserRolePickerIndex">
// 定义字典
import { onLoadDicUrl } from "@/flow/components/convert-name/convert"

const props = defineProps({
	modelValue: {
		type: [Number, String, Array],
		default: null,
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
})

const emits = defineEmits(["update:modelValue"])

const dicData = reactive({})
const onLoad = onLoadDicUrl({ key: "posts" })
onMounted(async () => {
	await onLoad(dicData)
})

const data = reactive({
	modelValue: null,
	updateMultiple: true,
})

const changeModelValue = value => {
	data.modelValue = value
	emits("update:modelValue", value)
}

// 监听双向绑定
watch(
	() => props.multiple,
	val => {
		data.updateMultiple = false
		changeModelValue(null)
		// 防止切换时报错
		setTimeout(() => {
			data.updateMultiple = true
		})
	},
)

onMounted(() => {
	data.modelValue = props.modelValue
})
</script>
