<template>
  <div class="app-container">
    <el-container class="manual-container">
      <el-aside width="280px" class="sidebar">
        <div class="sidebar-content">
          <div class="category-section">
            <h4 class="category-title">操作手册列表</h4>
            <el-menu
                :default-active="activeMenuIndex"
                class="manual-menu"
                @select="handleDocSelect"
            >
              <el-menu-item
                  v-for="(item, index) in tableData"
                  :key="item.id"
                  :index="item.id"
                  class="manual-menu-item"
              >
                <span class="menu-item-content">
                  <span class="menu-title-dot"></span>
                  <span class="menu-title">{{ item.docName }}</span>
                </span>
              </el-menu-item>
            </el-menu>
          </div>
        </div>
      </el-aside>
      <el-main class="main-content">
        <div class="content-header" v-if="currentDoc">
          <div class="doc-info">
            <h2 class="doc-title">{{ currentDoc.docName }}</h2>
            <div class="doc-meta">
              <span class="meta-item">格式：{{ fileType(currentDoc.fileName) }}</span>
              <span class="meta-item">上传人：{{ currentDoc.uploadUserName || '未知上传人' }}</span>
              <span class="meta-item">版本号：{{ currentDoc.version }}</span>
              <span class="meta-item">上传时间：{{ currentDoc.uploadTime }}</span>
            </div>
          </div>
          <div class="doc-actions">
            <el-button type="primary" plain icon="Download" @click="downloadDoc">
              下载文件
            </el-button>
          </div>
        </div>

        <div class="content-body">
          <el-card class="preview-card" shadow="never">
            <FilePreview :fileRaw="fileRaw" v-if="fileRaw.raw"></FilePreview>
            <div v-else class="empty-content">
              <el-empty description="请选择要查看的操作手册"></el-empty>
            </div>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts" name="PreviewDoc">
import FilePreview from '@/views/fileManagement/fileLunch/components/FilePreview.vue'
import {startDownload} from "@/api/ess/downloadCenter/downloadCenter"
import {
  detailManualCfgInfo,
  getNavManualCfgListPage
} from "@/api/ess/doc/manualCfgInfo"
import {handleBlobFile} from "@/utils"

const {proxy} = getCurrentInstance()

const fileId = ref()
const activeMenuIndex = ref('0')
const currentDoc = ref(null)
const fileRaw = ref({
  name: "",
  raw: null
})

const loading = ref(true)
// 查询参数
const queryParams = reactive({
  limit: 10,
  page: 1
})
// 表格数据
const tableData = ref([])
const total = ref(0)

// 获取文档分页
const getList = async () => {
  loading.value = true
  try {
    const res = await getNavManualCfgListPage(queryParams)
    tableData.value = res.object.records
    total.value = res.object.total

    // 如果有数据且没有选中项，默认选中第一项
    if (tableData.value.length > 0 && !currentDoc.value) {
      fileId.value = tableData.value[0].id
      currentDoc.value = tableData.value[0]
      activeMenuIndex.value = tableData.value[0].id
      await getFileDetail()
    }
  } catch (error) {
    console.error('获取文档列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取文档详情并获取PDF
const getFileDetail = async () => {
  if (!fileId.value) return

  try {
    const fileDetail = await detailManualCfgInfo(fileId.value)
    fileRaw.value.name = fileDetail.object.fileName
    currentDoc.value = {
      ...currentDoc.value,
      ...fileDetail.object,
    }

    const res = await startDownload(fileDetail.object.fileUrl)
    const blob = new Blob([res.data], {type: 'application/pdf'})
    fileRaw.value.raw = URL.createObjectURL(blob)
  } catch (error) {
    console.error('获取文件详情失败:', error)
    proxy.$modal.msgError('获取文件失败')
  }
}

// 文件类型
const fileType = (fileName) => {
  return fileName ? fileName.replace(/[?#].*$/, "").split(".").pop().toUpperCase() : "PDF"
}

// 选择文档
const handleDocSelect = async (id) => {
  const selectedItem = tableData.value.find((item) => item.id === id)
  activeMenuIndex.value = selectedItem.id
  if (selectedItem) {
    fileId.value = selectedItem.id
    currentDoc.value = selectedItem
    await getFileDetail()
  }
}

// 下载文档
const downloadDoc = () => {
  startDownload(currentDoc.value.fileUrl).then(resp => {
    handleBlobFile(resp.data, currentDoc.value.docName+ ".pdf")
  })
}

onMounted(() => {
  const docId = history.state.docId
  if (docId) {
    fileId.value = docId
    activeMenuIndex.value = docId
  }
  getFileDetail()
  getList()
})

onUnmounted(() => {
  if (fileRaw.value.raw) {
    URL.revokeObjectURL(fileRaw.value.raw)
  }
})
</script>

<style scoped lang="scss">
.manual-container {
  height: calc(100vh - 140px);
  background: #f5f5f5;
}

.sidebar {
  background: #fff;
  border-radius: 5px 0 0 5px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-right: 10px;
}

.sidebar-header {
  padding: 20px 16px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.sidebar-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.category-section {
  margin-bottom: 24px;
}

.category-title {
  margin: 0 0 12px 16px;
  font-size: 15px;
  font-weight: 500;
  color: #000000;
}

.manual-menu {
  border: none;
  background: transparent;

  :deep(.el-menu-item) {
    height: 40px;
    line-height: 40px;
    margin: 0 8px 4px;
    border-radius: 4px;
    padding: 0 10px !important;

    &:hover {
      background-color: rgba(36, 168, 126, 0.11);
      color: var(--el-color-primary);
    }

    &.is-active {
      background-color: rgba(36, 168, 126, 0.11);
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }
}

.menu-item-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.menu-title-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--el-color-primary);
  margin-right: 8px;
  flex-shrink: 0;
}

.menu-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.main-content {
  padding: 0;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  border-radius: 0 5px 5px 0;
}

.content-header {
  background: #fff;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 2px;
    background: repeating-linear-gradient(
            to right,
            #F3F3F3 0,
            #F3F3F3 5px,
            transparent 5px,
            transparent 10px
    );
  }
}

.doc-info {
  flex: 1;
}

.doc-title {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.doc-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #6B6B6B;
}

.meta-item {
  display: flex;
  align-items: center;
}

.doc-actions {
  margin-left: 16px;
}

.content-body {
  flex: 1;
  overflow: hidden;
}

.preview-card {
  height: 100%;
  border: none !important;
  border-radius:0;

  :deep(.el-card__body) {
    height: 100%;
    padding: 0;
  }
}

.empty-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>
