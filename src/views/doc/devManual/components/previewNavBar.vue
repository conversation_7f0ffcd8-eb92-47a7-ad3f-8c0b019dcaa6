<template>
  <div class="navbar">
    <div
        v-if="!settingsStore.topNav && settingsStore.homeLogoUrl"
        class="logo-wrapper cursor-default"
    >
      <img :src="settingsStore.homeLogoUrl"/>
      <span>电子印章平台开发手册</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import useSettingsStore from "@/store/modules/settings"

const settingsStore = useSettingsStore()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;
@import url("@/assets/fonts/font.scss");

.navbar {
  height: $base-nav-height;
  overflow: hidden;
  position: fixed;
  z-index: 2001;
  top: 0;
  left: 0;
  width: 100%;
  background: var(--el-color-primary);
  color: #fff;
}

.logo-wrapper {
  height: 100%;
  display: inline-flex;
  align-items: center;
  margin-left: 20px;
  font-size: 24px;
  font-family: "DingTalk";

  img {
    height: 70%;
    object-fit: contain;
    margin-right: 10px;
  }
}

</style>
