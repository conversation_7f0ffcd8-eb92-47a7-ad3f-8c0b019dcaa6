import request from "@/utils/request"
import { eduUser } from "@/config/constant"

// 查询登录日志列表
export function list(query) {
	return request({
		url: eduUser + "/monitor/logininfor/list",
		method: "get",
		params: query,
	})
}

// 删除登录日志
export function delLogininfor(infoId) {
	return request({
		url: eduUser + "/monitor/logininfor/" + infoId,
		method: "delete",
	})
}

// 解锁用户登录状态
export function unlockLogininfor(userName) {
	return request({
		url: eduUser + "/monitor/logininfor/unlock/" + userName,
		method: "get",
	})
}
// 清空登录日志
export function cleanLogininfor() {
	return request({
		url: eduUser + "/monitor/logininfor/clean",
		method: "delete",
	})
}
