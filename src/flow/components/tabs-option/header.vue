<template>
	<div>
		<div class="header">
			<el-tabs v-model="form.active" @tab-click="activeMenu">
				<el-tab-pane label="① 表单设置" name="tabsSetting" />
				<el-tab-pane label="② 表单设计" name="formDesign" />
			</el-tabs>

			<div v-if="form.active !== 'formDesign'" class="btn-publish">
				<el-button icon="CaretRight" type="primary" @click="activeMenu({paneName: 'formDesign'})">
					下一步
				</el-button>
			</div>
			<div v-if="form.active === 'formDesign'" class="btn-publish">
				<el-button icon="CirclePlus" type="primary" @click="publish('-1')">
					暂存
				</el-button>
				<el-button icon="Promotion" type="primary" @click="publish('1')">
					发布
				</el-button>
			</div>
			<div class="btn-back">
				<el-button icon="Back" circle @click="exitTabsForm" />
				<span style="margin-left: 20px;">
					<i :class="props.currFlowForm.icon" />  <span>{{ props.currFlowForm.formName }}</span>
				</span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="TabsDesignHeader">
import { useI18n } from "vue-i18n"
import { useMessageBox } from "@/hooks/message"

const $emit = defineEmits(["handleDesignTabs", "syncCurrFlowForm", "publish"])
const props = defineProps({
	currFlowForm: {
		type: Object,
		default: null,
	},
})
const form = reactive({
	active: "tabsSetting",
})

function publish(status) {
	$emit("publish", form.active, () => {
		props.currFlowForm[form.active] = true
	}, status)
}
async function exitTabsForm() {
	let text = ""
	// 判断是否都保存
	if (props.currFlowForm.formDesign !== true) { text += "【表单设计】," }
	if (props.currFlowForm.tabsSetting !== true) { text += "【表单设置】" }
	if (!text) {
		$emit("handleDesignTabs", false)
		return
	}
	try {
		await useMessageBox().confirm(text + "未保存，是否继续退出?")
	}
	catch {
		return
	}
	$emit("handleDesignTabs", false)
}

function activeMenu(tab) {
	let menu = tab.paneName
	let active = form.active
	$emit("syncCurrFlowForm", preSave => {
		if (!preSave) { menu = active }
		props.currFlowForm[menu] = false
		form.active = menu
		props.currFlowForm.active = menu
	})
}

// 关闭提示
function listenPage() {
	window.onbeforeunload = function(e) {
		e = e || window.event
		if (e) {
			e.returnValue = "关闭提示"
		}
		return "关闭提示"
	}
}

onMounted(() => {
	listenPage()
})
</script>
<style lang="scss" scoped>
  .header {
    min-width: 980px;

    .el-tabs {
      position: absolute;
      top: 15px;
      z-index: 999;
      display: flex;
      justify-content: center;
      width: 100%;
    }

    .btn-publish {
      position: absolute;
      top: 20px;
      z-index: 1000;
      right: 40px !important;
      i {
        margin-right: 6px;
      }
      button {
        width: 74px;
        height: 28px;
        border-radius: 15px;
      }
    }
    .btn-back {
      position: absolute;
      top: 20px;
      z-index: 1000;
      left: 20px !important;
      font-size: 18px;
      i {
        margin-right: 6px;
      }
      button {
        width: 44px;
        height: 28px;
        border-radius: 15px;
      }
    }
  }
</style>

<style lang="scss" scoped>

</style>
