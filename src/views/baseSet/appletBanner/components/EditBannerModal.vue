<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="550px"
      :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="名称" prop="bannerName">
        <el-input
            v-model="form.bannerName"
            placeholder="请输入名称"
            maxlength="50"
            clearable
        />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort" :min="0"/>
      </el-form-item>
      <el-form-item label="图片" prop="imgUrl">
        <el-image
            v-if="form.imgUrl"
            style="width: 100%; height: 200px; margin-bottom: 10px"
            fit="cover"
            :preview-src-list="[imageApi + getFullImageUrl]"
            :src="imageApi + getFullImageUrl"
        />
        <FileUpload
            ref="fileUploadRef"
            v-model="form.imgUrl"
            :file-type="['png', 'jpg', 'jpeg']"
            :highlight-tip="false"
            :show-list="false"
            :btn-text="form.imgUrl ? '重新上传' : '上传图片'"
            class="upload-file-wrapper"
        />
      </el-form-item>
      <el-form-item label="跳转链接" prop="jumpUrl">
        <el-input v-model="form.jumpUrl" placeholder="请输入跳转链接" clearable/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 取消</el-button>
      <el-button type="success" :loading="loading" @click="submitAndAdd">
        保存并新建
      </el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"> 保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup name="EditBannerModal">
import { imageApi } from "@/config/constant"
import { addBannerCfgInfo, updateBannerCfgInfo } from "@/api/ess/baseSet/bannerCfgInfo"
import { deleteMinio } from "@/api/file/file"
import { rule } from "@/utils/validate.js";

const { proxy } = getCurrentInstance()

const title = ref("新增")
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref(null)

const form = ref({
  bannerName: null,
  jumpUrl: null,
  imgUrl: null,
  sort: null,
})
const rules = ref({
  bannerName: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { validator: rule.specialCharacter, trigger: "blur" }
  ],
  sort: [{ required: true, message: "请输入排序", trigger: ["blur", "change"] }],
  imgUrl: [{ required: true, message: "请上传图片", trigger: "change" }],
})
const emit = defineEmits(["change"])

const open = (item) => {
  dialogVisible.value = true
  nextTick(() => {
    if (item?.id) {
      form.value = { ...item, oldUrl: item.imgUrl }
    }
  })
  title.value = item?.id ? "编辑" : "新增"
}
// 取消
const handleClose = () => {
  proxy.resetForm("formRef")
  dialogVisible.value = false
}

// 保存并新建
const submitAndAdd = () => {
  submitForm("add")
}
// 确定
const submitForm = (type = "save") => {
  proxy.$refs["formRef"].validate(async (valid) => {
    if (!valid) {
      return
    }

    loading.value = true
    const actionUrl = form.value.id ? updateBannerCfgInfo : addBannerCfgInfo
    if (form.value.id && form.value.imgUrl !== form.value.oldUrl) {
      deleteOldImage() // 删除旧图片
    }
    try {
      const res = await actionUrl(form.value)
      if (+res.code === 200) {
        handleSuccess(type)
      }
    } catch (error) {
      console.error("提交表单时出错:", error)
    } finally {
      loading.value = false
    }
  })
}

const handleSuccess = (type) => {
  const message = form.value.id ? "修改成功" : "新增成功"
  proxy.$modal.msgSuccess(message)
  // 保存并新增
  if (type === "add") {
    title.value = "新增"
    proxy.resetForm("formRef")
    form.value.id = null
    emit("change")
  } else {
    handleClose()
    form.value.id ? emit("change") : emit("refresh")
  }
}

const deleteOldImage = async () => {
  try {
    await deleteMinio({ filename: form.value.oldUrl })
  } catch (err) {
  }
}

const fileUploadRef = ref(null)
// 添加计算属性获取完整URL
const getFullImageUrl = computed(() => {
  if (!form.value.imgUrl) return ""
  return fileUploadRef.value?.getFullUrlForParent(form.value.imgUrl) || form.value.imgUrl
})

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.upload-file-wrapper {
  :deep(.upload-file-box) {
    display: flex;
    align-items: center;

    .upload-file-uploader {
      margin-right: 10px;
    }
  }
}
</style>
