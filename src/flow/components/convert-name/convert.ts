import { ElMessage } from 'element-plus'
import { BasicTableProps } from '@/hooks/table'
import { validateNull } from '@/utils/validate'
import { listDicData, listDicUrl } from '@/api/jsonflow/common'
/**
 * 远程请求数据字典工具类
 *
 * <AUTHOR>
 * @date 2023-04-25 16:41:52
 */
interface DicDataInterface {
	key: string;
	method?: string;
	dicUrl?: string;
	prefix?: string;
	cascades?: string[];
}

const dicPostUrls = {
	listWidthUserIds: '/cloud-jsonflow/user-role-auditor/list/user-ids',
	listByFlowInstIds: '/cloud-jsonflow/run-flow/list/flow-inst-id',
  listByOrderIds: "/cloud-order/run-application/list/order-id",
	listByRunNodeIds: '/cloud-jsonflow/run-node/list/run-node-id',
	listByRunJobIds: '/cloud-jsonflow/run-job/list/run-job-id'
}

/**
 * 默认回调映射关系
 */
const dicPostFun = {
    createUser: dicPostUrls.listWidthUserIds,
    initiatorId: dicPostUrls.listWidthUserIds,
    carbonCopyPerson: dicPostUrls.listWidthUserIds,
    userId: dicPostUrls.listWidthUserIds,
    handleUserId: dicPostUrls.listWidthUserIds,
    parFlowInstId: dicPostUrls.listByFlowInstIds,
    subFlowInstId: dicPostUrls.listByFlowInstIds,
    flowInstId: dicPostUrls.listByFlowInstIds,
    orderId: dicPostUrls.listByOrderIds,
    runNodeId: dicPostUrls.listByRunNodeIds,
    runJobId: dicPostUrls.listByRunJobIds,
    fromRunNodeId: dicPostUrls.listByRunNodeIds,
    toRunNodeId: dicPostUrls.listByRunNodeIds,
}

/**
 * 表格数据加载完成后事件
 */
export const onLoaded = (...keyObjs: DicDataInterface[]) => {
	return async (state: BasicTableProps) => {
		let dataList = state.dataList
		if (dataList && dataList.length > 0) {
			try {
				let dicData = {}
				for (const keyObj of keyObjs) {
					let { key, dicUrl } = keyObj
					let keys = dataList.map(m => m[key]).flat()
						.filter(f => !validateNull(f))
					let ids = keys.filter((item, index) => {
						return keys.indexOf(item, 0) === index
					})
					if (validateNull(ids)) continue
					if (!dicUrl) dicUrl = dicPostFun[key]
					// @ts-ignore
					let res = await listDicData(dicUrl, ids)
					validateNull(res.data) ? dicData[key] = [] : dicData[key] = res.data
				}
				Object.assign(state.dicData, dicData)
			}
			catch (err: any) {
			// 捕获异常并显示错误提示
				ElMessage.error(err.msg || err.data.msg)
			}
		}
	}
}

const cascadeUrls = {
	listFlowNodeByDefFlowId: '/cloud-jsonflow/flow-node/list/def-flow-id/{{key}}',
	listFlowNodeRelByDefFlowId: '/cloud-jsonflow/flow-node-rel/list/def-flow-id/{{key}}',
	listNodeJobByFlowNodeId: '/cloud-jsonflow/node-job/list/flow-node-id/{{key}}',
	listPreRunNodeByFlowInstId: '/cloud-jsonflow/run-node/flow-inst-id/pre/{{key}}?runNodeId={{runNodeId}}',
	listAnyJumpRunNodeByFlowInstId: '/cloud-jsonflow/run-node/flow-inst-id/any-jump/{{key}}?runNodeId={{runNodeId}}',
	listRunNodeByFlowInstId: '/cloud-jsonflow/run-node/list/flow-inst-id/{{key}}',
	listUsersByRunNodeId: '/cloud-jsonflow/run-job/list/users/run-node-id/{{key}}',
	listUserByRunNodeId: '/cloud-jsonflow/run-job/list/user/run-node-id/{{key}}',
	listRoleByRunNodeId: '/cloud-jsonflow/run-job/list/role/run-node-id/{{key}}',
	listPostByRunNodeId: '/cloud-jsonflow/run-job/list/post/run-node-id/{{key}}',
	listDeptByRunNodeId: '/cloud-jsonflow/run-job/list/dept/run-node-id/{{key}}',
	listRunJobByRunNodeId: '/cloud-jsonflow/run-job/list/run-job/run-node-id/{{key}}',
	getRunFlowByCode: '/cloud-jsonflow/run-flow/code/{{key}}',
	getRunFlowByFlowInstId: '/cloud-jsonflow/run-flow/{{key}}',
	listUserByRoleId: '/cloud-jsonflow/user-role-auditor/list-users/{{key}}?jobType={{jobType}}'
}

/**
 * 默认回调映射关系
 */
const dicCascadeFun = {
	'flowNodeId': cascadeUrls.listFlowNodeByDefFlowId,
	'flowNodeRelId': cascadeUrls.listFlowNodeRelByDefFlowId,
	'nodeJobId': cascadeUrls.listNodeJobByFlowNodeId,
	'runReject.toRunNodeId': cascadeUrls.listPreRunNodeByFlowInstId,
	'runJob.toRunNodeId': cascadeUrls.listAnyJumpRunNodeByFlowInstId,
	'flowKey': cascadeUrls.getRunFlowByFlowInstId,
	'defFlowId': cascadeUrls.getRunFlowByFlowInstId,
	'userId': cascadeUrls.listUserByRoleId,
	'flowInstId': cascadeUrls.getRunFlowByCode,
	'runNodeId': cascadeUrls.listRunNodeByFlowInstId,
	'runJobId': cascadeUrls.listRunJobByRunNodeId,
	'fromRunNodeId': cascadeUrls.listRunNodeByFlowInstId,
	'toRunNodeId': cascadeUrls.listRunNodeByFlowInstId,
	'handleUserId': cascadeUrls.listUsersByRunNodeId,
	'anyJumpUserId': cascadeUrls.listUserByRunNodeId,
	'anyJumpRoleId': cascadeUrls.listRoleByRunNodeId,
	'anyJumpPostId': cascadeUrls.listPostByRunNodeId,
	'anyJumpDeptId': cascadeUrls.listDeptByRunNodeId
}

/**
 * 表格级联数据事件
 * dynkey 动态表格KEY
 */
export const onCascaded = (...keyObjs: DicDataInterface[]) => {
	return async (state: any, form?: any, dynkey?: any) => {
		let dataList = dynkey ? form[dynkey] : state.dataList
		if (dataList && dataList.length > 0) {
			try {
				let cascadeDic = {}
				for (const keyObj of keyObjs) {
					let { key, dicUrl, cascades } = keyObj
					if (!cascades) continue
					for (const cascade of cascades) {
						let realDicUrl = dicUrl
						if (!realDicUrl) realDicUrl = dicCascadeFun[cascade]
						for (let i = 0; i < dataList.length; i++) {
							let param = dataList[i][key]
							if (validateNull(param)) continue
							if (!validateNull(cascadeDic[param])) continue
							// @ts-ignore
							let url = realDicUrl.replace('{{key}}', param)
							if (url.indexOf('?') !== -1) {
								let params = url.split('?')[1].split('=')
								let p = dataList[i][params[0]]
								if (validateNull(p)) continue
								url = url.replace('{{' + params[0] + '}}', p)
							}
							// @ts-ignore
							let res = await listDicUrl(url)
							if (validateNull(res.data)) continue
							cascadeDic[param] = res.data
						}
					}
				}
				Object.assign(state.cascadeDic, cascadeDic)
			}
			catch (err: any) {
			// 捕获异常并显示错误提示
				ElMessage.error(err.msg || err.data.msg)
			}
		}
	}
}

/**
 * 表单级联数据变更事件
 * index 动态表格index
 */
export const onCascadeChange = (cascadeDic: any, ...keyObjs: DicDataInterface[]) => {
	return async (form: any, keyObjs2?: DicDataInterface, dynkey?: any, index?: number) => {
		try {
			if (keyObjs2 && !validateNull(keyObjs2)) keyObjs = [keyObjs2]
			for (const keyObj of keyObjs) {
				let { key, dicUrl, cascades, prefix } = keyObj
				if (!cascades) continue
				for (const cascade of cascades) {
					let realDicUrl = dicUrl
					let reqKey = cascade
					if (prefix) reqKey = prefix + '.' + cascade
					if (!realDicUrl) realDicUrl = dicCascadeFun[reqKey]
					// @ts-ignore
					let param = dynkey ? form[dynkey][index][key] : form[key]
					if (!realDicUrl || validateNull(param)) {
						if (validateNull(param)) clearCascadeVal(form, keyObjs2, cascade, dynkey, index)
						continue
					}
					let url = realDicUrl.replace('{{key}}', param)
					if (url.indexOf('?') !== -1) {
						let params = url.split('?')[1].split('=')
						// @ts-ignore
						let p = dynkey ? form[dynkey][index][params[0]] : form[params[0]]
						if (validateNull(p)) continue
						url = url.replace('{{' + params[0] + '}}', p)
					}
					// @ts-ignore
					let res = await listDicUrl(url)
					if (validateNull(res.object)) res.object = []
					let data = res.object
					if (!(data instanceof Array)) data = [data]
					if (typeof (index) !== 'undefined') { cascadeDic[param] = data }

					else { cascadeDic[cascade] = data }

					clearCascadeVal(form, keyObjs2, cascade, dynkey, index)
				}
			}
		}
		catch (err: any) {
			// 捕获异常并显示错误提示
			ElMessage.error(err.message || err.object.message)
		}
	}
}

function clearCascadeVal(form, keyObjs2, cascade, dynkey, index) {
	if (typeof (index) !== 'undefined') {
		if (!validateNull(keyObjs2)) form[dynkey][index][cascade] = null
	}
	else
		if (!validateNull(keyObjs2)) form[cascade] = null
}

const dicUrls = {
	listWidthUserIds: '/cloud-jsonflow/user-role-auditor/list/user-ids',
	flowApplicationGroupName: '/cloud-order/flow-application/list/group-name',
	listFlowApplication: '/cloud-order/flow-application/list',
	defFlows: '/cloud-jsonflow/def-flow/list',
	defFlowGroupName: '/cloud-jsonflow/def-flow/list/group-name',
	listRoleName: '/cloud-jsonflow/user-role-auditor/list/roles',
	listTableName: '/cloud-order/create-table/list',
	listUserKey: '/cloud-jsonflow/node-job/list/user-key',
	listFlowNodeId: '/cloud-jsonflow/flow-node/list',
	listNodeJobId: '/cloud-jsonflow/node-job/list',
	runFlows: '/cloud-jsonflow/run-flow/list',
	listDept: '/cloud-jsonflow/user-role-auditor/list/depts',
	listPost: '/cloud-jsonflow/user-role-auditor/list/posts',
	listUserKeyVal: '/cloud-jsonflow/node-job/list/user-key-val',
	listVarKey: '/cloud-jsonflow/flow-node-rel/list/var-key',
	listVarKeyVal: '/cloud-jsonflow/flow-node-rel/list/var-key-val',
	listVarVal: '/cloud-jsonflow/flow-clazz/list/var-val',
	listUsers: '/cloud-jsonflow/user-role-auditor/list/pageUsers?page=1&limit=50'
}

/**
 * 默认回调映射关系
 */
const dicUrlFun = {
	'groupName': dicUrls.flowApplicationGroupName,
	'defFlow.groupName': dicUrls.defFlowGroupName,
	'flowKey': dicUrls.defFlows,
	'carbonCopyPerson': dicUrls.listWidthUserIds,
	'tableName': dicUrls.listTableName,
	'permission': dicUrls.listRoleName,
	'defFlowId': dicUrls.defFlows,
	'userKey': dicUrls.listUserKey,
	'roleId': dicUrls.listRoleName,
	'userId': dicUrls.listWidthUserIds,
	'handleUserId': dicUrls.listWidthUserIds,
	'flowNodeId': dicUrls.listFlowNodeId,
	'nodeJobId': dicUrls.listNodeJobId,
	'initiatorId': dicUrls.listWidthUserIds,
	'createUser': dicUrls.listWidthUserIds,
	'code': dicUrls.runFlows,
	'flowInstId': dicUrls.runFlows,
	'receiveUser': dicUrls.listWidthUserIds,
	'receiveDept': dicUrls.listDept,
	'defFlows': dicUrls.defFlows,
	'userKeys': dicUrls.listUserKey,
	'userKeyVals': dicUrls.listUserKeyVal,
	'varKeyVals': dicUrls.listVarKeyVal,
	'varVals': dicUrls.listVarVal,
	'roles': dicUrls.listRoleName,
	'users': dicUrls.listUsers,
	'posts': dicUrls.listPost,
	'depts': dicUrls.listDept,
	'formId': dicUrls.listFlowApplication
}

/**
 * 加载业务字典数据
 */
export const onLoadDicUrl = (...keyObjs: DicDataInterface[]) => {
	return async (dicData: any) => {
		try {
			for (const keyObj of keyObjs) {
				let { key, dicUrl, prefix } = keyObj
				let res
				if (dicUrl) res = await listDicUrl(dicUrl)
				else {
					let reqKey = key
					if (prefix) reqKey = prefix + '.' + key
					res = await listDicUrl(dicUrlFun[reqKey])
				}
				validateNull(res.object) ? dicData[key] = [] : dicData[key] = res.object
			}
		}
		catch (err: any) {
			// 捕获异常并显示错误提示
			console.log(err)
			// ElMessage.error(err.message || err.data.message)
		}
	}
}

/**
 * 更新业务字典数据
 */
export const onUpdateDicData = (...keyObjs: DicDataInterface[]) => {
	return async (dicData: any, form: any, formKey?: any) => {
		if (!validateNull(dicData) && !validateNull(form)) {
			try {
				for (const keyObj of keyObjs) {
					let { key } = keyObj
					let newVal = form[key];
					let dicDataVal = dicData[key]
					if (validateNull(newVal) || validateNull(dicDataVal)) continue
					else {
						let exist = dicDataVal.filter(f => f[key] === newVal)
						if (!validateNull(exist)) continue
					}
          let realKey = formKey ? formKey : key;
					dicData[key].unshift({ [realKey]: newVal })
				}
			}
			catch (err: any) {
			// 捕获异常并显示错误提示
			// ElMessage.error(err.message || err.data.message)
			}
		}
	}
}
